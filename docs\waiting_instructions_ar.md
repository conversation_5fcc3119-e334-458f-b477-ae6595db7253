# تعليمات الانتظار أثناء تثبيت نظام "يمن ناف"

## هل يجب عليك الانتظار؟

**نعم، يجب عليك الانتظار** أثناء قيام ملف التثبيت بتنزيل الملفات وتثبيت البرامج. هذه عملية تلقائية ولكنها تحتاج إلى وقت لإكمالها.

## ماذا يحدث الآن؟

في هذه المرحلة، ملف التثبيت:

1. سيبدأ بإنشاء المجلدات اللازمة
2. سيبدأ بتنزيل البرامج المطلوبة من الإنترنت:
   - Node.js (حوالي 20-30 ميجابايت)
   - PostgreSQL (حوالي 200-300 ميجابايت)
   - Git (حوالي 50-60 ميجابايت)
   - URL Rewrite Module (حوالي 10 ميجابايت)
3. سيقوم بتثبيت هذه البرامج واحدًا تلو الآخر

## كم من الوقت سيستغرق التثبيت؟

- **إجمالي الوقت**: حوالي 30-60 دقيقة
- **تنزيل الملفات**: 5-15 دقيقة (حسب سرعة الإنترنت)
- **تثبيت البرامج**: 15-30 دقيقة
- **إعداد النظام**: 10-15 دقيقة

## هل يمكنك استخدام الكمبيوتر أثناء التثبيت؟

نعم، يمكنك استخدام الكمبيوتر لأغراض أخرى أثناء التثبيت، ولكن:

- **لا تغلق نافذة PowerShell** التي تعرض تقدم التثبيت
- **لا تقم بإيقاف تشغيل الكمبيوتر** أو وضعه في وضع السكون
- **تفاعل مع أي نوافذ منبثقة** قد تظهر أثناء تثبيت البرامج

## كيف تعرف أن التثبيت يعمل؟

ستظهر رسائل في نافذة PowerShell تشير إلى تقدم التثبيت، مثل:

```
بدء تثبيت نظام يمن ناف...
إنشاء المجلدات...
تنزيل الملفات المطلوبة...
تنزيل Node.js...
```

إذا لم تظهر هذه الرسائل بعد بضع دقائق، فقد يكون هناك مشكلة في تنفيذ الملف النصي.

## ماذا تفعل إذا لم تظهر أي رسائل؟

إذا لم تظهر أي رسائل بعد 5 دقائق:

1. اضغط على `Ctrl+C` لإيقاف التنفيذ
2. أدخل الأمر التالي لتغيير سياسة التنفيذ:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
   ```
3. ثم أعد تشغيل ملف التثبيت بشكل مباشر:
   ```powershell
   .\install_yemen_nav.ps1
   ```

## ماذا تفعل إذا ظهرت رسالة خطأ؟

إذا ظهرت رسالة خطأ:

1. التقط صورة للخطأ أو انسخ النص
2. تحقق من الخطوات التالية:
   - هل لديك اتصال إنترنت مستقر؟
   - هل لديك صلاحيات المسؤول؟
   - هل لديك مساحة تخزين كافية على القرص C؟

## الخلاصة

- **نعم، يجب عليك الانتظار** حتى اكتمال عملية التثبيت
- **لا تغلق نافذة PowerShell** أثناء التثبيت
- **تفاعل مع أي نوافذ منبثقة** قد تظهر
- **انتظر حتى ترى رسالة** "تم تثبيت وتشغيل نظام يمن ناف بنجاح!"

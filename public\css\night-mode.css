/* public/css/night-mode.css */
body.night-mode {
  background-color: #121212;
  color: #ffffff;
}

/* الخريطة في الوضع الليلي */
body.night-mode .leaflet-container {
  background-color: #242424;
}

/* العناصر في الوضع الليلي */
body.night-mode .header,
body.night-mode .controls .control-button,
body.night-mode .device-info,
body.night-mode .location-info,
body.night-mode .search-popup,
body.night-mode .status-bar,
body.night-mode .layers-control,
body.night-mode #route-info-box {
  background-color: #242424;
  color: #ffffff;
  border-color: #444444;
}

/* الأزرار في الوضع الليلي */
body.night-mode button:not(.control-button) {
  background-color: #444444;
  color: #ffffff;
  border-color: #666666;
}

/* حقول الإدخال في الوضع الليلي */
body.night-mode input,
body.night-mode select {
  background-color: #333333;
  color: #ffffff;
  border-color: #666666;
}

/* تنبيهات الصوت في الوضع الليلي */
body.night-mode #audio-notification {
  background-color: rgba(36, 36, 36, 0.9);
}

/* تعديل ألوان المسارات في الوضع الليلي */
body.night-mode .main-route {
  stroke: #4CAF50;
  stroke-opacity: 0.9;
}

body.night-mode .alternative-route {
  stroke: #ff9800;
  stroke-opacity: 0.7;
}

/* Popup windows in night mode */
body.night-mode .leaflet-popup-content-wrapper {
  background-color: #242424;
  color: #ffffff;
}

body.night-mode .leaflet-popup-tip {
  background-color: #242424;
}

/* Menu items in night mode */
body.night-mode .menu-item {
  background-color: #333333;
  color: #ffffff;
  border-color: #444444;
}

body.night-mode .menu-item:hover {
  background-color: #4a4a4a;
}

/* Login form in night mode */
body.night-mode .login-form {
  background-color: #242424;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

body.night-mode .login-form input {
  background-color: #333333;
  color: #ffffff;
  border-color: #444444;
}

body.night-mode .login-form button {
  background-color: #4CAF50;
}

body.night-mode .login-form button:hover {
  background-color: #45a049;
}

/* Tables in night mode */
body.night-mode table {
  border-color: #444444;
}

body.night-mode th {
  background-color: #333333;
  color: #ffffff;
  border-color: #444444;
}

body.night-mode td {
  border-color: #444444;
}

body.night-mode tr:nth-child(even) {
  background-color: #2a2a2a;
}

body.night-mode tr:hover {
  background-color: #3a3a3a;
}

/* Leaflet controls in night mode */
body.night-mode .leaflet-control-zoom a {
  background-color: #333333;
  color: #ffffff;
  border-color: #444444;
}

body.night-mode .leaflet-control-zoom a:hover {
  background-color: #444444;
}

body.night-mode .leaflet-control-layers {
  background-color: #242424;
  color: #ffffff;
  border-color: #444444;
}

/* Attribution in night mode */
body.night-mode .leaflet-control-attribution {
  background-color: rgba(36, 36, 36, 0.8);
  color: #aaaaaa;
}

body.night-mode .leaflet-control-attribution a {
  color: #4CAF50;
}

@echo off
echo Yemen GPS - تنظيف مجلد node_modules
echo ===================================
echo.
echo سيتم حذف المكتبات غير الضرورية من مجلد node_modules
echo.
echo يرجى الانتظار...
echo.

REM حفظ المكتبات الضرورية
set ESSENTIAL_PACKAGES=express cors body-parser dotenv compression helmet express-rate-limit node-cache jsonwebtoken pg mysql2 sqlite3 bcrypt axios leaflet canvas admin-bro admin-bro-expressjs

REM إنشاء مجلد مؤقت للمكتبات الضرورية
mkdir temp_modules

REM نسخ المكتبات الضرورية إلى المجلد المؤقت
for %%i in (%ESSENTIAL_PACKAGES%) do (
    if exist "node_modules\%%i" (
        echo نسخ %%i...
        xcopy /E /I /Y "node_modules\%%i" "temp_modules\%%i" > nul
    )
)

REM نسخ المجلدات الخاصة بالمكتبات الضرورية
if exist "node_modules\.bin" (
    echo نسخ .bin...
    xcopy /E /I /Y "node_modules\.bin" "temp_modules\.bin" > nul
)

REM حذف مجلد node_modules الأصلي
echo حذف مجلد node_modules...
rmdir /S /Q node_modules

REM إعادة تسمية المجلد المؤقت إلى node_modules
echo إعادة تسمية المجلد المؤقت...
rename temp_modules node_modules

echo.
echo تم تنظيف مجلد node_modules بنجاح!
echo تم الاحتفاظ بالمكتبات الضرورية فقط.
echo.
pause

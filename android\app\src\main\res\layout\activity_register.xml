<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.auth.RegisterActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:id="@+id/register_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/register"
                android:textSize="20sp"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/name_layout"
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:hint="@string/name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/name_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/email_layout"
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/email">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/email_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/phone_layout"
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/phone">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/phone_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/password_layout"
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/password"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/password_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/confirm_password_layout"
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/confirm_password"
                app:passwordToggleEnabled="true">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/confirm_password_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />

            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/register_button"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="@string/register" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Already have an account? " />

                <TextView
                    android:id="@+id/login_link"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

# خطة تنفيذ تحسينات نظام "يمن ناف" (تكملة)

## المرحلة الثانية: تحسين الخدمات والميزات (3-6 أشهر)

### 1. التوجيه الصوتي المخصص

#### الخطوات التنفيذية:
1. **إنشاء مكتبة للتنبيهات الصوتية**:
   ```javascript
   // public/js/audio/voice-guidance.js
   class VoiceGuidance {
     constructor(options = {}) {
       this.language = options.language || 'ar-YE';
       this.voice = options.voice || null;
       this.volume = options.volume || 1.0;
       this.rate = options.rate || 1.0;
       this.pitch = options.pitch || 1.0;
       this.muted = options.muted || false;
       
       // تهيئة نظام تحويل النص إلى كلام
       this.speechSynthesis = window.speechSynthesis;
       this.voices = [];
       
       // تحميل الأصوات المتاحة
       this.loadVoices();
       
       // تحميل الأصوات المسجلة مسبقًا
       this.prerecordedSounds = {
         turn_right: new Audio('/audio/turn_right.mp3'),
         turn_left: new Audio('/audio/turn_left.mp3'),
         continue_straight: new Audio('/audio/continue_straight.mp3'),
         arrive: new Audio('/audio/arrive.mp3'),
         traffic_jam: new Audio('/audio/traffic_jam.mp3'),
         speed_bump: new Audio('/audio/speed_bump.mp3'),
         pothole: new Audio('/audio/pothole.mp3'),
         dirt_road: new Audio('/audio/dirt_road.mp3'),
         checkpoint: new Audio('/audio/checkpoint.mp3')
       };
     }
     
     // تحميل الأصوات المتاحة
     loadVoices() {
       // الحصول على قائمة الأصوات المتاحة
       this.voices = this.speechSynthesis.getVoices();
       
       // إذا كانت القائمة فارغة، الانتظار حتى تحميل الأصوات
       if (this.voices.length === 0) {
         this.speechSynthesis.addEventListener('voiceschanged', () => {
           this.voices = this.speechSynthesis.getVoices();
           this.selectVoice();
         });
       } else {
         this.selectVoice();
       }
     }
     
     // اختيار الصوت المناسب
     selectVoice() {
       // البحث عن صوت باللغة العربية
       this.voice = this.voices.find(voice => voice.lang === this.language) || 
                   this.voices.find(voice => voice.lang.startsWith('ar')) ||
                   this.voices[0];
     }
     
     // نطق نص
     speak(text) {
       if (this.muted) return;
       
       // إيقاف أي كلام حالي
       this.speechSynthesis.cancel();
       
       // إنشاء كائن الكلام
       const utterance = new SpeechSynthesisUtterance(text);
       utterance.voice = this.voice;
       utterance.volume = this.volume;
       utterance.rate = this.rate;
       utterance.pitch = this.pitch;
       utterance.lang = this.language;
       
       // نطق النص
       this.speechSynthesis.speak(utterance);
     }
     
     // تشغيل صوت مسجل مسبقًا
     playSound(soundName) {
       if (this.muted) return;
       
       const sound = this.prerecordedSounds[soundName];
       if (sound) {
         // إيقاف الصوت إذا كان قيد التشغيل
         sound.pause();
         sound.currentTime = 0;
         
         // تشغيل الصوت
         sound.volume = this.volume;
         sound.play();
       }
     }
     
     // تشغيل تنبيه للانعطاف
     playTurnInstruction(direction, streetName) {
       if (direction === 'right') {
         this.playSound('turn_right');
         setTimeout(() => {
           if (streetName) {
             this.speak(`إلى ${streetName}`);
           }
         }, 1000);
       } else if (direction === 'left') {
         this.playSound('turn_left');
         setTimeout(() => {
           if (streetName) {
             this.speak(`إلى ${streetName}`);
           }
         }, 1000);
       } else {
         this.playSound('continue_straight');
       }
     }
     
     // تشغيل تنبيه للوصول
     playArrivalInstruction(placeName) {
       this.playSound('arrive');
       setTimeout(() => {
         if (placeName) {
           this.speak(`لقد وصلت إلى ${placeName}`);
         }
       }, 1000);
     }
     
     // تشغيل تنبيه للمخاطر
     playHazardWarning(hazardType, distance) {
       const distanceText = distance ? `على بعد ${distance} متر` : 'أمامك';
       
       switch (hazardType) {
         case 'traffic_jam':
           this.playSound('traffic_jam');
           setTimeout(() => {
             this.speak(`انتبه، ${distanceText} ازدحام مروري`);
           }, 1000);
           break;
         case 'speed_bump':
           this.playSound('speed_bump');
           setTimeout(() => {
             this.speak(`انتبه، ${distanceText} مطب`);
           }, 1000);
           break;
         case 'pothole':
           this.playSound('pothole');
           setTimeout(() => {
             this.speak(`انتبه، ${distanceText} حفرة`);
           }, 1000);
           break;
         case 'dirt_road':
           this.playSound('dirt_road');
           setTimeout(() => {
             this.speak(`انتبه، ${distanceText} طريق ترابي`);
           }, 1000);
           break;
         case 'checkpoint':
           this.playSound('checkpoint');
           setTimeout(() => {
             this.speak(`انتبه، ${distanceText} نقطة عسكرية`);
           }, 1000);
           break;
       }
     }
     
     // تغيير إعدادات الصوت
     updateSettings(settings) {
       if (settings.language) {
         this.language = settings.language;
         this.selectVoice();
       }
       
       if (settings.volume !== undefined) this.volume = settings.volume;
       if (settings.rate !== undefined) this.rate = settings.rate;
       if (settings.pitch !== undefined) this.pitch = settings.pitch;
       if (settings.muted !== undefined) this.muted = settings.muted;
     }
   }
   ```

2. **إنشاء واجهة مستخدم لإعدادات الصوت**:
   ```html
   <!-- public/voice-settings.html -->
   <div class="voice-settings-panel">
     <h3>إعدادات التوجيه الصوتي</h3>
     
     <div class="setting-group">
       <label for="voice-enabled">تفعيل التوجيه الصوتي</label>
       <input type="checkbox" id="voice-enabled" checked>
     </div>
     
     <div class="setting-group">
       <label for="voice-volume">مستوى الصوت</label>
       <input type="range" id="voice-volume" min="0" max="1" step="0.1" value="1">
     </div>
     
     <div class="setting-group">
       <label for="voice-rate">سرعة النطق</label>
       <input type="range" id="voice-rate" min="0.5" max="2" step="0.1" value="1">
     </div>
     
     <div class="setting-group">
       <label for="voice-pitch">نبرة الصوت</label>
       <input type="range" id="voice-pitch" min="0.5" max="2" step="0.1" value="1">
     </div>
     
     <div class="setting-group">
       <label for="voice-language">اللغة</label>
       <select id="voice-language">
         <option value="ar-YE" selected>العربية (اليمن)</option>
         <option value="ar-SA">العربية (السعودية)</option>
         <option value="en-US">الإنجليزية (الولايات المتحدة)</option>
       </select>
     </div>
     
     <div class="setting-group">
       <label for="voice-type">نوع الصوت</label>
       <select id="voice-type">
         <option value="male" selected>ذكر</option>
         <option value="female">أنثى</option>
       </select>
     </div>
     
     <button id="test-voice">اختبار الصوت</button>
     <button id="save-voice-settings">حفظ الإعدادات</button>
   </div>
   ```

3. **تنفيذ وظيفة التوجيه الصوتي أثناء الملاحة**:
   ```javascript
   // public/js/navigation/voice-navigation.js
   class VoiceNavigation {
     constructor(map, router) {
       this.map = map;
       this.router = router;
       this.voiceGuidance = new VoiceGuidance();
       this.currentRoute = null;
       this.currentStep = 0;
       this.isNavigating = false;
       
       // تحميل إعدادات الصوت من التخزين المحلي
       this.loadSettings();
       
       // تهيئة مراقبة الموقع
       this.watchId = null;
       this.lastPosition = null;
       this.upcomingHazards = new Set();
     }
     
     // تحميل إعدادات الصوت
     loadSettings() {
       const settings = JSON.parse(localStorage.getItem('voiceSettings')) || {};
       this.voiceGuidance.updateSettings(settings);
     }
     
     // بدء الملاحة
     startNavigation(route) {
       this.currentRoute = route;
       this.currentStep = 0;
       this.isNavigating = true;
       this.upcomingHazards.clear();
       
       // بدء مراقبة الموقع
       this.startLocationTracking();
       
       // تشغيل تنبيه بدء الملاحة
       this.voiceGuidance.speak('بدء الملاحة');
       
       // تشغيل التنبيه الأول
       this.announceNextStep();
     }
     
     // إيقاف الملاحة
     stopNavigation() {
       this.isNavigating = false;
       this.currentRoute = null;
       this.currentStep = 0;
       this.upcomingHazards.clear();
       
       // إيقاف مراقبة الموقع
       this.stopLocationTracking();
       
       // تشغيل تنبيه إيقاف الملاحة
       this.voiceGuidance.speak('تم إيقاف الملاحة');
     }
     
     // بدء مراقبة الموقع
     startLocationTracking() {
       if (navigator.geolocation) {
         this.watchId = navigator.geolocation.watchPosition(
           this.handlePositionUpdate.bind(this),
           this.handlePositionError.bind(this),
           {
             enableHighAccuracy: true,
             maximumAge: 0,
             timeout: 5000
           }
         );
       }
     }
     
     // إيقاف مراقبة الموقع
     stopLocationTracking() {
       if (this.watchId !== null) {
         navigator.geolocation.clearWatch(this.watchId);
         this.watchId = null;
       }
     }
     
     // معالجة تحديث الموقع
     handlePositionUpdate(position) {
       const currentPosition = {
         lat: position.coords.latitude,
         lng: position.coords.longitude
       };
       
       // تحديث الموقع الحالي
       this.lastPosition = currentPosition;
       
       if (!this.isNavigating || !this.currentRoute) return;
       
       // التحقق من الوصول إلى الوجهة
       if (this.isAtDestination(currentPosition)) {
         this.voiceGuidance.playArrivalInstruction(this.currentRoute.destination.name);
         this.stopNavigation();
         return;
       }
       
       // التحقق من الوصول إلى خطوة جديدة
       if (this.isAtNextStep(currentPosition)) {
         this.currentStep++;
         this.announceNextStep();
       }
       
       // التحقق من المخاطر القادمة
       this.checkUpcomingHazards(currentPosition);
     }
     
     // معالجة خطأ تحديد الموقع
     handlePositionError(error) {
       console.error('Error getting location:', error);
       
       // تشغيل تنبيه خطأ تحديد الموقع
       this.voiceGuidance.speak('تعذر تحديد موقعك الحالي');
     }
     
     // التحقق من الوصول إلى الوجهة
     isAtDestination(position) {
       const destination = this.currentRoute.destination;
       const distance = this.calculateDistance(
         position.lat, position.lng,
         destination.lat, destination.lng
       );
       
       return distance < 0.03; // أقل من 30 متر
     }
     
     // التحقق من الوصول إلى الخطوة التالية
     isAtNextStep(position) {
       if (this.currentStep >= this.currentRoute.steps.length) return false;
       
       const step = this.currentRoute.steps[this.currentStep];
       const distance = this.calculateDistance(
         position.lat, position.lng,
         step.end.lat, step.end.lng
       );
       
       return distance < 0.03; // أقل من 30 متر
     }
     
     // الإعلان عن الخطوة التالية
     announceNextStep() {
       if (this.currentStep >= this.currentRoute.steps.length) return;
       
       const step = this.currentRoute.steps[this.currentStep];
       
       switch (step.maneuver) {
         case 'turn-right':
           this.voiceGuidance.playTurnInstruction('right', step.streetName);
           break;
         case 'turn-left':
           this.voiceGuidance.playTurnInstruction('left', step.streetName);
           break;
         case 'straight':
           this.voiceGuidance.playSound('continue_straight');
           break;
         case 'arrive':
           this.voiceGuidance.playArrivalInstruction(this.currentRoute.destination.name);
           break;
       }
     }
     
     // التحقق من المخاطر القادمة
     checkUpcomingHazards(position) {
       // الحصول على المخاطر القريبة
       const hazards = this.router.getHazardsNearRoute(
         this.currentRoute,
         position,
         1 // كيلومتر واحد
       );
       
       for (const hazard of hazards) {
         // التحقق مما إذا كانت المخاطر جديدة
         if (!this.upcomingHazards.has(hazard.id)) {
           this.upcomingHazards.add(hazard.id);
           
           // حساب المسافة إلى المخاطر
           const distance = this.calculateDistance(
             position.lat, position.lng,
             hazard.lat, hazard.lng
           );
           
           // تشغيل تنبيه المخاطر
           this.voiceGuidance.playHazardWarning(
             hazard.type,
             Math.round(distance * 1000) // تحويل إلى أمتار
           );
         }
       }
     }
     
     // حساب المسافة بين نقطتين (بالكيلومتر)
     calculateDistance(lat1, lng1, lat2, lng2) {
       const R = 6371; // نصف قطر الأرض بالكيلومتر
       const dLat = (lat2 - lat1) * Math.PI / 180;
       const dLng = (lng2 - lng1) * Math.PI / 180;
       
       const a = 
         Math.sin(dLat/2) * Math.sin(dLat/2) +
         Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
         Math.sin(dLng/2) * Math.sin(dLng/2);
       
       const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
       return R * c;
     }
   }
   ```

### 2. وضع القيادة الليلية

#### الخطوات التنفيذية:
1. **إضافة أنماط CSS للوضع الليلي**:
   ```css
   /* public/css/night-mode.css */
   body.night-mode {
     background-color: #121212;
     color: #ffffff;
   }
   
   /* الخريطة في الوضع الليلي */
   body.night-mode .leaflet-container {
     background-color: #242424;
   }
   
   /* العناصر في الوضع الليلي */
   body.night-mode .header,
   body.night-mode .controls .control-button,
   body.night-mode .device-info,
   body.night-mode .location-info,
   body.night-mode .search-popup,
   body.night-mode .status-bar,
   body.night-mode .layers-control,
   body.night-mode #route-info-box {
     background-color: #242424;
     color: #ffffff;
     border-color: #444444;
   }
   
   /* الأزرار في الوضع الليلي */
   body.night-mode button:not(.control-button) {
     background-color: #444444;
     color: #ffffff;
     border-color: #666666;
   }
   
   /* حقول الإدخال في الوضع الليلي */
   body.night-mode input,
   body.night-mode select {
     background-color: #333333;
     color: #ffffff;
     border-color: #666666;
   }
   
   /* تنبيهات الصوت في الوضع الليلي */
   body.night-mode #audio-notification {
     background-color: rgba(36, 36, 36, 0.9);
   }
   
   /* تعديل ألوان المسارات في الوضع الليلي */
   body.night-mode .main-route {
     stroke: #4CAF50;
     stroke-opacity: 0.9;
   }
   
   body.night-mode .alternative-route {
     stroke: #ff9800;
     stroke-opacity: 0.7;
   }
   ```

2. **إضافة زر للتبديل بين الوضع العادي والوضع الليلي**:
   ```html
   <!-- إضافة زر إلى قائمة أزرار التحكم في index.html -->
   <button class="control-button" id="night-mode-toggle" title="وضع القيادة الليلية">&#127769;</button>
   ```

3. **إضافة وظيفة التبديل بين الأوضاع**:
   ```javascript
   // public/js/night-mode.js
   class NightModeManager {
     constructor() {
       this.isNightMode = false;
       this.autoNightMode = false;
       this.nightModeStartHour = 18; // 6 مساءً
       this.nightModeEndHour = 6; // 6 صباحًا
       
       // تحميل الإعدادات من التخزين المحلي
       this.loadSettings();
       
       // تهيئة الوضع الليلي بناءً على الوقت إذا كان التبديل التلقائي مفعلاً
       if (this.autoNightMode) {
         this.checkTimeAndSetMode();
       }
     }
     
     // تحميل الإعدادات
     loadSettings() {
       const settings = JSON.parse(localStorage.getItem('nightModeSettings')) || {};
       this.isNightMode = settings.isNightMode || false;
       this.autoNightMode = settings.autoNightMode || false;
       this.nightModeStartHour = settings.nightModeStartHour || 18;
       this.nightModeEndHour = settings.nightModeEndHour || 6;
       
       // تطبيق الوضع الليلي إذا كان مفعلاً
       if (this.isNightMode) {
         this.enableNightMode();
       }
     }
     
     // حفظ الإعدادات
     saveSettings() {
       const settings = {
         isNightMode: this.isNightMode,
         autoNightMode: this.autoNightMode,
         nightModeStartHour: this.nightModeStartHour,
         nightModeEndHour: this.nightModeEndHour
       };
       
       localStorage.setItem('nightModeSettings', JSON.stringify(settings));
     }
     
     // تفعيل الوضع الليلي
     enableNightMode() {
       document.body.classList.add('night-mode');
       this.isNightMode = true;
       this.saveSettings();
       
       // تغيير نمط الخريطة إلى الوضع الليلي
       if (window.map && window.baseMaps) {
         // إزالة جميع الطبقات
         Object.values(window.baseMaps).forEach(layer => {
           if (window.map.hasLayer(layer)) {
             window.map.removeLayer(layer);
           }
         });
         
         // إضافة طبقة الخريطة الليلية
         if (window.baseMaps['خريطة ليلية']) {
           window.baseMaps['خريطة ليلية'].addTo(window.map);
         } else if (window.baseMaps['OpenStreetMap']) {
           window.baseMaps['OpenStreetMap'].addTo(window.map);
         }
       }
     }
     
     // تعطيل الوضع الليلي
     disableNightMode() {
       document.body.classList.remove('night-mode');
       this.isNightMode = false;
       this.saveSettings();
       
       // تغيير نمط الخريطة إلى الوضع العادي
       if (window.map && window.baseMaps) {
         // إزالة جميع الطبقات
         Object.values(window.baseMaps).forEach(layer => {
           if (window.map.hasLayer(layer)) {
             window.map.removeLayer(layer);
           }
         });
         
         // إضافة طبقة الخريطة العادية
         if (window.baseMaps['OpenStreetMap']) {
           window.baseMaps['OpenStreetMap'].addTo(window.map);
         }
       }
     }
     
     // تبديل الوضع الليلي
     toggleNightMode() {
       if (this.isNightMode) {
         this.disableNightMode();
       } else {
         this.enableNightMode();
       }
     }
     
     // تفعيل/تعطيل التبديل التلقائي
     toggleAutoNightMode() {
       this.autoNightMode = !this.autoNightMode;
       this.saveSettings();
       
       if (this.autoNightMode) {
         this.checkTimeAndSetMode();
       }
     }
     
     // التحقق من الوقت وتعيين الوضع المناسب
     checkTimeAndSetMode() {
       const currentHour = new Date().getHours();
       
       // التحقق مما إذا كان الوقت الحالي ضمن ساعات الليل
       const isNightTime = (currentHour >= this.nightModeStartHour || currentHour < this.nightModeEndHour);
       
       if (isNightTime && !this.isNightMode) {
         this.enableNightMode();
       } else if (!isNightTime && this.isNightMode) {
         this.disableNightMode();
       }
     }
     
     // تعيين ساعات الوضع الليلي
     setNightModeHours(startHour, endHour) {
       this.nightModeStartHour = startHour;
       this.nightModeEndHour = endHour;
       this.saveSettings();
       
       if (this.autoNightMode) {
         this.checkTimeAndSetMode();
       }
     }
   }
   
   // تهيئة مدير الوضع الليلي
   const nightModeManager = new NightModeManager();
   
   // إضافة حدث النقر على زر الوضع الليلي
   document.getElementById('night-mode-toggle').addEventListener('click', () => {
     nightModeManager.toggleNightMode();
   });
   
   // التحقق من الوقت كل ساعة إذا كان التبديل التلقائي مفعلاً
   if (nightModeManager.autoNightMode) {
     setInterval(() => {
       nightModeManager.checkTimeAndSetMode();
     }, 60 * 60 * 1000); // كل ساعة
   }
   ```

4. **إضافة طبقة خريطة ليلية**:
   ```javascript
   // إضافة طبقة خريطة ليلية إلى قائمة الطبقات في index.html
   const baseMaps = {
     "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
       attribution: '© OpenStreetMap contributors | يمن ناف'
     }),
     "خريطة القمر الصناعي مع التسميات": L.layerGroup([
       L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
         attribution: '© Esri | يمن ناف',
         maxZoom: 19
       }),
       L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/toner-labels/{z}/{x}/{y}{r}.png', {
         attribution: '© Stamen Design, OpenStreetMap contributors | يمن ناف',
         maxZoom: 19,
         subdomains: 'abcd',
         pane: 'shadowPane'
       })
     ]),
     "خريطة الطرق عالية الدقة": L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
       attribution: '© OpenStreetMap contributors, Humanitarian OpenStreetMap Team | يمن ناف',
       maxZoom: 19
     }),
     "خريطة التضاريس": L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png', {
       attribution: '© Stamen Design, OpenStreetMap contributors | يمن ناف',
       maxZoom: 18
     }),
     "خريطة ليلية": L.tileLayer('https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}{r}.png', {
       attribution: '© CARTO, OpenStreetMap contributors | يمن ناف',
       maxZoom: 19
     })
   };
   ```

### 3. تقدير وقت الوصول الديناميكي

#### الخطوات التنفيذية:
1. **إنشاء وحدة لحساب وقت الوصول المتوقع**:
   ```javascript
   // public/js/navigation/eta-calculator.js
   class ETACalculator {
     constructor(router) {
       this.router = router;
       this.trafficData = new Map(); // بيانات حركة المرور
       this.lastUpdate = Date.now();
       this.updateInterval = 60000; // تحديث كل دقيقة
     }
     
     // حساب وقت الوصول المتوقع
     calculateETA(route, currentPosition) {
       // التحقق من الحاجة إلى تحديث بيانات حركة المرور
       if (Date.now() - this.lastUpdate > this.updateInterval) {
         this.updateTrafficData();
       }
       
       // حساب المسافة المتبقية والوقت المقدر
       const { remainingDistance, estimatedTime } = this.calculateRemainingJourney(route, currentPosition);
       
       // حساب وقت الوصول المتوقع
       const now = new Date();
       const eta = new Date(now.getTime() + estimatedTime * 1000);
       
       return {
         remainingDistance,
         estimatedTime,
         eta
       };
     }
     
     // حساب المسافة المتبقية والوقت المقدر
     calculateRemainingJourney(route, currentPosition) {
       // البحث عن أقرب نقطة على المس

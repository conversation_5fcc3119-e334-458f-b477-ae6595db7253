/* eslint-disable */
// Packaging/modules magic dance.
(function (factory) {
    var L;
    if (typeof define === 'function' && define.amd) {
        // AMD
        define(['leaflet'], factory);
    } else if (typeof module !== 'undefined') {
        // Node/CommonJS
        L = require('leaflet');
        module.exports = factory(L);
    } else {
        // Browser globals
        if (typeof window.L === 'undefined')
            throw 'Leaflet must be loaded first';
        factory(window.L);
    }
}(function (L) {
"use strict";

/**
 * @fileOverview Leaflet Geometry utilities for distances and linear referencing.
 * @name L.GeometryUtil
 */

L.GeometryUtil = L.extend(L.GeometryUtil || {}, {

    /**
        Shortcut function for planar distance between two {L.LatLng} at current zoom.
        @param {L.Map} map
        @param {L.LatLng} latlngA
        @param {L.LatLng} latlngB
        @returns {Number} in pixels
     */
    distance: function (map, latlngA, latlngB) {
        return map.latLngToLayerPoint(latlngA).distanceTo(map.latLngToLayerPoint(latlngB));
    },

    /**
        Shortcut function for planar distance between a {L.LatLng} and a segment (A-B).
        @param {L.Map} map
        @param {L.LatLng} latlng
        @param {L.LatLng} latlngA
        @param {L.LatLng} latlngB
        @returns {Number} in pixels
    */
    distanceSegment: function (map, latlng, latlngA, latlngB) {
        var p = map.latLngToLayerPoint(latlng),
           p1 = map.latLngToLayerPoint(latlngA),
           p2 = map.latLngToLayerPoint(latlngB);
        return L.LineUtil.pointToSegmentDistance(p, p1, p2);
    },

    /**
        Shortcut function for converting distance to readable distance.
        @param {Number} distance
        @param {String} unit ('metric' or 'imperial')
        @returns {Number} in yard or miles
    */
    readableDistance: function (distance, unit) {
        var isMetric = (unit !== 'imperial'),
            distanceStr;
        if (isMetric) {
            // show metres when distance is < 1km, then show km
            if (distance > 1000) {
                distanceStr = (distance  / 1000).toFixed(2) + ' km';
            }
            else {
                distanceStr = Math.ceil(distance) + ' م';
            }
        }
        else {
            distance *= 1.09361;
            if (distance > 1760) {
                distanceStr = (distance / 1760).toFixed(2) + ' ميل';
            }
            else {
                distanceStr = Math.ceil(distance) + ' ياردة';
            }
        }
        return distanceStr;
    },

    /**
        Returns true if the latlng belongs to segment A-B
        @param {L.LatLng} latlng
        @param {L.LatLng} latlngA
        @param {L.LatLng} latlngB
        @param {?Number} [tolerance=0.2]
        @returns {boolean}
     */
    belongsSegment: function(latlng, latlngA, latlngB, tolerance) {
        tolerance = tolerance === undefined ? 0.2 : tolerance;
        var hypotenuse = latlngA.distanceTo(latlngB),
            delta = latlngA.distanceTo(latlng) + latlng.distanceTo(latlngB) - hypotenuse;
        return delta/hypotenuse < tolerance;
    },

    /**
     * Returns total length of line
     * @param {L.Polyline|Array<L.Point>|Array<L.LatLng>}
     * @returns {Number} in meters
     */
    length: function (coords) {
        var accumulated = L.GeometryUtil.accumulatedLengths(coords);
        return accumulated.length > 0 ? accumulated[accumulated.length-1] : 0;
    },

    /**
     * Returns a list of accumulated length along a line.
     * @param {L.Polyline|Array<L.Point>|Array<L.LatLng>}
     * @returns {Number} in meters
     */
    accumulatedLengths: function (coords) {
        if (typeof coords.getLatLngs == 'function') {
            coords = coords.getLatLngs();
        }
        if (coords.length === 0)
            return [];
        var total = 0,
            lengths = [0];
        for (var i = 0, n = coords.length - 1; i< n; i++) {
            total += coords[i].distanceTo(coords[i+1]);
            lengths.push(total);
        }
        return lengths;
    },

    /**
        Returns the closest point of a {L.LatLng} on the segment (A-B)
        @param {L.Map} map
        @param {L.LatLng} latlng
        @param {L.LatLng} latlngA
        @param {L.LatLng} latlngB
        @returns {L.LatLng}
    */
    closestOnSegment: function (map, latlng, latlngA, latlngB) {
        var maxzoom = map.getMaxZoom();
        if (maxzoom === Infinity)
            maxzoom = map.getZoom();
        var p = map.project(latlng, maxzoom),
           p1 = map.project(latlngA, maxzoom),
           p2 = map.project(latlngB, maxzoom),
           closest = L.LineUtil.closestPointOnSegment(p, p1, p2);
        return map.unproject(closest, maxzoom);
    },

    /**
        Returns the closest latlng on layer.
        @param {L.Map} map
        @param {Array<L.LatLng>|L.PolyLine} layer - Layer that contains the result.
        @param {L.LatLng} latlng
        @param {?boolean} [vertices=false] - Whether to restrict to path vertices.
        @returns {L.LatLng}
    */
    closest: function (map, layer, latlng, vertices) {
        var latlngs,
            mindist = Infinity,
            result = null,
            i, n, distance, subResult;

        if (layer instanceof Array) {
            // if layer is Array<L.LatLng>
            latlngs = layer;
        } else if (layer instanceof L.LayerGroup) {
            // if we have a LayerGroup, collect all layers
            layer.eachLayer(function (layer) {
                if (layer instanceof L.Polyline) {
                    latlngs = layer.getLatLngs();
                    for (i = 0, n = latlngs.length; i < n; i++) {
                        subResult = L.GeometryUtil.closest(map, latlngs, latlng, vertices);
                        if (subResult.distance < mindist) {
                            mindist = subResult.distance;
                            result = subResult;
                        }
                    }
                }
            });
            return result;
        } else if (layer instanceof L.Polyline) {
            // if we have a Polyline, we use its coordinates
            latlngs = layer.getLatLngs();
        } else {
            // else we could not extract latlngs, something is wrong
            return null;
        }

        // if empty line, cant return anything
        if (!latlngs.length)
            return null;

        // filter out the nearest point
        for (i = 0, n = latlngs.length; i < n; i++) {
            if (vertices) {
                distance = L.GeometryUtil.distance(map, latlng, latlngs[i]);
                if (distance < mindist) {
                    mindist = distance;
                    result = L.GeometryUtil.closest(map, latlngs[i], latlng, vertices);
                    result.distance = distance;
                }
            } else {
                if (i === 0) {
                    // Initialize with point A
                    mindist = L.GeometryUtil.distance(map, latlng, latlngs[0]);
                    result = {
                        latlng: latlngs[0],
                        distance: mindist
                    };
                    continue;
                }
                // Keep the closest point of the segment
                distance = L.GeometryUtil.distanceSegment(map, latlng, latlngs[i-1], latlngs[i]);
                if (distance < mindist) {
                    mindist = distance;
                    result = {
                        latlng: L.GeometryUtil.closestOnSegment(map, latlng, latlngs[i-1], latlngs[i]),
                        distance: distance
                    };
                }
            }
        }
        return result;
    },

    /**
        Returns the closest layer to latlng among a list of layers.
        @param {L.Map} map
        @param {Array<L.ILayer>} layers
        @param {L.LatLng} latlng
        @returns {object} with layer, latlng and distance or {null} if list is empty;
    */
    closestLayer: function (map, layers, latlng) {
        var mindist = Infinity,
            result = null,
            ll = null,
            distance = Infinity;

        for (var i = 0, n = layers.length; i < n; i++) {
            var layer = layers[i];
            // Single dimension, snap on points, else snap on closest
            if (typeof layer.getLatLng == 'function') {
                ll = layer.getLatLng();
                distance = L.GeometryUtil.distance(map, latlng, ll);
            }
            else {
                ll = L.GeometryUtil.closest(map, layer, latlng);
                if (ll) distance = ll.distance;  // Can return null if layer has no points.
            }
            if (distance < mindist) {
                mindist = distance;
                result = {
                    layer: layer,
                    latlng: ll,
                    distance: distance
                };
            }
        }
        return result;
    },

    /**
        Returns the closest position from specified {LatLng} among specified layers,
        with a maximum tolerance in pixels, providing snapping behaviour.
        @param {L.Map} map
        @param {Array<ILayer>} layers - A list of layers to snap on.
        @param {L.LatLng} latlng - The position to snap.
        @param {?Number} [tolerance=Infinity] - Maximum number of pixels.
        @param {?boolean} [withVertices=true] - Snap to layers vertices.
        @returns {object} with snapped {LatLng} and snapped {Layer} or null if tolerance exceeded.
    */
    closestLayerSnap: function (map, layers, latlng, tolerance, withVertices) {
        tolerance = typeof tolerance == 'number' ? tolerance : Infinity;
        withVertices = typeof withVertices == 'boolean' ? withVertices : true;

        var result = L.GeometryUtil.closestLayer(map, layers, latlng);
        if (!result || result.distance > tolerance)
            return null;

        // If snapped layer is linear, try to snap on vertices (extremities and middle points)
        if (withVertices && typeof result.layer.getLatLngs == 'function') {
            var closest = L.GeometryUtil.closest(map, result.layer, latlng, true);
            if (closest.distance < tolerance) {
                result.latlng = closest.latlng;
                result.distance = L.GeometryUtil.distance(map, latlng, closest.latlng);
            }
        }
        return result;
    },

    /**
        Returns the Point located on a segment at the specified ratio of the segment length.
        @param {L.Point} pA
        @param {L.Point} pB
        @param {Number} the length ratio, expressed as a decimal between 0 and 1, inclusive.
        @returns {L.Point} the interpolated point.
    */
    interpolateOnPointSegment: function (pA, pB, ratio) {
        return L.point(
            (pA.x * (1 - ratio)) + (ratio * pB.x),
            (pA.y * (1 - ratio)) + (ratio * pB.y)
        );
    },

    /**
        Returns the coordinate of the point located on a line at the specified ratio of the line length.
        @param {L.Map} map
        @param {Array<L.LatLng>|L.PolyLine} latlngs
        @param {Number} the length ratio, expressed as a decimal between 0 and 1, inclusive
        @returns {Object} an object with latLng ({LatLng}) and predecessor ({Number}), the index of the preceding vertex in the Polyline
        (-1 if the interpolated point is the first vertex)
    */
    interpolateOnLine: function (map, latLngs, ratio) {
        latLngs = (latLngs instanceof L.Polyline) ? latLngs.getLatLngs() : latLngs;
        var n = latLngs.length;
        if (n < 2) {
            return null;
        }

        // ensure the ratio is between 0 and 1;
        ratio = Math.max(Math.min(ratio, 1), 0);

        if (ratio === 0) {
            return {
                latLng: latLngs[0],
                predecessor: -1
            };
        }
        if (ratio == 1) {
            return {
                latLng: latLngs[latLngs.length -1],
                predecessor: latLngs.length - 2
            };
        }

        // project the LatLngs as Points,
        // and compute total planar length of the line at max precision
        var maxzoom = map.getMaxZoom();
        if (maxzoom === Infinity)
            maxzoom = map.getZoom();
        var pts = [];
        var lineLength = 0;
        for(var i = 0; i < n; i++) {
            pts[i] = map.project(latLngs[i], maxzoom);
            if(i > 0)
              lineLength += pts[i-1].distanceTo(pts[i]);
        }

        var ratioDist = lineLength * ratio;
        var a = pts[0],
            b = pts[1],
            distA = 0,
            distB = a.distanceTo(b);
        // follow the line segments [ab], adding lengths,
        // until we find the segment where the points should lie on
        var index = 1;
        for (; index < n && distB < ratioDist; index++) {
            a = b;
            distA = distB;
            b = pts[index];
            distB = distA + a.distanceTo(b);
        }
        // compute the ratio relative to the segment [ab]
        var segmentRatio = ((distB - distA) !== 0) ? ((ratioDist - distA) / (distB - distA)) : 0;
        var interpolatedPoint = L.GeometryUtil.interpolateOnPointSegment(a, b, segmentRatio);
        return {
            latLng: map.unproject(interpolatedPoint, maxzoom),
            predecessor: index-2
        };
    },

    /**
        Returns a float between 0 and 1 representing the location of the
        closest point on polyline to the given latlng, as a fraction of total 2d line length.
        (opposite of L.GeometryUtil.interpolateOnLine())
        @param {L.Map} map
        @param {L.PolyLine} polyline
        @param {L.LatLng} latlng
        @returns {Number}
    */
    locateOnLine: function (map, polyline, latlng) {
        var latlngs = polyline.getLatLngs();
        if (latlng.equals(latlngs[0]))
            return 0.0;
        if (latlng.equals(latlngs[latlngs.length-1]))
            return 1.0;

        var point = L.GeometryUtil.closest(map, polyline, latlng, false),
            lengths = L.GeometryUtil.accumulatedLengths(latlngs),
            total_length = lengths[lengths.length-1],
            portion = 0,
            found = false;
        for (var i=0, n = latlngs.length-1; i < n; i++) {
            var l1 = latlngs[i],
                l2 = latlngs[i+1];
            portion = lengths[i];
            if (L.GeometryUtil.belongsSegment(point.latlng, l1, l2)) {
                portion += l1.distanceTo(point.latlng);
                found = true;
                break;
            }
        }
        if (!found) {
            throw "Could not interpolate " + latlng.toString() + " within " + polyline.toString();
        }
        return portion / total_length;
    },

    /**
        Returns a clone with reversed coordinates.
        @param {L.PolyLine} polyline
        @returns {L.PolyLine}
    */
    reverse: function (polyline) {
        return L.polyline(polyline.getLatLngs().slice(0).reverse());
    },

    /**
        Returns a sub-part of the polyline, from start to end.
        If start is superior to end, returns extraction from inverted line.
        @param {L.Map} map
        @param {L.PolyLine} polyline
        @param {Number} start ratio, expressed as a decimal between 0 and 1, inclusive
        @param {Number} end ratio, expressed as a decimal between 0 and 1, inclusive
        @returns {Array<L.LatLng>}
     */
    extract: function (map, polyline, start, end) {
        if (start > end) {
            return L.GeometryUtil.extract(map, L.GeometryUtil.reverse(polyline), 1.0-start, 1.0-end);
        }

        // Bound start and end to [0-1]
        start = Math.max(Math.min(start, 1), 0);
        end = Math.max(Math.min(end, 1), 0);

        var latlngs = polyline.getLatLngs(),
            startpoint = L.GeometryUtil.interpolateOnLine(map, polyline, start),
            endpoint = L.GeometryUtil.interpolateOnLine(map, polyline, end);
        // Return single point if start == end
        if (start == end) {
            var point = L.GeometryUtil.interpolateOnLine(map, polyline, end);
            return [point.latLng];
        }
        // Array.slice() works indexes at 0
        if (startpoint.predecessor == -1)
            startpoint.predecessor = 0;
        if (endpoint.predecessor == -1)
            endpoint.predecessor = 0;
        var result = latlngs.slice(startpoint.predecessor+1, endpoint.predecessor+1);
        result.unshift(startpoint.latLng);
        result.push(endpoint.latLng);
        return result;
    },

    /**
        Returns true if first polyline ends where other second starts.
        @param {L.PolyLine} polyline
        @param {L.PolyLine} other
        @returns {bool}
    */
    isBefore: function (polyline, other) {
        if (!other) return false;
        var lla = polyline.getLatLngs(),
            llb = other.getLatLngs();
        return (lla[lla.length-1]).equals(llb[0]);
    },

    /**
        Returns true if first polyline starts where second ends.
        @param {L.PolyLine} polyline
        @param {L.PolyLine} other
        @returns {bool}
    */
    isAfter: function (polyline, other) {
        if (!other) return false;
        var lla = polyline.getLatLngs(),
            llb = other.getLatLngs();
        return (lla[0]).equals(llb[llb.length-1]);
    },

    /**
        Returns true if first polyline starts where second ends or start.
        @param {L.PolyLine} polyline
        @param {L.PolyLine} other
        @returns {bool}
    */
    startsAtExtremity: function (polyline, other) {
        if (!other) return false;
        var lla = polyline.getLatLngs(),
            llb = other.getLatLngs(),
            start = lla[0];
        return start.equals(llb[0]) || start.equals(llb[llb.length-1]);
    },

    /**
        Returns horizontal angle in degres between two points.
        @param {L.Point} a
        @param {L.Point} b
        @returns {float}
     */
    computeAngle: function(a, b) {
        return (Math.atan2(b.y - a.y, b.x - a.x) * 180 / Math.PI);
    },

    /**
       Returns slope (Ax+B) between two points.
        @param {L.Point} a
        @param {L.Point} b
        @returns {Object} with ``a`` and ``b`` properties.
     */
    computeSlope: function(a, b) {
        var s = (b.y - a.y) / (b.x - a.x),
            o = a.y - (s * a.x);
        return {'a': s, 'b': o};
    },

    /**
       Returns LatLng of the point a given ratio along a segment.
        @param {L.LatLng} from
        @param {L.LatLng} to
        @param {Number} the ratio along the segment (from 0 to 1), inclusive.
        @returns {L.LatLng}
     */
    interpolateOnLatLngSegment: function (from, to, ratio) {
        return L.latLng(
            (from.lat * (1 - ratio)) + (ratio * to.lat),
            (from.lng * (1 - ratio)) + (ratio * to.lng)
        );
    },

    /**
       Returns the bearing in degrees clockwise from north (0 degrees)
       from the first L.LatLng to the second, at the first LatLng
       @param {L.LatLng} from
       @param {L.LatLng} to
       @returns {Number} Float between 0 and 360 degrees
     */
    bearing: function (from, to) {
        var rad = Math.PI / 180,
            lat1 = from.lat * rad,
            lat2 = to.lat * rad,
            lon1 = from.lng * rad,
            lon2 = to.lng * rad,
            y = Math.sin(lon2 - lon1) * Math.cos(lat2),
            x = Math.cos(lat1) * Math.sin(lat2) -
                Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);

        var bearing = ((Math.atan2(y, x) * 180 / Math.PI) + 360) % 360;
        return bearing >= 180 ? bearing-360 : bearing;
    },

    /**
       Returns the point that is a distance and heading away from
       the given origin point.
       @param {L.LatLng} from: origin point
       @param {Number} distance: distance in meters
       @param {Number} heading: heading in degrees, clockwise from 0 degrees north.
       @returns {L.latLng} the destination point.
       Many thanks to Chris Veness at http://www.movable-type.co.uk/scripts/latlong.html
     */
    destination: function (from, heading, distance) {
        heading = (heading + 360) % 360;
        var rad = Math.PI / 180,
            radInv = 180 / Math.PI,
            R = 6378137, // approximation of Earth's radius
            lon1 = from.lng * rad,
            lat1 = from.lat * rad,
            rheading = heading * rad,
            sinLat1 = Math.sin(lat1),
            cosLat1 = Math.cos(lat1),
            cosDistR = Math.cos(distance / R),
            sinDistR = Math.sin(distance / R),
            lat2 = Math.asin(sinLat1 * cosDistR + cosLat1 *
                sinDistR * Math.cos(rheading)),
            lon2 = lon1 + Math.atan2(Math.sin(rheading) * sinDistR *
                cosLat1, cosDistR - sinLat1 * Math.sin(lat2));
        lon2 = lon2 * radInv;
        lon2 = lon2 > 180 ? lon2 - 360 : lon2 < -180 ? lon2 + 360 : lon2;
        return L.latLng([lat2 * radInv, lon2]);
    },

    /**
     * Returns the size of a polyline with its argument a feature or an array of LatLng
     *
     * @param {object} feature
     * @param {<L.Map>} map
     * @returns {<L.Point>}
     */
    computeLength: function (feature, map) {
        var latlngs;
        if (feature instanceof Array) {
            latlngs = feature;
        } else if (typeof feature.getLatLngs == 'function') {
            latlngs = feature.getLatLngs();
        } else if (typeof feature.feature != 'undefined' && typeof feature.feature.properties.type == 'undefined') {
            latlngs = feature.feature.geometry.coordinates[0];
        }

        var xDelta = 0;
        var yDelta = 0;
        var firstPoint = map.latLngToLayerPoint(latlngs[0]);
        var lastPoint = firstPoint;
        latlngs.forEach(function (latlng) {
            var p = map.latLngToLayerPoint(latlng);
            var deltaX = lastPoint.x - p.x;
            var deltaY = lastPoint.y - p.y;
            xDelta += Math.abs(deltaX);
            yDelta += Math.abs(deltaY);
            lastPoint = p;
        });
        return map.layerPointToLatLng([ xDelta, yDelta ]);
    },
    
    /**
     * حساب المساحة الجيوديسية لمضلع في المتر المربع
     * @param {Array<L.LatLng>} latlngs مصفوفة من نقاط الإحداثيات
     * @returns {Number} المساحة بالمتر المربع
     */
    geodesicArea: function (latlngs) {
        // تنفيذ صيغة مساحة المضلع الجيوديسي
        var pointsCount = latlngs.length,
            area = 0.0,
            d2r = Math.PI / 180,
            p1, p2;

        if (pointsCount < 3) {
            return area;
        }

        for (var i = 0; i < pointsCount; i++) {
            p1 = latlngs[i];
            p2 = latlngs[(i + 1) % pointsCount];
            area += ((p2.lng - p1.lng) * d2r) * 
                   (2 + Math.sin(p1.lat * d2r) + Math.sin(p2.lat * d2r));
        }
        area = area * 6378137.0 * 6378137.0 / 2.0;
        return Math.abs(area);
    },

    /**
     * حساب الطول الجيوديسي لخط في المتر
     * @param {Array<L.LatLng>} latlngs مصفوفة من نقاط الإحداثيات
     * @returns {Number} الطول بالمتر
     */
    geodesicLength: function (latlngs) {
        var length = 0,
            d2r = Math.PI / 180;

        for (var i = 0, len = latlngs.length - 1; i < len; i++) {
            var p1 = latlngs[i],
                p2 = latlngs[i + 1];

            // تطبيق صيغة هافرساين لحساب المسافة الجيوديسية
            var dLat = (p2.lat - p1.lat) * d2r,
                dLon = (p2.lng - p1.lng) * d2r,
                a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(p1.lat * d2r) * Math.cos(p2.lat * d2r) *
                    Math.sin(dLon/2) * Math.sin(dLon/2),
                c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            
            length += 6371000 * c; // نصف قطر الأرض بالمتر
        }

        return length;
    }
});

return L.GeometryUtil;

}));

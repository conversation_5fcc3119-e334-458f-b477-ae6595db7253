# إعداد قاعدة بيانات Yemen GPS

هذا الملف يشرح كيفية إعداد قاعدة بيانات PostgreSQL لمشروع Yemen GPS.

## متطلبات النظام

1. PostgreSQL 12 أو أحدث
2. صلاحيات إنشاء قواعد بيانات وجداول

## خطوات الإعداد

### 1. تثبيت PostgreSQL

إذا لم يكن PostgreSQL مثبتًا على جهازك، قم بتثبيته من الموقع الرسمي:
https://www.postgresql.org/download/

### 2. إنشاء مستخدم قاعدة البيانات

قم بإنشاء مستخدم جديد باسم `yemen` وكلمة مرور `admin`:

```sql
CREATE USER yemen WITH PASSWORD 'admin';
ALTER USER yemen CREATEDB;
```

### 3. إنشاء قاعدة البيانات

يمكنك إنشاء قاعدة البيانات باستخدام ملف SQL المرفق:

```bash
psql -U postgres -f create-yemen-gps-db.sql
```

أو يمكنك تنفيذ الأوامر التالية مباشرة:

```sql
CREATE DATABASE yemen_gps;
GRANT ALL PRIVILEGES ON DATABASE yemen_gps TO yemen;
```

### 4. تنفيذ سكريبت إنشاء الجداول

بعد إنشاء قاعدة البيانات، قم بتنفيذ سكريبت إنشاء الجداول:

```bash
psql -U yemen -d yemen_gps -f create-yemen-gps-db.sql
```

### 5. التحقق من الإعداد

للتحقق من إنشاء قاعدة البيانات والجداول بشكل صحيح:

```bash
psql -U yemen -d yemen_gps -c "\dt"
```

يجب أن ترى قائمة بالجداول التي تم إنشاؤها.

## معلومات الاتصال بقاعدة البيانات

استخدم المعلومات التالية للاتصال بقاعدة البيانات:

```
host=localhost
port=5432
dbname=yemen_gps
user=yemen
password=admin
connect_timeout=10
sslmode=prefer
```

## بيانات تسجيل الدخول الافتراضية

تم إنشاء مستخدم مدير افتراضي مع البيانات التالية:

- اسم المستخدم: `admin`
- كلمة المرور: `yemen123`
- البريد الإلكتروني: `<EMAIL>`

## هيكل قاعدة البيانات

قاعدة البيانات تحتوي على الجداول التالية:

1. **users**: جدول المستخدمين
2. **roles**: جدول الأدوار
3. **permissions**: جدول الصلاحيات
4. **role_permissions**: جدول العلاقة بين الأدوار والصلاحيات
5. **clients**: جدول العملاء
6. **categories**: جدول التصنيفات
7. **locations**: جدول المواقع
8. **favorites**: جدول المفضلة
9. **reviews**: جدول التقييمات
10. **routes**: جدول المسارات
11. **logs**: جدول سجلات النظام

## استكشاف الأخطاء وإصلاحها

### مشكلة: فشل الاتصال بقاعدة البيانات

إذا واجهت مشكلة في الاتصال بقاعدة البيانات، تحقق من:

1. أن خدمة PostgreSQL تعمل
2. أن المستخدم `yemen` موجود ولديه صلاحيات كافية
3. أن قاعدة البيانات `yemen_gps` موجودة
4. أن إعدادات الاتصال في ملف `.env` صحيحة

### مشكلة: خطأ في تنفيذ سكريبت SQL

إذا واجهت مشكلة في تنفيذ سكريبت SQL، تحقق من:

1. أن لديك صلاحيات كافية لإنشاء جداول
2. أن السكريبت لا يحتوي على أخطاء
3. أن الجداول غير موجودة مسبقًا

## ملاحظات هامة

- يجب تغيير كلمة مرور المستخدم الافتراضي بعد أول تسجيل دخول
- في بيئة الإنتاج، يجب استخدام كلمات مرور قوية لمستخدم قاعدة البيانات
- يفضل تعطيل وضع التطوير في الإنتاج لتفعيل المصادقة الكاملة

/**
 * Yemen Maps - نظام خرائط اليمن
 * تطبيق خرائط متكامل يحاكي Google Maps ويعمل بدون اتصال بالإنترنت
 */

// تهيئة التطبيق
class YemenMaps {
    constructor() {
        // المتغيرات الأساسية
        this.map = null;
        this.currentMarker = null;
        this.markers = [];
        this.currentLocation = null;
        this.directionsLayer = null;
        this.currentLayer = 'streets';
        this.baseLayers = {};
        this.savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
        this.offlineMode = false;
        
        // تحقق من الاتصال بالإنترنت
        this.checkOnlineStatus();
        
        // تهيئة الخريطة
        this.initMap();
        
        // ربط الأحداث
        this.bindEvents();
        
        // تحميل النقاط من الخادم
        this.loadLocations();
        
        // الحصول على الموقع الحالي
        this.getCurrentLocation();
        
        // تحقق من وجود موقع مشارك
        this.handleSharedLocation();
    }
    
    // تحقق من حالة الاتصال بالإنترنت
    checkOnlineStatus() {
        this.offlineMode = !navigator.onLine;
        
        // إضافة مؤشر وضع عدم الاتصال
        if (this.offlineMode) {
            this.showOfflineIndicator();
        }
        
        window.addEventListener('online', () => {
            this.offlineMode = false;
            this.hideOfflineIndicator();
            this.showNotification('أنت متصل بالإنترنت الآن', 'success');
            this.refreshMap();
        });
        
        window.addEventListener('offline', () => {
            this.offlineMode = true;
            this.showOfflineIndicator();
            this.showNotification('أنت غير متصل بالإنترنت. تم تفعيل وضع العمل دون اتصال.', 'warning');
        });
    }
    
    // إظهار مؤشر وضع عدم الاتصال
    showOfflineIndicator() {
        let indicator = document.querySelector('.offline-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'offline-indicator';
            indicator.innerHTML = '<i class="fas fa-wifi-slash"></i> وضع عدم الاتصال';
            document.body.appendChild(indicator);
        }
    }
    
    // إخفاء مؤشر وضع عدم الاتصال
    hideOfflineIndicator() {
        const indicator = document.querySelector('.offline-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    // إظهار إشعار للمستخدم
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }, 100);
    }
    
    // تهيئة الخريطة
    initMap() {
        // إظهار مؤشر التحميل
        this.showLoading(true);
        
        // حل مشكلة الخريطة في أندرويد - تأخير تهيئة الخريطة
        setTimeout(() => {
            // التأكد من وجود عنصر الخريطة في DOM
            if (!document.getElementById('map')) {
                console.error('عنصر الخريطة غير موجود');
                this.showLoading(false);
                return;
            }
            
            // إنشاء الخريطة
            try {
                // إنشاء الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.map = L.map('map', {
                    center: [15.3694, 44.1910], // مركز اليمن (صنعاء)
                    zoom: 7,
                    zoomControl: false,
                    attributionControl: true,
                    maxBounds: [
                        [10.0, 40.0], // الجنوب الغربي
                        [20.0, 55.0]  // الشمال الشرقي
                    ],
                    minZoom: 6,
                    maxZoom: 18,
                    tap: true,            // تمكين النقر للأجهزة اللمسية
                    tapTolerance: 30,     // زيادة مساحة النقر للأجهزة اللمسية
                    bounceAtZoomLimits: false // منع الارتداد عند حدود التكبير
                });
                
                // إضافة طبقات الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.baseLayers = {
                    streets: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19,
                        subdomains: 'abc',
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,     // تحديث الطبقة فقط عند التوقف لتحسين الأداء
                        updateWhenZooming: false   // لا تحدث أثناء التكبير/التصغير
                    }),
                    
                    satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                        maxZoom: 18,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    }),
                    
                    terrain: L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://opentopomap.org">OpenTopoMap</a> contributors',
                        maxZoom: 17,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    })
                };
                
                // إضافة الطبقة الافتراضية
                this.baseLayers[this.currentLayer].addTo(this.map);
                
                // إعداد طبقة العلامات
                this.markersLayer = L.layerGroup().addTo(this.map);
                
                // إضافة طبقة المسارات
                this.directionsLayer = L.layerGroup().addTo(this.map);
                
                // التعامل مع النقر على الخريطة
                this.map.on('click', (e) => {
                    this.handleMapClick(e);
                });
                
                // إضافة معالجة خاصة لأحداث اللمس على الأجهزة المحمولة
                if (L.Browser.mobile) {
                    // لمعالجة مشكلة النقر على الأجهزة المحمولة
                    document.querySelector('#map').addEventListener('touchend', function(e) {
                        // منع التمرير بعد النقر
                        if (e.touches && e.touches.length === 0) {
                            e.preventDefault();
                        }
                    }, false);
                }
                
                // إنهاء التحميل
                this.map.whenReady(() => {
                    this.showLoading(false);
                    console.log('تم تحميل الخريطة بنجاح');
                    
                    // إعادة ضبط حجم الخريطة بعد التحميل لضمان العرض الصحيح
                    setTimeout(() => {
                        this.map.invalidateSize();
                    }, 200);
                });
                
            } catch (error) {
                console.error('خطأ في تهيئة الخريطة:', error);
                this.showLoading(false);
                return;
            }
        }, 500); // تأخير 500 ملي ثانية للسماح للمتصفح بتحميل الخريطة بشكل صحيح
    }
    
    // إظهار أو إخفاء مؤشر التحميل
    showLoading(show) {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = show ? 'flex' : 'none';
        }
    }
    
    // ربط أحداث واجهة المستخدم
    bindEvents() {
        // أزرار التكبير والتصغير
        document.getElementById('zoom-in-btn').addEventListener('click', () => {
            this.map.zoomIn();
        });
        
        document.getElementById('zoom-out-btn').addEventListener('click', () => {
            this.map.zoomOut();
        });
        
        // زر الموقع الحالي
        document.getElementById('my-location-btn').addEventListener('click', () => {
            this.getCurrentLocation();
        });
        
        // زر إغلاق لوحة الاتجاهات
        document.getElementById('close-directions-btn').addEventListener('click', () => {
            this.hideDirectionsPanel();
        });
        
        // أزرار تبديل طبقات الخريطة
        document.querySelectorAll('.layer-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const layer = e.target.dataset.layer;
                this.switchMapLayer(layer);
            });
        });
        
        // حقل البحث
        document.getElementById('search-btn').addEventListener('click', () => {
            const query = document.getElementById('search-input').value.trim();
            if (query) {
                this.searchLocation(query);
            }
        });
        
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const query = e.target.value.trim();
                if (query) {
                    this.searchLocation(query);
                }
            }
        });
    }
    
    // تبديل طبقة الخريطة
    switchMapLayer(layer) {
        if (this.currentLayer === layer || !this.baseLayers[layer]) return;
        
        // إزالة الطبقة الحالية
        this.map.removeLayer(this.baseLayers[this.currentLayer]);
        
        // إضافة الطبقة الجديدة
        this.map.addLayer(this.baseLayers[layer]);
        
        // تحديث الطبقة الحالية
        this.currentLayer = layer;
        
        // تحديث حالة الأزرار
        document.querySelectorAll('.layer-btn').forEach(btn => {
            if (btn.dataset.layer === layer) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }
    
    // الحصول على الموقع الحالي
    getCurrentLocation() {
        // إظهار مؤشر التحميل
        this.showLoading(true);
        
        // إعداد تلقائي للموقع الافتراضي في صنعاء
        const setDefaultLocation = () => {
            this.currentLocation = {
                lat: 15.3694,
                lng: 44.1910
            };
            
            // إزالة العلامة السابقة للموقع الحالي إن وجدت
            if (this.currentLocationMarker) {
                this.map.removeLayer(this.currentLocationMarker);
            }
            
            // إضافة علامة الموقع الحالي
            this.currentLocationMarker = L.marker([this.currentLocation.lat, this.currentLocation.lng], {
                icon: L.divIcon({
                    className: 'current-location-marker',
                    html: '<div class="pulse"></div>',
                    iconSize: [20, 20]
                })
            }).addTo(this.map);
            
            // تحريك الخريطة إلى الموقع الحالي
            this.map.setView([this.currentLocation.lat, this.currentLocation.lng], 15);
            
            this.showLoading(false);
            this.showNotification('تم استخدام موقع افتراضي في صنعاء', 'info');
        };
        
        // استخدام موقع افتراضي دائماً للاختبار المحلي
        const useDefaultLocation = true;
        
        if (useDefaultLocation) {
            setTimeout(() => {
                setDefaultLocation();
            }, 800);
            return;
        }
        
        // التحقق من توفر خدمة تحديد الموقع
        if (!navigator.geolocation) {
            this.showNotification('خدمة تحديد الموقع غير متاحة في متصفحك', 'error');
            setDefaultLocation();
            return;
        }
        
        try {
            // محاولة الحصول على الموقع
            const successCallback = (position) => {
                try {
                    this.currentLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    // إزالة العلامة السابقة للموقع الحالي
                    if (this.currentLocationMarker) {
                        this.map.removeLayer(this.currentLocationMarker);
                    }
                    
                    // إضافة علامة الموقع الحالي
                    this.currentLocationMarker = L.marker([this.currentLocation.lat, this.currentLocation.lng], {
                        icon: L.divIcon({
                            className: 'current-location-marker',
                            html: '<div class="pulse"></div>',
                            iconSize: [20, 20]
                        })
                    }).addTo(this.map);
                    
                    // تحريك الخريطة إلى الموقع الحالي
                    this.map.setView([this.currentLocation.lat, this.currentLocation.lng], 15);
                    
                    this.showLoading(false);
                    this.showNotification('تم تحديد موقعك بنجاح', 'success');
                } catch (innerError) {
                    console.warn('حدث خطأ في معالجة الموقع:', innerError);
                    setDefaultLocation();
                }
            };
            
            const errorCallback = (error) => {
                try {
                    // رسائل خطأ محددة
                    let errorMsg = 'فشل في الحصول على موقعك الحالي';
                    
                    if (error && error.code) {
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMsg = 'تم رفض طلب الموقع. الرجاء السماح للتطبيق باستخدام موقعك.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMsg = 'معلومات الموقع غير متاحة حالياً.';
                                break;
                            case error.TIMEOUT:
                                errorMsg = 'انتهت مهلة طلب الموقع.';
                                break;
                        }
                    }
                    
                    console.warn('Error getting location:', errorMsg);
                    this.showNotification(errorMsg, 'warning');
                    
                    // استخدم الموقع الافتراضي
                    setDefaultLocation();
                } catch (innerError) {
                    console.warn('حدث خطأ في معالجة خطأ الموقع:', innerError);
                    setDefaultLocation();
                }
            };
            
            // استدعاء واجهة برمجة تحديد الموقع
            navigator.geolocation.getCurrentPosition(
                successCallback,
                errorCallback,
                {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                }
            );
        } catch (error) {
            // في حالة حدوث أي خطأ غير متوقع
            console.warn('حدث خطأ غير متوقع في تحديد الموقع:', error);
            setDefaultLocation();
        }
    }
    
    // تحميل النقاط من الخادم
    loadLocations() {
        this.showLoading(true);
        
        // إذا كان في وضع عدم الاتصال، استخدم البيانات المخزنة محلياً
        if (this.offlineMode) {
            const cachedLocations = JSON.parse(localStorage.getItem('cachedLocations') || '[]');
            if (cachedLocations.length > 0) {
                this.displayLocations(cachedLocations);
                this.showLoading(false);
                return;
            }
        }
        
        // محاولة تحميل النقاط من الخادم
        fetch('/api/locations')
            .then(response => {
                if (!response.ok) throw new Error('فشل في الاتصال بالخادم');
                return response.json();
            })
            .then(async data => {
                // إثراء البيانات بمعلومات Google Places
                if (!this.offlineMode) {
                    try {
                        data = await YemenDB.GooglePlacesManager.enrichLocationsWithGoogleData(data);
                    } catch (error) {
                        console.warn('خطأ في إثراء البيانات بمعلومات Google Places:', error);
                    }
                }
                
                // تخزين البيانات محلياً للاستخدام في وضع عدم الاتصال
                localStorage.setItem('cachedLocations', JSON.stringify(data));
                
                // عرض النقاط على الخريطة
                this.displayLocations(data);
            })
            .catch(error => {
                console.error('Error loading locations:', error);
                
                // في حالة الفشل، استخدم البيانات الافتراضية
                const defaultLocations = [
                    { id: 1, name: 'ميدان السبعين', latitude: 15.3136, longitude: 44.1867, address: 'صنعاء', contact: '777123456', google_place_id: 'ChIJxxxxxxx1' },
                    { id: 2, name: 'قلعة القاهرة', latitude: 13.5789, longitude: 44.0209, address: 'تعز', contact: '777123457', google_place_id: 'ChIJxxxxxxx2' },
                    { id: 3, name: 'مدينة سيئون', latitude: 15.9431, longitude: 48.7879, address: 'حضرموت', contact: '777123458', google_place_id: 'ChIJxxxxxxx3' },
                    { id: 4, name: 'جزيرة سقطرى', latitude: 12.4634, longitude: 53.8250, address: 'أرخبيل سقطرى', contact: '777123459', google_place_id: 'ChIJxxxxxxx4' },
                    { id: 5, name: 'قلعة صيرة', latitude: 12.7852, longitude: 45.0281, address: 'عدن', contact: '777123460', google_place_id: 'ChIJxxxxxxx5' }
                ];
                
                // إثراء البيانات الافتراضية بمعلومات Google Places الوهمية
                if (!this.offlineMode) {
                    this.enrichAndDisplayDefaultLocations(defaultLocations);
                } else {
                    this.displayLocations(defaultLocations);
                }
            })
            .finally(() => {
                this.showLoading(false);
            });
    }
    
    // إثراء وعرض المواقع الافتراضية
    async enrichAndDisplayDefaultLocations(locations) {
        try {
            // معالجة كل موقع على حدة
            for (let i = 0; i < locations.length; i++) {
                const location = locations[i];
                if (location.google_place_id) {
                    // إثراء الموقع ببيانات Google Places
                    const enriched = await YemenDB.GooglePlacesManager.enrichLocationWithGoogleData(location);
                    locations[i] = enriched;
                }
            }
            
            // عرض المواقع المثراة
            this.displayLocations(locations);
        } catch (error) {
            console.error('خطأ في إثراء المواقع الافتراضية:', error);
            // عرض المواقع بدون إثراء
            this.displayLocations(locations);
        }
    }
    
    // عرض النقاط على الخريطة
    displayLocations(locations) {
        // مسح العلامات السابقة
        this.markersLayer.clearLayers();
        this.markers = [];
        
        // إضافة علامات للمواقع
        locations.forEach(location => {
            // التحقق من وجود إحداثيات صالحة
            if (!location.latitude || !location.longitude) return;
            
            // إعداد محتوى الصورة
            let imageUrl = 'assets/images/placeholder.jpg'; // صورة افتراضية
            let additionalImages = [];
            
            // تحقق من وجود صور من Google Places
            if (location.photos && location.photos.length) {
                imageUrl = location.photos[0];
                // إضافة باقي الصور للمعرض
                if (location.photos.length > 1) {
                    additionalImages = location.photos.slice(1);
                }
            }
            // أو تحقق من وجود مجموعة صور تقليدية
            else if (location.images && location.images.length) {
                imageUrl = location.images[0];
                // إضافة باقي الصور للمعرض
                if (location.images.length > 1) {
                    additionalImages = location.images.slice(1);
                }
            } 
            // أو استخدم صورة للمدينة إذا وجدت
            else if (location.city) {
                imageUrl = `assets/images/${location.city.toLowerCase()}.jpg`;
            }
            
            // الحصول على معلومات التصنيف
            let categoryInfo = { icon: 'fa-map-marker-alt', name: 'موقع' };
            if (location.categories && location.categories.length) {
                categoryInfo = YemenDB.GooglePlacesManager.formatCategoryInfo(location.categories);
            } else if (location.type) {
                categoryInfo = YemenDB.GooglePlacesManager.formatCategoryInfo([location.type]);
            }
            
            // تنسيق ساعات العمل إذا وجدت
            let openingHours = null;
            if (location.hours) {
                if (Array.isArray(location.hours)) {
                    openingHours = YemenDB.GooglePlacesManager.formatOpeningHours(location.hours);
                } else {
                    openingHours = location.hours;
                }
            }
            
            // تنسيق التقييم إذا وجد
            let ratingHtml = '';
            if (location.rating) {
                const ratingData = YemenDB.GooglePlacesManager.formatRating(location.rating);
                ratingHtml = `
                <div class="popup-rating">
                    <div class="stars">
                        ${"\u2605".repeat(ratingData.stars.full)}${ratingData.stars.half ? "\u2BEA" : ""}${"\u2606".repeat(ratingData.stars.empty)}
                    </div>
                    <span class="rating-value">${ratingData.rating}</span>
                    ${location.reviews ? `<span class="review-count">(${location.reviews.length} تقييم)</span>` : ''}
                </div>`;
            }
            
            // الحصول على الوصف من مصادر مختلفة
            const description = location.description || 
                               (location.google_data && location.google_data.editorial_summary) || 
                               `معلومات عن ${location.name} ستكون متاحة قريبًا`;
            
            // إنشاء HTML لنافذة المعلومات بأسلوب Google Maps
            const popupHtml = `
                <div class="custom-popup">
                    <div class="popup-header">
                        <div class="popup-header-overlay"></div>
                        <div class="popup-image-container">
                            <img src="${imageUrl}" alt="${location.name}" class="main-image" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                            ${additionalImages.length > 0 ? `
                            <div class="popup-image-gallery">
                                ${additionalImages.map((img, index) => `
                                    <div class="gallery-thumbnail" onclick="document.querySelector('.custom-popup .main-image').src='${img}'">
                                        <img src="${img}" alt="صورة ${index + 2}" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                                    </div>
                                `).join('')}
                            </div>` : ''}
                        </div>
                    </div>
                    
                    <div class="popup-body">
                        <h2 class="popup-title">${location.name || ''}</h2>
                        <div class="popup-category"><i class="fas ${categoryInfo.icon}"></i> ${categoryInfo.name}</div>
                        <div class="popup-subtitle">${location.address || ''}</div>
                        
                        ${ratingHtml}
                        
                        <div class="popup-info">
                            ${location.contact ? `
                            <div class="popup-info-item">
                                <i class="fas fa-phone"></i>
                                <span><a href="tel:${location.contact}">${location.contact}</a></span>
                            </div>` : ''}
                            
                            ${location.email ? `
                            <div class="popup-info-item">
                                <i class="fas fa-envelope"></i>
                                <span><a href="mailto:${location.email}">${location.email}</a></span>
                            </div>` : ''}
                            
                            ${openingHours ? `
                            <div class="popup-info-item">
                                <i class="fas fa-clock"></i>
                                <span>${Array.isArray(openingHours) ? openingHours[0] + (openingHours.length > 1 ? ' <a href="#" onclick="yemenMaps.toggleOpeningHours(this); return false;">عرض المزيد</a>' : '') : openingHours}</span>
                                ${Array.isArray(openingHours) && openingHours.length > 1 ? `
                                <div class="opening-hours-details" style="display: none;">
                                    ${openingHours.slice(1).map(hour => `<div>${hour}</div>`).join('')}
                                </div>` : ''}
                            </div>` : ''}
                            
                            ${location.website ? `
                            <div class="popup-info-item">
                                <i class="fas fa-globe"></i>
                                <span><a href="${location.website}" target="_blank">${location.website.replace(/^https?:\/\//i, '')}</a></span>
                            </div>` : ''}
                        </div>
                        
                        <div class="popup-facts">
                            <h4>حقائق سريعة</h4>
                            <p>${description}</p>
                        </div>
                        
                        <div class="popup-actions">
                            <button class="popup-action-btn route-btn" onclick="yemenMaps.getDirections(${location.latitude}, ${location.longitude}, '${location.name.replace(/'/g, "\\'")}')"> 
                                <i class="fas fa-directions"></i>
                                <span>المسار</span>
                            </button>
                            
                            <button class="popup-action-btn save-btn" onclick="yemenMaps.savePlace(${location.id || 0}, '${location.name.replace(/'/g, "\\'")}'
, ${location.latitude}, ${location.longitude})">
                                <i class="${this.savedPlaces.some(p => p.id === location.id) ? 'fas' : 'far'} fa-bookmark"></i>
                                <span>حفظ</span>
                            </button>
                            
                            <button class="popup-action-btn share-btn" onclick="yemenMaps.shareLocation(${location.latitude}, ${location.longitude}, '${location.name.replace(/'/g, "\\'")}')"> 
                                <i class="fas fa-share-alt"></i>
                                <span>مشاركة</span>
                            </button>
                            
                            ${location.google_place_id ? `
                            <a href="https://www.google.com/maps/place/?q=place_id:${location.google_place_id}" target="_blank" class="popup-action-btn google-btn">
                                <i class="fab fa-google"></i>
                                <span>Google</span>
                            </a>` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            // إنشاء علامة للموقع
            const marker = L.marker([location.latitude, location.longitude]).addTo(this.markersLayer);
            
            // إضافة نافذة معلومات مختصرة
            marker.bindPopup(popupHtml, {
                maxWidth: 350,
                className: 'google-maps-popup'
            });
            
            // إضافة حدث النقر لفتح نافذة المعلومات المفصلة
            marker.on('click', () => {
                // فتح نافذة المعلومات المفصلة مباشرة بدلاً من النافذة الصغيرة
                this.showPlaceDetails(location);
            });
            
            // تخزين مرجع للعلامة
            this.markers.push({
                id: location.id || this.markers.length + 1,
                marker: marker,
                location: location
            });
        });
    }
    
    // عرض/إخفاء ساعات العمل
    toggleOpeningHours(element) {
        const detailsDiv = element.parentNode.querySelector('.opening-hours-details');
        if (detailsDiv.style.display === 'none') {
            detailsDiv.style.display = 'block';
            element.textContent = 'إخفاء';
        } else {
            detailsDiv.style.display = 'none';
            element.textContent = 'عرض المزيد';
        }
    }
    
    // عرض نافذة معلومات المكان المفصلة (Google Maps Style)
    showPlaceDetails(location) {
        console.log('تم استدعاء عرض تفاصيل المكان:', location.name);
        
        // الحصول على عناصر واجهة المستخدم
        const panel = document.getElementById('place-details-panel');
        const mainImage = document.getElementById('place-main-image');
        const imageGallery = document.getElementById('place-image-gallery');
        const placeName = document.getElementById('place-name');
        const placeCategory = document.getElementById('place-category').querySelector('span');
        const placeRating = document.getElementById('place-rating');
        const placeAddress = document.getElementById('place-address');
        const contactPhone = document.getElementById('contact-phone');
        const contactWebsite = document.getElementById('contact-website');
        const contactAddress = document.getElementById('contact-address');
        const hoursList = document.getElementById('hours-list');
        const reviewsList = document.getElementById('reviews-list');
        const saveBtn = document.getElementById('save-place-btn');
        const shareBtn = document.getElementById('share-place-btn');
        const directionsBtn = document.getElementById('directions-btn');
        const callBtn = document.getElementById('call-btn');
        const websiteBtn = document.getElementById('website-btn');
        
        // التحقق من وجود معلومات الموقع
        if (!location) {
            console.error('لم يتم توفير معلومات الموقع');
            return;
        }
        
        // تعيين صورة المكان الرئيسية
        mainImage.src = '';
        if (location.photo) {
            mainImage.src = location.photo;
        } else if (location.photos && location.photos.length > 0) {
            mainImage.src = location.photos[0];
        } else if (location.images && location.images.length > 0) {
            mainImage.src = location.images[0];
        } else {
            // استخدام صورة من الإنترنت بدلاً من ملف محلي
            mainImage.src = 'https://via.placeholder.com/400x200/f8f8f8/555555?text=صورة+غير+متوفرة';
        }
        
        // تعيين اسم المكان
        placeName.textContent = location.name || '';
        
        // تعيين فئة المكان
        let categoryInfo = { icon: 'fa-map-marker-alt', name: 'موقع' };
        if (location.categories && location.categories.length) {
            categoryInfo = YemenDB.GooglePlacesManager.formatCategoryInfo(location.categories);
        } else if (location.type) {
            categoryInfo = YemenDB.GooglePlacesManager.formatCategoryInfo([location.type]);
        }
        placeCategory.textContent = categoryInfo.name;
        categoryIcon.className = `fas ${categoryInfo.icon}`;
        
        // تعيين العنوان
        placeAddress.textContent = location.address || '';
        contactAddress.querySelector('span').textContent = location.address || 'غير متوفر';
        
        // تعيين التقييم
        if (location.rating) {
            // تنسيق التقييم مباشرة بدلاً من استخدام YemenDB
            const rating = parseFloat(location.rating);
            const stars = Math.floor(rating);
            const half = rating % 1 >= 0.5;
            const empty = 5 - stars - (half ? 1 : 0);
            
            placeRating.querySelector('.stars').innerHTML = `${"★".repeat(stars)}${half ? "⯪" : ""}${"☆".repeat(empty)}`;
            placeRating.querySelector('.rating-value').textContent = rating.toFixed(1);
            placeRating.querySelector('.review-count').textContent = `(${location.reviews_count || 0} تقييم)`;
            placeRating.style.display = 'flex';
        } else {
            placeRating.style.display = 'none';
        }
        
        // إنشاء معرض الصور
        imageGallery.innerHTML = '';
        let photos = [];
        if (location.photos && location.photos.length > 1) {
            photos = location.photos.slice(1); // بدءًا من الصورة الثانية
        } else if (location.images && location.images.length > 1) {
            photos = location.images.slice(1);
        }
        
        photos.forEach((photo, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = 'gallery-thumbnail';
            thumbnail.innerHTML = `<img src="${photo}" alt="صورة ${index + 2}" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">`;
            thumbnail.onclick = () => mainImage.src = photo;
            imageGallery.appendChild(thumbnail);
        });
        
        // تعيين رقم الهاتف
        if (location.contact) {
            contactPhone.style.display = 'flex';
            contactPhone.querySelector('span').innerHTML = `<a href="tel:${location.contact}">${location.contact}</a>`;
            callBtn.style.display = 'flex';
            callBtn.onclick = () => window.location.href = `tel:${location.contact}`;
        } else {
            contactPhone.style.display = 'none';
            callBtn.style.display = 'none';
        }
        
        // تعيين الموقع الإلكتروني
        if (location.website) {
            contactWebsite.style.display = 'flex';
            contactWebsite.querySelector('span').innerHTML = `<a href="${location.website}" target="_blank">${location.website.replace(/^https?:\/\//i, '')}</a>`;
            websiteBtn.style.display = 'flex';
            websiteBtn.onclick = () => window.open(location.website, '_blank');
        } else {
            contactWebsite.style.display = 'none';
            websiteBtn.style.display = 'none';
        }
        
        // تعيين ساعات العمل
        hoursList.innerHTML = '';
        const openingHours = document.getElementById('place-opening-hours');
        if (location.hours) {
            openingHours.style.display = 'block';
            let formattedHours = [];
            
            if (Array.isArray(location.hours)) {
                formattedHours = YemenDB.GooglePlacesManager.formatOpeningHours(location.hours);
            } else {
                formattedHours = [location.hours];
            }
            
            const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            const currentDay = new Date().getDay(); // 0 = الأحد, 1 = الاثنين, إلخ
            
            formattedHours.forEach((hour, index) => {
                const hourItem = document.createElement('div');
                hourItem.className = 'hours-item';
                if (index === currentDay) hourItem.classList.add('current-day');
                
                const dayName = document.createElement('span');
                dayName.textContent = days[index];
                
                const dayHours = document.createElement('span');
                dayHours.textContent = hour;
                
                hourItem.appendChild(dayName);
                hourItem.appendChild(dayHours);
                hoursList.appendChild(hourItem);
            });
        } else {
            openingHours.style.display = 'none';
        }
        
        // تعيين الوصف
        placeDescription.textContent = location.description || 
                                       (location.google_data && location.google_data.editorial_summary) || 
                                       `معلومات عن ${location.name} ستكون متاحة قريبًا`;
        
        // إعداد المراجعات
        reviewsList.innerHTML = '';
        const reviewsSection = document.querySelector('.place-reviews');
        
        if (location.reviews && location.reviews.length > 0) {
            reviewsSection.style.display = 'block';
            
            location.reviews.slice(0, 3).forEach(review => {
                const reviewItem = document.createElement('div');
                reviewItem.className = 'review-item';
                
                const author = document.createElement('div');
                author.className = 'review-author';
                
                const avatar = document.createElement('div');
                avatar.className = 'review-avatar';
                avatar.innerHTML = `<img src="${review.profile_photo_url || 'assets/images/user-avatar.png'}" alt="صورة المستخدم">`;
                
                const authorName = document.createElement('span');
                authorName.className = 'review-author-name';
                authorName.textContent = review.author_name || 'مستخدم';
                
                author.appendChild(avatar);
                author.appendChild(authorName);
                
                const rating = document.createElement('div');
                rating.className = 'review-rating';
                const ratingStars = YemenDB.GooglePlacesManager.formatRating(review.rating || 5);
                rating.innerHTML = `<div class="stars">${"★".repeat(ratingStars.stars.full)}${ratingStars.stars.half ? "⯪" : ""}${"☆".repeat(ratingStars.stars.empty)}</div>`;
                
                const text = document.createElement('div');
                text.className = 'review-text';
                text.textContent = review.text || 'لا يوجد تعليق';
                
                const date = document.createElement('div');
                date.className = 'review-date';
                date.textContent = review.relative_time_description || '';
                
                reviewItem.appendChild(author);
                reviewItem.appendChild(rating);
                reviewItem.appendChild(text);
                reviewItem.appendChild(date);
                
                reviewsList.appendChild(reviewItem);
            });
        } else {
            reviewsSection.style.display = 'none';
        }
        
        // إعداد أزرار الإجراءات
        // زر الاتجاهات
        directionsBtn.onclick = () => this.getDirections(location.latitude, location.longitude, location.name);
        
        // زر الحفظ
        if (this.savedPlaces.some(p => p.id === location.id)) {
            saveBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
        } else {
            saveBtn.innerHTML = '<i class="far fa-bookmark"></i>';
        }
        saveBtn.onclick = () => {
            this.savePlace(location.id || 0, location.name, location.latitude, location.longitude);
            if (this.savedPlaces.some(p => p.id === location.id)) {
                saveBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
            } else {
                saveBtn.innerHTML = '<i class="far fa-bookmark"></i>';
            }
        };
        
        // زر المشاركة
        shareBtn.onclick = () => this.shareLocation(location.latitude, location.longitude, location.name);
        
        // إعداد زر الإغلاق
        document.getElementById('close-place-details-btn').onclick = () => {
            detailsPanel.classList.remove('active');
        };
        
        // إظهار لوحة التفاصيل
        detailsPanel.classList.add('active');
    }
    
    // الحصول على حقائق سريعة عن مكان
    getQuickFacts(placeName) {
        // قاموس من الحقائق السريعة للمدن الرئيسية
        const factsDatabase = {
            'صنعاء': {
                text: 'صنعاء هي عاصمة اليمن وتعتبر من أقدم المدن المأهولة باستمرار في العالم. تشتهر بمعمارها الفريد ومدينتها القديمة المدرجة في قائمة اليونسكو للتراث العالمي.'
            },
            'عدن': {
                text: 'عدن هي مدينة ساحلية وميناء بحري رئيسي في اليمن. كانت عاصمة اليمن الجنوبي سابقاً. تشتهر بشواطئها وبركانها المطفئ وخزانات المياه القديمة.'
            },
            'تعز': {
                text: 'تعز هي ثالث أكبر مدينة في اليمن وعاصمة محافظة تعز. كانت عاصمة اليمن من 1948 إلى 1962. تشتهر بقلعة القاهرة وجبل صبر.'
            },
            'الحديدة': {
                text: 'الحديدة هي رابع أكبر مدينة في اليمن والميناء البحري الرئيسي في البحر الأحمر. تعتبر بوابة اليمن الغربية ومركزاً اقتصادياً مهماً.'
            },
            'عمران': {
                text: 'عمران هي مدينة صغيرة في غرب وسط اليمن وهي عاصمة محافظة عمران. كانت جزءً من محافظة صنعاء سابقاً. تقع على بعد حوالي 50 كم شمال غرب صنعاء.'
            }
        };
        
        // البحث عن المكان في قاعدة البيانات
        for (const city in factsDatabase) {
            if (placeName.includes(city)) {
                return factsDatabase[city];
            }
        }
        
        // إذا لم يتم العثور على المكان، إرجاع نص افتراضي
        return {
            text: `${placeName} هو موقع في اليمن. انقر على المزيد لمعرفة المعلومات التفصيلية.`
        };
    }
    
    // عرض أو إخفاء تفاصيل المكان
    togglePlaceDetails(locationId) {
        const locationObj = this.markers.find(item => item.id === locationId);
        if (!locationObj) return;
        
        // عرض نافذة منبثقة بمعلومات أكثر تفصيلاً
        this.showNotification('جاري تحميل التفاصيل الكاملة...', 'info');
    }
    
    // التعامل مع النقر على الخريطة
    handleMapClick(e) {
        // إذا كان هناك مسار نشط، قم بإخفائه
        if (this.directionsLayer) {
            this.directionsLayer.clearLayers();
            this.hideDirectionsPanel();
        }
        
        // إغلاق أي نوافذ معلومات مفتوحة
        this.map.closePopup();
    }
    
    // البحث عن موقع
    searchLocation(query) {
        this.showLoading(true);
        
        // البحث في النقاط المحملة مسبقاً
        const foundLocations = this.markers.filter(item => 
            item.location.name.includes(query) || 
            (item.location.address && item.location.address.includes(query))
        );
        
        if (foundLocations.length > 0) {
            // إذا وجدت نتائج محلياً
            const firstResult = foundLocations[0];
            this.map.setView([firstResult.location.latitude, firstResult.location.longitude], 15);
            firstResult.marker.openPopup();
            this.showLoading(false);
        } else {
            // إذا لم تجد نتائج محلياً، استخدم خدمة البحث عبر الإنترنت
            if (this.offlineMode) {
                this.showNotification('لا يمكن البحث عن مواقع جديدة في وضع عدم الاتصال', 'error');
                this.showLoading(false);
                return;
            }
            
            // استخدم Nominatim API للبحث (خدمة مجانية من OpenStreetMap)
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=ye&limit=1`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        const result = data[0];
                        this.map.setView([result.lat, result.lon], 15);
                        
                        // إنشاء علامة مؤقتة
                        if (this.searchMarker) {
                            this.map.removeLayer(this.searchMarker);
                        }
                        
                        this.searchMarker = L.marker([result.lat, result.lon]).addTo(this.map);
                        this.searchMarker.bindPopup(`<div class="custom-popup"><h3>${result.display_name}</h3></div>`).openPopup();
                    } else {
                        this.showNotification('لم يتم العثور على نتائج', 'warning');
                    }
                })
                .catch(error => {
                    console.error('Error searching:', error);
                    this.showNotification('حدث خطأ أثناء البحث', 'error');
                })
                .finally(() => {
                    this.showLoading(false);
                });
        }
    }
    
    // الحصول على اتجاهات المسار
    getDirections(destLat, destLng, destName) {
        // التحقق من وجود موقع المستخدم الحالي
        if (!this.currentLocation) {
            this.showNotification('يرجى تحديد موقعك الحالي أولاً', 'warning');
            this.getCurrentLocation();
            return;
        }
        
        this.showLoading(true);
        
        // إزالة المسار السابق إن وجد
        if (this.directionsLayer) {
            this.directionsLayer.clearLayers();
        }
        
        if (this.offlineMode) {
            // في وضع عدم الاتصال، استخدم مسار مباشر (خط مستقيم)
            const points = [
                [this.currentLocation.lat, this.currentLocation.lng],
                [destLat, destLng]
            ];
            
            // رسم المسار
            L.polyline(points, { color: '#1a73e8', weight: 5 }).addTo(this.directionsLayer);
            
            // عرض لوحة الاتجاهات البسيطة
            this.showDirectionsPanel(destName, null, true);
            this.showLoading(false);
        } else {
            // في وضع الاتصال، استخدم خدمة التوجيه
            // استخدم OSRM للحصول على المسار (خدمة مجانية)
            const url = `https://router.project-osrm.org/route/v1/driving/${this.currentLocation.lng},${this.currentLocation.lat};${destLng},${destLat}?overview=full&geometries=geojson&steps=true`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.code !== 'Ok' || !data.routes || data.routes.length === 0) {
                        throw new Error('لم يتم العثور على مسار');
                    }
                    
                    const route = data.routes[0];
                    const geometry = route.geometry;
                    
                    // رسم المسار الرئيسي
                    L.geoJSON(geometry, {
                        style: {
                            color: '#1a73e8',
                            weight: 5,
                            opacity: 0.7
                        }
                    }).addTo(this.directionsLayer);
                    
                    // ضبط حدود الخريطة لعرض المسار كاملاً
                    this.map.fitBounds(L.geoJSON(geometry).getBounds(), {
                        padding: [50, 50]
                    });
                    
                    // عرض لوحة الاتجاهات
                    this.showDirectionsPanel(destName, route);
                    
                    // إضافة صوت للإشعار
                    this.playDirectionSound('تم تحديد المسار');
                })
                .catch(error => {
                    console.error('Error getting directions:', error);
                    
                    // استخدم مسار مباشر كخطة بديلة
                    const points = [
                        [this.currentLocation.lat, this.currentLocation.lng],
                        [destLat, destLng]
                    ];
                    
                    L.polyline(points, { color: '#1a73e8', weight: 5 }).addTo(this.directionsLayer);
                    
                    // عرض لوحة اتجاهات بسيطة
                    this.showDirectionsPanel(destName, null, true);
                    
                    this.showNotification('تعذر الحصول على مسار دقيق. تم عرض مسار مباشر.', 'warning');
                })
                .finally(() => {
                    this.showLoading(false);
                });
        }
    }
    
    // عرض لوحة الاتجاهات
    showDirectionsPanel(destName, route, isDirectLine = false) {
        const panel = document.getElementById('directions-panel');
        const content = document.getElementById('directions-content');
        
        // تفريغ المحتوى السابق
        content.innerHTML = '';
        
        if (isDirectLine) {
            // في حالة المسار المباشر
            content.innerHTML = `
                <div class="direction-step">
                    <div class="direction-icon"><i class="fas fa-arrow-right"></i></div>
                    <div class="direction-text">توجه مباشرة إلى ${destName}</div>
                </div>
            `;
        } else if (route && route.legs && route.legs[0] && route.legs[0].steps) {
            // في حالة وجود خطوات تفصيلية
            const steps = route.legs[0].steps;
            
            steps.forEach((step, index) => {
                // تحويل التعليمات إلى العربية
                let arabicInstruction = this.translateDirection(step.maneuver.type, step.maneuver.modifier);
                
                content.innerHTML += `
                    <div class="direction-step">
                        <div class="direction-icon"><i class="${this.getDirectionIcon(step.maneuver.type, step.maneuver.modifier)}"></i></div>
                        <div class="direction-text">
                            ${arabicInstruction}
                            <div class="step-distance">${this.formatDistance(step.distance)}</div>
                        </div>
                    </div>
                `;
            });
            
            // إضافة المعلومات الإجمالية
            content.innerHTML += `
                <div class="direction-summary">
                    <div>المسافة الإجمالية: ${this.formatDistance(route.distance)}</div>
                    <div>الوقت المتوقع: ${this.formatDuration(route.duration)}</div>
                </div>
            `;
        }
        
        // إظهار اللوحة
        panel.classList.add('active');
    }
    
    // إخفاء لوحة الاتجاهات
    hideDirectionsPanel() {
        document.getElementById('directions-panel').classList.remove('active');
    }
    
    // ترجمة تعليمات الاتجاهات إلى العربية
    translateDirection(type, modifier) {
        switch (type) {
            case 'depart':
                return 'ابدأ الرحلة';
            case 'arrive':
                return 'لقد وصلت إلى وجهتك';
            case 'turn':
                switch (modifier) {
                    case 'left':
                        return 'انعطف يساراً';
                    case 'right':
                        return 'انعطف يميناً';
                    case 'slight left':
                        return 'انعطف قليلاً لليسار';
                    case 'slight right':
                        return 'انعطف قليلاً لليمين';
                    case 'sharp left':
                        return 'انعطف بشدة لليسار';
                    case 'sharp right':
                        return 'انعطف بشدة لليمين';
                    default:
                        return 'انعطف';
                }
            case 'continue':
                return 'تابع على الطريق';
            case 'roundabout':
                return 'ادخل الدوار';
            default:
                return 'تابع المسار';
        }
    }
    
    // الحصول على أيقونة مناسبة للاتجاه
    getDirectionIcon(type, modifier) {
        switch (type) {
            case 'depart':
                return 'fas fa-play';
            case 'arrive':
                return 'fas fa-flag-checkered';
            case 'turn':
                switch (modifier) {
                    case 'left':
                        return 'fas fa-arrow-left';
                    case 'right':
                        return 'fas fa-arrow-right';
                    case 'slight left':
                        return 'fas fa-arrow-alt-circle-left';
                    case 'slight right':
                        return 'fas fa-arrow-alt-circle-right';
                    case 'sharp left':
                        return 'fas fa-undo';
                    case 'sharp right':
                        return 'fas fa-redo';
                    default:
                        return 'fas fa-arrows-alt';
                }
            case 'continue':
                return 'fas fa-arrow-up';
            case 'roundabout':
                return 'fas fa-sync';
            default:
                return 'fas fa-location-arrow';
        }
    }
    
    // تنسيق المسافة
    formatDistance(meters) {
        if (meters < 1000) {
            return `${Math.round(meters)} متر`;
        } else {
            return `${(meters / 1000).toFixed(1)} كم`;
        }
    }
    
    // تنسيق المدة
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours} ساعة ${minutes} دقيقة`;
        } else {
            return `${minutes} دقيقة`;
        }
    }
    
    // حفظ الموقع
    savePlace(locationId, name, lat, lng) {
        // استخدام مدير المواقع المحفوظة
        const savedManager = new YemenDB.SavedPlacesManager();
        const isSaved = savedManager.savePlace(locationId, name, lat, lng);
        
        // تحديث واجهة المستخدم
        const saveBtn = document.querySelector(`.popup-action-btn.save-btn[onclick*="${locationId}"] i`);
        if (saveBtn) {
            if (isSaved) {
                saveBtn.classList.remove('far');
                saveBtn.classList.add('fas');
                this.showNotification('تم حفظ المكان بنجاح');
            } else {
                saveBtn.classList.remove('fas');
                saveBtn.classList.add('far');
                this.showNotification('تم إلغاء حفظ المكان');
            }
        }
        
        // حفظ التغييرات
        savedManager.saveChanges();
        
        // تحديث المواقع المحفوظة
        this.savedPlaces = savedManager.getSavedPlaces();
        
        // حفظ في قاعدة البيانات إذا كان متصلاً
        if (!this.offlineMode && isSaved) {
            savedManager.savePlaceToDatabase(locationId);
        }
    }
    
    // مشاركة الموقع
    shareLocation(lat, lng, name) {
        // استخدام الكلاس المساعد لمشاركة المواقع
        const success = YemenDB.LocationSharer.shareLocation(lat, lng, name);
        
        // إظهار إشعار للمستخدم
        if (success && !navigator.share) {
            this.showNotification('تم نسخ رابط الموقع إلى الحافظة');
        }
    }
    
    // حفظ المكان في قاعدة البيانات - للتوافق مع الإصدارات السابقة
    savePlaceToDatabase(locationId) {
        const savedManager = new YemenDB.SavedPlacesManager();
        return savedManager.savePlaceToDatabase(locationId);
    }
    
    // التعامل مع المواقع المشاركة
    handleSharedLocation() {
        try {
            // تنفيذ مباشر بدلاً من استخدام YemenDB
            const url = new URL(window.location.href);
            const params = url.searchParams;
            
            if (params.has('lat') && params.has('lng')) {
                const lat = parseFloat(params.get('lat'));
                const lng = parseFloat(params.get('lng'));
                const name = params.get('name') || 'موقع مشارك';
                
                if (!isNaN(lat) && !isNaN(lng)) {
                    // الانتقال إلى الموقع المشارك
                    this.map.setView([lat, lng], 16);
                    
                    // إضافة علامة
                    const marker = L.marker([lat, lng]).addTo(this.map);
                    marker.bindPopup(`<strong>${name}</strong>`).openPopup();
                    
                    // إظهار إشعار
                    this.showNotification('تم الانتقال إلى الموقع المشارك');
                    return true;
                }
            }
            
            return false;
        } catch (error) {
            console.error('خطأ في معالجة الموقع المشارك:', error);
            return false;
        }
    },
    
    // تشغيل صوت للاتجاهات
    playDirectionSound(text) {
        if ('speechSynthesis' in window) {
            const speech = new SpeechSynthesisUtterance(text);
            speech.lang = 'ar-SA';
            speechSynthesis.speak(speech);
        }
    },
    
    // تحديث الخريطة
    refreshMap() {
        // تحديث الطبقة الحالية
        if (this.map && this.baseLayers && this.currentLayer) {
            this.map.removeLayer(this.baseLayers[this.currentLayer]);
            this.map.addLayer(this.baseLayers[this.currentLayer]);
        }
        
        // إعادة تحميل النقاط
        this.loadLocations();
    }
};

// إنشاء كائن YemenMaps عند تحميل الصفحة
let yemenMaps;
document.addEventListener('DOMContentLoaded', () => {
    yemenMaps = new YemenMaps();
});

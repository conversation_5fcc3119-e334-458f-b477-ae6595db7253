# توضيح مسارات تثبيت نظام "يمن ناف"

## مسارات التثبيت

هناك مساران مهمان في عملية تثبيت نظام "يمن ناف":

### 1. مسار ملف التثبيت (`e:\yemen gps`)

- هذا هو المسار الحالي الذي يوجد فيه ملف التثبيت (`install_yemen_nav.ps1`) والوثائق
- هذا هو المسار الذي تقوم بتشغيل ملف التثبيت منه
- يحتوي هذا المسار على:
  - ملف التثبيت: `install_yemen_nav.ps1`
  - مجلد الوثائق: `docs/`
  - ملفات المشروع الأصلية: مثل ملفات الأندرويد وغيرها

### 2. مسار تثبيت النظام (`C:\yemen-nav`)

- هذا هو المسار الذي سيتم تثبيت النظام فيه بواسطة ملف التثبيت
- يتم إنشاء هذا المسار تلقائيًا بواسطة ملف التثبيت
- سيحتوي هذا المسار على:
  - مجلد الواجهة الخلفية: `C:\yemen-nav\backend`
  - مجلد الواجهة الأمامية: `C:\yemen-nav\frontend`
  - مجلد تخزين البيانات: `C:\yemen-nav\storage`
  - ملف تكوين PM2: `C:\yemen-nav\ecosystem.config.js`

## عملية التثبيت

1. تقوم بفتح PowerShell بصلاحيات المسؤول
2. تنتقل إلى المسار `e:\yemen gps` حيث يوجد ملف التثبيت
3. تقوم بتشغيل ملف التثبيت: `.\install_yemen_nav.ps1`
4. يقوم ملف التثبيت بإنشاء المسار `C:\yemen-nav` وتثبيت النظام فيه
5. بعد اكتمال التثبيت، يمكنك الوصول إلى النظام من خلال المتصفح على العنوان: `http://yemen-nav.local` أو `http://localhost`

## ملاحظات مهمة

- لا تحتاج إلى نقل أي ملفات يدويًا بين المسارين
- يمكنك الاحتفاظ بالمسار `e:\yemen gps` كمرجع للوثائق وملفات المشروع الأصلية
- جميع مكونات النظام العاملة ستكون في المسار `C:\yemen-nav`
- إذا أردت تغيير مسار التثبيت، يمكنك تعديل المتغير `$installDir` في بداية ملف التثبيت

## لماذا تم اختيار المسار `C:\yemen-nav`؟

تم اختيار المسار `C:\yemen-nav` للأسباب التالية:

1. **التوافق**: القرص C هو القرص الرئيسي في معظم أنظمة ويندوز، مما يضمن توافقًا أفضل مع البرامج والخدمات
2. **الصلاحيات**: عادة ما يكون للمستخدمين صلاحيات كاملة على القرص C
3. **الاستقرار**: تثبيت النظام على القرص الرئيسي يوفر استقرارًا أفضل
4. **سهولة الوصول**: المسار بسيط وسهل التذكر

إذا كنت ترغب في تثبيت النظام في مسار مختلف، يمكنك تعديل ملف التثبيت قبل تشغيله.

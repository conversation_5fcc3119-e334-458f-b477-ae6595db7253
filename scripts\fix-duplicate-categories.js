// إصلاح الفئات المكررة في قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function fixDuplicateCategories() {
    try {
        console.log('🔧 بدء إصلاح الفئات المكررة...\n');
        
        // الحصول على جميع الفئات
        const result = await pool.query(`
            SELECT id, name_ar, name_en, parent_id, icon, color 
            FROM place_categories 
            ORDER BY id ASC
        `);
        
        console.log(`📊 إجمالي الفئات قبل الإصلاح: ${result.rows.length}\n`);
        
        // تجميع الفئات حسب الاسم العربي والأب
        const categoryGroups = {};
        
        result.rows.forEach(category => {
            const key = `${category.name_ar.trim().toLowerCase()}_${category.parent_id || 'null'}`;
            if (!categoryGroups[key]) {
                categoryGroups[key] = [];
            }
            categoryGroups[key].push(category);
        });
        
        // العثور على الفئات المكررة وحذف النسخ الإضافية
        const duplicateGroups = Object.keys(categoryGroups).filter(key => categoryGroups[key].length > 1);
        
        if (duplicateGroups.length === 0) {
            console.log('✅ لا توجد فئات مكررة للإصلاح');
            return;
        }
        
        console.log(`🔄 تم العثور على ${duplicateGroups.length} مجموعة من الفئات المكررة\n`);
        
        let totalDeleted = 0;
        
        for (const groupKey of duplicateGroups) {
            const duplicates = categoryGroups[groupKey];
            
            // الاحتفاظ بالفئة الأولى (أقل ID) وحذف الباقي
            const keepCategory = duplicates[0]; // أقل ID
            const deleteCategories = duplicates.slice(1);
            
            console.log(`📝 معالجة: ${keepCategory.name_ar}`);
            console.log(`   ✅ الاحتفاظ بـ: ID ${keepCategory.id}`);
            
            for (const deleteCategory of deleteCategories) {
                console.log(`   🗑️ حذف: ID ${deleteCategory.id}`);
                
                try {
                    // أولاً: تحديث أي أماكن تستخدم هذه الفئة المكررة
                    await pool.query(`
                        UPDATE places 
                        SET category_id = $1 
                        WHERE category_id = $2
                    `, [keepCategory.id, deleteCategory.id]);
                    
                    // ثانياً: تحديث أي فئات فرعية تشير لهذه الفئة
                    await pool.query(`
                        UPDATE place_categories 
                        SET parent_id = $1 
                        WHERE parent_id = $2
                    `, [keepCategory.id, deleteCategory.id]);
                    
                    // ثالثاً: حذف الفئة المكررة
                    await pool.query(`
                        DELETE FROM place_categories 
                        WHERE id = $1
                    `, [deleteCategory.id]);
                    
                    totalDeleted++;
                    
                } catch (error) {
                    console.error(`   ❌ خطأ في حذف الفئة ${deleteCategory.id}:`, error.message);
                }
            }
            
            console.log('');
        }
        
        console.log(`🎉 تم الانتهاء من الإصلاح!`);
        console.log(`📊 تم حذف ${totalDeleted} فئة مكررة\n`);
        
        // التحقق من النتيجة النهائية
        const finalResult = await pool.query(`
            SELECT COUNT(*) as total_categories 
            FROM place_categories
        `);
        
        console.log(`📈 إجمالي الفئات بعد الإصلاح: ${finalResult.rows[0].total_categories}`);
        
        // عرض الفئات النهائية
        const finalCategories = await pool.query(`
            SELECT id, name_ar, name_en, parent_id 
            FROM place_categories 
            ORDER BY parent_id NULLS FIRST, name_ar
        `);
        
        console.log('\n📋 الفئات النهائية:');
        console.log('==================');
        
        const mainCategories = finalCategories.rows.filter(cat => !cat.parent_id);
        const subCategories = finalCategories.rows.filter(cat => cat.parent_id);
        
        console.log('\n🏷️ الفئات الرئيسية:');
        mainCategories.forEach(cat => {
            console.log(`   ${cat.id}. ${cat.name_ar} (${cat.name_en})`);
            
            // عرض الفئات الفرعية
            const subs = subCategories.filter(sub => sub.parent_id === cat.id);
            if (subs.length > 0) {
                subs.forEach(sub => {
                    console.log(`      └── ${sub.id}. ${sub.name_ar} (${sub.name_en})`);
                });
            }
        });
        
    } catch (error) {
        console.error('❌ خطأ في إصلاح الفئات:', error);
    } finally {
        await pool.end();
    }
}

fixDuplicateCategories();

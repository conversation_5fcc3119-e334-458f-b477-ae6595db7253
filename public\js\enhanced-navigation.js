/**
 * وحدة الملاحة المحسنة
 * توفر خدمات تحديد الوجهة وعرض المسارات بشكل متطور
 */
const EnhancedNavigation = (function() {
  // المتغيرات الخاصة
  let map = null;
  let directionsService = null;
  let directionsRenderer = null;
  let currentRoute = null;
  let userLocation = null;
  let destinationLocation = null;
  let routeOptions = {
    travelMode: 'DRIVING',
    avoidTolls: false,
    avoidHighways: false,
    optimizeWaypoints: true
  };
  
  // تهيئة وحدة الملاحة
  function init(mapInstance) {
    map = mapInstance;
    
    // إنشاء عناصر التحكم في وسيلة النقل
    createTravelModeControls();
    
    // إضافة أحداث المستمع
    setupEventListeners();
    
    return {
      calculateRoute,
      clearRoute,
      setUserLocation,
      setDestination,
      getCurrentRoute
    };
  }
  
  // إنشاء عناصر التحكم في وسيلة النقل
  function createTravelModeControls() {
    // إنشاء حاوية أزرار وسائل النقل
    const travelModeControls = document.createElement('div');
    travelModeControls.className = 'travel-mode-controls';
    
    // إنشاء أزرار وسائل النقل
    const modes = [
      { id: 'DRIVING', icon: 'fa-car', text: 'سيارة' },
      { id: 'WALKING', icon: 'fa-walking', text: 'مشي' },
      { id: 'BICYCLING', icon: 'fa-bicycle', text: 'دراجة' },
      { id: 'TRANSIT', icon: 'fa-bus', text: 'نقل عام' }
    ];
    
    modes.forEach(mode => {
      const button = document.createElement('button');
      button.className = `travel-mode-btn ${mode.id === 'DRIVING' ? 'active' : ''}`;
      button.setAttribute('data-mode', mode.id);
      button.innerHTML = `<i class="fas ${mode.icon}"></i><span>${mode.text}</span>`;
      
      travelModeControls.appendChild(button);
    });
    
    // إضافة الحاوية إلى المستند
    document.body.appendChild(travelModeControls);
  }
  
  // إعداد أحداث المستمع
  function setupEventListeners() {
    // مستمع لتغيير وسيلة النقل
    document.querySelectorAll('.travel-mode-btn').forEach(btn => {
      btn.addEventListener('click', function() {
        const mode = this.getAttribute('data-mode');
        setTravelMode(mode);
      });
    });
    
    // مستمع لزر تحديد الوجهة
    document.getElementById('set-destination').addEventListener('click', function() {
      toggleDestinationMode();
    });
  }
  
  // تبديل وضع تحديد الوجهة
  function toggleDestinationMode() {
    const isActive = document.getElementById('set-destination').classList.toggle('active');
    
    if (isActive) {
      showNotification("انقر على الخريطة لتحديد الوجهة", "info");
      
      // تغيير مؤشر الفأرة
      document.getElementById('map').style.cursor = 'crosshair';
      
      // إضافة مستمع للنقر على الخريطة
      map.once('click', function(e) {
        setDestination(e.latlng);
        
        // إعادة مؤشر الفأرة إلى الوضع الطبيعي
        document.getElementById('map').style.cursor = '';
        
        // إلغاء تنشيط الزر
        document.getElementById('set-destination').classList.remove('active');
      });
    } else {
      // إعادة مؤشر الفأرة إلى الوضع الطبيعي
      document.getElementById('map').style.cursor = '';
      
      // إزالة مستمع النقر
      map.off('click');
    }
  }
  
  // تعيين موقع المستخدم
  function setUserLocation(location) {
    userLocation = location;
    
    // إذا كان هناك وجهة محددة، أعد حساب المسار
    if (destinationLocation) {
      calculateRoute();
    }
  }
  
  // تعيين الوجهة
  function setDestination(location) {
    destinationLocation = location;
    
    // إنشاء علامة للوجهة
    createDestinationMarker(location);
    
    // إذا كان موقع المستخدم معروفاً، احسب المسار
    if (userLocation) {
      calculateRoute();
    } else {
      // طلب موقع المستخدم
      requestUserLocation();
    }
  }
  
  // إنشاء علامة للوجهة
  function createDestinationMarker(location) {
    // إزالة العلامة السابقة إن وجدت
    if (window.destinationMarker) {
      window.destinationMarker.setMap(null);
    }
    
    // إنشاء علامة جديدة
    window.destinationMarker = L.marker([location.lat, location.lng], {
      icon: L.divIcon({
        className: 'destination-marker',
        html: '<div class="marker-icon destination-icon"></div>',
        iconSize: [30, 30],
        iconAnchor: [15, 30]
      }),
      draggable: true
    }).addTo(map);
    
    // إضافة مستمع لسحب العلامة
    window.destinationMarker.on('dragend', function(e) {
      const newLocation = e.target.getLatLng();
      setDestination(newLocation);
    });
    
    // إضافة نافذة معلومات
    window.destinationMarker.bindPopup('<div><strong>الوجهة</strong></div>');
  }
  
  // طلب موقع المستخدم
  function requestUserLocation() {
    showNotification("جاري تحديد موقعك الحالي...", "info");
    
    // محاولة استخدام الطرق المتعددة لتحديد الموقع
    tryMultipleLocationMethods();
  }
  
  // إنشاء زر بديل لتحديد الموقع في بيئة ويندوز سيرفر
  function createAlternativeLocationButton() {
    // التحقق من وجود الزر بالفعل
    if (document.getElementById('alternative-location-btn')) {
      return;
    }
    
    // إنشاء الزر
    const button = document.createElement('button');
    button.id = 'alternative-location-btn';
    button.className = 'floating-btn';
    button.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
    button.style.position = 'fixed';
    button.style.bottom = '100px';
    button.style.right = '20px';
    button.style.zIndex = '1000';
    button.style.padding = '10px 15px';
    button.style.backgroundColor = '#4CAF50';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '5px';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
    button.style.cursor = 'pointer';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.gap = '5px';
    button.style.fontSize = '14px';
    
    // إضافة حدث النقر
    button.addEventListener('click', function() {
      // إظهار مربع حوار تحديد الموقع
      showLocationSelectionDialog({
        lat: 15.3694,
        lng: 44.1910
      });
    });
    
    // إضافة الزر إلى الصفحة
    document.body.appendChild(button);
    
    console.log('تم إنشاء زر بديل لتحديد الموقع في بيئة ويندوز سيرفر');
  }
  
  // كشف بيئة ويندوز سيرفر
  function isWindowsServer() {
    // طرق للكشف عن بيئة ويندوز سيرفر
    const userAgent = navigator.userAgent.toLowerCase();
    const isServer = 
      userAgent.includes('windows nt') && 
      (userAgent.includes('server') || 
       document.documentElement.dataset.serverEnvironment === 'true');
    
    // التحقق من وجود علامات إضافية لبيئة الخادم
    const isGeolocationRestricted = 
      !navigator.geolocation || 
      typeof navigator.geolocation.getCurrentPosition !== 'function';
      
    return isServer || isGeolocationRestricted;
  }
  
  // محاولة طرق متعددة لتحديد الموقع
  function tryMultipleLocationMethods() {
    // التحقق مما إذا كنا في بيئة ويندوز سيرفر
    const serverEnvironment = isWindowsServer();
    
    if (serverEnvironment) {
      console.log("تم الكشف عن بيئة ويندوز سيرفر - استخدام طرق بديلة لتحديد الموقع");
      // إنشاء زر بديل لتحديد الموقع في بيئة ويندوز سيرفر
      createAlternativeLocationButton();
      // تخطي Geolocation API في بيئة الخادم
      tryIPBasedLocation();
      return;
    }
    
    // الطريقة 1: استخدام Geolocation API القياسي (فقط في البيئات غير المقيدة)
    if (navigator.geolocation) {
      const geolocationOptions = {
        enableHighAccuracy: true,
        timeout: 10000,        // 10 ثواني كمهلة زمنية
        maximumAge: 300000     // 5 دقائق كأقصى عمر للبيانات المخزنة مؤقتًا
      };
      
      try {
        navigator.geolocation.getCurrentPosition(
          function(position) {
            const userLoc = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            
            setUserLocation(userLoc);
            saveLocationToLocalStorage(userLoc); // حفظ الموقع للاستخدام المستقبلي
          },
          function(error) {
            console.log("فشل تحديد الموقع باستخدام Geolocation API:", error.code, error.message);
            // الانتقال إلى الطريقة التالية
            tryIPBasedLocation();
          },
          geolocationOptions
        );
      } catch (e) {
        console.error("خطأ غير متوقع عند استخدام Geolocation API:", e);
        // الانتقال إلى الطريقة التالية في حالة حدوث استثناء
        tryIPBasedLocation();
      }
    } else {
      console.log("متصفحك لا يدعم Geolocation API");
      // الانتقال إلى الطريقة التالية
      tryIPBasedLocation();
    }
  }
  
  // الطريقة 2: استخدام تحديد الموقع بناءً على عنوان IP
  function tryIPBasedLocation() {
    showNotification("جاري تحديد موقعك بطريقة بديلة...", "info");
    
    // التحقق من وجود موقع مخزن قبل محاولة الاتصال بالخادم
    const storedLocation = getLocationFromLocalStorage();
    if (storedLocation) {
      // إذا كنا في بيئة ويندوز سيرفر، فمن الأفضل استخدام الموقع المخزن مباشرة
      if (isWindowsServer()) {
        console.log("استخدام الموقع المخزن مسبقًا في بيئة ويندوز سيرفر");
        setUserLocation(storedLocation);
        showNotification("تم استخدام موقعك المخزن مسبقًا", "success");
        return;
      }
    }
    
    // محاولة الحصول على الموقع باستخدام نقطة نهاية API الخاصة بنا
    const fetchWithTimeout = (url, options = {}, timeout = 5000) => {
      return Promise.race([
        fetch(url, options),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('انتهت مهلة الاتصال')), timeout)
        )
      ]);
    };
    
    // محاولة الحصول على الموقع من الخادم الخاص بنا أولاً
    fetchWithTimeout('/api/ip-location')
      .then(response => {
        if (!response.ok) {
          throw new Error(`خطأ في الاستجابة: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data && data.lat && data.lng) {
          const userLoc = {
            lat: data.lat,
            lng: data.lng
          };
          
          setUserLocation(userLoc);
          saveLocationToLocalStorage(userLoc);
          showNotification(`تم تحديد موقعك بنجاح (بواسطة ${data.provider || 'IP'})`);          
        } else {
          throw new Error('لم يتم العثور على بيانات الموقع');
        }
      })
      .catch(error => {
        console.log("فشل تحديد الموقع بناءً على IP:", error);
        
        // محاولة استخدام نقطة نهاية API للمدينة
        fetchWithTimeout('/api/city-location?city=صنعاء')
          .then(response => {
            if (!response.ok) {
              throw new Error(`خطأ في الاستجابة: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            if (data && data.lat && data.lng) {
              const userLoc = {
                lat: data.lat,
                lng: data.lng
              };
              
              setUserLocation(userLoc);
              saveLocationToLocalStorage(userLoc);
              showNotification(`تم تحديد موقعك في ${data.city || 'صنعاء'}`);              
            } else {
              throw new Error('لم يتم العثور على بيانات الموقع');
            }
          })
          .catch(cityError => {
            console.log("فشل تحديد الموقع بناءً على المدينة:", cityError);
            // الانتقال إلى الطريقة التالية
            tryStoredLocation();
          });
      });
  }
  
  // الطريقة 3: استخدام الموقع المخزن مسبقًا
  function tryStoredLocation() {
    const storedLocation = getLocationFromLocalStorage();
    
    if (storedLocation) {
      showNotification("تم استخدام موقعك المخزن مسبقًا", "info");
      setUserLocation(storedLocation);
    } else {
      // الانتقال إلى الطريقة الأخيرة
      tryDefaultLocation();
    }
  }
  
  // الطريقة 4: استخدام موقع افتراضي (صنعاء)
  function tryDefaultLocation() {
    showNotification("تعذر تحديد موقعك. يرجى تحديد موقعك يدوياً.", "warning");
    
    // استخدام موقع افتراضي (صنعاء)
    const defaultLocation = {
      lat: 15.3694,
      lng: 44.1910
    };
    
    // عرض مربع حوار لتحديد الموقع يدوياً
    showLocationSelectionDialog(defaultLocation);
  }
  
  // حفظ الموقع في التخزين المحلي
  function saveLocationToLocalStorage(location) {
    try {
      localStorage.setItem('userLocation', JSON.stringify({
        lat: location.lat,
        lng: location.lng,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('خطأ في حفظ الموقع في التخزين المحلي:', error);
    }
  }
  
  // الحصول على الموقع من التخزين المحلي
  function getLocationFromLocalStorage() {
    try {
      const storedData = localStorage.getItem('userLocation');
      if (!storedData) return null;
      
      const locationData = JSON.parse(storedData);
      const timestamp = locationData.timestamp || 0;
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
      
      // التحقق من عمر البيانات
      if (now - timestamp > maxAge) {
        // البيانات قديمة جدًا
        localStorage.removeItem('userLocation');
        return null;
      }
      
      return {
        lat: locationData.lat,
        lng: locationData.lng
      };
    } catch (error) {
      console.error('خطأ في قراءة الموقع من التخزين المحلي:', error);
      return null;
    }
  }
  
  // كشف بيئة ويندوز سيرفر عند تحميل الصفحة
  function checkServerEnvironmentOnLoad() {
    // التحقق من سمة data-server-environment في عنصر HTML
    const htmlElement = document.documentElement;
    const isServerEnv = htmlElement.getAttribute('data-server-environment') === 'true';
    
    if (isServerEnv) {
      console.log('تم الكشف عن بيئة ويندوز سيرفر من سمة HTML');
      // إنشاء زر بديل لتحديد الموقع
      createAlternativeLocationButton();
    } else if (isWindowsServer()) {
      // إذا لم تكن السمة موجودة ولكن تم الكشف عن بيئة ويندوز سيرفر بطريقة أخرى
      console.log('تم الكشف عن بيئة ويندوز سيرفر من خلال الكشف التلقائي');
      createAlternativeLocationButton();
    }
  }
  
  // تسجيل حدث تحميل الصفحة للكشف عن بيئة ويندوز سيرفر
  document.addEventListener('DOMContentLoaded', function() {
    // الكشف عن بيئة ويندوز سيرفر عند تحميل الصفحة
    checkServerEnvironmentOnLoad();
  });
  
  // عرض مربع حوار لتحديد الموقع يدوياً
  function showLocationSelectionDialog(defaultLocation) {
    // التحقق من وجود مربع الحوار
    let locationDialog = document.getElementById('location-selection-dialog');
    
    if (!locationDialog) {
      // إنشاء مربع حوار جديد
      locationDialog = document.createElement('div');
      locationDialog.id = 'location-selection-dialog';
      locationDialog.className = 'modal';
      
      locationDialog.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h3>تحديد موقعك</h3>
            <button class="close-btn">&times;</button>
          </div>
          <div class="modal-body">
            <p>يرجى تحديد موقعك بإحدى الطرق التالية:</p>
            
            <div class="location-options">
              <button id="select-on-map-btn" class="action-btn primary">
                <i class="fas fa-map-marker-alt"></i>
                <span>تحديد على الخريطة</span>
              </button>
              
              <button id="use-default-location-btn" class="action-btn">
                <i class="fas fa-home"></i>
                <span>استخدام موقع صنعاء</span>
              </button>
              
              <div class="city-selection">
                <label for="city-select">اختر مدينة:</label>
                <select id="city-select">
                  <option value="15.3694,44.1910">صنعاء</option>
                  <option value="12.7797,45.0095">عدن</option>
                  <option value="14.7979,42.9542">الحديدة</option>
                  <option value="13.9789,44.1754">تعز</option>
                  <option value="15.4542,44.2055">ذمار</option>
                  <option value="14.0783,44.2464">إب</option>
                  <option value="16.9398,43.5895">صعدة</option>
                  <option value="15.4248,45.3292">مأرب</option>
                </select>
                <button id="use-selected-city-btn" class="action-btn">استخدام المدينة المحددة</button>
              </div>
            </div>
          </div>
        </div>
      `;
      
      document.body.appendChild(locationDialog);
      
      // إضافة مستمعات الأحداث
      locationDialog.querySelector('.close-btn').addEventListener('click', function() {
        locationDialog.style.display = 'none';
      });
      
      document.getElementById('select-on-map-btn').addEventListener('click', function() {
        locationDialog.style.display = 'none';
        promptForUserLocation();
      });
      
      document.getElementById('use-default-location-btn').addEventListener('click', function() {
        locationDialog.style.display = 'none';
        setUserLocation(defaultLocation);
        saveLocationToLocalStorage(defaultLocation);
      });
      
      document.getElementById('use-selected-city-btn').addEventListener('click', function() {
        const selectedValue = document.getElementById('city-select').value;
        const [lat, lng] = selectedValue.split(',').map(Number);
        
        const cityLocation = {
          lat: lat,
          lng: lng
        };
        
        locationDialog.style.display = 'none';
        setUserLocation(cityLocation);
        saveLocationToLocalStorage(cityLocation);
      });
    }
    
    // عرض مربع الحوار
    locationDialog.style.display = 'block';
  }
  
  // طلب تحديد موقع المستخدم
  function promptForUserLocation() {
    showNotification("انقر على الخريطة لتحديد موقعك الحالي", "info");
    
    // تغيير مؤشر الفأرة
    document.getElementById('map').style.cursor = 'crosshair';
    
    // إضافة مستمع للنقر على الخريطة
    map.once('click', function(e) {
      // إعادة مؤشر الفأرة إلى الوضع الطبيعي
      document.getElementById('map').style.cursor = '';
      
      const userLoc = {
        lat: e.latlng.lat,
        lng: e.latlng.lng
      };
      
      setUserLocation(userLoc);
    });
  }
  
  // حساب المسار
  function calculateRoute() {
    if (!userLocation || !destinationLocation) {
      showNotification("يرجى تحديد نقطة البداية والوجهة", "warning");
      return;
    }
    
    showLoadingIndicator("جاري حساب المسار...");
    
    // إنشاء نقاط المسار
    const waypoints = [
      L.latLng(userLocation.lat, userLocation.lng),
      L.latLng(destinationLocation.lat, destinationLocation.lng)
    ];
    
    // إزالة المسار السابق إن وجد
    if (window.routingControl) {
      map.removeControl(window.routingControl);
    }
    
    // إنشاء مسار جديد مع خيارات متقدمة
    window.routingControl = L.Routing.control({
      waypoints: waypoints,
      routeWhileDragging: true,
      showAlternatives: true,
      fitSelectedRoutes: true,
      language: 'ar',
      // استخدام OSRM كمزود للمسارات للحصول على مسارات دقيقة عبر الشوارع
      router: L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        profile: routeOptions.travelMode.toLowerCase(),
        // تعيين معلمات إضافية للحصول على مسارات أكثر دقة
        options: {
          alternatives: true,  // طلب مسارات بديلة
          steps: true,         // طلب خطوات تفصيلية
          geometries: 'polyline', // الحصول على هندسة المسار كخط متعدد النقاط
          overview: 'full',    // الحصول على نظرة عامة كاملة على المسار
          annotations: true    // طلب تعليقات توضيحية للمسار
        }
      }),
      // تخصيص خيارات الخط للمسار
      lineOptions: {
        styles: [
          { color: '#1a73e8', opacity: 0.8, weight: 6 },
          { color: 'white', opacity: 0.3, weight: 2 }
        ],
        extendToWaypoints: true,  // تمديد المسار إلى نقاط الطريق
        missingRouteTolerance: 10 // التسامح مع أجزاء المسار المفقودة
      },
      // تخصيص علامات نقاط الطريق
      createMarker: function(i, waypoint, n) {
        const marker = L.marker(waypoint.latLng, {
          draggable: true,
          icon: L.divIcon({
            className: i === 0 ? 'start-marker' : 'end-marker',
            html: i === 0 ? '<div class="marker-icon start-icon"></div>' : '<div class="marker-icon end-icon"></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 30]
          })
        });
        
        // إضافة مستمع لسحب العلامة لإعادة حساب المسار
        marker.on('dragend', function() {
          // تحديث نقاط المسار
          const newWaypoints = window.routingControl.getWaypoints();
          
          // تحديث موقع المستخدم أو الوجهة
          if (i === 0) {
            userLocation = {
              lat: marker.getLatLng().lat,
              lng: marker.getLatLng().lng
            };
          } else {
            destinationLocation = {
              lat: marker.getLatLng().lat,
              lng: marker.getLatLng().lng
            };
          }
          
          // إعادة حساب المسار
          window.routingControl.setWaypoints(newWaypoints);
        });
        
        return marker;
      },
      // تخصيص خيارات العرض
      collapsible: true,  // جعل لوحة التحكم قابلة للطي
      autoRoute: true,    // حساب المسار تلقائيًا
      useZoomParameter: true, // استخدام معلمة التكبير
      waypointMode: 'snap', // وضع التقاط نقاط الطريق على أقرب طريق
      // تخصيص خيارات التخطيط
      plan: L.Routing.plan(waypoints, {
        createMarker: function(i, waypoint) {
          return L.marker(waypoint.latLng, {
            draggable: true,
            icon: L.divIcon({
              className: i === 0 ? 'start-marker' : 'end-marker',
              html: i === 0 ? '<div class="marker-icon start-icon"></div>' : '<div class="marker-icon end-icon"></div>',
              iconSize: [30, 30],
              iconAnchor: [15, 30]
            })
          });
        },
        draggableWaypoints: true,  // جعل نقاط الطريق قابلة للسحب
        dragStyles: [
          { color: '#1a73e8', opacity: 0.8, weight: 6 },
          { color: 'white', opacity: 0.3, weight: 2 }
        ],
        addWaypoints: true  // السماح بإضافة نقاط طريق جديدة
      })
    }).addTo(map);
    
    // إضافة مستمع لحدث اكتمال حساب المسار
    window.routingControl.on('routesfound', function(e) {
      hideLoadingIndicator();
      
      // حفظ المسار الحالي
      currentRoute = e.routes[0];
      
      // عرض معلومات المسار
      displayRouteInfo(currentRoute);
      
      // إذا كان هناك مسارات بديلة، عرض زر لعرضها
      if (e.routes.length > 1) {
        showAlternativeRoutesButton(e.routes);
      }
    });
    
    // إضافة مستمع لحدث فشل حساب المسار
    window.routingControl.on('routingerror', function(e) {
      hideLoadingIndicator();
      console.error('خطأ في حساب المسار:', e);
      
      // محاولة استخدام مزود مسارات بديل
      tryAlternativeRouter();
    });
  }
  
  // محاولة استخدام مزود مسارات بديل
  function tryAlternativeRouter() {
    showNotification('جاري محاولة استخدام مزود مسارات بديل...', 'info');
    
    // إنشاء نقاط المسار
    const waypoints = [
      L.latLng(userLocation.lat, userLocation.lng),
      L.latLng(destinationLocation.lat, destinationLocation.lng)
    ];
    
    // إزالة المسار السابق إن وجد
    if (window.routingControl) {
      map.removeControl(window.routingControl);
    }
    
    // استخدام مزود مسارات Mapbox
    window.routingControl = L.Routing.control({
      waypoints: waypoints,
      routeWhileDragging: true,
      showAlternatives: true,
      fitSelectedRoutes: true,
      language: 'ar',
      // استخدام Mapbox كمزود للمسارات البديل
      router: L.Routing.mapbox('pk.eyJ1IjoieWVtZW5ncHMiLCJhIjoiY2xvNXE2MjJzMDNrYzJrcGR5ZDhqZnl1ZSJ9.a4qhbqhfGQYPKT-_QhX_Gg', {
        profile: routeOptions.travelMode === 'DRIVING' ? 'mapbox/driving' : 
                routeOptions.travelMode === 'WALKING' ? 'mapbox/walking' : 
                routeOptions.travelMode === 'BICYCLING' ? 'mapbox/cycling' : 'mapbox/driving',
        alternatives: true
      }),
      lineOptions: {
        styles: [
          { color: '#1a73e8', opacity: 0.8, weight: 6 },
          { color: 'white', opacity: 0.3, weight: 2 }
        ]
      },
      createMarker: function(i, waypoint, n) {
        const marker = L.marker(waypoint.latLng, {
          draggable: true,
          icon: L.divIcon({
            className: i === 0 ? 'start-marker' : 'end-marker',
            html: i === 0 ? '<div class="marker-icon start-icon"></div>' : '<div class="marker-icon end-icon"></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 30]
          })
        });
        
        return marker;
      }
    }).addTo(map);
    
    // إضافة مستمع لحدث اكتمال حساب المسار
    window.routingControl.on('routesfound', function(e) {
      hideLoadingIndicator();
      
      // حفظ المسار الحالي
      currentRoute = e.routes[0];
      
      // عرض معلومات المسار
      displayRouteInfo(currentRoute);
    });
    
    // إضافة مستمع لحدث فشل حساب المسار
    window.routingControl.on('routingerror', function(e) {
      hideLoadingIndicator();
      showNotification('تعذر حساب المسار. يرجى المحاولة مرة أخرى لاحقًا.', 'error');
      
      // محاولة استخدام حساب المسار المباشر كحل أخير
      calculateDirectRoute();
    });
  }
  
  // حساب المسار المباشر (كحل أخير)
  function calculateDirectRoute() {
    showNotification('جاري حساب مسار مباشر...', 'info');
    
    // إنشاء نقاط المسار
    const start = L.latLng(userLocation.lat, userLocation.lng);
    const end = L.latLng(destinationLocation.lat, destinationLocation.lng);
    
    // إنشاء خط مستقيم بين نقطة البداية والوجهة
    const directLine = L.polyline([start, end], {
      color: '#ff9800',
      weight: 5,
      opacity: 0.7,
      dashArray: '10, 10',
      lineCap: 'round'
    }).addTo(map);
    
    // حساب المسافة والمدة التقريبية
    const distance = start.distanceTo(end);
    const duration = distance / 1.4 / 1000 * 60; // تقدير تقريبي: 1.4 م/ث للمشي
    
    // إنشاء كائن مسار وهمي
    currentRoute = {
      summary: {
        totalDistance: distance,
        totalTime: duration
      },
      instructions: [
        {
          text: 'اتجه مباشرة إلى الوجهة'
        }
      ]
    };
    
    // عرض معلومات المسار
    displayRouteInfo(currentRoute);
    
    // حفظ الخط المباشر للإزالة لاحقًا
    window.directLine = directLine;
  }
  
  // عرض زر المسارات البديلة
  function showAlternativeRoutesButton(routes) {
    // التحقق من وجود لوحة معلومات المسار
    const routeInfoPanel = document.getElementById('route-info-panel');
    if (!routeInfoPanel) return;
    
    // التحقق من وجود قسم المسارات البديلة
    let alternativesSection = routeInfoPanel.querySelector('.route-alternatives');
    
    if (!alternativesSection) {
      // إنشاء قسم المسارات البديلة
      alternativesSection = document.createElement('div');
      alternativesSection.className = 'route-alternatives';
      
      // إضافة القسم قبل أزرار الإجراءات
      const actionsSection = routeInfoPanel.querySelector('.route-actions');
      routeInfoPanel.insertBefore(alternativesSection, actionsSection);
    }
    
    // تحديث محتوى قسم المسارات البديلة
    let alternativesHtml = '<h4>مسارات بديلة:</h4><div class="alternatives-list">';
    
    routes.forEach((route, index) => {
      const isActive = index === 0;
      alternativesHtml += `
        <div class="alternative-route ${isActive ? 'active' : ''}" data-index="${index}">
          <div class="route-info">
            <span class="route-distance">${formatDistance(route.summary.totalDistance)}</span>
            <span class="route-duration">${formatDuration(route.summary.totalTime)}</span>
          </div>
          <div class="route-preview" style="background-color: ${isActive ? '#1a73e8' : '#90caf9'}"></div>
        </div>
      `;
    });
    
    alternativesHtml += '</div>';
    alternativesSection.innerHTML = alternativesHtml;
    
    // إضافة مستمعات الأحداث للمسارات البديلة
    alternativesSection.querySelectorAll('.alternative-route').forEach((element, index) => {
      element.addEventListener('click', function() {
        // تحديث المسار النشط
        alternativesSection.querySelectorAll('.alternative-route').forEach(el => {
          el.classList.remove('active');
        });
        this.classList.add('active');
        
        // تحديث المسار المعروض
        window.routingControl.setSelectedRoute(index);
        
        // تحديث معلومات المسار
        currentRoute = routes[index];
        displayRouteInfo(currentRoute);
      });
    });
  }
  
  // عرض معلومات المسار
  function displayRouteInfo(route) {
    // التحقق من وجود لوحة معلومات المسار
    let routeInfoPanel = document.getElementById('route-info-panel');
    
    if (!routeInfoPanel) {
      // إنشاء لوحة معلومات المسار
      routeInfoPanel = document.createElement('div');
      routeInfoPanel.id = 'route-info-panel';
      routeInfoPanel.className = 'route-info-panel';
      
      routeInfoPanel.innerHTML = `
        <div class="route-header">
          <h3>معلومات المسار</h3>
          <button id="close-route-info" class="close-btn">&times;</button>
        </div>
        
        <div class="route-summary">
          <!-- سيتم ملء هذا القسم ديناميكياً -->
        </div>
        
        <div class="route-steps">
          <!-- سيتم ملء هذا القسم ديناميكياً -->
        </div>
        
        <div class="route-actions">
          <button id="clear-route" class="action-btn">
            <i class="fas fa-times"></i>
            <span>مسح المسار</span>
          </button>
          <button id="share-route" class="action-btn">
            <i class="fas fa-share-alt"></i>
            <span>مشاركة المسار</span>
          </button>
        </div>
      `;
      
      // إضافة اللوحة إلى المستند
      document.body.appendChild(routeInfoPanel);
      
      // إضافة مستمعات الأحداث
      document.getElementById('close-route-info').addEventListener('click', function() {
        routeInfoPanel.style.display = 'none';
      });
      
      document.getElementById('clear-route').addEventListener('click', function() {
        clearRoute();
      });
      
      document.getElementById('share-route').addEventListener('click', function() {
        shareRoute();
      });
    }
    
    // تحديث معلومات المسار
    const summary = route.summary;
    const instructions = route.instructions;
    
    // تحديث ملخص المسار
    const routeSummaryElement = routeInfoPanel.querySelector('.route-summary');
    routeSummaryElement.innerHTML = `
      <div class="route-distance">
        <i class="fas fa-road"></i>
        <span>${formatDistance(summary.totalDistance)}</span>
      </div>
      <div class="route-duration">
        <i class="fas fa-clock"></i>
        <span>${formatDuration(summary.totalTime)}</span>
      </div>
    `;
    
    // تحديث خطوات المسار
    const routeStepsElement = routeInfoPanel.querySelector('.route-steps');
    let stepsHtml = '<h4>خطوات المسار:</h4><ol>';
    
    instructions.forEach(instruction => {
      stepsHtml += `<li>${instruction.text}</li>`;
    });
    
    stepsHtml += '</ol>';
    routeStepsElement.innerHTML = stepsHtml;
    
    // عرض لوحة معلومات المسار
    routeInfoPanel.style.display = 'block';
  }
  
  // تنسيق المسافة
  function formatDistance(distance) {
    if (distance < 1000) {
      return `${Math.round(distance)} متر`;
    } else {
      return `${(distance / 1000).toFixed(1)} كم`;
    }
  }
  
  // تنسيق المدة
  function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours} ساعة ${minutes} دقيقة`;
    } else {
      return `${minutes} دقيقة`;
    }
  }
  
  // تعيين وسيلة النقل
  function setTravelMode(mode) {
    routeOptions.travelMode = mode;
    
    // تحديث واجهة المستخدم
    document.querySelectorAll('.travel-mode-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    
    document.querySelector(`.travel-mode-btn[data-mode="${mode}"]`).classList.add('active');
    
    // إعادة حساب المسار إذا كان هناك مسار نشط
    if (currentRoute) {
      calculateRoute();
    }
  }
  
  // مسح المسار الحالي
  function clearRoute() {
    // إزالة المسار
    if (window.routingControl) {
      map.removeControl(window.routingControl);
      window.routingControl = null;
    }
    
    // إزالة علامة الوجهة
    if (window.destinationMarker) {
      map.removeLayer(window.destinationMarker);
      window.destinationMarker = null;
    }
    
    // إعادة تعيين المتغيرات
    currentRoute = null;
    destinationLocation = null;
    
    // إخفاء لوحة معلومات المسار
    const routeInfoPanel = document.getElementById('route-info-panel');
    if (routeInfoPanel) {
      routeInfoPanel.style.display = 'none';
    }
  }
  
  // مشاركة المسار
  function shareRoute() {
    if (!window.routingControl) return;
    
    const waypoints = window.routingControl.getWaypoints();
    if (waypoints.length < 2) return;
    
    const start = waypoints[0].latLng;
    const end = waypoints[1].latLng;
    
    // إنشاء رابط المشاركة
    const shareUrl = `${window.location.origin}${window.location.pathname}?route=true&startLat=${start.lat}&startLng=${start.lng}&endLat=${end.lat}&endLng=${end.lng}`;
    
    // مشاركة الرابط
    shareUrl(shareUrl, 'مسار على يمن GPS');
  }
  
  // مشاركة رابط
  function shareUrl(url, title) {
    // التحقق من دعم واجهة مشاركة الويب
    if (navigator.share) {
      navigator.share({
        title: title,
        text: `شاهد هذا على يمن GPS: ${title}`,
        url: url
      })
      .catch(error => {
        console.error('خطأ في مشاركة الرابط:', error);
        fallbackShare(url);
      });
    } else {
      fallbackShare(url);
    }
  }
  
  // مشاركة احتياطية
  function fallbackShare(url) {
    // إنشاء حقل نص مؤقت
    const textarea = document.createElement('textarea');
    textarea.value = url;
    textarea.style.position = 'fixed';
    textarea.style.opacity = 0;
    
    document.body.appendChild(textarea);
    textarea.select();
    
    try {
      // نسخ الرابط إلى الحافظة
      const successful = document.execCommand('copy');
      
      if (successful) {
        showNotification('تم نسخ رابط المشاركة إلى الحافظة', 'success');
      } else {
        showNotification('تعذر نسخ الرابط', 'error');
      }
    } catch (err) {
      showNotification('تعذر نسخ الرابط', 'error');
    }
    
    document.body.removeChild(textarea);
  }
  
  // الحصول على المسار الحالي
  function getCurrentRoute() {
    return currentRoute;
  }
  
  // تصدير الواجهة العامة
  return {
    init,
    calculateRoute,
    clearRoute,
    setUserLocation,
    setDestination,
    getCurrentRoute
  };
})();

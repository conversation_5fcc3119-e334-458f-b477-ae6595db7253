// Configuración para las llamadas a la API
const API_CONFIG = {
    // Utiliza la URL relativa para que funcione tanto local como remotamente
    baseUrl: '',
    endpoints: {
        locations: '/api/locations',
        testConnection: '/test'
    },
    // Función para obtener la URL completa de un endpoint
    getUrl: function(endpoint) {
        return this.baseUrl + this.endpoints[endpoint];
    }
};

// Para pruebas, verifica si la API está disponible
async function testApiConnection() {
    try {
        const response = await fetch(API_CONFIG.getUrl('testConnection'));
        if (response.ok) {
            console.log('✅ Conexión a la API exitosa');
            return true;
        }
        console.error('❌ Error al conectar con la API:', response.status);
        return false;
    } catch (error) {
        console.error('❌ Error de conexión:', error);
        return false;
    }
}

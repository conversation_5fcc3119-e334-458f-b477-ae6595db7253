// public/js/offline-utils.js
(function() {
  'use strict';

  // كائن عام للإعدادات
  const OfflineUtils = {
    // حالة الاتصال
    isOnline: navigator.onLine,
    syncInProgress: false,
    pendingSync: false,
    dbName: 'YemenNavOfflineDB',
    dbVersion: 1,
    db: null,
    
    // التهيئة الأولية
    init: function() {
      console.log('تهيئة أدوات وضع عدم الاتصال');
      this.setupEventListeners();
      this.initDatabase();
      this.createToastContainer();
      this.setupConnectionIndicator();
      
      // تحديث حالة الاتصال الأولية
      this.updateConnectionStatus();
      
      return this;
    },
    
    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
      window.addEventListener('online', this.handleOnlineEvent.bind(this));
      window.addEventListener('offline', this.handleOfflineEvent.bind(this));
      window.addEventListener('load', this.checkPendingSync.bind(this));
      
      // استماع لرسائل من Service Worker
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage.bind(this));
    },
    
    // معالجة حدث الاتصال بالإنترنت
    handleOnlineEvent: function() {
      console.log('تم استعادة الاتصال بالإنترنت');
      this.isOnline = true;
      this.updateConnectionStatus();
      this.showToast('تم استعادة الاتصال بالإنترنت', 'success');
      
      // بدء المزامنة إذا كانت هناك عناصر في انتظار المزامنة
      this.checkPendingSync();
    },
    
    // معالجة حدث فقدان الاتصال
    handleOfflineEvent: function() {
      console.log('انقطع الاتصال بالإنترنت');
      this.isOnline = false;
      this.updateConnectionStatus();
      this.showToast('انقطع الاتصال بالإنترنت. سيتم حفظ تغييراتك محلياً.', 'warning');
    },
    
    // معالجة رسائل من Service Worker
    handleServiceWorkerMessage: function(event) {
      console.log('رسالة من Service Worker:', event.data);
      
      if (event.data && event.data.type) {
        switch(event.data.type) {
          case 'SYNC_SUCCESS':
            this.syncInProgress = false;
            this.showToast(`تمت مزامنة ${event.data.count} من العناصر بنجاح`, 'success');
            break;
          case 'SYNC_ERROR':
            this.syncInProgress = false;
            this.pendingSync = true;
            this.showToast(`فشل في المزامنة: ${event.data.error}`, 'error');
            break;
          case 'OFFLINE_MAP_SAVED':
            this.showToast(`تم حفظ منطقة "${event.data.name}" للاستخدام دون اتصال`, 'success');
            break;
        }
      }
    },
    
    // التحقق من وجود عناصر في انتظار المزامنة
    checkPendingSync: function() {
      if (!this.isOnline || this.syncInProgress) {
        return;
      }
      
      this.getUnsyncedItems().then(items => {
        if (items && items.length > 0) {
          this.syncInProgress = true;
          this.updateConnectionStatus();
          this.showToast(`جاري مزامنة ${items.length} من العناصر...`, 'info');
          
          // طلب مزامنة من Service Worker
          navigator.serviceWorker.ready.then(registration => {
            registration.sync.register('sync-locations').then(() => {
              console.log('تم تسجيل مزامنة الخلفية');
            }).catch(err => {
              console.error('فشل في تسجيل المزامنة:', err);
              this.syncInProgress = false;
              this.updateConnectionStatus();
            });
          });
        }
      });
    },
    
    // تحديث مؤشر حالة الاتصال
    updateConnectionStatus: function() {
      const indicator = document.getElementById('connection-indicator');
      if (!indicator) return;
      
      const dot = indicator.querySelector('.connection-dot');
      const text = indicator.querySelector('.connection-text');
      
      indicator.className = 'connection-indicator';
      
      if (!this.isOnline) {
        indicator.classList.add('connection-indicator-offline');
        dot.className = 'connection-dot connection-dot-offline';
        text.textContent = 'غير متصل بالإنترنت';
      } else if (this.syncInProgress) {
        indicator.classList.add('connection-indicator-syncing');
        dot.className = 'connection-dot connection-dot-syncing';
        text.textContent = 'جاري المزامنة...';
      } else {
        indicator.classList.add('connection-indicator-online');
        dot.className = 'connection-dot connection-dot-online';
        text.textContent = 'متصل بالإنترنت';
      }
    },
    
    // إنشاء مؤشر حالة الاتصال
    setupConnectionIndicator: function() {
      if (document.getElementById('connection-indicator')) return;
      
      const indicator = document.createElement('div');
      indicator.id = 'connection-indicator';
      indicator.className = 'connection-indicator';
      
      const dot = document.createElement('span');
      dot.className = 'connection-dot';
      
      const text = document.createElement('span');
      text.className = 'connection-text';
      
      indicator.appendChild(dot);
      indicator.appendChild(text);
      
      // إضافة المؤشر إلى الموقع المناسب حسب صفحة الموقع
      const mapControls = document.querySelector('.controls, .map-controls');
      if (mapControls) {
        mapControls.appendChild(indicator);
      } else {
        // إنشاء رسالة في أعلى الصفحة
        const banner = document.createElement('div');
        banner.className = 'offline-banner';
        banner.id = 'offline-banner';
        banner.appendChild(indicator);
        
        document.body.insertAdjacentElement('afterbegin', banner);
      }
      
      this.updateConnectionStatus();
    },
    
    // تهيئة قاعدة البيانات
    initDatabase: function() {
      return new Promise((resolve, reject) => {
        if (this.db) {
          resolve(this.db);
          return;
        }
        
        const request = indexedDB.open(this.dbName, this.dbVersion);
        
        request.onerror = event => {
          console.error('فشل في فتح قاعدة البيانات المحلية:', event);
          reject(event);
        };
        
        request.onupgradeneeded = event => {
          const db = event.target.result;
          
          // إنشاء مخزن للمواقع غير المتزامنة
          if (!db.objectStoreNames.contains('offlineLocations')) {
            const locationsStore = db.createObjectStore('offlineLocations', { keyPath: 'tempId', autoIncrement: true });
            locationsStore.createIndex('synced', 'synced', { unique: false });
            locationsStore.createIndex('timestamp', 'timestamp', { unique: false });
          }
          
          // إنشاء مخزن لمناطق الخرائط المحفوظة
          if (!db.objectStoreNames.contains('mapRegions')) {
            const regionsStore = db.createObjectStore('mapRegions', { keyPath: 'id', autoIncrement: true });
            regionsStore.createIndex('name', 'name', { unique: false });
          }
          
          // إنشاء مخزن للإعدادات
          if (!db.objectStoreNames.contains('settings')) {
            db.createObjectStore('settings', { keyPath: 'id' });
          }
        };
        
        request.onsuccess = event => {
          this.db = event.target.result;
          console.log('تم فتح قاعدة البيانات المحلية بنجاح');
          resolve(this.db);
        };
      });
    },
    
    // إنشاء حاوية الإشعارات
    createToastContainer: function() {
      if (document.querySelector('.toast-container')) return;
      
      const container = document.createElement('div');
      container.className = 'toast-container';
      document.body.appendChild(container);
    },
    
    // إظهار إشعار 
    showToast: function(message, type = 'info', duration = 5000) {
      this.initDatabase().then(() => {
        const container = document.querySelector('.toast-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const header = document.createElement('div');
        header.className = 'toast-header';
        
        const title = document.createElement('div');
        title.className = 'toast-title';
        
        // تعيين عنوان الإشعار حسب النوع
        switch(type) {
          case 'success':
            title.textContent = 'تم بنجاح';
            break;
          case 'error':
            title.textContent = 'خطأ';
            break;
          case 'warning':
            title.textContent = 'تنبيه';
            break;
          default:
            title.textContent = 'معلومات';
        }
        
        const closeBtn = document.createElement('button');
        closeBtn.className = 'toast-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', () => {
          toast.remove();
        });
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        const body = document.createElement('div');
        body.className = 'toast-body';
        body.textContent = message;
        
        const progress = document.createElement('div');
        progress.className = 'toast-progress';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'toast-progress-bar';
        progress.appendChild(progressBar);
        
        toast.appendChild(header);
        toast.appendChild(body);
        toast.appendChild(progress);
        
        container.appendChild(toast);
        
        // إظهار الإشعار بعد إضافته
        setTimeout(() => {
          toast.classList.add('show');
        }, 10);
        
        // تسجيل الإشعار في قاعدة البيانات المحلية
        this.saveNotification({
          message,
          type,
          timestamp: new Date().getTime()
        });
        
        // إخفاء الإشعار بعد المدة المحددة
        if (duration > 0) {
          setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
              toast.remove();
            }, 300);
          }, duration);
        }
      });
    },
    
    // حفظ إشعار في قاعدة البيانات المحلية
    saveNotification: function(notification) {
      if (!this.db) return;
      
      const transaction = this.db.transaction(['settings'], 'readwrite');
      const store = transaction.objectStore('settings');
      
      // الحصول على سجل الإشعارات الحالي
      store.get('notifications').then(result => {
        let notifications = [];
        
        if (result && result.value) {
          notifications = result.value;
        }
        
        // إضافة الإشعار الجديد في بداية المصفوفة
        notifications.unshift(notification);
        
        // الاحتفاظ بآخر 20 إشعار فقط
        if (notifications.length > 20) {
          notifications = notifications.slice(0, 20);
        }
        
        // حفظ المصفوفة المحدثة
        store.put({
          id: 'notifications',
          value: notifications
        });
      }).catch(error => {
        console.error('فشل في حفظ الإشعار:', error);
        
        // إنشاء سجل جديد إذا لم يكن موجودًا
        store.put({
          id: 'notifications',
          value: [notification]
        });
      });
    },
    
    // الحصول على العناصر غير المتزامنة
    getUnsyncedItems: function() {
      return new Promise((resolve, reject) => {
        this.initDatabase().then(db => {
          if (!db.objectStoreNames.contains('offlineLocations')) {
            resolve([]);
            return;
          }
          
          const transaction = db.transaction(['offlineLocations'], 'readonly');
          const store = transaction.objectStore('offlineLocations');
          const index = store.index('synced');
          
          // استخدام 0 بدلاً من false لأن IDBKeyRange.only لا يقبل قيم boolean
          // في قاعدة البيانات، نستخدم 0 للإشارة إلى false و 1 للإشارة إلى true
          const range = IDBKeyRange.only(0);
          
          // استخدام طريقة بديلة للحصول على البيانات من المؤشر
          const request = index.openCursor(range);
          const items = [];
          
          request.onsuccess = function(event) {
            const cursor = event.target.result;
            if (cursor) {
              items.push(cursor.value);
              cursor.continue();
            } else {
              // انتهى المؤشر من المرور على جميع العناصر
              resolve(items);
            }
          };
          
          request.onerror = function(event) {
            console.error('فشل في الحصول على العناصر غير المتزامنة:', event.target.error);
            reject(event.target.error);
          };
        });
      });
    },
    
    // حفظ موقع في وضع عدم اتصال
    saveOfflineLocation: function(location) {
      return new Promise((resolve, reject) => {
        if (!location || !location.name || !location.lat || !location.lng) {
          reject(new Error('بيانات الموقع غير مكتملة'));
          return;
        }
        
        this.initDatabase().then(db => {
          // إضافة البيانات الوصفية للموقع
          const locationData = {
            ...location,
            timestamp: new Date().getTime(),
            synced: false,
            attemptedSync: false
          };
          
          // حفظ في قاعدة البيانات المحلية
          const transaction = db.transaction(['offlineLocations'], 'readwrite');
          const store = transaction.objectStore('offlineLocations');
          
          store.add(locationData).then(id => {
            // إظهار إشعار بنجاح الحفظ
            this.showToast('تم حفظ الموقع محليًا. ستتم المزامنة عند استعادة الاتصال.', 'success');
            
            // وضع علامة على وجود مزامنة معلقة
            this.pendingSync = true;
            
            // محاولة المزامنة إذا كان متصلاً
            if (this.isOnline) {
              this.checkPendingSync();
            }
            
            resolve({ ...locationData, tempId: id });
          }).catch(error => {
            console.error('فشل في حفظ الموقع محليًا:', error);
            this.showToast('فشل في حفظ الموقع محليًا', 'error');
            reject(error);
          });
        });
      });
    },
    
    // عرض مربع حوار التأكيد
    showConfirmDialog: function(message, callback) {
      // التحقق من وجود مربع حوار سابق وإزالته
      const existingDialog = document.getElementById('confirm-dialog');
      if (existingDialog) {
        existingDialog.remove();
      }
      
      // إنشاء مربع الحوار
      const dialog = document.createElement('div');
      dialog.id = 'confirm-dialog';
      dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s;
      `;
      
      // إنشاء محتوى مربع الحوار
      const dialogContent = document.createElement('div');
      dialogContent.style.cssText = `
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-20px);
        transition: transform 0.3s;
      `;
      
      // إنشاء رسالة مربع الحوار
      const messageElement = document.createElement('p');
      messageElement.textContent = message;
      messageElement.style.marginBottom = '20px';
      
      // إنشاء أزرار مربع الحوار
      const buttonsContainer = document.createElement('div');
      buttonsContainer.style.cssText = `
        display: flex;
        justify-content: flex-end;
        gap: 10px;
      `;
      
      // زر الإلغاء
      const cancelButton = document.createElement('button');
      cancelButton.textContent = 'إلغاء';
      cancelButton.className = 'btn btn-outline-primary';
      cancelButton.addEventListener('click', () => {
        closeDialog();
        if (callback) callback(false);
      });
      
      // زر التأكيد
      const confirmButton = document.createElement('button');
      confirmButton.textContent = 'تأكيد';
      confirmButton.className = 'btn btn-primary';
      confirmButton.addEventListener('click', () => {
        closeDialog();
        if (callback) callback(true);
      });
      
      // إضافة الأزرار إلى الحاوية
      buttonsContainer.appendChild(cancelButton);
      buttonsContainer.appendChild(confirmButton);
      
      // إضافة العناصر إلى مربع الحوار
      dialogContent.appendChild(messageElement);
      dialogContent.appendChild(buttonsContainer);
      dialog.appendChild(dialogContent);
      
      // إضافة مربع الحوار إلى المستند
      document.body.appendChild(dialog);
      
      // إظهار مربع الحوار بتأثير متحرك
      setTimeout(() => {
        dialog.style.opacity = '1';
        dialogContent.style.transform = 'translateY(0)';
      }, 10);
      
      // وظيفة إغلاق مربع الحوار
      function closeDialog() {
        dialog.style.opacity = '0';
        dialogContent.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          dialog.remove();
        }, 300);
      }
    },
    
    // طلب الإذن لتخزين البيانات محليًا
    requestStoragePermission: function() {
      return new Promise((resolve, reject) => {
        if ('storage' in navigator && 'persist' in navigator.storage) {
          navigator.storage.persist().then(isPersisted => {
            if (isPersisted) {
              console.log('تم منح إذن التخزين الدائم');
              resolve(true);
            } else {
              console.warn('لم يتم منح إذن التخزين الدائم');
              resolve(false);
            }
          });
        } else {
          // التخزين الدائم غير مدعوم
          console.warn('التخزين الدائم غير مدعوم في هذا المتصفح');
          resolve(false);
        }
      });
    },
    
    // حفظ الإعدادات
    saveSetting: function(key, value) {
      return this.initDatabase().then(db => {
        const transaction = db.transaction(['settings'], 'readwrite');
        const store = transaction.objectStore('settings');
        
        return store.put({
          id: key,
          value: value
        });
      });
    },
    
    // الحصول على الإعدادات
    getSetting: function(key) {
      return this.initDatabase().then(db => {
        const transaction = db.transaction(['settings'], 'readonly');
        const store = transaction.objectStore('settings');
        
        return store.get(key).then(result => {
          return result ? result.value : null;
        });
      });
    },
    
    // تنسيق الحجم لعرضه بشكل مقروء
    formatSize: function(bytes) {
      if (bytes < 1024) {
        return bytes + ' بايت';
      } else if (bytes < 1024 * 1024) {
        return (bytes / 1024).toFixed(1) + ' كيلوبايت';
      } else if (bytes < 1024 * 1024 * 1024) {
        return (bytes / (1024 * 1024)).toFixed(1) + ' ميجابايت';
      } else {
        return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' جيجابايت';
      }
    },
    
    // تحويل التاريخ إلى صيغة مقروءة
    formatDate: function(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };
  
  // تصدير الكائن إلى النافذة العالمية
  window.OfflineUtils = OfflineUtils;
  
  // تهيئة الأدوات عند تحميل الصفحة
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      OfflineUtils.init();
    });
  } else {
    OfflineUtils.init();
  }
  
})();

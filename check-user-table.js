// سكريبت للتحقق من هيكل جدول المستخدمين
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5433, // استخدام المنفذ الصحيح 5433
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function checkUserTable() {
  try {
    console.log('جاري التحقق من هيكل جدول المستخدمين...');
    const tableInfo = await pool.query(
      "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'users' ORDER BY ordinal_position"
    );
    
    console.log('هيكل جدول المستخدمين:');
    tableInfo.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type}`);
    });

    // التحقق من بيانات المستخدم المسؤول
    const adminInfo = await pool.query(
      "SELECT * FROM users WHERE username = 'admin'"
    );

    if (adminInfo.rows.length > 0) {
      console.log('\nبيانات المستخدم المسؤول:');
      const admin = adminInfo.rows[0];
      Object.keys(admin).forEach(key => {
        // لا تطبع كلمة المرور للحفاظ على الأمان
        if (!key.includes('password')) {
          console.log(`- ${key}: ${admin[key]}`);
        } else {
          console.log(`- ${key}: [محجوب لأسباب أمنية]`);
        }
      });
    } else {
      console.log('\nلم يتم العثور على مستخدم باسم "admin"');
    }

  } catch (error) {
    console.error('حدث خطأ أثناء التحقق من هيكل جدول المستخدمين:', error);
  } finally {
    await pool.end();
  }
}

checkUserTable();

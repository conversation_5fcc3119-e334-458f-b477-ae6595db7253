// إضافة تصحيح لتوجيه طلبات login.html إلى الملف الصحيح
app.get('/login.html', (req, res) => {
  console.log('تم استلام طلب لصفحة تسجيل الدخول');
  
  // استخدام المسار المطلق للملف
  const loginPath = path.join(__dirname, 'public', 'login.html');
  console.log('المسار المستخدم:', loginPath);
  
  // التحقق من وجود الملف
  if (fs.existsSync(loginPath)) {
    console.log('الملف موجود، جاري إرساله...');
    res.sendFile(loginPath);
  } else {
    console.error('ملف تسجيل الدخول غير موجود في المسار:', loginPath);
    res.status(404).send('صفحة تسجيل الدخول غير موجودة');
  }
});

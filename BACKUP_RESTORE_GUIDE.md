# 💾 دليل النسخ الاحتياطي والاستعادة

## 🎯 نظرة عامة

هذا الدليل يحتوي على جميع المعلومات اللازمة لاستعادة المشروع من الصفر أو المتابعة من حيث توقفنا.

## 📋 قائمة المراجعة السريعة

### ✅ الملفات الأساسية الموجودة:
- [ ] `index.html` (الخريطة الرسمية)
- [ ] `public/index.html` (نسخة الإنتاج)
- [ ] `assets/` (الأصول الأساسية)
- [ ] `public/assets/` (أصول الإنتاج)
- [ ] `simple-server.js` (الخادم)
- [ ] `scripts/` (سكريبتات التحميل)

### ✅ قاعدة البيانات:
- [ ] PostgreSQL يعمل على المنفذ 5432
- [ ] قاعدة البيانات `yemen_gps` موجودة
- [ ] المستخدم `yemen` بكلمة مرور `admin`
- [ ] الجداول موجودة مع البيانات

### ✅ الخادم:
- [ ] Node.js مثبت
- [ ] المكتبات مثبتة (`npm install`)
- [ ] الخادم يعمل على المنفذ 8000

## 🔧 خطوات الاستعادة الكاملة

### 1. إعداد قاعدة البيانات

#### أ. تثبيت PostgreSQL:
```bash
# Windows (باستخدام Chocolatey)
choco install postgresql

# أو تحميل من الموقع الرسمي
# https://www.postgresql.org/download/windows/
```

#### ب. إنشاء قاعدة البيانات:
```sql
-- الاتصال كمستخدم postgres
psql -U postgres

-- إنشاء المستخدم
CREATE USER yemen WITH PASSWORD 'admin';

-- إنشاء قاعدة البيانات
CREATE DATABASE yemen_gps OWNER yemen;

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE yemen_gps TO yemen;

-- الخروج والاتصال بقاعدة البيانات الجديدة
\q
psql -U yemen -d yemen_gps
```

#### ج. إنشاء الجداول:
```sql
-- جدول المحافظات
CREATE TABLE governorates (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- جدول الفئات
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(100) NOT NULL,
    name_en VARCHAR(100) NOT NULL,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- جدول الأماكن
CREATE TABLE places (
    id SERIAL PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description_ar TEXT,
    description_en TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    governorate_id INTEGER REFERENCES governorates(id),
    category_id INTEGER REFERENCES categories(id),
    phone VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(255),
    whatsapp VARCHAR(20),
    rating DECIMAL(2,1),
    google_place_id VARCHAR(255) UNIQUE,
    osm_id BIGINT,
    osm_type VARCHAR(20),
    photos JSONB DEFAULT '[]',
    opening_hours JSONB DEFAULT '{}',
    price_level INTEGER,
    place_types JSONB DEFAULT '[]',
    verified BOOLEAN DEFAULT FALSE,
    source VARCHAR(50) DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. إعداد الملفات

#### أ. هيكل المجلدات:
```bash
mkdir yemen-gps
cd yemen-gps
mkdir public assets scripts backup-files
mkdir public/assets public/images public/images/places
mkdir assets/css assets/js assets/images
```

#### ب. الملفات الأساسية المطلوبة:
1. **index.html** - الخريطة الرسمية
2. **assets/css/style.css** - الأنماط
3. **assets/js/app.js** - منطق التطبيق
4. **public/places.html** - صفحة الأماكن
5. **simple-server.js** - الخادم

### 3. إعداد Node.js والمكتبات

#### أ. تثبيت Node.js:
```bash
# تحميل من الموقع الرسمي
# https://nodejs.org/

# أو باستخدام Chocolatey
choco install nodejs
```

#### ب. تثبيت المكتبات:
```bash
# في المجلد الرئيسي
npm init -y
npm install express pg path

# في مجلد scripts
cd scripts
npm install axios pg
```

### 4. تشغيل النظام

#### أ. تشغيل قاعدة البيانات:
```bash
# Windows
net start postgresql-x64-14

# أو من Services.msc
```

#### ب. تحضير قاعدة البيانات:
```bash
cd scripts
node prepare-database.js
```

#### ج. تشغيل الخادم:
```bash
node simple-server.js
```

#### د. اختبار النظام:
```bash
# فتح المتصفح على:
# http://localhost:8000/
# http://localhost:8000/places
```

## 🔑 المعلومات الحساسة

### قاعدة البيانات:
```
Host: localhost
Port: 5432
Database: yemen_gps
Username: yemen
Password: admin
Superuser: postgres
Superuser Password: yemen123
```

### الخادم:
```
Port: 8000
Protocol: HTTP
Base URL: http://localhost:8000
```

### Google API (اختياري):
```
Service: Google Places API
Console: https://console.cloud.google.com/
Required APIs: Places API, Maps JavaScript API
Estimated Cost: $100-1000 depending on usage
```

## 📁 الملفات الحرجة للنسخ الاحتياطي

### 1. ملفات التطبيق:
- `index.html` (الخريطة الرسمية)
- `assets/css/style.css`
- `assets/js/app.js`
- `public/places.html`
- `simple-server.js`

### 2. ملفات قاعدة البيانات:
```bash
# إنشاء نسخة احتياطية
pg_dump -U yemen -d yemen_gps > backup_yemen_gps.sql

# استعادة النسخة الاحتياطية
psql -U yemen -d yemen_gps < backup_yemen_gps.sql
```

### 3. ملفات الإعدادات:
- `scripts/package.json`
- `scripts/config.js` (إذا تم إنشاؤه)
- جميع ملفات `scripts/`

### 4. الصور:
- `public/images/places/` (صور الأماكن)
- `assets/images/` (أصول التطبيق)

## 🚨 استكشاف الأخطاء الشائعة

### خطأ في قاعدة البيانات:
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**الحل:**
1. تأكد من تشغيل PostgreSQL
2. تحقق من إعدادات الاتصال
3. تأكد من وجود قاعدة البيانات والمستخدم

### خطأ في الخادم:
```
Error: listen EADDRINUSE :::8000
```
**الحل:**
1. أوقف العمليات على المنفذ 8000
2. غيّر المنفذ في `simple-server.js`
3. أعد تشغيل الخادم

### خطأ في الملفات:
```
Error: Cannot find module
```
**الحل:**
1. تأكد من تثبيت المكتبات: `npm install`
2. تحقق من مسارات الملفات
3. تأكد من وجود جميع الملفات المطلوبة

## 📞 معلومات الدعم

### الملفات المرجعية:
- `TODAY_PROGRESS_SUMMARY.md` - ملخص ما تم إنجازه
- `DATA_DOWNLOAD_GUIDE.md` - دليل تحميل البيانات
- `OFFICIAL_MAP_README.md` - توثيق الخريطة
- `scripts/README.md` - دليل السكريبتات

### الأوامر السريعة:
```bash
# تشغيل سريع
node simple-server.js

# تحضير قاعدة البيانات
cd scripts && node prepare-database.js

# تحميل بيانات مجانية
cd scripts && node free-data-download.js

# فحص الإحداثيات
cd scripts && node check-coordinates.js
```

## ✅ قائمة التحقق النهائية

قبل اعتبار النظام جاهز، تأكد من:

- [ ] PostgreSQL يعمل ويمكن الاتصال به
- [ ] قاعدة البيانات `yemen_gps` موجودة مع الجداول
- [ ] الخادم يعمل على `http://localhost:8000`
- [ ] الخريطة تظهر في كامل الصفحة
- [ ] صفحة الأماكن تعرض البيانات
- [ ] الاتجاهات تنتقل للخريطة المحلية
- [ ] جميع الملفات في مكانها الصحيح
- [ ] السكريبتات جاهزة للتشغيل

**إذا تم تحقيق جميع النقاط أعلاه، فالنظام جاهز للاستخدام! 🎉**

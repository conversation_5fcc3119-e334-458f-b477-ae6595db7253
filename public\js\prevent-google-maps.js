// public/js/prevent-google-maps.js
// منع فتح روابط Google Maps وتوجيهها إلى استخدام المسارات المحلية

(function() {
  'use strict';

  /**
   * اعتراض روابط Google Maps وتوجيهها إلى استخدام المسارات المحلية
   */
  function interceptGoogleMapsLinks() {
    // اعتراض جميع النقرات على الروابط
    document.addEventListener('click', function(event) {
      // التحقق مما إذا كان العنصر المنقور عليه هو رابط
      let target = event.target;
      
      // البحث عن أقرب عنصر <a> إذا تم النقر على عنصر داخل الرابط
      while (target && target.tagName !== 'A') {
        target = target.parentElement;
      }
      
      // إذا لم يكن العنصر رابطاً، لا تفعل شيئاً
      if (!target || target.tagName !== 'A') return;
      
      // الحصول على الرابط
      const href = target.getAttribute('href');
      
      // إذا لم يكن هناك رابط، لا تفعل شيئاً
      if (!href) return;
      
      // التحقق مما إذا كان الرابط يشير إلى Google Maps
      if (isGoogleMapsLink(href)) {
        // منع السلوك الافتراضي (فتح الرابط)
        event.preventDefault();
        
        // استخراج الإحداثيات من الرابط
        const coordinates = extractCoordinatesFromGoogleMapsLink(href);
        
        if (coordinates) {
          // إظهار رسالة للمستخدم
          showNotification('جاري استخدام المسارات المحلية بدلاً من Google Maps...', 'info');
          
          // استخدام المسارات المحلية
          useLocalRouting(coordinates);
        } else {
          // إذا لم يتم العثور على إحداثيات، أظهر رسالة خطأ
          showNotification('تعذر استخراج الإحداثيات من الرابط. يرجى تحديد الوجهة يدوياً.', 'error');
        }
      }
    });
    
    // اعتراض روابط الهاتف التي تحتوي على إحداثيات
    interceptPhoneLinks();
  }
  
  /**
   * التحقق مما إذا كان الرابط يشير إلى Google Maps
   * @param {String} url الرابط المراد فحصه
   * @return {Boolean} صحيح إذا كان الرابط يشير إلى Google Maps
   */
  function isGoogleMapsLink(url) {
    return url.includes('google.com/maps') || 
           url.includes('maps.google.com') || 
           url.includes('maps.app.goo.gl') ||
           url.includes('goo.gl/maps');
  }
  
  /**
   * استخراج الإحداثيات من رابط Google Maps
   * @param {String} url رابط Google Maps
   * @return {Object|null} كائن يحتوي على خط العرض وخط الطول، أو null إذا لم يتم العثور على إحداثيات
   */
  function extractCoordinatesFromGoogleMapsLink(url) {
    // محاولة استخراج الإحداثيات من الرابط
    
    // النمط 1: ?q=lat,lng
    let match = url.match(/[?&]q=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (match) {
      return {
        lat: parseFloat(match[1]),
        lng: parseFloat(match[2])
      };
    }
    
    // النمط 2: @lat,lng,
    match = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (match) {
      return {
        lat: parseFloat(match[1]),
        lng: parseFloat(match[2])
      };
    }
    
    // النمط 3: ll=lat,lng
    match = url.match(/[?&]ll=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (match) {
      return {
        lat: parseFloat(match[1]),
        lng: parseFloat(match[2])
      };
    }
    
    // النمط 4: daddr=lat,lng
    match = url.match(/[?&]daddr=(-?\d+\.\d+),(-?\d+\.\d+)/);
    if (match) {
      return {
        lat: parseFloat(match[1]),
        lng: parseFloat(match[2])
      };
    }
    
    // إذا لم يتم العثور على إحداثيات، أرجع null
    return null;
  }
  
  /**
   * استخدام المسارات المحلية بدلاً من Google Maps
   * @param {Object} coordinates كائن يحتوي على خط العرض وخط الطول
   */
  function useLocalRouting(coordinates) {
    // إضافة علامة للوجهة
    const destinationIcon = L.divIcon({
      html: '<div style="background-color: #F44336; width: 14px; height: 14px; border-radius: 50%; border: 2px solid white;"></div>',
      className: 'destination-marker',
      iconSize: [18, 18],
      iconAnchor: [9, 9]
    });
    
    // إزالة العلامة السابقة إن وجدت
    if (window.destinationMarker) {
      window.map.removeLayer(window.destinationMarker);
    }
    
    // إضافة علامة جديدة
    window.destinationMarker = L.marker([coordinates.lat, coordinates.lng], {
      icon: destinationIcon
    }).addTo(window.map);
    
    // الحصول على موقع المستخدم الحالي
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        function(position) {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          
          // إضافة علامة لموقع المستخدم الحالي إذا لم تكن موجودة
          if (!window.userLocationMarker) {
            window.userLocationMarker = L.marker([userLat, userLng], {
              icon: L.divIcon({
                html: '🚗',
                className: 'car-icon',
                iconSize: [24, 24],
                iconAnchor: [12, 12]
              })
            }).addTo(window.map);
          } else {
            // تحديث موقع العلامة
            window.userLocationMarker.setLatLng([userLat, userLng]);
          }
          
          // استخدام وحدة توجيه المسارات المحلية
          if (window.googleRouting && typeof window.googleRouting.findRoutes === 'function') {
            window.googleRouting.findRoutes(
              { lat: userLat, lng: userLng },
              { lat: coordinates.lat, lng: coordinates.lng },
              'car'
            );
            
            // إظهار زر إلغاء الوجهة
            document.getElementById('set-destination').style.display = 'none';
            document.getElementById('cancel-destination').style.display = 'block';
          } else {
            // إذا لم تكن وحدة التوجيه متاحة، استخدم Leaflet Routing Machine كبديل
            if (!window.routingControl) {
              window.routingControl = L.Routing.control({
                waypoints: [
                  L.latLng(userLat, userLng),
                  L.latLng(coordinates.lat, coordinates.lng)
                ],
                routeWhileDragging: false,
                language: 'ar',
                showAlternatives: true,
                altLineOptions: {
                  styles: [
                    {color: 'black', opacity: 0.15, weight: 9},
                    {color: 'white', opacity: 0.8, weight: 6},
                    {color: 'blue', opacity: 0.5, weight: 4}
                  ]
                },
                lineOptions: {
                  styles: [
                    {color: 'black', opacity: 0.15, weight: 9},
                    {color: 'white', opacity: 0.8, weight: 6},
                    {color: 'green', opacity: 0.5, weight: 4}
                  ]
                },
                router: L.Routing.osrmv1({
                  serviceUrl: 'https://router.project-osrm.org/route/v1',
                  profile: 'driving'
                }),
                createMarker: function() { return null; } // لا تنشئ علامات تلقائية
              }).addTo(window.map);
              
              // إظهار زر إلغاء الوجهة
              document.getElementById('set-destination').style.display = 'none';
              document.getElementById('cancel-destination').style.display = 'block';
            } else {
              // تحديث نقاط المسار
              window.routingControl.setWaypoints([
                L.latLng(userLat, userLng),
                L.latLng(coordinates.lat, coordinates.lng)
              ]);
            }
          }
          
          // تحريك الخريطة لتظهر كلا من الموقع الحالي والوجهة
          const bounds = L.latLngBounds(
            L.latLng(userLat, userLng),
            L.latLng(coordinates.lat, coordinates.lng)
          );
          window.map.fitBounds(bounds, { padding: [50, 50] });
        },
        function(error) {
          console.error('خطأ في تحديد الموقع:', error);
          showNotification('تعذر تحديد موقعك الحالي. يرجى تفعيل خدمة تحديد الموقع.', 'error');
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      );
    } else {
      showNotification('متصفحك لا يدعم تحديد الموقع الجغرافي.', 'error');
    }
  }
  
  /**
   * اعتراض روابط الهاتف التي تحتوي على إحداثيات
   */
  function interceptPhoneLinks() {
    // اعتراض روابط الهاتف التي تحتوي على إحداثيات
    document.addEventListener('click', function(event) {
      // التحقق مما إذا كان العنصر المنقور عليه هو رابط
      let target = event.target;
      
      // البحث عن أقرب عنصر <a> إذا تم النقر على عنصر داخل الرابط
      while (target && target.tagName !== 'A') {
        target = target.parentElement;
      }
      
      // إذا لم يكن العنصر رابطاً، لا تفعل شيئاً
      if (!target || target.tagName !== 'A') return;
      
      // الحصول على الرابط
      const href = target.getAttribute('href');
      
      // إذا لم يكن هناك رابط، لا تفعل شيئاً
      if (!href) return;
      
      // التحقق مما إذا كان الرابط يشير إلى رقم هاتف ويحتوي على إحداثيات
      if (href.startsWith('tel:') && href.includes('&q=')) {
        // منع السلوك الافتراضي (الاتصال)
        event.preventDefault();
        
        // استخراج رقم الهاتف والإحداثيات
        const phoneMatch = href.match(/tel:([^&]+)/);
        const coordsMatch = href.match(/&q=(-?\d+\.\d+),(-?\d+\.\d+)/);
        
        if (phoneMatch && coordsMatch) {
          const phoneNumber = phoneMatch[1];
          const coordinates = {
            lat: parseFloat(coordsMatch[1]),
            lng: parseFloat(coordsMatch[2])
          };
          
          // عرض خيارات للمستخدم
          if (confirm(`هل تريد الاتصال برقم ${phoneNumber} أم عرض الموقع على الخريطة؟\n\nانقر "موافق" للاتصال أو "إلغاء" لعرض الموقع.`)) {
            // الاتصال برقم الهاتف
            window.location.href = `tel:${phoneNumber}`;
          } else {
            // عرض الموقع على الخريطة
            useLocalRouting(coordinates);
          }
        }
      }
    });
  }
  
  // تهيئة الوظيفة عند تحميل الصفحة
  window.addEventListener('DOMContentLoaded', interceptGoogleMapsLinks);
  
})();

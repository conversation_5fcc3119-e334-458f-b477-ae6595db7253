<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>خرائط اليمن - Yemen GPS</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/yemen-maps.css">
</head>
<body>
    <!-- Search Bar -->
    <div class="search-container">
        <div class="search-box">
            <input type="text" id="search-input" class="search-input" placeholder="ابحث عن موقع أو عنوان...">
            <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
        </div>
    </div>
    
    <!-- Map Container -->
    <div id="map-container">
        <div id="map"></div>
    </div>
    
    <!-- Layer Controls -->
    <div class="layer-controls">
        <button class="layer-btn active" data-layer="streets">خريطة الشوارع</button>
        <button class="layer-btn" data-layer="satellite">الأقمار الصناعية</button>
        <button class="layer-btn" data-layer="terrain">التضاريس</button>
    </div>
    
    <!-- Map Controls -->
    <div class="map-controls">
        <button id="zoom-in-btn" class="map-control-btn" title="تكبير"><i class="fas fa-plus"></i></button>
        <button id="zoom-out-btn" class="map-control-btn" title="تصغير"><i class="fas fa-minus"></i></button>
        <button id="my-location-btn" class="map-control-btn" title="موقعي الحالي"><i class="fas fa-location-arrow"></i></button>
    </div>
    
    <!-- Loading Indicator -->
    <div id="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <div>جاري التحميل...</div>
    </div>
    
    <!-- Directions Panel -->
    <div id="directions-panel">
        <div class="directions-header">
            <h3>الاتجاهات</h3>
            <button id="close-directions-btn" class="map-control-btn" style="width: 32px; height: 32px;"><i class="fas fa-times"></i></button>
        </div>
        <div class="directions-content" id="directions-content">
            <!-- Direction steps will be inserted here dynamically -->
        </div>
    </div>
    
    <!-- نافذة معلومات المكان المفصلة (Google Maps Style) -->
    <div id="place-details-panel" class="place-details-panel">
        <div class="place-details-header">
            <div class="place-header-actions">
                <button id="close-place-details-btn" class="place-header-btn"><i class="fas fa-arrow-right"></i></button>
                <button id="share-place-btn" class="place-header-btn"><i class="fas fa-share-alt"></i></button>
                <button id="save-place-btn" class="place-header-btn"><i class="far fa-bookmark"></i></button>
            </div>
            <div class="place-search-container">
                <input type="text" id="place-search-input" placeholder="ابحث عن مكان...">
                <button id="place-search-clear"><i class="fas fa-times"></i></button>
                <button id="place-search-btn"><i class="fas fa-search"></i></button>
            </div>
        </div>
        
        <div class="place-details-content">
            <!-- صورة المكان الرئيسية -->
            <div class="place-image-container">
                <img id="place-main-image" src="" alt="صورة المكان" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                <div class="place-image-overlay"></div>
                <div class="place-image-gallery" id="place-image-gallery">
                    <!-- سيتم إضافة معرض الصور هنا ديناميكيًا -->
                </div>
            </div>
            
            <!-- معلومات المكان الأساسية -->
            <div class="place-basic-info">
                <h1 id="place-name">اسم المكان</h1>
                <div class="place-category" id="place-category"><i class="fas fa-map-marker-alt"></i> <span>موقع</span></div>
                <div class="place-rating" id="place-rating">
                    <div class="stars">★★★★☆</div>
                    <span class="rating-value">4.5</span>
                    <span class="review-count">(123 تقييم)</span>
                </div>
                <div class="place-address" id="place-address">العنوان</div>
            </div>
            
            <!-- أزرار الإجراءات السريعة -->
            <div class="place-quick-actions">
                <button id="directions-btn" class="quick-action-btn">
                    <i class="fas fa-directions"></i>
                    <span>المسار</span>
                </button>
                <button id="call-btn" class="quick-action-btn">
                    <i class="fas fa-phone"></i>
                    <span>اتصال</span>
                </button>
                <button id="website-btn" class="quick-action-btn">
                    <i class="fas fa-globe"></i>
                    <span>الموقع</span>
                </button>
                <button id="more-btn" class="quick-action-btn">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>المزيد</span>
                </button>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="place-contact-info">
                <div class="place-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="contact-item" id="contact-phone">
                        <i class="fas fa-phone"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="contact-item" id="contact-website">
                        <i class="fas fa-globe"></i>
                        <span>الموقع الإلكتروني</span>
                    </div>
                    <div class="contact-item" id="contact-address">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>العنوان</span>
                    </div>
                </div>
            </div>
            
            <!-- ساعات العمل -->
            <div class="place-opening-hours" id="place-opening-hours">
                <div class="place-info-section">
                    <h3>ساعات العمل</h3>
                    <div class="hours-list" id="hours-list">
                        <!-- سيتم إضافة ساعات العمل هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
            
            <!-- حقائق سريعة -->
            <div class="place-quick-facts">
                <div class="place-info-section">
                    <h3>حقائق سريعة</h3>
                    <p id="place-description">وصف المكان سيظهر هنا.</p>
                </div>
            </div>
            
            <!-- المراجعات والتقييمات -->
            <div class="place-reviews">
                <div class="place-info-section">
                    <h3>المراجعات</h3>
                    <div class="reviews-list" id="reviews-list">
                        <!-- سيتم إضافة المراجعات هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    
    <!-- Database Helper Script -->
    <script src="assets/js/yemen-db-helper.js"></script>
    
    <!-- Main App Script -->
    <script src="assets/js/yemen-maps.js"></script>
</body>
</html>

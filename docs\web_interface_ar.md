# واجهة الويب لنظام "يمن ناف" (Yemen Nav)

## نظرة عامة

يوفر نظام "يمن ناف" واجهة ويب متكاملة تتيح للمستخدمين الوصول إلى خرائط النظام وإدارتها عبر متصفح الإنترنت، بالإضافة إلى تطبيق الهاتف المحمول. تسمح واجهة الويب بعرض الخرائط، وإضافة نقاط وعلامات جديدة، ومزامنة البيانات مع تطبيق الهاتف المحمول.

## المميزات الرئيسية

### 1. عرض الخرائط

- **خرائط تفاعلية**: عرض خرائط اليمن بشكل تفاعلي مع إمكانية التكبير والتصغير والتحريك
- **طبقات متعددة**: عرض طبقات مختلفة من البيانات (الطرق، المعالم، النقاط المضافة)
- **أنماط مختلفة**: دعم أنماط مختلفة للخرائط (عادي، قمر صناعي، تضاريس)
- **وضع ليلي**: دعم وضع ليلي لتقليل إجهاد العين أثناء الاستخدام الليلي

### 2. إضافة وتعديل النقاط

- **إضافة نقاط جديدة**: إمكانية إضافة نقاط جديدة بالنقر على الخريطة
- **تصنيف النقاط**: تصنيف النقاط حسب النوع (ازدحام، مطب، حفرة، طريق ترابي، نقطة عسكرية)
- **إضافة تفاصيل**: إضافة وصف، صور، وتفاصيل إضافية للنقاط
- **تعديل النقاط**: تعديل النقاط الموجودة (للمستخدمين المصرح لهم)
- **حذف النقاط**: حذف النقاط (للمستخدمين المصرح لهم)

### 3. المسارات والتوجيه

- **إنشاء مسارات**: إنشاء مسارات بين نقطتين أو أكثر
- **حساب المسافة والوقت**: حساب المسافة والوقت المتوقع للمسار
- **تجنب النقاط الخطرة**: إمكانية تجنب أنواع معينة من النقاط (مثل الطرق الترابية)
- **حفظ المسارات**: حفظ المسارات المفضلة للاستخدام لاحقًا

### 4. المزامنة والتكامل

- **مزامنة مع التطبيق**: مزامنة النقاط والمسارات مع تطبيق الهاتف المحمول
- **تصدير البيانات**: تصدير البيانات بتنسيقات مختلفة (GeoJSON، KML، GPX)
- **استيراد البيانات**: استيراد بيانات من مصادر خارجية
- **مشاركة الروابط**: مشاركة روابط مباشرة لمواقع أو مسارات محددة

### 5. لوحة تحكم المسؤول

- **إدارة المستخدمين**: إضافة، تعديل، وحذف المستخدمين
- **إدارة الصلاحيات**: تحديد صلاحيات المستخدمين
- **مراجعة النقاط**: مراجعة واعتماد النقاط المضافة من المستخدمين
- **إحصائيات**: عرض إحصائيات استخدام النظام

## التقنيات المستخدمة

### 1. الواجهة الأمامية (Frontend)

- **إطار العمل**: React.js لبناء واجهة المستخدم
- **مكتبة الخرائط**: Mapbox GL JS / Leaflet لعرض الخرائط التفاعلية
- **التصميم**: Material-UI / Bootstrap للتصميم المتجاوب
- **إدارة الحالة**: Redux / Context API لإدارة حالة التطبيق

### 2. الواجهة الخلفية (Backend)

- **لغة البرمجة**: Node.js / Java Spring Boot
- **قاعدة البيانات**: PostgreSQL مع امتداد PostGIS للبيانات الجغرافية
- **API**: RESTful API / GraphQL للتواصل مع التطبيق
- **المصادقة**: JWT (JSON Web Tokens) للمصادقة وإدارة الجلسات

### 3. البنية التحتية

- **استضافة**: خوادم مستضافة في اليمن لتقليل زمن الاستجابة
- **CDN**: شبكة توصيل المحتوى لتسريع تحميل الخرائط
- **النسخ الاحتياطي**: نظام نسخ احتياطي تلقائي للبيانات
- **مراقبة**: أدوات مراقبة لضمان توفر النظام

## هيكل واجهة الويب

### 1. الصفحة الرئيسية

تعرض الصفحة الرئيسية خريطة تفاعلية لليمن مع:

- شريط بحث للعثور على المواقع
- قائمة جانبية للوصول إلى الميزات المختلفة
- أزرار للتحكم في الخريطة (تكبير، تصغير، تغيير النمط)
- طبقات يمكن تفعيلها أو إلغاؤها (مثل طبقة الازدحام، طبقة المطبات)

### 2. صفحة إضافة نقطة

تتيح هذه الصفحة للمستخدمين إضافة نقاط جديدة من خلال:

- تحديد الموقع على الخريطة
- اختيار نوع النقطة من قائمة منسدلة
- إدخال وصف للنقطة
- إضافة صور (اختياري)
- تحديد مستوى الخطورة (منخفض، متوسط، عالي)

### 3. صفحة المسارات

تتيح هذه الصفحة للمستخدمين إنشاء وحفظ المسارات:

- تحديد نقطة البداية والنهاية
- إضافة نقاط وسيطة
- اختيار خيارات التوجيه (أقصر مسار، تجنب الطرق الترابية)
- حفظ المسار ومشاركته

### 4. لوحة تحكم المستخدم

توفر لوحة تحكم المستخدم:

- عرض النقاط المضافة بواسطة المستخدم
- عرض المسارات المحفوظة
- إعدادات الحساب
- سجل النشاط

### 5. لوحة تحكم المسؤول

توفر لوحة تحكم المسؤول:

- قائمة بالنقاط المنتظرة للمراجعة
- إدارة المستخدمين والصلاحيات
- إحصائيات النظام
- إدارة البيانات الأساسية

## تجربة المستخدم

### 1. تسجيل الدخول والتسجيل

- تسجيل حساب جديد باستخدام البريد الإلكتروني أو رقم الهاتف
- تسجيل الدخول باستخدام اسم المستخدم وكلمة المرور
- إمكانية استعادة كلمة المرور المنسية
- ربط الحساب مع تطبيق الهاتف المحمول

### 2. استخدام الخريطة

- تحميل الخريطة بشكل سريع مع دعم التحميل التدريجي
- تكبير وتصغير الخريطة بسلاسة
- عرض النقاط بألوان ورموز مختلفة حسب النوع
- عرض معلومات إضافية عند النقر على نقطة

### 3. إضافة نقاط

- نقرة واحدة لبدء إضافة نقطة جديدة
- نموذج بسيط وسهل الاستخدام
- تحديد الموقع بدقة باستخدام البحث أو النقر على الخريطة
- تأكيد بصري بعد إضافة النقطة

### 4. المسارات والتوجيه

- واجهة بديهية لإنشاء المسارات
- عرض تفاصيل المسار (المسافة، الوقت المتوقع، النقاط على الطريق)
- خيارات متعددة للمسارات البديلة
- إمكانية تعديل المسار بسحب النقاط

## التكامل مع تطبيق الهاتف المحمول

### 1. مزامنة البيانات

- مزامنة النقاط المضافة بين واجهة الويب وتطبيق الهاتف المحمول
- مزامنة المسارات المحفوظة
- مزامنة إعدادات المستخدم
- مزامنة في الخلفية عند توفر اتصال الإنترنت

### 2. تكامل الحسابات

- استخدام نفس الحساب للوصول إلى واجهة الويب وتطبيق الهاتف المحمول
- إدارة الصلاحيات بشكل موحد
- تسجيل الخروج من جميع الأجهزة

### 3. مشاركة البيانات

- إنشاء رابط مباشر لمشاركة موقع أو مسار
- فتح الرابط في تطبيق الهاتف المحمول أو واجهة الويب
- تصدير البيانات بتنسيقات مختلفة

## الأمان والخصوصية

### 1. أمان البيانات

- تشفير جميع الاتصالات باستخدام HTTPS
- تشفير البيانات الحساسة في قاعدة البيانات
- حماية من هجمات حقن SQL وهجمات XSS

### 2. خصوصية المستخدم

- سياسة خصوصية واضحة
- خيارات للتحكم في مشاركة البيانات
- إمكانية حذف الحساب وجميع البيانات المرتبطة به

### 3. حماية النظام

- حماية من هجمات DDoS
- حماية من محاولات الاختراق
- مراقبة مستمرة للنشاط المشبوه

## متطلبات النظام

### 1. متطلبات المتصفح

- متصفح حديث يدعم JavaScript وHTML5
- دعم WebGL لعرض الخرائط ثلاثية الأبعاد
- اتصال إنترنت (يمكن استخدام بعض الميزات دون اتصال بعد التحميل الأولي)

### 2. متطلبات الخادم

- خادم ويب مع دعم HTTPS
- قاعدة بيانات PostgreSQL مع امتداد PostGIS
- مساحة تخزين كافية للخرائط والصور

## خطة التنفيذ

### 1. المرحلة الأولى: الميزات الأساسية

- تطوير واجهة الخريطة الأساسية
- دعم عرض النقاط الموجودة
- تنفيذ نظام المصادقة الأساسي
- ربط قاعدة البيانات مع تطبيق الهاتف المحمول

### 2. المرحلة الثانية: إضافة النقاط والمسارات

- تطوير واجهة إضافة النقاط
- تنفيذ نظام المراجعة والاعتماد
- تطوير ميزات إنشاء المسارات
- تحسين المزامنة مع تطبيق الهاتف المحمول

### 3. المرحلة الثالثة: لوحة التحكم والتحسينات

- تطوير لوحة تحكم المستخدم
- تطوير لوحة تحكم المسؤول
- تحسين الأداء والاستجابة
- إضافة ميزات متقدمة (مثل التحليلات والتقارير)

## الخلاصة

توفر واجهة الويب لنظام "يمن ناف" طريقة سهلة وفعالة للوصول إلى خرائط النظام وإدارتها عبر متصفح الإنترنت. تتكامل الواجهة بشكل كامل مع تطبيق الهاتف المحمول، مما يوفر تجربة مستخدم موحدة عبر جميع الأجهزة. تتيح الواجهة للمستخدمين إضافة نقاط وعلامات جديدة، وإنشاء مسارات، ومشاركة البيانات، مما يساهم في تحسين دقة وفائدة النظام لجميع المستخدمين.

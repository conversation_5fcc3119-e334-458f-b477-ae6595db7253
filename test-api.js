// اختبار API الأماكن
const http = require('http');

function testAPI(endpoint, description) {
    return new Promise((resolve, reject) => {
        console.log(`🔍 اختبار: ${description}`);
        console.log(`📡 الطلب: GET http://localhost:3000${endpoint}`);
        
        http.get(`http://localhost:3000${endpoint}`, (res) => {
            let data = '';
            
            res.on('data', chunk => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    console.log(`✅ النتيجة: ${result.success ? 'نجح' : 'فشل'}`);
                    if (result.data) {
                        console.log(`📊 عدد العناصر: ${result.data.length}`);
                        if (result.data.length > 0) {
                            console.log(`📝 مثال: ${JSON.stringify(result.data[0], null, 2)}`);
                        }
                    }
                    console.log('---\n');
                    resolve(result);
                } catch (error) {
                    console.log(`❌ خطأ في تحليل JSON: ${error.message}`);
                    console.log(`📄 البيانات الخام: ${data}`);
                    console.log('---\n');
                    reject(error);
                }
            });
        }).on('error', (error) => {
            console.log(`❌ خطأ في الطلب: ${error.message}`);
            console.log('---\n');
            reject(error);
        });
    });
}

async function runTests() {
    console.log('🚀 بدء اختبار APIs الأماكن...\n');
    
    try {
        // اختبار الفئات
        await testAPI('/api/places/categories', 'الحصول على فئات الأماكن');
        
        // اختبار المحافظات
        await testAPI('/api/places/governorates', 'الحصول على المحافظات');
        
        // اختبار الأماكن
        await testAPI('/api/places/places?limit=5', 'الحصول على الأماكن (5 عناصر)');
        
        console.log('🎉 انتهى الاختبار!');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
    }
}

runTests();

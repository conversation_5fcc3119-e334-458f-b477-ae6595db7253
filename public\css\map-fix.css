/**
 * تصحيحات CSS لضمان ظهور الخريطة
 * Yemen Nav - تحديث 2025
 */

/* التأكد من أن حاوية الخريطة تأخذ مساحة كاملة */
.map-container {
    flex: 1;
    position: relative;
    z-index: 1;
    height: 100vh; /* ارتفاع كامل الشاشة */
    width: 100%; /* عرض كامل */
    overflow: hidden;
}

/* التأكد من أن عنصر الخريطة يظهر بالكامل */
#map {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* تصحيح عرض حاوية التطبيق */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    position: relative;
    overflow: hidden;
}

/* التأكد من أن طبقات Leaflet تظهر بشكل صحيح */
.leaflet-container {
    height: 100%;
    width: 100%;
}

/* تأكد من عدم تداخل العناصر الأخرى مع الخريطة */
.search-container,
.map-controls,
.menu-button,
.sidebar {
    z-index: 1000;
}

/* تعديل موضع أزرار التحكم في الخريطة */
.map-controls {
    position: absolute;
    bottom: 24px;
    left: 24px;
    z-index: 999;
}

/* إصلاح العناصر المنبثقة */
.popup-container,
.notification-container {
    z-index: 9999;
}

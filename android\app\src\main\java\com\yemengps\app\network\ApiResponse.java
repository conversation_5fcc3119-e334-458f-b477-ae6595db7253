package com.yemengps.app.network;

/**
 * Generic wrapper class for API responses
 * @param <T> The type of data in the response
 */
public class ApiResponse<T> {
    
    private boolean success;
    private String message;
    private T data;
    private ErrorDetails error;
    
    /**
     * Default constructor
     */
    public ApiResponse() {
    }
    
    /**
     * Constructor for a successful response
     * @param data The data
     * @param message The message
     */
    public ApiResponse(T data, String message) {
        this.success = true;
        this.data = data;
        this.message = message;
    }
    
    /**
     * Constructor for an error response
     * @param message The error message
     * @param error The error details
     */
    public ApiResponse(String message, ErrorDetails error) {
        this.success = false;
        this.message = message;
        this.error = error;
    }
    
    /**
     * Check if the response was successful
     * @return True if the response was successful, false otherwise
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * Set whether the response was successful
     * @param success True if the response was successful, false otherwise
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    /**
     * Get the message
     * @return The message
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Set the message
     * @param message The message
     */
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * Get the data
     * @return The data
     */
    public T getData() {
        return data;
    }
    
    /**
     * Set the data
     * @param data The data
     */
    public void setData(T data) {
        this.data = data;
    }
    
    /**
     * Get the error details
     * @return The error details
     */
    public ErrorDetails getError() {
        return error;
    }
    
    /**
     * Set the error details
     * @param error The error details
     */
    public void setError(ErrorDetails error) {
        this.error = error;
    }
    
    /**
     * Class for error details
     */
    public static class ErrorDetails {
        private String code;
        private String details;
        
        /**
         * Default constructor
         */
        public ErrorDetails() {
        }
        
        /**
         * Constructor with parameters
         * @param code The error code
         * @param details The error details
         */
        public ErrorDetails(String code, String details) {
            this.code = code;
            this.details = details;
        }
        
        /**
         * Get the error code
         * @return The error code
         */
        public String getCode() {
            return code;
        }
        
        /**
         * Set the error code
         * @param code The error code
         */
        public void setCode(String code) {
            this.code = code;
        }
        
        /**
         * Get the error details
         * @return The error details
         */
        public String getDetails() {
            return details;
        }
        
        /**
         * Set the error details
         * @param details The error details
         */
        public void setDetails(String details) {
            this.details = details;
        }
    }
}

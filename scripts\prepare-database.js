// سكريبت تحضير قاعدة البيانات لتحميل البيانات
const { Pool } = require('pg');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function prepareDatabaseForDownload() {
    console.log('🔧 تحضير قاعدة البيانات لتحميل البيانات...\n');

    try {
        // إضافة الأعمدة الجديدة المطلوبة
        const alterQueries = [
            // أعمدة Google Places API
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS google_place_id VARCHAR(255) UNIQUE`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1)`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS photos JSONB DEFAULT '[]'`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS opening_hours JSONB DEFAULT '{}'`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS price_level INTEGER`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS place_types JSONB DEFAULT '[]'`,
            
            // أعمدة OpenStreetMap
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS osm_id BIGINT`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS osm_type VARCHAR(20)`,
            
            // أعمدة إضافية مفيدة
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS whatsapp VARCHAR(20)`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS verified BOOLEAN DEFAULT FALSE`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS source VARCHAR(50) DEFAULT 'manual'`,
            `ALTER TABLE places ADD COLUMN IF NOT EXISTS last_updated TIMESTAMP DEFAULT NOW()`
        ];

        console.log('📝 إضافة الأعمدة الجديدة...');
        for (const query of alterQueries) {
            try {
                await pool.query(query);
                console.log(`   ✅ ${query.split('ADD COLUMN IF NOT EXISTS')[1]?.split(' ')[0] || 'عمود'}`);
            } catch (error) {
                if (!error.message.includes('already exists')) {
                    console.error(`   ❌ خطأ: ${error.message}`);
                }
            }
        }

        // إنشاء الفهارس للأداء
        console.log('\n🔍 إنشاء الفهارس...');
        const indexQueries = [
            `CREATE INDEX IF NOT EXISTS idx_places_google_id ON places(google_place_id)`,
            `CREATE INDEX IF NOT EXISTS idx_places_osm ON places(osm_id, osm_type)`,
            `CREATE INDEX IF NOT EXISTS idx_places_location ON places(latitude, longitude)`,
            `CREATE INDEX IF NOT EXISTS idx_places_governorate ON places(governorate_id)`,
            `CREATE INDEX IF NOT EXISTS idx_places_category ON places(category_id)`,
            `CREATE INDEX IF NOT EXISTS idx_places_rating ON places(rating)`,
            `CREATE INDEX IF NOT EXISTS idx_places_source ON places(source)`,
            `CREATE INDEX IF NOT EXISTS idx_places_verified ON places(verified)`
        ];

        for (const query of indexQueries) {
            try {
                await pool.query(query);
                console.log(`   ✅ ${query.split('idx_places_')[1]?.split(' ')[0] || 'فهرس'}`);
            } catch (error) {
                console.error(`   ❌ خطأ في الفهرس: ${error.message}`);
            }
        }

        // إنشاء جدول لسجل التحميل
        console.log('\n📊 إنشاء جدول سجل التحميل...');
        const downloadLogTable = `
            CREATE TABLE IF NOT EXISTS download_log (
                id SERIAL PRIMARY KEY,
                source VARCHAR(50) NOT NULL,
                governorate VARCHAR(100),
                place_type VARCHAR(100),
                places_found INTEGER DEFAULT 0,
                places_saved INTEGER DEFAULT 0,
                photos_downloaded INTEGER DEFAULT 0,
                errors INTEGER DEFAULT 0,
                started_at TIMESTAMP DEFAULT NOW(),
                completed_at TIMESTAMP,
                status VARCHAR(20) DEFAULT 'running',
                notes TEXT
            )
        `;

        await pool.query(downloadLogTable);
        console.log('   ✅ جدول download_log');

        // إنشاء جدول للصور
        console.log('\n🖼️ إنشاء جدول الصور...');
        const photosTable = `
            CREATE TABLE IF NOT EXISTS place_photos (
                id SERIAL PRIMARY KEY,
                place_id INTEGER REFERENCES places(id) ON DELETE CASCADE,
                photo_url VARCHAR(500),
                photo_path VARCHAR(500),
                photo_reference VARCHAR(500),
                width INTEGER,
                height INTEGER,
                source VARCHAR(50) DEFAULT 'google',
                is_primary BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT NOW()
            )
        `;

        await pool.query(photosTable);
        console.log('   ✅ جدول place_photos');

        // إنشاء جدول للمراجعات
        console.log('\n⭐ إنشاء جدول المراجعات...');
        const reviewsTable = `
            CREATE TABLE IF NOT EXISTS place_reviews (
                id SERIAL PRIMARY KEY,
                place_id INTEGER REFERENCES places(id) ON DELETE CASCADE,
                author_name VARCHAR(255),
                author_url VARCHAR(500),
                language VARCHAR(10),
                profile_photo_url VARCHAR(500),
                rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                relative_time_description VARCHAR(100),
                text TEXT,
                time INTEGER,
                source VARCHAR(50) DEFAULT 'google',
                created_at TIMESTAMP DEFAULT NOW()
            )
        `;

        await pool.query(reviewsTable);
        console.log('   ✅ جدول place_reviews');

        // إضافة بيانات افتراضية للفئات إذا لم تكن موجودة
        console.log('\n📂 التحقق من الفئات...');
        const categoriesCheck = await pool.query('SELECT COUNT(*) FROM categories');
        
        if (parseInt(categoriesCheck.rows[0].count) === 0) {
            console.log('   📝 إضافة الفئات الافتراضية...');
            const defaultCategories = [
                ['سياحة', 'Tourism', 'tourist_attraction'],
                ['دينية', 'Religious', 'place_of_worship'],
                ['مطاعم', 'Restaurants', 'restaurant'],
                ['فنادق', 'Hotels', 'lodging'],
                ['صحة', 'Healthcare', 'hospital'],
                ['تعليم', 'Education', 'school'],
                ['خدمات', 'Services', 'bank'],
                ['تسوق', 'Shopping', 'shopping_mall'],
                ['مواصلات', 'Transportation', 'bus_station'],
                ['أخرى', 'Others', 'establishment']
            ];

            for (const [nameAr, nameEn, icon] of defaultCategories) {
                await pool.query(
                    'INSERT INTO categories (name_ar, name_en, icon) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING',
                    [nameAr, nameEn, icon]
                );
                console.log(`     ✅ ${nameAr}`);
            }
        } else {
            console.log('   ✅ الفئات موجودة مسبقاً');
        }

        // إضافة بيانات افتراضية للمحافظات إذا لم تكن موجودة
        console.log('\n🏛️ التحقق من المحافظات...');
        const governoratesCheck = await pool.query('SELECT COUNT(*) FROM governorates');
        
        if (parseInt(governoratesCheck.rows[0].count) === 0) {
            console.log('   📝 إضافة المحافظات الافتراضية...');
            const defaultGovernorates = [
                ['صنعاء', 'Sanaa'],
                ['عدن', 'Aden'],
                ['تعز', 'Taiz'],
                ['الحديدة', 'Hodeidah'],
                ['إب', 'Ibb'],
                ['ذمار', 'Dhamar'],
                ['المكلا', 'Mukalla'],
                ['مأرب', 'Marib'],
                ['حضرموت', 'Hadramout'],
                ['لحج', 'Lahij'],
                ['أبين', 'Abyan'],
                ['شبوة', 'Shabwah'],
                ['المهرة', 'Al Mahrah'],
                ['الجوف', 'Al Jawf'],
                ['صعدة', 'Saada'],
                ['حجة', 'Hajjah'],
                ['عمران', 'Amran'],
                ['البيضاء', 'Al Bayda'],
                ['ريمة', 'Raymah'],
                ['الضالع', 'Ad Dali']
            ];

            for (const [nameAr, nameEn] of defaultGovernorates) {
                await pool.query(
                    'INSERT INTO governorates (name_ar, name_en) VALUES ($1, $2) ON CONFLICT DO NOTHING',
                    [nameAr, nameEn]
                );
                console.log(`     ✅ ${nameAr}`);
            }
        } else {
            console.log('   ✅ المحافظات موجودة مسبقاً');
        }

        // إنشاء دالة لتنظيف البيانات المكررة
        console.log('\n🧹 إنشاء دالة تنظيف البيانات...');
        const cleanupFunction = `
            CREATE OR REPLACE FUNCTION cleanup_duplicate_places()
            RETURNS INTEGER AS $$
            DECLARE
                deleted_count INTEGER;
            BEGIN
                -- حذف الأماكن المكررة بناءً على الإحداثيات والاسم
                WITH duplicates AS (
                    SELECT id, ROW_NUMBER() OVER (
                        PARTITION BY name_ar, ROUND(latitude::numeric, 4), ROUND(longitude::numeric, 4)
                        ORDER BY created_at DESC
                    ) as rn
                    FROM places
                )
                DELETE FROM places 
                WHERE id IN (
                    SELECT id FROM duplicates WHERE rn > 1
                );
                
                GET DIAGNOSTICS deleted_count = ROW_COUNT;
                RETURN deleted_count;
            END;
            $$ LANGUAGE plpgsql;
        `;

        await pool.query(cleanupFunction);
        console.log('   ✅ دالة cleanup_duplicate_places');

        // عرض إحصائيات قاعدة البيانات
        console.log('\n📊 إحصائيات قاعدة البيانات:');
        
        const stats = await pool.query(`
            SELECT 
                (SELECT COUNT(*) FROM places) as total_places,
                (SELECT COUNT(*) FROM places WHERE photos IS NOT NULL AND photos != '[]') as places_with_photos,
                (SELECT COUNT(*) FROM categories) as total_categories,
                (SELECT COUNT(*) FROM governorates) as total_governorates
        `);

        const { total_places, places_with_photos, total_categories, total_governorates } = stats.rows[0];
        
        console.log(`   📍 إجمالي الأماكن: ${total_places}`);
        console.log(`   📷 أماكن مع صور: ${places_with_photos}`);
        console.log(`   📂 الفئات: ${total_categories}`);
        console.log(`   🏛️ المحافظات: ${total_governorates}`);

        console.log('\n✅ تم تحضير قاعدة البيانات بنجاح!');
        console.log('\n🚀 يمكنك الآن تشغيل سكريبتات التحميل:');
        console.log('   • node scripts/free-data-download.js (مجاني)');
        console.log('   • node scripts/quick-download.js (سريع)');
        console.log('   • node scripts/download-places-data.js (شامل)');

    } catch (error) {
        console.error('❌ خطأ في تحضير قاعدة البيانات:', error);
    } finally {
        await pool.end();
    }
}

// تشغيل السكريبت
if (require.main === module) {
    prepareDatabaseForDownload();
}

module.exports = prepareDatabaseForDownload;

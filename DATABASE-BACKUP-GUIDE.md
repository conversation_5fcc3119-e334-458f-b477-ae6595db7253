# دليل النسخ الاحتياطية لقاعدة بيانات Yemen GPS

## نظرة عامة

يحتوي هذا الدليل على جميع الطرق المتاحة لنسخ واستعادة قاعدة بيانات Yemen GPS مع جميع الجداول والبيانات.

## معلومات قاعدة البيانات

- **نوع قاعدة البيانات**: PostgreSQL
- **اسم قاعدة البيانات**: `yemen_gps`
- **المستخدم**: `yemen`
- **كلمة المرور**: `admin`
- **المضيف**: `localhost`
- **المنفذ**: `5432`

## الجداول الموجودة في قاعدة البيانات

1. **users** - بيانات المستخدمين
2. **locations** - المواقع الجغرافية
3. **categories** - تصنيفات المواقع
4. **clients** - بيانات العملاء
5. **advertisements** - الإعلانات
6. **events** - الأحداث والفعاليات
7. **reviews** - التقييمات والمراجعات
8. **favorites** - المواقع المفضلة
9. **routes** - المسارات
10. **geo_areas** - المناطق الجغرافية
11. **notifications** - الإشعارات
12. **sessions** - جلسات المستخدمين
13. **devices** - الأجهزة المسجلة
14. **location_images** - صور المواقع
15. **location_history** - تاريخ تغييرات المواقع
16. **system_settings** - إعدادات النظام
17. **roles** - الأدوار
18. **permissions** - الصلاحيات
19. **role_permissions** - ربط الأدوار بالصلاحيات
20. **logs** - سجلات النظام

## الطرق المتاحة للنسخ الاحتياطي

### 1. استخدام ملف Batch (Windows)

#### إنشاء نسخة احتياطية:
```bash
backup-database.bat
```

#### استعادة نسخة احتياطية:
```bash
restore-database.bat
```

### 2. استخدام JavaScript

#### إنشاء نسخة احتياطية:
```bash
# نسخة احتياطية بتاريخ تلقائي
node database-backup-manager.js backup

# نسخة احتياطية باسم مخصص
node database-backup-manager.js backup my_custom_backup.sql
```

#### استعادة نسخة احتياطية:
```bash
node database-backup-manager.js restore yemen_gps_backup_2025-05-24.sql
```

#### عرض النسخ الاحتياطية المتاحة:
```bash
node database-backup-manager.js list
```

#### التحقق من حالة قاعدة البيانات:
```bash
node database-backup-manager.js status
```

### 3. استخدام أوامر PostgreSQL المباشرة

#### إنشاء نسخة احتياطية:
```bash
# Windows
set PGPASSWORD=admin
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps -f backup_file.sql --verbose --clean --if-exists --create

# Linux/Mac
PGPASSWORD=admin pg_dump -h localhost -p 5432 -U yemen -d yemen_gps -f backup_file.sql --verbose --clean --if-exists --create
```

#### استعادة نسخة احتياطية:
```bash
# Windows
set PGPASSWORD=admin
psql -h localhost -p 5432 -U yemen -d postgres -f backup_file.sql --verbose

# Linux/Mac
PGPASSWORD=admin psql -h localhost -p 5432 -U yemen -d postgres -f backup_file.sql --verbose
```

## خيارات النسخ الاحتياطي المتقدمة

### نسخ احتياطي مضغوط:
```bash
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps -Fc -f backup_file.dump
```

### نسخ احتياطي لجدول واحد فقط:
```bash
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps -t users -f users_backup.sql
```

### نسخ احتياطي للبيانات فقط (بدون هيكل):
```bash
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps --data-only -f data_only_backup.sql
```

### نسخ احتياطي للهيكل فقط (بدون بيانات):
```bash
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps --schema-only -f schema_only_backup.sql
```

## النسخ الاحتياطي التلقائي

### إنشاء مهمة مجدولة (Windows Task Scheduler):

1. افتح Task Scheduler
2. أنشئ مهمة جديدة
3. اختر التوقيت المطلوب (يومي، أسبوعي، إلخ)
4. اختر الإجراء: تشغيل `backup-database.bat`

### إنشاء Cron Job (Linux):

```bash
# تشغيل نسخة احتياطية يومياً في الساعة 2:00 صباحاً
0 2 * * * /path/to/your/project/backup-database.sh
```

## استكشاف الأخطاء وإصلاحها

### الأخطاء الشائعة:

1. **خطأ في الاتصال بقاعدة البيانات:**
   - تأكد من تشغيل خدمة PostgreSQL
   - تحقق من بيانات الاتصال (المستخدم، كلمة المرور، المنفذ)

2. **خطأ في الصلاحيات:**
   - تأكد من أن المستخدم `yemen` له صلاحيات كافية
   - قد تحتاج لتشغيل الأمر كمدير

3. **نفاد المساحة:**
   - تحقق من المساحة المتاحة على القرص الصلب
   - احذف النسخ الاحتياطية القديمة غير المطلوبة

4. **ملف النسخة الاحتياطية تالف:**
   - تحقق من حجم الملف
   - جرب إنشاء نسخة احتياطية جديدة

### التحقق من سلامة النسخة الاحتياطية:

```bash
# التحقق من صحة ملف SQL
psql -h localhost -p 5432 -U yemen -d postgres -f backup_file.sql --dry-run

# عرض محتويات النسخة الاحتياطية
head -n 50 backup_file.sql
tail -n 50 backup_file.sql
```

## أفضل الممارسات

1. **النسخ الاحتياطي المنتظم:**
   - قم بإنشاء نسخ احتياطية يومية
   - احتفظ بنسخ احتياطية أسبوعية وشهرية

2. **التخزين الآمن:**
   - احفظ النسخ الاحتياطية في مواقع متعددة
   - استخدم التشفير للنسخ الاحتياطية الحساسة

3. **اختبار الاستعادة:**
   - اختبر عملية الاستعادة بانتظام
   - تأكد من سلامة البيانات المستعادة

4. **توثيق العمليات:**
   - احتفظ بسجل للنسخ الاحتياطية المنشأة
   - وثق أي تغييرات في هيكل قاعدة البيانات

## مثال على سكريبت نسخ احتياطي متقدم

```javascript
// استخدام مدير النسخ الاحتياطية في التطبيق
const DatabaseBackupManager = require('./database-backup-manager');

async function dailyBackup() {
    const backupManager = new DatabaseBackupManager();
    
    try {
        // إنشاء نسخة احتياطية
        const result = await backupManager.createBackup();
        console.log('تم إنشاء النسخة الاحتياطية:', result.fileName);
        
        // حذف النسخ القديمة (أكثر من 30 يوم)
        const backups = backupManager.listBackups();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        for (const backup of backups) {
            if (backup.created < thirtyDaysAgo) {
                backupManager.deleteBackup(backup.name);
                console.log('تم حذف النسخة القديمة:', backup.name);
            }
        }
        
    } catch (error) {
        console.error('خطأ في النسخ الاحتياطي:', error.message);
        // إرسال تنبيه للمدير
    }
}

// تشغيل النسخ الاحتياطي اليومي
dailyBackup();
```

## الدعم والمساعدة

إذا واجهت أي مشاكل في عملية النسخ الاحتياطي أو الاستعادة:

1. تحقق من ملفات السجل (logs)
2. راجع هذا الدليل للحلول الشائعة
3. تأكد من تحديث PostgreSQL إلى أحدث إصدار
4. استشر وثائق PostgreSQL الرسمية

---

**ملاحظة مهمة:** تأكد دائماً من اختبار عملية الاستعادة في بيئة تطوير قبل تطبيقها على بيئة الإنتاج.

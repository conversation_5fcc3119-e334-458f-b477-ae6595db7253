# موقع ملف تشغيل الخادم (run_server.bat)

## أين يوجد ملف run_server.bat؟

ملف `run_server.bat` موجود في المجلد الرئيسي للمشروع:

```
E:\yemen gps\run_server.bat
```

كما يظهر من نتيجة الأمر:

```
Directory: E:\yemen gps


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a----          5/2/2025   2:53 AM           1554 run_server.bat
```

## كيفية الوصول إلى الملف

يمكنك الوصول إلى الملف بإحدى الطرق التالية:

### الطريقة 1: من خلال مستكشف الملفات (File Explorer)

1. افتح مستكشف الملفات (اضغط على `Win + E`)
2. انتقل إلى المسار `E:\yemen gps`
3. ست<PERSON><PERSON> الملف `run_server.bat` في هذا المجلد

### الطريقة 2: من خلال سطر الأوامر

1. افتح موجه الأوامر (Command Prompt) أو PowerShell
2. انتقل إلى المجلد الرئيسي للمشروع:
   ```
   cd /d E:\yemen gps
   ```
3. يمكنك التحقق من وجود الملف باستخدام الأمر:
   ```
   dir run_server.bat
   ```

## كيفية تشغيل الملف

لتشغيل ملف `run_server.bat`:

1. **الطريقة المباشرة**: انقر نقرًا مزدوجًا على الملف `run_server.bat` في مستكشف الملفات
2. **من خلال سطر الأوامر**: انتقل إلى المجلد `E:\yemen gps` واكتب:
   ```
   run_server.bat
   ```

## ما الذي يفعله هذا الملف؟

ملف `run_server.bat` هو ملف دفعة (Batch File) يقوم بتشغيل خادم نظام "يمن ناف". يقوم هذا الملف بما يلي:

1. التحقق من تثبيت Node.js
2. تثبيت الحزم المطلوبة إذا لم تكن موجودة
3. التحقق من وجود ملف `.env` وإنشائه إذا لم يكن موجودًا
4. تشغيل الخادم على المنفذ 3000

## ملاحظات مهمة

1. **المتطلبات الأساسية**: تأكد من تثبيت Node.js قبل تشغيل هذا الملف
2. **قاعدة البيانات**: تأكد من إعداد قاعدة البيانات باستخدام ملف `create_db.bat` قبل تشغيل الخادم
3. **ملف server.js**: تأكد من وجود ملف `server.js` في نفس المجلد

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء تشغيل الملف، يمكنك الرجوع إلى دليل تشغيل النظام الشامل في `docs/running_system_ar.md`.

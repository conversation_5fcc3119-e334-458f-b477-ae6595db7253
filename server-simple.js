/**
 * خادم API بسيط لتوفير البيانات من قاعدة البيانات PostgreSQL لصفحة admin.html
 */

const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const path = require('path');

// إنشاء تطبيق Express
const app = express();
const port = 3000;

// إعداد اتصال قاعدة البيانات PostgreSQL
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// استخدام middleware
app.use(cors()); // للسماح بالطلبات من أصول مختلفة
app.use(express.json()); // لتحليل طلبات JSON
app.use(express.static(path.join(__dirname, 'public'))); // لخدمة الملفات الثابتة

// وظيفة مساعدة للتحقق من كلمة المرور
async function verifyPassword(plainPassword, hashedPassword) {
    // في الإصدار البسيط، نقوم بمقارنة مباشرة
    // في الإصدار الحقيقي، يجب استخدام bcrypt أو أي خوارزمية تشفير أخرى
    return plainPassword === hashedPassword;
}

// وظيفة مساعدة لإنشاء توكن JWT بسيط
function generateToken(user) {
    // في الإصدار البسيط، نقوم بإنشاء توكن وهمي
    // في الإصدار الحقيقي، يجب استخدام JWT
    return 'token_' + user.id + '_' + Date.now();
}

// اختبار الاتصال بقاعدة البيانات
pool.connect((err, client, done) => {
    if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err);
    } else {
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        done();
    }
});

// طريقة مساعدة للتعامل مع الاستعلامات
const query = async (text, params) => {
    try {
        const result = await pool.query(text, params);
        return result.rows;
    } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error);
        throw error;
    }
};

// ===== نقاط النهاية API =====

// الحصول على جميع المستخدمين
app.get('/api/admin/users', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع المستخدمين...');
        const users = await query(`
            SELECT
                u.id,
                u.username,
                u.full_name,
                u.email,
                u.phone,
                u.role_id,
                r.role_name,
                u.is_active,
                u.registration_date
            FROM
                users u
            LEFT JOIN
                roles r ON u.role_id = r.id
            ORDER BY
                u.id
        `);
        console.log(`تم العثور على ${users.length} مستخدم`);
        res.json(users);
    } catch (error) {
        console.error('خطأ في الحصول على المستخدمين:', error);
        res.status(500).json({ error: 'خطأ في الحصول على المستخدمين' });
    }
});

// إضافة مستخدم جديد
app.post('/api/admin/users', async (req, res) => {
    try {
        const { username, full_name, email, phone, password, role_id, is_active } = req.body;

        // التحقق من البيانات المطلوبة
        if (!username || !full_name || !email) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
        const existingUsers = await query('SELECT id FROM users WHERE username = $1', [username]);
        if (existingUsers.length > 0) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }

        // إنشاء كلمة مرور افتراضية إذا لم يتم توفيرها
        const passwordToUse = password || 'password123';

        // في الإصدار البسيط، نستخدم كلمة المرور كما هي
        // في الإصدار الحقيقي، يجب تشفير كلمة المرور باستخدام bcrypt

        // إدراج المستخدم الجديد
        const result = await query(`
            INSERT INTO users (
                username,
                full_name,
                email,
                phone,
                password,
                role_id,
                is_active,
                registration_date
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
            RETURNING id
        `, [username, full_name, email, phone, passwordToUse, role_id || 2, is_active === false ? false : true]);

        console.log(`تم إضافة المستخدم الجديد برقم ${result[0].id}`);
        res.status(201).json({
            success: true,
            message: 'تم إضافة المستخدم بنجاح',
            id: result[0].id
        });
    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error);
        res.status(500).json({ error: 'خطأ في إضافة المستخدم' });
    }
});

// تحديث مستخدم
app.put('/api/admin/users/:id', async (req, res) => {
    const userId = req.params.id;
    try {
        const { username, full_name, email, phone, password, role_id, is_active } = req.body;

        console.log(`طلب تحديث المستخدم رقم ${userId}:`, req.body);

        // التحقق من البيانات المطلوبة
        if (!username || !full_name || !email) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // معالجة خاصة للمستخدم admin (المستخدم الثابت)
        if (userId == 1 || username.toLowerCase() === 'admin') {
            console.log('تحديث بيانات المستخدم الثابت (admin)');

            // نتظاهر بأن التحديث تم بنجاح
            return res.json({
                success: true,
                message: 'تم تحديث المستخدم بنجاح',
                user: {
                    id: 1,
                    username: 'admin',
                    full_name: full_name,
                    email: email,
                    phone: phone,
                    role_id: 1,
                    is_active: true
                }
            });
        }

        // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
        const existingUsers = await query('SELECT id FROM users WHERE username = $1 AND id != $2', [username, userId]);
        if (existingUsers.length > 0) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }

        // بناء استعلام التحديث
        let updateQuery = `
            UPDATE users
            SET
                username = $1,
                full_name = $2,
                email = $3,
                phone = $4,
                role_id = $5,
                is_active = $6
        `;

        let params = [username, full_name, email, phone, role_id, is_active];

        // إضافة تحديث كلمة المرور إذا تم توفيرها
        if (password) {
            updateQuery += `, password = $${params.length + 1}`;
            params.push(password);
        }

        // إضافة شرط WHERE وتنفيذ الاستعلام
        updateQuery += ` WHERE id = $${params.length + 1}`;
        params.push(userId);

        await query(updateQuery, params);

        console.log(`تم تحديث المستخدم رقم ${userId} بنجاح`);
        res.json({ success: true, message: 'تم تحديث المستخدم بنجاح' });
    } catch (error) {
        console.error('خطأ في تحديث المستخدم:', error);
        res.status(500).json({ error: 'خطأ في تحديث المستخدم', details: error.message });
    }
});

// حذف مستخدم
app.delete('/api/admin/users/:id', async (req, res) => {
    const userId = req.params.id;
    try {
        console.log(`طلب حذف المستخدم رقم ${userId}...`);
        await query('DELETE FROM users WHERE id = $1', [userId]);
        console.log(`تم حذف المستخدم رقم ${userId} بنجاح`);
        res.json({ success: true, message: 'تم حذف المستخدم بنجاح' });
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        res.status(500).json({ error: 'خطأ في حذف المستخدم' });
    }
});

// الحصول على جميع المواقع
app.get('/api/admin/locations', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع المواقع...');
        const locations = await query(`
            SELECT
                l.id,
                l.name,
                l.description,
                l.lat,
                l.lng,
                l.category_id,
                c.name as category_name,
                l.status
            FROM
                locations l
            LEFT JOIN
                categories c ON l.category_id = c.id
            ORDER BY
                l.id
        `);
        console.log(`تم العثور على ${locations.length} موقع`);
        res.json(locations);
    } catch (error) {
        console.error('خطأ في الحصول على المواقع:', error);
        res.status(500).json({ error: 'خطأ في الحصول على المواقع' });
    }
});

// إضافة موقع جديد
app.post('/api/admin/locations', async (req, res) => {
    try {
        const { name, description, category_id, lat, lng, status } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name || !category_id || !lat || !lng) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // إدراج الموقع الجديد
        const result = await query(`
            INSERT INTO locations (
                name,
                description,
                category_id,
                lat,
                lng,
                status,
                created_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
            RETURNING id
        `, [name, description, category_id, lat, lng, status || 'active']);

        console.log(`تم إضافة الموقع الجديد برقم ${result[0].id}`);
        res.status(201).json({
            success: true,
            message: 'تم إضافة الموقع بنجاح',
            id: result[0].id
        });
    } catch (error) {
        console.error('خطأ في إضافة الموقع:', error);
        res.status(500).json({ error: 'خطأ في إضافة الموقع' });
    }
});

// تحديث موقع
app.put('/api/admin/locations/:id', async (req, res) => {
    const locationId = req.params.id;
    try {
        const { name, description, category_id, lat, lng, status } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name || !category_id || !lat || !lng) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // تحديث الموقع
        await query(`
            UPDATE locations
            SET
                name = $1,
                description = $2,
                category_id = $3,
                lat = $4,
                lng = $5,
                status = $6,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $7
        `, [name, description, category_id, lat, lng, status, locationId]);

        console.log(`تم تحديث الموقع رقم ${locationId} بنجاح`);
        res.json({ success: true, message: 'تم تحديث الموقع بنجاح' });
    } catch (error) {
        console.error('خطأ في تحديث الموقع:', error);
        res.status(500).json({ error: 'خطأ في تحديث الموقع' });
    }
});

// حذف موقع
app.delete('/api/admin/locations/:id', async (req, res) => {
    const locationId = req.params.id;
    try {
        console.log(`طلب حذف الموقع رقم ${locationId}...`);
        await query('DELETE FROM locations WHERE id = $1', [locationId]);
        console.log(`تم حذف الموقع رقم ${locationId} بنجاح`);
        res.json({ success: true, message: 'تم حذف الموقع بنجاح' });
    } catch (error) {
        console.error('خطأ في حذف الموقع:', error);
        res.status(500).json({ error: 'خطأ في حذف الموقع' });
    }
});

// الحصول على جميع التصنيفات
app.get('/api/admin/categories', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع التصنيفات...');

        // استعلام مباشر للحصول على التصنيفات
        const categories = await query(`
            SELECT
                id,
                name,
                '' as description,
                COALESCE(icon, 'fa-tag') as icon,
                '#198754' as color,
                null as parent_id
            FROM
                categories
            ORDER BY
                id
        `);

        console.log(`تم العثور على ${categories.length} تصنيف`);
        res.json(categories);
    } catch (error) {
        console.error('خطأ في الحصول على التصنيفات:', error);
        // إرجاع مصفوفة فارغة بدلاً من خطأ
        res.json([]);
    }
});

// إضافة تصنيف جديد
app.post('/api/admin/categories', async (req, res) => {
    try {
        const { name, description, icon, color, parent_id } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // إدراج التصنيف الجديد
        const result = await query(`
            INSERT INTO categories (
                name,
                description,
                icon,
                color,
                parent_id
            )
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
        `, [name, description, icon, color, parent_id]);

        console.log(`تم إضافة التصنيف الجديد برقم ${result[0].id}`);
        res.status(201).json({
            success: true,
            message: 'تم إضافة التصنيف بنجاح',
            id: result[0].id
        });
    } catch (error) {
        console.error('خطأ في إضافة التصنيف:', error);
        res.status(500).json({ error: 'خطأ في إضافة التصنيف' });
    }
});

// تحديث تصنيف
app.put('/api/admin/categories/:id', async (req, res) => {
    const categoryId = req.params.id;
    try {
        const { name, description, icon, color, parent_id } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // تحديث التصنيف
        await query(`
            UPDATE categories
            SET
                name = $1,
                description = $2,
                icon = $3,
                color = $4,
                parent_id = $5
            WHERE id = $6
        `, [name, description, icon, color, parent_id, categoryId]);

        console.log(`تم تحديث التصنيف رقم ${categoryId} بنجاح`);
        res.json({ success: true, message: 'تم تحديث التصنيف بنجاح' });
    } catch (error) {
        console.error('خطأ في تحديث التصنيف:', error);
        res.status(500).json({ error: 'خطأ في تحديث التصنيف' });
    }
});

// حذف تصنيف
app.delete('/api/admin/categories/:id', async (req, res) => {
    const categoryId = req.params.id;
    try {
        // التحقق من عدم وجود مواقع مرتبطة بهذا التصنيف
        const relatedLocations = await query('SELECT COUNT(*) as count FROM locations WHERE category_id = $1', [categoryId]);
        if (relatedLocations[0].count > 0) {
            return res.status(400).json({
                error: 'لا يمكن حذف هذا التصنيف لأنه مرتبط بمواقع',
                count: relatedLocations[0].count
            });
        }

        // التحقق من عدم وجود تصنيفات فرعية
        const relatedCategories = await query('SELECT COUNT(*) as count FROM categories WHERE parent_id = $1', [categoryId]);
        if (relatedCategories[0].count > 0) {
            return res.status(400).json({
                error: 'لا يمكن حذف هذا التصنيف لأنه يحتوي على تصنيفات فرعية',
                count: relatedCategories[0].count
            });
        }

        console.log(`طلب حذف التصنيف رقم ${categoryId}...`);
        await query('DELETE FROM categories WHERE id = $1', [categoryId]);
        console.log(`تم حذف التصنيف رقم ${categoryId} بنجاح`);
        res.json({ success: true, message: 'تم حذف التصنيف بنجاح' });
    } catch (error) {
        console.error('خطأ في حذف التصنيف:', error);
        res.status(500).json({ error: 'خطأ في حذف التصنيف' });
    }
});

// الحصول على جميع العملاء
app.get('/api/admin/clients', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع العملاء...');

        // التحقق من وجود جدول العملاء
        const tableCheck = await query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'clients'
            ) as exists
        `);

        if (!tableCheck[0].exists) {
            console.log('جدول العملاء غير موجود، إرجاع مصفوفة فارغة');
            return res.json([]);
        }

        // التحقق من أعمدة الجدول
        const columnCheck = await query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'clients'
        `);

        const columns = columnCheck.map(col => col.column_name);
        console.log('أعمدة جدول العملاء:', columns);

        // بناء استعلام ديناميكي بناءً على الأعمدة المتاحة
        let selectColumns = ['id', 'name'];

        if (columns.includes('email')) selectColumns.push('email');
        else selectColumns.push("'' as email");

        if (columns.includes('phone')) selectColumns.push('phone');
        else selectColumns.push("'' as phone");

        if (columns.includes('address')) selectColumns.push('address');
        else selectColumns.push("'' as address");

        if (columns.includes('devicesn')) selectColumns.push('devicesn as "deviceSN"');
        else selectColumns.push("'' as \"deviceSN\"");

        if (columns.includes('licensen')) selectColumns.push('licensen as "licenseN"');
        else selectColumns.push("'' as \"licenseN\"");

        if (columns.includes('status')) selectColumns.push('status');
        else selectColumns.push("'active' as status");

        const clientsQuery = `
            SELECT
                ${selectColumns.join(', ')}
            FROM
                clients
            ORDER BY
                id
        `;

        console.log('استعلام العملاء:', clientsQuery);
        const clients = await query(clientsQuery);

        console.log(`تم العثور على ${clients.length} عميل`);
        res.json(clients);
    } catch (error) {
        console.error('خطأ في الحصول على العملاء:', error);
        // إرجاع مصفوفة فارغة بدلاً من خطأ
        res.json([]);
    }
});

// إضافة عميل جديد
app.post('/api/admin/clients', async (req, res) => {
    try {
        const { name, email, phone, address, deviceSN, licenseN, status } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // إدراج العميل الجديد
        const result = await query(`
            INSERT INTO clients (
                name,
                email,
                phone,
                address,
                devicesn,
                licensen,
                status
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
        `, [name, email, phone, address, deviceSN, licenseN, status || 'active']);

        console.log(`تم إضافة العميل الجديد برقم ${result[0].id}`);
        res.status(201).json({
            success: true,
            message: 'تم إضافة العميل بنجاح',
            id: result[0].id
        });
    } catch (error) {
        console.error('خطأ في إضافة العميل:', error);
        res.status(500).json({ error: 'خطأ في إضافة العميل' });
    }
});

// تحديث عميل
app.put('/api/admin/clients/:id', async (req, res) => {
    const clientId = req.params.id;
    try {
        const { name, email, phone, address, deviceSN, licenseN, status } = req.body;

        // التحقق من البيانات المطلوبة
        if (!name) {
            return res.status(400).json({ error: 'البيانات غير مكتملة' });
        }

        // تحديث العميل
        await query(`
            UPDATE clients
            SET
                name = $1,
                email = $2,
                phone = $3,
                address = $4,
                devicesn = $5,
                licensen = $6,
                status = $7
            WHERE id = $8
        `, [name, email, phone, address, deviceSN, licenseN, status, clientId]);

        console.log(`تم تحديث العميل رقم ${clientId} بنجاح`);
        res.json({ success: true, message: 'تم تحديث العميل بنجاح' });
    } catch (error) {
        console.error('خطأ في تحديث العميل:', error);
        res.status(500).json({ error: 'خطأ في تحديث العميل' });
    }
});

// حذف عميل
app.delete('/api/admin/clients/:id', async (req, res) => {
    const clientId = req.params.id;
    try {
        console.log(`طلب حذف العميل رقم ${clientId}...`);
        await query('DELETE FROM clients WHERE id = $1', [clientId]);
        console.log(`تم حذف العميل رقم ${clientId} بنجاح`);
        res.json({ success: true, message: 'تم حذف العميل بنجاح' });
    } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        res.status(500).json({ error: 'خطأ في حذف العميل' });
    }
});

// معالجة الأخطاء العامة
app.use((err, req, res, next) => {
    console.error('خطأ عام في الخادم:', err);
    res.status(500).json({ error: 'حدث خطأ في الخادم', details: err.message });
});

// نقطة نهاية API للمصادقة
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: 'اسم المستخدم وكلمة المرور مطلوبان' });
        }

        console.log(`محاولة تسجيل دخول: ${username}, ${password}`);

        // التحقق من بيانات الاعتماد الثابتة للمسؤول
        if (username === 'admin' && password === 'yemen123') {
            console.log('تسجيل دخول ناجح باستخدام بيانات الاعتماد الثابتة للمسؤول');

            // إنشاء توكن المصادقة
            const token = 'admin_token_' + Date.now();

            // إرجاع بيانات المستخدم والتوكن
            return res.json({
                token,
                user: {
                    id: 1,
                    username: 'admin',
                    fullName: 'مدير النظام',
                    email: '<EMAIL>',
                    phone: '',
                    roleId: 1,
                    type: 'admin',
                    permissions: 'all'
                }
            });
        }

        // البحث عن المستخدم في قاعدة البيانات
        const users = await query(`
            SELECT
                id,
                username,
                password,
                full_name,
                email,
                phone,
                role_id,
                is_active,
                type,
                permissions
            FROM
                users
            WHERE
                username = $1
        `, [username]);

        // التحقق من وجود المستخدم
        if (users.length === 0) {
            console.log('المستخدم غير موجود');
            return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
        }

        const user = users[0];

        // التحقق من أن المستخدم نشط
        if (!user.is_active) {
            console.log('الحساب غير نشط');
            return res.status(401).json({ error: 'الحساب غير نشط' });
        }

        // التحقق من كلمة المرور
        const isPasswordValid = await verifyPassword(password, user.password);

        if (!isPasswordValid) {
            console.log('كلمة المرور غير صحيحة');
            return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
        }

        console.log('تسجيل دخول ناجح من قاعدة البيانات');

        // إنشاء توكن المصادقة
        const token = generateToken(user);

        // تحديث آخر تسجيل دخول
        await query(`
            UPDATE users
            SET last_login = CURRENT_TIMESTAMP
            WHERE id = $1
        `, [user.id]);

        // إرجاع بيانات المستخدم والتوكن
        res.json({
            token,
            user: {
                id: user.id,
                username: user.username,
                fullName: user.full_name,
                email: user.email,
                phone: user.phone,
                roleId: user.role_id,
                type: user.type,
                permissions: user.permissions
            }
        });
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء تسجيل الدخول' });
    }
});

// نقطة نهاية API للتحقق من التوكن
app.get('/api/verify-token', async (req, res) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];

        if (!token) {
            return res.status(401).json({ error: 'التوكن غير موجود' });
        }

        // في الإصدار البسيط، نتحقق فقط من وجود التوكن
        // في الإصدار الحقيقي، يجب التحقق من صحة التوكن باستخدام JWT

        res.json({ valid: true });
    } catch (error) {
        console.error('خطأ في التحقق من التوكن:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء التحقق من التوكن' });
    }
});

// إضافة مسار للتحقق من حالة الخادم
app.get('/status', (req, res) => {
    res.json({ status: 'running', timestamp: new Date().toISOString() });
});

// إضافة مسار للتحقق من حالة قاعدة البيانات
app.get('/db-status', async (req, res) => {
    try {
        const result = await pool.query('SELECT NOW()');
        res.json({
            status: 'connected',
            timestamp: result.rows[0].now,
            message: 'تم الاتصال بقاعدة البيانات بنجاح'
        });
    } catch (error) {
        console.error('خطأ في التحقق من حالة قاعدة البيانات:', error);
        res.status(500).json({
            status: 'error',
            message: 'فشل الاتصال بقاعدة البيانات',
            error: error.message
        });
    }
});

// بدء تشغيل الخادم
try {
    const server = app.listen(port, () => {
        console.log(`الخادم يعمل على المنفذ ${port}`);
        console.log(`يمكنك الوصول إلى لوحة التحكم على http://localhost:${port}/admin.html`);
        console.log(`للتحقق من حالة الخادم: http://localhost:${port}/status`);
        console.log(`للتحقق من حالة قاعدة البيانات: http://localhost:${port}/db-status`);
    });

    // معالجة أحداث الخادم
    server.on('error', (error) => {
        console.error('خطأ في الخادم:', error);
    });
} catch (error) {
    console.error('خطأ في بدء تشغيل الخادم:', error);
}

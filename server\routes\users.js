const express = require('express');
const router = express.Router();
const db = require('../db');

router.post('/api/users/update', async (req, res) => {
  try {
    const { userId, userData } = req.body;
    
    const result = await db.query(
      'UPDATE users SET data = $1 WHERE id = $2 RETURNING *',
      [userData, userId]
    );
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

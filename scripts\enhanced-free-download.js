// سكريبت تحميل محسن يجمع بين المصادر المجانية والمدفوعة
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// إعدادات قاعدة البيانات
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// مفتاح Google API (سيتم اختباره أولاً)
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';

// مجلد حفظ الصور
const IMAGES_DIR = path.join(__dirname, '..', 'public', 'images', 'places');

// إنشاء مجلد الصور إذا لم يكن موجوداً
if (!fs.existsSync(IMAGES_DIR)) {
    fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

// جميع المحافظات اليمنية مع إحداثياتها
const YEMEN_GOVERNORATES = [
    { name: 'صنعاء', name_en: 'Sanaa', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', name_en: 'Aden', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', name_en: 'Taiz', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', name_en: 'Hodeidah', lat: 14.7978, lng: 42.9545 },
    { name: 'إب', name_en: 'Ibb', lat: 13.9667, lng: 44.1833 },
    { name: 'ذمار', name_en: 'Dhamar', lat: 14.5426, lng: 44.4054 },
    { name: 'المكلا', name_en: 'Mukalla', lat: 14.5425, lng: 49.1242 },
    { name: 'مأرب', name_en: 'Marib', lat: 15.4694, lng: 45.3222 },
    { name: 'حضرموت', name_en: 'Hadramout', lat: 15.9500, lng: 48.2000 },
    { name: 'لحج', name_en: 'Lahij', lat: 13.0582, lng: 44.8819 },
    { name: 'أبين', name_en: 'Abyan', lat: 13.9667, lng: 45.3667 },
    { name: 'شبوة', name_en: 'Shabwah', lat: 14.5333, lng: 46.8333 },
    { name: 'المهرة', name_en: 'Al Mahrah', lat: 16.7167, lng: 52.1667 },
    { name: 'الجوف', name_en: 'Al Jawf', lat: 16.6333, lng: 45.6333 },
    { name: 'صعدة', name_en: 'Saada', lat: 16.9333, lng: 43.7667 },
    { name: 'حجة', name_en: 'Hajjah', lat: 15.6833, lng: 43.6000 },
    { name: 'عمران', name_en: 'Amran', lat: 15.6667, lng: 44.0000 },
    { name: 'البيضاء', name_en: 'Al Bayda', lat: 14.1667, lng: 45.5667 },
    { name: 'ريمة', name_en: 'Raymah', lat: 14.3833, lng: 43.5167 },
    { name: 'الضالع', name_en: 'Ad Dali', lat: 13.7000, lng: 44.7333 }
];

// أنواع الأماكن للبحث
const PLACE_TYPES = [
    'restaurant', 'lodging', 'hospital', 'school', 'university',
    'bank', 'atm', 'gas_station', 'shopping_mall', 'mosque',
    'tourist_attraction', 'museum', 'park', 'airport', 'bus_station'
];

class EnhancedDownloader {
    constructor() {
        this.downloadedCount = 0;
        this.errorCount = 0;
        this.googleApiWorking = false;
        this.startTime = Date.now();
    }

    // اختبار Google API
    async testGoogleAPI() {
        try {
            console.log('🔍 اختبار Google Places API...');
            
            const response = await axios.get('https://maps.googleapis.com/maps/api/place/nearbysearch/json', {
                params: {
                    key: GOOGLE_API_KEY,
                    location: '15.3547,44.2066',
                    radius: 1000,
                    type: 'restaurant'
                }
            });

            if (response.data.status === 'OK') {
                console.log('   ✅ Google API يعمل بشكل صحيح!');
                this.googleApiWorking = true;
                return true;
            } else {
                console.log(`   ❌ Google API لا يعمل: ${response.data.status}`);
                if (response.data.error_message) {
                    console.log(`   📝 رسالة الخطأ: ${response.data.error_message}`);
                }
                return false;
            }
        } catch (error) {
            console.log(`   ❌ خطأ في اختبار Google API: ${error.message}`);
            return false;
        }
    }

    // البحث باستخدام Google Places API
    async searchWithGoogle(governorate, type) {
        if (!this.googleApiWorking) return [];

        try {
            const response = await axios.get('https://maps.googleapis.com/maps/api/place/nearbysearch/json', {
                params: {
                    key: GOOGLE_API_KEY,
                    location: `${governorate.lat},${governorate.lng}`,
                    radius: 50000,
                    type: type,
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                console.log(`   🔍 Google: تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            }
            return [];
        } catch (error) {
            console.log(`   ❌ خطأ في Google API: ${error.message}`);
            return [];
        }
    }

    // البحث باستخدام Nominatim (مجاني)
    async searchWithNominatim(governorate, type) {
        try {
            const typeMap = {
                'restaurant': 'restaurant',
                'lodging': 'hotel',
                'hospital': 'hospital',
                'school': 'school',
                'university': 'university',
                'bank': 'bank',
                'gas_station': 'fuel',
                'shopping_mall': 'shopping',
                'mosque': 'mosque',
                'tourist_attraction': 'tourism',
                'museum': 'museum',
                'park': 'park',
                'airport': 'airport',
                'bus_station': 'bus'
            };

            const searchType = typeMap[type] || type;
            const query = `${searchType} in ${governorate.name}, Yemen`;

            const response = await axios.get('https://nominatim.openstreetmap.org/search', {
                params: {
                    q: query,
                    format: 'json',
                    countrycodes: 'ye',
                    limit: 50,
                    extratags: 1,
                    namedetails: 1,
                    addressdetails: 1
                },
                headers: {
                    'User-Agent': 'YemenGPS/1.0 (<EMAIL>)'
                }
            });

            console.log(`   🔍 Nominatim: تم العثور على ${response.data.length} مكان`);
            return response.data.map(place => ({
                name: place.display_name.split(',')[0],
                geometry: {
                    location: {
                        lat: parseFloat(place.lat),
                        lng: parseFloat(place.lon)
                    }
                },
                formatted_address: place.display_name,
                place_id: `nominatim_${place.osm_id}`,
                source: 'nominatim'
            }));
        } catch (error) {
            console.log(`   ❌ خطأ في Nominatim: ${error.message}`);
            return [];
        }
    }

    // الحصول على تفاصيل مكان من Google
    async getGooglePlaceDetails(placeId) {
        if (!this.googleApiWorking) return null;

        try {
            const response = await axios.get('https://maps.googleapis.com/maps/api/place/details/json', {
                params: {
                    key: GOOGLE_API_KEY,
                    place_id: placeId,
                    fields: 'name,formatted_address,geometry,photos,rating,reviews,formatted_phone_number,website,opening_hours',
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                return response.data.result;
            }
            return null;
        } catch (error) {
            console.log(`   ❌ خطأ في تفاصيل المكان: ${error.message}`);
            return null;
        }
    }

    // تحميل صورة من Google
    async downloadGooglePhoto(photoRef, placeId, index = 0) {
        if (!this.googleApiWorking) return null;

        try {
            const response = await axios.get('https://maps.googleapis.com/maps/api/place/photo', {
                params: {
                    key: GOOGLE_API_KEY,
                    photoreference: photoRef,
                    maxwidth: 800
                },
                responseType: 'stream'
            });

            const fileName = `${placeId.replace(/[^a-zA-Z0-9]/g, '_')}_${index}.jpg`;
            const filePath = path.join(IMAGES_DIR, fileName);
            
            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    console.log(`   📷 تم تحميل الصورة: ${fileName}`);
                    resolve(`/images/places/${fileName}`);
                });
                writer.on('error', reject);
            });
        } catch (error) {
            console.log(`   ❌ خطأ في تحميل الصورة: ${error.message}`);
            return null;
        }
    }

    // حفظ مكان في قاعدة البيانات
    async savePlace(place, governorateId, photos = []) {
        try {
            const query = `
                INSERT INTO places (
                    name_ar, name_en, description_ar, description_en,
                    latitude, longitude, governorate_id, category_id,
                    phone, website, rating, google_place_id,
                    photos, source, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()
                )
                ON CONFLICT (google_place_id) DO UPDATE SET
                    name_ar = EXCLUDED.name_ar,
                    phone = EXCLUDED.phone,
                    website = EXCLUDED.website,
                    rating = EXCLUDED.rating,
                    photos = EXCLUDED.photos,
                    updated_at = NOW()
                RETURNING id
            `;

            const values = [
                place.name || 'غير محدد',
                place.name || 'Unknown',
                place.formatted_address || '',
                place.formatted_address || '',
                place.geometry?.location?.lat || 0,
                place.geometry?.location?.lng || 0,
                governorateId,
                1, // فئة افتراضية
                place.formatted_phone_number || null,
                place.website || null,
                place.rating || null,
                place.place_id,
                JSON.stringify(photos),
                place.source || 'google'
            ];

            await pool.query(query, values);
            this.downloadedCount++;
            console.log(`   💾 تم حفظ: ${place.name}`);
            
        } catch (error) {
            this.errorCount++;
            console.error(`   ❌ خطأ في الحفظ: ${error.message}`);
        }
    }

    // الحصول على معرف المحافظة
    async getGovernorateId(governorateName) {
        try {
            const result = await pool.query(
                'SELECT id FROM governorates WHERE name_ar = $1 OR name_en = $1',
                [governorateName]
            );
            return result.rows[0]?.id || 1;
        } catch (error) {
            console.error(`خطأ في الحصول على معرف المحافظة: ${error.message}`);
            return 1;
        }
    }

    // معالجة مكان واحد
    async processPlace(place, governorateId) {
        try {
            console.log(`\n📍 معالجة: ${place.name}`);
            
            let photos = [];
            let detailedPlace = place;

            // إذا كان من Google، احصل على التفاصيل والصور
            if (place.source !== 'nominatim' && this.googleApiWorking) {
                const details = await this.getGooglePlaceDetails(place.place_id);
                if (details) {
                    detailedPlace = { ...place, ...details };
                    
                    // تحميل الصور
                    if (details.photos && details.photos.length > 0) {
                        console.log(`   📷 تحميل ${Math.min(details.photos.length, 3)} صور...`);
                        
                        for (let i = 0; i < Math.min(details.photos.length, 3); i++) {
                            const photoPath = await this.downloadGooglePhoto(
                                details.photos[i].photo_reference,
                                place.place_id,
                                i
                            );
                            if (photoPath) {
                                photos.push(photoPath);
                            }
                            await this.delay(500);
                        }
                    }
                }
            }

            await this.savePlace(detailedPlace, governorateId, photos);
            
        } catch (error) {
            console.error(`   ❌ خطأ في معالجة المكان: ${error.message}`);
            this.errorCount++;
        }
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // تشغيل التحميل الشامل
    async downloadAllPlaces() {
        console.log('🚀 بدء تحميل شامل للأماكن اليمنية...\n');
        
        // اختبار Google API أولاً
        await this.testGoogleAPI();
        
        if (this.googleApiWorking) {
            console.log('✅ سيتم استخدام Google Places API مع الصور');
        } else {
            console.log('⚠️ سيتم استخدام المصادر المجانية فقط (بدون صور)');
        }
        
        console.log('\n' + '='.repeat(50));

        try {
            for (const governorate of YEMEN_GOVERNORATES) {
                console.log(`\n🏛️ معالجة محافظة: ${governorate.name}`);
                
                const governorateId = await this.getGovernorateId(governorate.name);
                
                for (const type of PLACE_TYPES) {
                    console.log(`\n🔍 البحث عن ${type}:`);
                    
                    let places = [];
                    
                    // جرب Google API أولاً
                    if (this.googleApiWorking) {
                        const googlePlaces = await this.searchWithGoogle(governorate, type);
                        places = places.concat(googlePlaces);
                        await this.delay(1000);
                    }
                    
                    // أضف نتائج Nominatim
                    const nominatimPlaces = await this.searchWithNominatim(governorate, type);
                    places = places.concat(nominatimPlaces);
                    
                    // معالجة جميع الأماكن
                    for (const place of places) {
                        await this.processPlace(place, governorateId);
                        await this.delay(1000);
                    }
                    
                    await this.delay(2000);
                }
                
                await this.delay(3000);
            }
            
            this.printSummary();
            
        } catch (error) {
            console.error('❌ خطأ عام في التحميل:', error);
        } finally {
            await pool.end();
        }
    }

    // طباعة ملخص العملية
    printSummary() {
        const duration = Math.round((Date.now() - this.startTime) / 1000);
        
        console.log('\n' + '='.repeat(50));
        console.log('📊 ملخص عملية التحميل الشاملة');
        console.log('='.repeat(50));
        console.log(`⏱️  الوقت المستغرق: ${duration} ثانية`);
        console.log(`✅ تم تحميلها بنجاح: ${this.downloadedCount}`);
        console.log(`❌ فشل في التحميل: ${this.errorCount}`);
        console.log(`🌐 المصادر: ${this.googleApiWorking ? 'Google + Nominatim' : 'Nominatim فقط'}`);
        console.log(`📷 مجلد الصور: ${IMAGES_DIR}`);
        console.log(`💾 قاعدة البيانات: yemen_gps`);
        console.log('='.repeat(50));
    }
}

// تشغيل السكريبت
if (require.main === module) {
    const downloader = new EnhancedDownloader();
    downloader.downloadAllPlaces();
}

module.exports = EnhancedDownloader;

// نواة نظام الخرائط المستقل
class OfflineMapsCore {
    constructor() {
        this.map = null;
        this.view = null;
        this.layers = {
            street: null,
            satellite: null,
            terrain: null
        };
        this.currentLayer = 'street';
        this.markers = [];
        this.currentLocation = null;
        this.isFullscreen = false;
        
        // إعدادات افتراضية للخريطة (صنعاء، اليمن)
        this.defaultCenter = [44.2066, 15.3547]; // [longitude, latitude]
        this.defaultZoom = 12;
        
        this.init();
    }

    // تهيئة الخريطة
    init() {
        this.createView();
        this.createLayers();
        this.createMap();
        this.setupEventListeners();
        this.hideLoadingScreen();
    }

    // إنشاء العرض (View)
    createView() {
        this.view = new ol.View({
            center: ol.proj.fromLonLat(this.defaultCenter),
            zoom: this.defaultZoom,
            minZoom: 3,
            maxZoom: 20
        });
    }

    // إنشاء طبقات الخريطة
    createLayers() {
        // طبقة خرائط الشوارع (OpenStreetMap)
        this.layers.street = new ol.layer.Tile({
            source: new ol.source.OSM({
                url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                attributions: '© OpenStreetMap contributors'
            }),
            visible: true
        });

        // طبقة صور الأقمار الصناعية (Bing Maps)
        this.layers.satellite = new ol.layer.Tile({
            source: new ol.source.BingMaps({
                key: 'AqTGBsziZHIJYYxgivLBf0hVdrAk9mWO5cQcb8Yux8sW5M8c8opEC2lZqKR1ZZXf',
                imagerySet: 'Aerial',
                maxZoom: 19
            }),
            visible: false
        });

        // طبقة خرائط التضاريس
        this.layers.terrain = new ol.layer.Tile({
            source: new ol.source.XYZ({
                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}',
                attributions: '© Esri'
            }),
            visible: false
        });
    }

    // إنشاء الخريطة
    createMap() {
        this.map = new ol.Map({
            target: 'map',
            layers: [
                this.layers.street,
                this.layers.satellite,
                this.layers.terrain
            ],
            view: this.view,
            controls: ol.control.defaults({
                zoom: false,
                attribution: false,
                rotate: false
            })
        });

        // إضافة تحكم الإسناد
        this.map.addControl(new ol.control.Attribution({
            collapsible: true,
            collapsed: true
        }));
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // أزرار التكبير والتصغير
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.zoomIn();
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.zoomOut();
        });

        // زر الموقع الحالي
        document.getElementById('myLocationBtn').addEventListener('click', () => {
            this.goToCurrentLocation();
        });

        // زر طبقات الخريطة
        document.getElementById('layersBtn').addEventListener('click', () => {
            this.toggleLayersPanel();
        });

        // زر ملء الشاشة
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // زر الاتجاهات
        document.getElementById('directionsBtn').addEventListener('click', () => {
            this.toggleDirectionsPanel();
        });

        // إغلاق اللوحات
        document.getElementById('closeLayersPanel').addEventListener('click', () => {
            this.hideLayersPanel();
        });

        document.getElementById('closeDirectionsPanel').addEventListener('click', () => {
            this.hideDirectionsPanel();
        });

        document.getElementById('closePointPopup').addEventListener('click', () => {
            this.hidePointPopup();
        });

        // تغيير طبقات الخريطة
        document.querySelectorAll('input[name="mapLayer"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.switchLayer(e.target.value);
            });
        });

        // النقر على الخريطة
        this.map.on('click', (event) => {
            this.handleMapClick(event);
        });

        // تحريك الخريطة
        this.map.on('moveend', () => {
            this.onMapMove();
        });

        // تغيير مستوى التكبير
        this.view.on('change:resolution', () => {
            this.onZoomChange();
        });

        // أحداث لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            this.handleKeyPress(e);
        });

        // تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    // إخفاء شاشة التحميل
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 300);
        }, 1500);
    }

    // تكبير الخريطة
    zoomIn() {
        const currentZoom = this.view.getZoom();
        this.view.animate({
            zoom: currentZoom + 1,
            duration: 250
        });
    }

    // تصغير الخريطة
    zoomOut() {
        const currentZoom = this.view.getZoom();
        this.view.animate({
            zoom: currentZoom - 1,
            duration: 250
        });
    }

    // الانتقال للموقع الحالي
    goToCurrentLocation() {
        if (navigator.geolocation) {
            const locationBtn = document.getElementById('myLocationBtn');
            locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const coords = [position.coords.longitude, position.coords.latitude];
                    this.currentLocation = coords;
                    
                    this.view.animate({
                        center: ol.proj.fromLonLat(coords),
                        zoom: 16,
                        duration: 1000
                    });

                    // إضافة علامة للموقع الحالي
                    this.addCurrentLocationMarker(coords);
                    
                    locationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    this.showToast('تم تحديد موقعك الحالي', 'success');
                },
                (error) => {
                    locationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    this.showToast('لا يمكن تحديد موقعك الحالي', 'error');
                    console.error('خطأ في تحديد الموقع:', error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        } else {
            this.showToast('المتصفح لا يدعم تحديد الموقع', 'error');
        }
    }

    // إضافة علامة للموقع الحالي
    addCurrentLocationMarker(coords) {
        // إزالة العلامة السابقة إن وجدت
        this.removeCurrentLocationMarker();

        // إنشاء علامة جديدة
        const marker = new ol.Feature({
            geometry: new ol.geom.Point(ol.proj.fromLonLat(coords)),
            type: 'current-location'
        });

        const markerStyle = new ol.style.Style({
            image: new ol.style.Circle({
                radius: 8,
                fill: new ol.style.Fill({ color: '#1a73e8' }),
                stroke: new ol.style.Stroke({ color: '#ffffff', width: 3 })
            })
        });

        marker.setStyle(markerStyle);

        // إضافة العلامة للخريطة
        const vectorSource = new ol.source.Vector({
            features: [marker]
        });

        const vectorLayer = new ol.layer.Vector({
            source: vectorSource
        });

        this.map.addLayer(vectorLayer);
        this.currentLocationLayer = vectorLayer;
    }

    // إزالة علامة الموقع الحالي
    removeCurrentLocationMarker() {
        if (this.currentLocationLayer) {
            this.map.removeLayer(this.currentLocationLayer);
            this.currentLocationLayer = null;
        }
    }

    // تبديل لوحة الطبقات
    toggleLayersPanel() {
        const panel = document.getElementById('layersPanel');
        panel.classList.toggle('hidden');
    }

    // إخفاء لوحة الطبقات
    hideLayersPanel() {
        document.getElementById('layersPanel').classList.add('hidden');
    }

    // تبديل لوحة الاتجاهات
    toggleDirectionsPanel() {
        const panel = document.getElementById('directionsPanel');
        panel.classList.toggle('hidden');
    }

    // إخفاء لوحة الاتجاهات
    hideDirectionsPanel() {
        document.getElementById('directionsPanel').classList.add('hidden');
    }

    // إخفاء نافذة معلومات النقطة
    hidePointPopup() {
        document.getElementById('pointInfoPopup').classList.add('hidden');
    }

    // تبديل ملء الشاشة
    toggleFullscreen() {
        if (!this.isFullscreen) {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    }

    // تبديل طبقة الخريطة
    switchLayer(layerType) {
        // إخفاء جميع الطبقات
        Object.values(this.layers).forEach(layer => {
            layer.setVisible(false);
        });

        // إظهار الطبقة المحددة
        if (this.layers[layerType]) {
            this.layers[layerType].setVisible(true);
            this.currentLayer = layerType;
        }
    }

    // معالجة النقر على الخريطة
    handleMapClick(event) {
        const coordinate = event.coordinate;
        const lonLat = ol.proj.toLonLat(coordinate);
        
        // البحث عن معالم في النقطة المنقورة
        const features = this.map.getFeaturesAtPixel(event.pixel);
        
        if (features && features.length > 0) {
            const feature = features[0];
            if (feature.get('type') !== 'current-location') {
                this.showPointInfo(feature, event.pixel);
            }
        } else {
            // إضافة نقطة جديدة أو إخفاء النوافذ المفتوحة
            this.hidePointPopup();
        }
    }

    // عرض معلومات النقطة
    showPointInfo(feature, pixel) {
        const popup = document.getElementById('pointInfoPopup');
        const properties = feature.getProperties();
        
        // تحديث محتوى النافذة
        document.getElementById('pointTitle').textContent = properties.name || 'مكان غير معروف';
        
        // تحديد موقع النافذة
        const mapSize = this.map.getSize();
        const left = pixel[0] + 10;
        const top = pixel[1] - 10;
        
        popup.style.left = left + 'px';
        popup.style.top = top + 'px';
        popup.classList.remove('hidden');
    }

    // معالجة تحريك الخريطة
    onMapMove() {
        // يمكن إضافة منطق لتحديث البيانات المرئية
    }

    // معالجة تغيير مستوى التكبير
    onZoomChange() {
        const zoom = this.view.getZoom();
        // يمكن إضافة منطق لإظهار/إخفاء طبقات حسب مستوى التكبير
    }

    // معالجة ضغط المفاتيح
    handleKeyPress(event) {
        switch(event.key) {
            case 'Escape':
                this.hideAllPanels();
                break;
            case '+':
                this.zoomIn();
                break;
            case '-':
                this.zoomOut();
                break;
        }
    }

    // معالجة تغيير حجم النافذة
    handleResize() {
        if (this.map) {
            this.map.updateSize();
        }
    }

    // إخفاء جميع اللوحات
    hideAllPanels() {
        this.hideLayersPanel();
        this.hideDirectionsPanel();
        this.hidePointPopup();
    }

    // عرض رسالة تنبيه
    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = type === 'success' ? 'fa-check-circle' : 
                    type === 'error' ? 'fa-exclamation-circle' : 
                    type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';
        
        toast.innerHTML = `
            <i class="fas ${icon} toast-icon"></i>
            <span class="toast-text">${message}</span>
        `;
        
        container.appendChild(toast);
        
        // إزالة الرسالة بعد 3 ثوان
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                container.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // الحصول على الخريطة
    getMap() {
        return this.map;
    }

    // الحصول على العرض
    getView() {
        return this.view;
    }

    // الحصول على الطبقة الحالية
    getCurrentLayer() {
        return this.currentLayer;
    }
}

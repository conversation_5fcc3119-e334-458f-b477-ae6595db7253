// سكريبت التحميل المتوازي للجهاز الثاني
// يحمل النصف الثاني من الأنواع والمناطق

const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class ParallelDownloadDevice2 {
    constructor() {
        this.apiKey = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';
        this.processedPlaces = new Set();
        this.totalPlaces = 0;
        this.downloadedImages = 0;
        
        // إعداد قاعدة البيانات المحلية للجهاز الثاني
        this.pool = new Pool({
            user: 'yemen',
            host: 'localhost',
            database: 'yemen_gps_device2', // قاعدة بيانات منفصلة
            password: 'admin',
            port: 5432,
        });

        // المناطق المخصصة للجهاز الثاني (النصف الثاني)
        this.areas = [
            { name: 'معين', center: { lat: 15.3547, lng: 44.2066 }, radius: 5000 },
            { name: 'بني الحارث', center: { lat: 15.4200, lng: 44.1800 }, radius: 8000 },
            { name: 'همدان', center: { lat: 15.3200, lng: 44.1500 }, radius: 6000 },
            { name: 'أزال', center: { lat: 15.3800, lng: 44.1600 }, radius: 7000 },
            { name: 'الثورة', center: { lat: 15.3600, lng: 44.2200 }, radius: 4000 },
            { name: 'سنحان', center: { lat: 15.2800, lng: 44.3000 }, radius: 10000 }
        ];

        // أنواع الأماكن المخصصة للجهاز الثاني (النصف الثاني)
        this.placeTypes = [
            'bank', 'atm', 'mosque', 'church', 'shopping_mall', 'store',
            'gas_station', 'car_repair', 'pharmacy', 'beauty_salon',
            'gym', 'park', 'tourist_attraction', 'museum', 'library',
            'post_office', 'police', 'fire_station', 'government_office',
            'courthouse', 'embassy', 'city_hall', 'local_government_office',
            'accounting', 'lawyer', 'insurance_agency', 'real_estate_agency',
            'travel_agency', 'moving_company', 'storage', 'laundry',
            'hair_care', 'spa', 'dentist', 'veterinary_care', 'pet_store',
            'florist', 'funeral_home', 'cemetery', 'place_of_worship',
            'hindu_temple', 'synagogue', 'electronics_store', 'furniture_store',
            'home_goods_store', 'clothing_store', 'shoe_store', 'jewelry_store',
            'book_store', 'bicycle_store', 'car_dealer', 'car_rental',
            'taxi_stand', 'bus_station', 'train_station', 'airport',
            'subway_station', 'light_rail_station', 'transit_station'
        ];

        this.init();
    }

    async init() {
        console.log('🚀 بدء التحميل المتوازي للجهاز الثاني...');
        console.log(`📍 المناطق المخصصة: ${this.areas.length} منطقة`);
        console.log(`🏷️ أنواع الأماكن: ${this.placeTypes.length} نوع`);
        console.log(`🔍 إجمالي عمليات البحث: ${this.areas.length * this.placeTypes.length}`);
        console.log('============================================================\n');

        // إنشاء قاعدة البيانات والجداول
        await this.setupDatabase();

        // بدء التحميل
        await this.startDownload();
    }

    async setupDatabase() {
        try {
            // إنشاء جدول الأماكن
            await this.pool.query(`
                CREATE TABLE IF NOT EXISTS places (
                    id SERIAL PRIMARY KEY,
                    google_place_id VARCHAR(255) UNIQUE NOT NULL,
                    name_ar VARCHAR(500),
                    name_en VARCHAR(500),
                    description_ar TEXT,
                    description_en TEXT,
                    latitude DECIMAL(10, 8),
                    longitude DECIMAL(11, 8),
                    address_ar TEXT,
                    address_en TEXT,
                    phone VARCHAR(50),
                    website VARCHAR(500),
                    rating DECIMAL(3, 2),
                    user_ratings_total INTEGER,
                    price_level INTEGER,
                    category_id INTEGER,
                    area_name VARCHAR(100),
                    place_type VARCHAR(100),
                    photos JSON,
                    opening_hours JSON,
                    reviews JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            `);

            console.log('✅ تم إعداد قاعدة البيانات للجهاز الثاني');
        } catch (error) {
            console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
        }
    }

    async startDownload() {
        for (const placeType of this.placeTypes) {
            console.log(`\n🏷️ البحث عن: ${placeType}`);
            
            for (const area of this.areas) {
                try {
                    console.log(`🔍 البحث المكثف عن ${placeType} في ${area.name}...`);
                    
                    const places = await this.searchPlaces(placeType, area);
                    console.log(`   ✅ تم العثور على ${places.length} مكان`);

                    for (const place of places) {
                        await this.processPlace(place, area.name, placeType);
                        await this.delay(200); // تأخير لتجنب تجاوز حدود API
                    }

                } catch (error) {
                    console.log(`   ❌ خطأ في البحث عن ${placeType} في ${area.name}: ${error.message}`);
                }
            }
        }

        console.log('\n🎉 اكتمل التحميل للجهاز الثاني!');
        console.log(`📊 إجمالي الأماكن: ${this.totalPlaces}`);
        console.log(`📷 إجمالي الصور: ${this.downloadedImages}`);
    }

    async searchPlaces(type, area) {
        const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
        
        const params = {
            location: `${area.center.lat},${area.center.lng}`,
            radius: area.radius,
            type: type,
            key: this.apiKey,
            language: 'ar'
        };

        try {
            const response = await axios.get(url, { params });
            return response.data.results || [];
        } catch (error) {
            throw new Error(`فشل في البحث: ${error.message}`);
        }
    }

    async processPlace(place, areaName, placeType) {
        if (this.processedPlaces.has(place.place_id)) {
            return;
        }
        this.processedPlaces.add(place.place_id);

        console.log(`\n📍 معالجة: ${place.name}`);

        try {
            // تحميل الصور
            const photos = await this.downloadPlacePhotos(place);
            
            // حفظ في قاعدة البيانات
            await this.savePlace(place, areaName, placeType, photos);
            
            this.totalPlaces++;
            console.log(`   💾 تم حفظ: ${place.name}`);

        } catch (error) {
            console.log(`   ❌ خطأ في معالجة ${place.name}: ${error.message}`);
        }
    }

    async downloadPlacePhotos(place) {
        if (!place.photos || place.photos.length === 0) {
            return [];
        }

        console.log(`   📷 تحميل ${Math.min(place.photos.length, 5)} صور...`);
        const downloadedPhotos = [];

        for (let i = 0; i < Math.min(place.photos.length, 5); i++) {
            try {
                const photo = place.photos[i];
                const photoUrl = `https://maps.googleapis.com/maps/api/place/photo?maxwidth=1200&photoreference=${photo.photo_reference}&key=${this.apiKey}`;
                
                const fileName = `${place.place_id.replace(/[^a-zA-Z0-9]/g, '_')}_${i}.jpg`;
                const filePath = path.join('public', 'images', 'places', fileName);
                
                // إنشاء المجلد إذا لم يكن موجوداً
                const dir = path.dirname(filePath);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }

                const response = await axios({
                    method: 'GET',
                    url: photoUrl,
                    responseType: 'stream'
                });

                const writer = fs.createWriteStream(filePath);
                response.data.pipe(writer);

                await new Promise((resolve, reject) => {
                    writer.on('finish', resolve);
                    writer.on('error', reject);
                });

                downloadedPhotos.push(`/images/places/${fileName}`);
                this.downloadedImages++;
                console.log(`   📷 صورة ${i + 1}: ${fileName}`);

            } catch (error) {
                console.log(`   ⚠️ فشل تحميل الصورة ${i + 1}: ${error.message}`);
            }
        }

        return downloadedPhotos;
    }

    async savePlace(place, areaName, placeType, photos) {
        const query = `
            INSERT INTO places (
                google_place_id, name_ar, name_en, latitude, longitude,
                address_ar, phone, rating, user_ratings_total, price_level,
                category_id, area_name, place_type, photos
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            ON CONFLICT (google_place_id) DO UPDATE SET
                name_ar = EXCLUDED.name_ar,
                photos = EXCLUDED.photos,
                updated_at = NOW()
        `;

        const values = [
            place.place_id,
            place.name,
            place.name,
            place.geometry?.location?.lat,
            place.geometry?.location?.lng,
            place.vicinity,
            null, // phone - يحتاج Place Details API
            place.rating,
            place.user_ratings_total,
            place.price_level,
            this.getCategoryId(placeType),
            areaName,
            placeType,
            JSON.stringify(photos)
        ];

        await this.pool.query(query, values);
    }

    getCategoryId(placeType) {
        const categoryMap = {
            'bank': 7, 'atm': 7, 'mosque': 2, 'church': 2,
            'shopping_mall': 8, 'store': 8, 'gas_station': 9,
            'car_repair': 7, 'pharmacy': 5, 'beauty_salon': 7,
            'gym': 7, 'park': 1, 'tourist_attraction': 1,
            'museum': 1, 'library': 6, 'post_office': 7,
            'police': 7, 'fire_station': 7, 'government_office': 7
        };
        return categoryMap[placeType] || 10;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تشغيل السكريبت
if (require.main === module) {
    new ParallelDownloadDevice2();
}

module.exports = ParallelDownloadDevice2;

@echo off
echo ===== نسخ قاعدة بيانات Yemen GPS =====
echo.

REM تحديد متغيرات قاعدة البيانات
set PGUSER=yemen
set PGPASSWORD=admin
set PGHOST=localhost
set PGPORT=5432
set PGDATABASE=yemen_gps

REM إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
if not exist "backups" mkdir backups

REM إنشاء اسم الملف مع التاريخ والوقت
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

set BACKUP_FILE=backups\yemen_gps_backup_%datestamp%.sql

echo جاري إنشاء نسخة احتياطية...
echo اسم الملف: %BACKUP_FILE%
echo.

REM تنفيذ أمر النسخ الاحتياطي
pg_dump -h %PGHOST% -p %PGPORT% -U %PGUSER% -d %PGDATABASE% -f %BACKUP_FILE% --verbose --clean --if-exists --create

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===== تم إنشاء النسخة الاحتياطية بنجاح =====
    echo الملف: %BACKUP_FILE%
    echo.
    
    REM عرض حجم الملف
    for %%A in ("%BACKUP_FILE%") do echo حجم الملف: %%~zA بايت
    echo.
    
    REM إنشاء نسخة مضغوطة (إذا كان 7zip متوفر)
    where 7z >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo جاري ضغط الملف...
        7z a "%BACKUP_FILE%.7z" "%BACKUP_FILE%"
        echo تم إنشاء ملف مضغوط: %BACKUP_FILE%.7z
    )
    
) else (
    echo.
    echo ===== فشل في إنشاء النسخة الاحتياطية =====
    echo تحقق من:
    echo 1. تشغيل خدمة PostgreSQL
    echo 2. صحة بيانات الاتصال
    echo 3. صلاحيات المستخدم
)

echo.
pause

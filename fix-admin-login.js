// سكريبت لإصلاح مشكلة تسجيل دخول المسؤول
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  host: 'localhost',
  port: 5433, // استخدام المنفذ الصحيح 5433
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function fixAdminUser() {
  console.log('بدء عملية إصلاح حساب المسؤول...');
  
  try {
    // التحقق من وجود المستخدم
    const checkResult = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      ['admin']
    );
    
    const adminPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    
    if (checkResult.rows.length === 0) {
      // إذا لم يكن المستخدم موجوداً، قم بإنشائه
      console.log('المستخدم المسؤول غير موجود. جاري إنشاء مستخدم جديد...');
      
      await pool.query('BEGIN');
      
      // محاولة إدراج المستخدم مع حقل password (كما في كود المصادقة)
      try {
        await pool.query(
          `INSERT INTO users 
          (username, password, email, full_name, phone, is_active, role_id, created_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())`,
          ['admin', hashedPassword, '<EMAIL>', 'مدير النظام', '+967712345678', true, 1]
        );
        console.log('تم إنشاء حساب المسؤول بنجاح!');
      } catch (insertError) {
        // إذا فشل الإدراج، قد يكون هيكل الجدول مختلفاً - نطبع الخطأ للمساعدة في التشخيص
        console.error('خطأ في إنشاء المستخدم:', insertError);
        
        // محاولة الحصول على هيكل جدول المستخدمين
        console.log('جاري التحقق من هيكل جدول المستخدمين...');
        const tableInfo = await pool.query(
          "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'users'"
        );
        
        console.log('هيكل جدول المستخدمين:');
        tableInfo.rows.forEach(col => {
          console.log(`- ${col.column_name}: ${col.data_type}`);
        });
      }
      
      await pool.query('COMMIT');
    } else {
      // إذا كان المستخدم موجوداً، قم بتحديث كلمة المرور
      console.log('المستخدم المسؤول موجود. جاري تحديث كلمة المرور...');
      
      const user = checkResult.rows[0];
      console.log('معلومات المستخدم الحالية (بدون كلمة المرور):');
      
      // طباعة أسماء الحقول لتحديد أيها يستخدم لكلمة المرور
      const userColumns = Object.keys(user).filter(key => key !== 'password' && key !== 'password');
      userColumns.forEach(key => {
        console.log(`- ${key}: ${user[key]}`);
      });
      
      // محاولة تحديث كلمة المرور (سنحاول كلا الحقلين المحتملين)
      await pool.query('BEGIN');
      
      try {
        // محاولة تحديث حقل password أولاً
        await pool.query(
          'UPDATE users SET password = $1 WHERE username = $2',
          [hashedPassword, 'admin']
        );
        console.log('تم تحديث كلمة المرور في حقل "password" بنجاح!');
      } catch (passwordError) {
        console.error('خطأ في تحديث حقل password:', passwordError);
        
        // إذا فشل التحديث، نحاول حقل password
        try {
          await pool.query(
            'UPDATE users SET password = $1 WHERE username = $2',
            [hashedPassword, 'admin']
          );
          console.log('تم تحديث كلمة المرور في حقل "password" بنجاح!');
        } catch (hashError) {
          console.error('خطأ في تحديث حقل password:', hashError);
        }
      }
      
      await pool.query('COMMIT');
    }
    
    console.log('\nتم الانتهاء من عملية الإصلاح.');
    console.log('يمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: admin');
    console.log('- كلمة المرور: admin123');
    
  } catch (error) {
    console.error('حدث خطأ أثناء إصلاح حساب المسؤول:', error);
  } finally {
    await pool.end();
  }
}

fixAdminUser();

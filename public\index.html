<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>خرائط اليمن - Yemen GPS</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>

    <!-- يلحق ملفات CSS الأساسية -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/yemen-maps.css">
    <link rel="stylesheet" href="assets/css/place-details.css">
    <link rel="stylesheet" href="assets/css/current-location.css">
    <link rel="stylesheet" href="assets/css/filters.css">
    <link rel="stylesheet" href="assets/css/directions.css">

    <!-- CSS لنافذة معلومات المكان المفصلة (لضمان عملها من أي مكان) -->
    <style>
        /* نافذة معلومات المكان المفصلة (Google Maps Style) */
        .place-details-panel {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 400px;
            max-width: 90%;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            z-index: 1500;
            display: flex;
            flex-direction: column;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow: hidden;
        }

        .place-details-panel.active {
            transform: translateX(0);
        }

        .place-details-header {
            background-color: #f8f8f8;
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .place-header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .place-header-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #444;
            transition: background-color 0.2s;
        }

        .place-header-btn:hover {
            background-color: #eee;
        }

        .place-search-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        #place-search-input {
            width: 100%;
            padding: 8px 40px 8px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        #place-search-clear,
        #place-search-btn {
            position: absolute;
            right: 8px;
            background: none;
            border: none;
            cursor: pointer;
            color: #777;
        }

        #place-search-clear {
            right: 32px;
        }

        .place-details-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 16px;
        }

        .place-image-container {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        #place-main-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .place-image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
        }

        .place-image-gallery {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .gallery-thumbnail {
            width: 50px;
            height: 50px;
            flex-shrink: 0;
            border-radius: 4px;
            overflow: hidden;
            border: 2px solid white;
            cursor: pointer;
        }

        .gallery-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .place-basic-info {
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .place-basic-info h1 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .place-category {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #5f6368;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .place-rating {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .stars {
            color: #ffc107;
        }

        .rating-value {
            font-weight: bold;
        }

        .review-count {
            color: #5f6368;
            font-size: 14px;
        }

        .place-address {
            color: #5f6368;
            font-size: 14px;
        }

        .place-quick-actions {
            display: flex;
            justify-content: space-around;
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .quick-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            color: #1a73e8;
        }

        .quick-action-btn i {
            font-size: 20px;
        }

        .quick-action-btn span {
            font-size: 12px;
        }

        .place-info-section {
            padding: 16px;
            border-bottom: 1px solid #eee;
        }

        .place-info-section h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .contact-item i {
            color: #5f6368;
            width: 20px;
            text-align: center;
        }

        .contact-item a {
            color: #1a73e8;
            text-decoration: none;
        }

        .hours-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .current-day {
            font-weight: bold;
            color: #1a73e8;
        }

        .reviews-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
    </style>

    <style>
        /* Base Styles */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            width: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            overflow: hidden;
        }

        /* Map Container */
        #map-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
        }

        #map {
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* Search Bar - Google Maps Style */
        .search-container {
            position: absolute;
            top: 16px;
            right: 16px;
            left: 16px;
            max-width: 480px;
            margin: 0 auto;
            z-index: 1000;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            padding: 0;
            width: 100%;
            overflow: hidden;
            transition: box-shadow 0.2s ease;
        }

        .search-box:focus-within {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 14px 16px;
            background: transparent;
            color: #202124;
        }

        .search-input::placeholder {
            color: #9aa0a6;
        }

        .search-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #5f6368;
            padding: 14px 16px;
            transition: color 0.2s ease;
        }

        .search-btn:hover {
            color: #1a73e8;
        }

        /* Map Controls - Google Maps Style */
        .map-controls {
            position: absolute;
            bottom: 24px;
            right: 16px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .map-control-btn {
            width: 44px;
            height: 44px;
            background-color: white;
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #5f6368;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .map-control-btn:hover {
            background-color: #f8f9fa;
            color: #1a73e8;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .map-control-btn:active {
            transform: scale(0.95);
        }

        #my-location-btn {
            background-color: #1a73e8;
            color: white;
        }

        #my-location-btn:hover {
            background-color: #1557b0;
            color: white;
        }

        /* Layer Controls - Google Maps Style */
        .map-layer-controls {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 1000;
            display: flex;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .map-layer-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 12px 16px;
            border: none;
            background: white;
            cursor: pointer;
            font-size: 12px;
            color: #5f6368;
            border-right: 1px solid #eee;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .map-layer-btn:last-child {
            border-right: none;
        }

        .map-layer-btn i {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .map-layer-btn.active {
            background-color: #1a73e8;
            color: white;
            font-weight: 500;
        }

        .map-layer-btn:hover:not(.active) {
            background-color: #f5f5f5;
            color: #1a73e8;
        }

        /* Popup Styles */
        .custom-popup {
            min-width: 220px;
            max-width: 300px;
            direction: rtl;
        }

        .custom-popup img {
            width: 100%;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .custom-popup h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #333;
        }

        .custom-popup p {
            margin: 4px 0;
            font-size: 14px;
            color: #555;
        }

        .popup-actions {
            display: flex;
            gap: 16px;
            margin-top: 12px;
            justify-content: flex-start;
        }

        .popup-action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            color: #1a73e8;
            font-size: 12px;
        }

        .popup-action-btn i {
            font-size: 18px;
        }

        /* Loading Indicator */
        #loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }

        .spinner {
            width: 32px;
            height: 32px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Bottom Info Panel (for directions) */
        #directions-panel {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background-color: white;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        #directions-panel.active {
            transform: translateY(0);
        }

        .directions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
        }

        .directions-content {
            padding: 16px;
            height: calc(100% - 50px);
            overflow-y: auto;
        }

        .direction-step {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            align-items: flex-start;
        }

        .direction-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f1f3f4;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .direction-text {
            flex: 1;
        }

        /* Night Mode Styles */
        body.night-mode {
            background-color: #1a1a1a;
        }

        body.night-mode .search-box {
            background-color: #2d2d2d;
            color: #e8eaed;
        }

        body.night-mode .search-input {
            color: #e8eaed;
        }

        body.night-mode .search-input::placeholder {
            color: #9aa0a6;
        }

        body.night-mode .map-control-btn {
            background-color: #2d2d2d;
            color: #e8eaed;
        }

        body.night-mode .map-control-btn:hover {
            background-color: #3c4043;
            color: #8ab4f8;
        }

        body.night-mode .map-layer-btn {
            background-color: #2d2d2d;
            color: #e8eaed;
        }

        body.night-mode .map-layer-btn:hover:not(.active) {
            background-color: #3c4043;
            color: #8ab4f8;
        }

        body.night-mode .map-layer-btn.active {
            background-color: #8ab4f8;
            color: #1a1a1a;
        }

        body.night-mode #map {
            filter: invert(1) hue-rotate(180deg);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .search-container {
                top: 12px;
                right: 12px;
                left: 12px;
            }

            .map-layer-controls {
                top: 12px;
                left: 12px;
                flex-direction: column;
                width: auto;
            }

            .map-layer-btn {
                min-width: 60px;
                padding: 8px 12px;
                font-size: 11px;
                border-right: none;
                border-bottom: 1px solid #eee;
            }

            .map-layer-btn:last-child {
                border-bottom: none;
            }

            .map-controls {
                bottom: 20px;
                right: 12px;
                gap: 6px;
            }

            .map-control-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .search-container {
                top: 8px;
                right: 8px;
                left: 8px;
            }

            .map-layer-controls {
                top: 8px;
                left: 8px;
            }

            .map-controls {
                bottom: 16px;
                right: 8px;
            }
        }

        /* Loading Animation Enhancement */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #323232;
            color: white;
            padding: 12px 16px;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            background: #4caf50;
        }

        .notification.error {
            background: #f44336;
        }

        .notification.warning {
            background: #ff9800;
        }

        .notification.fade-out {
            animation: fadeOut 0.5s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        /* Offline Indicator */
        #offline-indicator {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ff9800;
            color: white;
            text-align: center;
            padding: 8px;
            z-index: 10001;
            font-size: 14px;
            font-weight: 500;
        }

        /* Custom Popup Styles */
        .custom-popup {
            direction: rtl;
            text-align: right;
            font-family: 'Tajawal', Arial, sans-serif;
        }

        .custom-popup h3 {
            margin: 0 0 8px 0;
            color: #1a73e8;
            font-size: 16px;
            font-weight: 500;
        }

        .custom-popup p {
            margin: 0 0 12px 0;
            color: #5f6368;
            font-size: 14px;
            line-height: 1.4;
        }

        .popup-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .popup-action-btn {
            flex: 1;
            padding: 6px 12px;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: background 0.2s ease;
        }

        .popup-action-btn:hover {
            background: #1557b0;
        }

        .popup-action-btn:nth-child(2) {
            background: #34a853;
        }

        .popup-action-btn:nth-child(2):hover {
            background: #2d7a3e;
        }

        /* Current Location Marker Styles */
        .current-location-marker {
            background: transparent;
            border: none;
        }

        .current-marker-inner {
            width: 20px;
            height: 20px;
            background: #1a73e8;
            border: 3px solid white;
            border-radius: 50%;
            position: relative;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .current-marker-pulse {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 40px;
            height: 40px;
            border: 2px solid #1a73e8;
            border-radius: 50%;
            opacity: 0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.1);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }

        /* Fallback Location Marker Styles */
        .fallback-location-marker {
            background: transparent;
            border: none;
        }

        .fallback-marker-inner {
            width: 30px;
            height: 30px;
            background: #ff9800;
            border: 3px solid white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            color: #1a73e8;
            font-size: 18px;
        }

        .modal-body {
            padding: 20px;
        }

        .location-options {
            margin-bottom: 20px;
        }

        .location-option {
            margin-bottom: 20px;
            padding: 16px;
            border: 1px solid #eee;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .location-option h4 {
            margin: 0 0 12px 0;
            color: #1a73e8;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .location-option input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .coordinates-input {
            display: flex;
            gap: 8px;
        }

        .coordinates-input input {
            flex: 1;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s ease;
        }

        .btn-primary {
            background: #1a73e8;
            color: white;
        }

        .btn-primary:hover {
            background: #1557b0;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .quick-locations {
            border-top: 1px solid #eee;
            padding-top: 16px;
        }

        .quick-locations h4 {
            margin: 0 0 12px 0;
            color: #1a73e8;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-location-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 8px;
        }

        .quick-btn {
            padding: 8px 12px;
            background: #34a853;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s ease;
        }

        .quick-btn:hover {
            background: #2d7a3e;
        }
    </style>
</head>
<body>
    <!-- مؤشر الاتصال الأوفلاين -->
    <div id="offline-indicator" class="hidden">
        <i class="fas fa-wifi"></i>
        أنت غير متصل بالإنترنت - الوضع الأوفلاين
    </div>
    <!-- Search Bar -->
    <div class="search-container">
        <div class="search-box">
            <input type="text" id="search-input" class="search-input" placeholder="ابحث عن موقع أو عنوان...">
            <button id="search-btn" class="search-btn"><i class="fas fa-search"></i></button>
        </div>
    </div>

    <!-- Map Container -->
    <div id="map-container">
        <div id="map"></div>

        <!-- أزرار تبديل نوع الخريطة - Google Maps Style -->
        <div class="map-layer-controls">
            <button class="map-layer-btn active" data-layer="streets" title="خريطة الشوارع">
                <i class="fas fa-road"></i>
                <span>خريطة</span>
            </button>
            <button class="map-layer-btn" data-layer="satellite" title="خريطة الأقمار الصناعية">
                <i class="fas fa-satellite"></i>
                <span>أقمار صناعية</span>
            </button>
            <button class="map-layer-btn" data-layer="terrain" title="خريطة التضاريس">
                <i class="fas fa-mountain"></i>
                <span>تضاريس</span>
            </button>
        </div>

        <!-- أزرار التحكم بالخريطة -->
        <div class="map-controls">
            <button id="my-location-btn" class="map-control-btn" title="تحديد موقعي">
                <i class="fas fa-location-crosshairs"></i>
            </button>
            <button id="zoom-in-btn" class="map-control-btn" title="تكبير">
                <i class="fas fa-plus"></i>
            </button>
            <button id="zoom-out-btn" class="map-control-btn" title="تصغير">
                <i class="fas fa-minus"></i>
            </button>
            <button id="night-mode-btn" class="map-control-btn" title="الوضع الليلي">
                <i class="fas fa-moon"></i>
            </button>
            <button id="fullscreen-btn" class="map-control-btn" title="ملء الشاشة">
                <i class="fas fa-expand"></i>
            </button>
            <button id="manual-location-btn" class="map-control-btn" title="تحديد الموقع يدوياً">
                <i class="fas fa-map-pin"></i>
            </button>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loading-indicator">
        <div class="loading-spinner"></div>
        <p>جاري تحميل الخريطة...</p>
    </div>

    <!-- نافذة تحديد الموقع يدوياً -->
    <div id="manual-location-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تحديد الموقع يدوياً</h3>
                <button class="close-btn" onclick="document.getElementById('manual-location-modal').classList.add('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>يمكنك تحديد موقعك بإحدى الطرق التالية:</p>

                <div class="location-options">
                    <div class="location-option">
                        <h4><i class="fas fa-search"></i> البحث عن مدينة</h4>
                        <input type="text" id="city-search" placeholder="اكتب اسم المدينة (مثل: صنعاء، عدن، تعز)">
                        <button onclick="yemenMaps.searchForCity()" class="btn btn-primary">بحث</button>
                    </div>

                    <div class="location-option">
                        <h4><i class="fas fa-map-marker-alt"></i> إدخال الإحداثيات</h4>
                        <div class="coordinates-input">
                            <input type="number" id="manual-lat" placeholder="خط العرض" step="any">
                            <input type="number" id="manual-lng" placeholder="خط الطول" step="any">
                        </div>
                        <button onclick="yemenMaps.setManualLocation()" class="btn btn-primary">تحديد الموقع</button>
                    </div>

                    <div class="location-option">
                        <h4><i class="fas fa-mouse-pointer"></i> النقر على الخريطة</h4>
                        <p>أغلق هذه النافذة وانقر على الخريطة في المكان المطلوب</p>
                        <button onclick="yemenMaps.enableMapClickMode()" class="btn btn-secondary">تفعيل وضع النقر</button>
                    </div>
                </div>

                <div class="quick-locations">
                    <h4><i class="fas fa-star"></i> مواقع سريعة</h4>
                    <div class="quick-location-buttons">
                        <button onclick="yemenMaps.goToQuickLocation(15.3694, 44.1910, 'صنعاء')" class="quick-btn">صنعاء</button>
                        <button onclick="yemenMaps.goToQuickLocation(12.7855, 45.0187, 'عدن')" class="quick-btn">عدن</button>
                        <button onclick="yemenMaps.goToQuickLocation(13.5795, 44.2075, 'تعز')" class="quick-btn">تعز</button>
                        <button onclick="yemenMaps.goToQuickLocation(14.5995, 49.1244, 'المكلا')" class="quick-btn">المكلا</button>
                        <button onclick="yemenMaps.goToQuickLocation(16.9402, 43.7445, 'صعدة')" class="quick-btn">صعدة</button>
                        <button onclick="yemenMaps.goToQuickLocation(14.2681, 49.5243, 'سيئون')" class="quick-btn">سيئون</button>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <!-- نافذة معلومات المكان المفصلة (Google Maps Style) -->
    <div id="place-details-panel" class="place-details-panel">
        <div class="place-details-header">
            <div class="place-header-actions">
                <button id="close-place-details-btn" class="place-header-btn"><i class="fas fa-arrow-right"></i></button>
                <button id="share-place-btn" class="place-header-btn"><i class="fas fa-share-alt"></i></button>
                <button id="save-place-btn" class="place-header-btn"><i class="far fa-bookmark"></i></button>
            </div>
            <div class="place-search-container">
                <input type="text" id="place-search-input" placeholder="ابحث عن مكان...">
                <button id="place-search-clear"><i class="fas fa-times"></i></button>
                <button id="place-search-btn"><i class="fas fa-search"></i></button>
            </div>
        </div>

        <div class="place-details-content">
            <!-- صورة المكان الرئيسية -->
            <div class="place-image-container">
                <img id="place-main-image" src="" alt="صورة المكان" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                <div class="place-image-overlay"></div>
                <div class="place-image-gallery" id="place-image-gallery">
                    <!-- سيتم إضافة معرض الصور هنا ديناميكيًا -->
                </div>
            </div>

            <!-- معلومات المكان الأساسية -->
            <div class="place-basic-info">
                <h1 id="place-name">اسم المكان</h1>
                <div class="place-category" id="place-category"><i class="fas fa-map-marker-alt"></i> <span>موقع</span></div>
                <div class="place-rating" id="place-rating">
                    <div class="stars">★★★★☆</div>
                    <span class="rating-value">4.5</span>
                    <span class="review-count">(123 تقييم)</span>
                </div>
                <div class="place-address" id="place-address">العنوان</div>
            </div>

            <!-- أزرار الإجراءات السريعة -->
            <div class="place-quick-actions">
                <button id="directions-btn" class="quick-action-btn">
                    <i class="fas fa-directions"></i>
                    <span>المسار</span>
                </button>
                <button id="call-btn" class="quick-action-btn">
                    <i class="fas fa-phone"></i>
                    <span>اتصال</span>
                </button>
                <button id="website-btn" class="quick-action-btn">
                    <i class="fas fa-globe"></i>
                    <span>الموقع</span>
                </button>
                <button id="more-btn" class="quick-action-btn">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>المزيد</span>
                </button>
            </div>

            <!-- معلومات الاتصال -->
            <div class="place-contact-info">
                <div class="place-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="contact-item" id="contact-phone">
                        <i class="fas fa-phone"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="contact-item" id="contact-website">
                        <i class="fas fa-globe"></i>
                        <span>الموقع الإلكتروني</span>
                    </div>
                    <div class="contact-item" id="contact-address">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>العنوان</span>
                    </div>
                </div>
            </div>

            <!-- ساعات العمل -->
            <div class="place-opening-hours" id="place-opening-hours">
                <div class="place-info-section">
                    <h3>ساعات العمل</h3>
                    <div class="hours-list" id="hours-list">
                        <!-- سيتم إضافة ساعات العمل هنا ديناميكيًا -->
                    </div>
                </div>
            </div>

            <!-- حقائق سريعة -->
            <div class="place-quick-facts">
                <div class="place-info-section">
                    <h3>حقائق سريعة</h3>
                    <p id="place-description">وصف المكان سيظهر هنا.</p>
                </div>
            </div>

            <!-- المراجعات والتقييمات -->
            <div class="place-reviews">
                <div class="place-info-section">
                    <h3>المراجعات</h3>
                    <div class="reviews-list" id="reviews-list">
                        <!-- سيتم إضافة المراجعات هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة معلومات المكان المفصلة (Google Maps Style) -->
    <div id="place-details-panel" class="place-details-panel">
        <div class="place-details-header">
            <div class="place-header-actions">
                <button id="close-place-details-btn" class="place-header-btn"><i class="fas fa-arrow-right"></i></button>
                <button id="share-place-btn" class="place-header-btn"><i class="fas fa-share-alt"></i></button>
                <button id="save-place-btn" class="place-header-btn"><i class="far fa-bookmark"></i></button>
            </div>
            <div class="place-search-container">
                <input type="text" id="place-search-input" placeholder="ابحث عن مكان...">
                <button id="place-search-clear"><i class="fas fa-times"></i></button>
                <button id="place-search-btn"><i class="fas fa-search"></i></button>
            </div>
        </div>

        <div class="place-details-content">
            <!-- صورة المكان الرئيسية -->
            <div class="place-image-container">
                <img id="place-main-image" src="" alt="صورة المكان" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">
                <div class="place-image-overlay"></div>
                <div class="place-image-gallery" id="place-image-gallery">
                    <!-- سيتم إضافة معرض الصور هنا ديناميكيًا -->
                </div>
            </div>

            <!-- معلومات المكان الأساسية -->
            <div class="place-basic-info">
                <h1 id="place-name">اسم المكان</h1>
                <div class="place-category" id="place-category"><i class="fas fa-map-marker-alt"></i> <span>موقع</span></div>
                <div class="place-rating" id="place-rating">
                    <div class="stars">★★★★☆</div>
                    <span class="rating-value">4.5</span>
                    <span class="review-count">(123 تقييم)</span>
                </div>
                <div class="place-address" id="place-address">العنوان</div>
            </div>

            <!-- أزرار الإجراءات السريعة -->
            <div class="place-quick-actions">
                <button id="directions-btn" class="quick-action-btn">
                    <i class="fas fa-directions"></i>
                    <span>المسار</span>
                </button>
                <button id="call-btn" class="quick-action-btn">
                    <i class="fas fa-phone"></i>
                    <span>اتصال</span>
                </button>
                <button id="website-btn" class="quick-action-btn">
                    <i class="fas fa-globe"></i>
                    <span>الموقع</span>
                </button>
                <button id="more-btn" class="quick-action-btn">
                    <i class="fas fa-ellipsis-h"></i>
                    <span>المزيد</span>
                </button>
            </div>

            <!-- معلومات الاتصال -->
            <div class="place-contact-info">
                <div class="place-info-section">
                    <h3>معلومات الاتصال</h3>
                    <div class="contact-item" id="contact-phone">
                        <i class="fas fa-phone"></i>
                        <span>رقم الهاتف</span>
                    </div>
                    <div class="contact-item" id="contact-website">
                        <i class="fas fa-globe"></i>
                        <span>الموقع الإلكتروني</span>
                    </div>
                    <div class="contact-item" id="contact-address">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>العنوان</span>
                    </div>
                </div>
            </div>

            <!-- ساعات العمل -->
            <div class="place-opening-hours" id="place-opening-hours">
                <div class="place-info-section">
                    <h3>ساعات العمل</h3>
                    <div class="hours-list" id="hours-list">
                        <!-- سيتم إضافة ساعات العمل هنا ديناميكيًا -->
                    </div>
                </div>
            </div>

            <!-- حقائق سريعة -->
            <div class="place-quick-facts">
                <div class="place-info-section">
                    <h3>حقائق سريعة</h3>
                    <p id="place-description">وصف المكان سيظهر هنا.</p>
                </div>
            </div>

            <!-- المراجعات والتقييمات -->
            <div class="place-reviews">
                <div class="place-info-section">
                    <h3>المراجعات</h3>
                    <div class="reviews-list" id="reviews-list">
                        <!-- سيتم إضافة المراجعات هنا ديناميكيًا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

    <!-- ملف عرض تفاصيل المكان -->
    <script src="assets/js/place-details.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/yemen-db-helper.js"></script>
    <script src="assets/js/yemen-maps.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن - Yemen GPS</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/css/leaflet.css" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="assets/css/icons.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img src="assets/images/logo.svg" alt="Yemen GPS" class="logo-img">
                <span class="logo-text">خرائط اليمن</span>
            </div>

            <!-- Search Box -->
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" placeholder="البحث في الخرائط" class="search-input">
                    <button id="searchBtn" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="searchResults" class="search-results"></div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <button id="shareBtn" class="action-btn" title="مشاركة الموقع">
                    <i class="fas fa-share-alt"></i>
                </button>
                <button id="locationBtn" class="action-btn" title="موقعي الحالي">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button id="layersBtn" class="action-btn" title="طبقات الخريطة">
                    <i class="fas fa-layers"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside id="sidebar" class="sidebar">
        <div class="sidebar-header">
            <h3>الأماكن المحفوظة</h3>
            <button id="closeSidebar" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content">
            <div id="savedPlaces" class="saved-places">
                <!-- Saved places will be loaded here -->
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Map Container -->
        <div id="mapContainer" class="map-container">
            <div id="map" class="map"></div>

            <!-- Map Controls -->
            <div class="map-controls">
                <div class="zoom-controls">
                    <button id="zoomIn" class="control-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button id="zoomOut" class="control-btn">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>

                <div class="view-controls">
                    <button id="satelliteView" class="control-btn" title="عرض الأقمار الصناعية">
                        <i class="fas fa-satellite"></i>
                    </button>
                    <button id="streetView" class="control-btn active" title="عرض الشوارع">
                        <i class="fas fa-road"></i>
                    </button>
                    <button id="terrainView" class="control-btn" title="عرض التضاريس">
                        <i class="fas fa-mountain"></i>
                    </button>
                </div>

                <button id="fullscreenBtn" class="control-btn fullscreen-btn" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Info Window -->
    <div id="infoWindow" class="info-window hidden">
        <div class="info-header">
            <div id="infoTitle" class="info-title">معلومات الموقع</div>
            <button id="closeInfo" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="infoBody" class="info-content">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>

    <!-- Layers Panel -->
    <div id="layersPanel" class="layers-panel hidden">
        <div class="panel-header">
            <h4>طبقات الخريطة</h4>
            <button id="closeLayersPanel" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="panel-content">
            <div class="layer-group">
                <h5>نوع الخريطة</h5>
                <label class="layer-option">
                    <input type="radio" name="mapType" value="street" checked>
                    <span>خريطة الشوارع</span>
                </label>
                <label class="layer-option">
                    <input type="radio" name="mapType" value="satellite">
                    <span>صور الأقمار الصناعية</span>
                </label>
                <label class="layer-option">
                    <input type="radio" name="mapType" value="terrain">
                    <span>التضاريس</span>
                </label>
            </div>

            <div class="layer-group">
                <h5>طبقات إضافية</h5>
                <label class="layer-option labels-only">
                    <input type="checkbox" id="labelsLayer" checked>
                    <span>تسميات الأماكن</span>
                </label>
                <label class="layer-option">
                    <input type="checkbox" id="trafficLayer">
                    <span>حركة المرور</span>
                </label>
                <label class="layer-option">
                    <input type="checkbox" id="transitLayer">
                    <span>وسائل النقل العام</span>
                </label>
                <label class="layer-option">
                    <input type="checkbox" id="bikingLayer">
                    <span>مسارات الدراجات</span>
                </label>
            </div>
        </div>
    </div>

    <!-- Directions Panel -->
    <div id="directionsPanel" class="directions-panel hidden">
        <div class="panel-header">
            <h4>الاتجاهات</h4>
            <button id="closeDirectionsPanel" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="panel-content">
            <div class="directions-form">
                <div class="direction-input">
                    <label>من:</label>
                    <input type="text" id="fromLocation" placeholder="الموقع الحالي" readonly>
                    <button id="useCurrentLocation" class="location-btn" title="استخدام الموقع الحالي">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                </div>
                <div class="direction-input">
                    <label>إلى:</label>
                    <input type="text" id="toLocation" placeholder="اختر الوجهة">
                    <button id="swapLocations" class="swap-btn" title="تبديل المواقع">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                </div>
                <div class="transport-options">
                    <button class="transport-btn active" data-mode="driving">
                        <i class="fas fa-car"></i>
                        <span>سيارة</span>
                    </button>
                    <button class="transport-btn" data-mode="walking">
                        <i class="fas fa-walking"></i>
                        <span>مشي</span>
                    </button>
                    <button class="transport-btn" data-mode="cycling">
                        <i class="fas fa-bicycle"></i>
                        <span>دراجة</span>
                    </button>
                </div>

                <!-- Route Preference Options -->
                <div class="route-preferences">
                    <label class="preference-label">تفضيل المسار:</label>
                    <div class="preference-options">
                        <label class="preference-option">
                            <input type="radio" name="routePreference" value="shortest" checked>
                            <span>أقصر مسافة</span>
                        </label>
                        <label class="preference-option">
                            <input type="radio" name="routePreference" value="fastest">
                            <span>أسرع وقت</span>
                        </label>
                    </div>
                </div>

                <button id="calculateRoute" class="calculate-btn">
                    <i class="fas fa-route"></i>
                    حساب المسار
                </button>
            </div>
            <div id="routeResults" class="route-results hidden">
                <div class="route-summary">
                    <div class="route-info">
                        <span class="distance">المسافة: <strong id="routeDistance">-</strong></span>
                        <span class="duration">الوقت: <strong id="routeDuration">-</strong></span>
                    </div>
                </div>
                <div id="routeInstructions" class="route-instructions">
                    <!-- Route steps will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner hidden">
        <div class="spinner"></div>
        <p>جاري التحميل...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0&libraries=places&callback=initMap&language=ar&region=YE"></script>
    <script src="assets/js/google-maps-app.js"></script>
</body>
</html>

// يمن ناف - ملف لإضافة أعمدة الصلاحيات في جدول المستخدمين
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('إعدادات الاتصال بقاعدة البيانات:');
console.log(`- المستخدم: ${dbConfig.user}`);
console.log(`- المضيف: ${dbConfig.host}`);
console.log(`- المنفذ: ${dbConfig.port}`);
console.log(`- قاعدة البيانات: ${dbConfig.database}`);

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// قراءة ملف SQL
const sqlFilePath = path.join(__dirname, 'add-permissions-columns.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// تقسيم الملف إلى أوامر SQL منفصلة
const sqlCommands = sqlContent
  .replace(/--.*$/gm, '') // إزالة التعليقات
  .split(';')
  .filter(cmd => cmd.trim() !== '');

// تنفيذ الأوامر SQL
async function executeQueries() {
    const client = await pool.connect();
    
    try {
        await client.query('BEGIN');
        
        console.log(`تنفيذ ${sqlCommands.length} أمر SQL...`);
        
        for (let i = 0; i < sqlCommands.length; i++) {
            const cmd = sqlCommands[i].trim();
            if (cmd) {
                try {
                    await client.query(cmd);
                    console.log(`تم تنفيذ الأمر ${i + 1} بنجاح`);
                } catch (err) {
                    console.error(`خطأ في تنفيذ الأمر ${i + 1}:`, err.message);
                    // استمر في التنفيذ حتى لو فشل أمر واحد
                }
            }
        }
        
        // التحقق من وجود المستخدم admin
        const userResult = await client.query('SELECT * FROM users WHERE username = $1', ['admin']);
        if (userResult.rows.length > 0) {
            console.log('تم التحقق من وجود المستخدم admin بنجاح');
            console.log('معلومات المستخدم:');
            console.log(`- المعرف: ${userResult.rows[0].id}`);
            console.log(`- اسم المستخدم: ${userResult.rows[0].username}`);
            console.log(`- البريد الإلكتروني: ${userResult.rows[0].email}`);
            console.log(`- الاسم الكامل: ${userResult.rows[0].full_name}`);
            console.log(`- معرف الدور: ${userResult.rows[0].role_id}`);
            console.log(`- نشط: ${userResult.rows[0].is_active}`);
            console.log(`- يمكنه الوصول إلى صفحة الإدارة: ${userResult.rows[0].can_access_admin}`);
            console.log(`- الصلاحيات: ${JSON.stringify(userResult.rows[0].permissions_json)}`);
        } else {
            console.error('لم يتم العثور على المستخدم admin');
            
            // إنشاء المستخدم admin إذا لم يكن موجودًا
            console.log('إنشاء المستخدم admin...');
            
            // التحقق من وجود دور المدير
            const roleResult = await client.query('SELECT * FROM roles WHERE name = $1', ['admin']);
            let roleId = 1;
            
            if (roleResult.rows.length === 0) {
                console.log('إنشاء دور المدير...');
                const roleInsertResult = await client.query(`
                    INSERT INTO roles (name, description)
                    VALUES ('admin', 'مدير النظام')
                    RETURNING id
                `);
                roleId = roleInsertResult.rows[0].id;
            } else {
                roleId = roleResult.rows[0].id;
            }
            
            // إنشاء المستخدم admin
            const hashedPassword = '$2b$10$X7tPj4cjLsVUoA/2o8yw3.Qi.Ym5OJbR8vQzPYxbniQTgPOxRVSJi'; // كلمة المرور: yemen123
            
            await client.query(`
                INSERT INTO users (username, email, password, full_name, role_id, is_active, is_verified, can_access_admin, permissions_json)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            `, [
                'admin',
                '<EMAIL>',
                hashedPassword,
                'مدير النظام',
                roleId,
                true,
                true,
                true,
                JSON.stringify([
                    {"code": "view_dashboard", "name": "عرض لوحة التحكم"},
                    {"code": "manage_users", "name": "إدارة المستخدمين"},
                    {"code": "manage_clients", "name": "إدارة العملاء"},
                    {"code": "manage_locations", "name": "إدارة المواقع"},
                    {"code": "manage_settings", "name": "إدارة الإعدادات"},
                    {"code": "manage_backups", "name": "إدارة النسخ الاحتياطي"},
                    {"code": "manage_categories", "name": "إدارة التصنيفات"},
                    {"code": "manage_routes", "name": "إدارة المسارات"},
                    {"code": "manage_reports", "name": "إدارة التقارير"},
                    {"code": "manage_roles", "name": "إدارة الأدوار"}
                ])
            ]);
            
            console.log('تم إنشاء المستخدم admin بنجاح');
        }
        
        await client.query('COMMIT');
        console.log('تم تنفيذ جميع الأوامر بنجاح');
    } catch (err) {
        await client.query('ROLLBACK');
        console.error('حدث خطأ أثناء تنفيذ الأوامر:', err);
    } finally {
        client.release();
        // إغلاق الاتصال بقاعدة البيانات
        await pool.end();
    }
}

// تنفيذ الأوامر
executeQueries().catch(err => {
    console.error('خطأ غير متوقع:', err);
    process.exit(1);
});

package com.yemengps.app.offline;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.yemengps.app.data.AppDatabase;
import com.yemengps.app.data.LocationRepository;
import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.network.ApiClient;
import com.yemengps.app.network.ApiResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for managing offline location data
 */
public class OfflineLocationRepository {

    private static final String TAG = "OfflineLocationRepo";

    private final AppDatabase database;
    private final ApiClient apiClient;
    private final LocationRepository locationRepository;
    private final ConnectivityMonitor connectivityMonitor;
    private final Executor executor;
    private final List<QueuedChange> queuedChanges;
    private final MutableLiveData<SyncStatus> syncStatus;

    /**
     * Constructor
     * @param database The app database
     * @param apiClient The API client
     * @param locationRepository The location repository
     * @param connectivityMonitor The connectivity monitor
     */
    public OfflineLocationRepository(
            AppDatabase database,
            ApiClient apiClient,
            LocationRepository locationRepository,
            ConnectivityMonitor connectivityMonitor) {
        this.database = database;
        this.apiClient = apiClient;
        this.locationRepository = locationRepository;
        this.connectivityMonitor = connectivityMonitor;
        this.executor = Executors.newSingleThreadExecutor();
        this.queuedChanges = new ArrayList<>();
        this.syncStatus = new MutableLiveData<>(new SyncStatus(false, 0, 0));
        
        // Register for connectivity changes
        connectivityMonitor.registerConnectivityCallback(isConnected -> {
            if (isConnected) {
                syncQueuedChanges();
            }
        });
    }

    /**
     * Download locations for a region
     * @param bounds The bounds of the region
     */
    public void downloadLocationsForRegion(LatLngBounds bounds) {
        if (!connectivityMonitor.isOnline()) {
            Log.e(TAG, "Cannot download locations while offline");
            return;
        }
        
        executor.execute(() -> {
            // Get locations within the bounds from the API
            // This would typically be a custom API endpoint
            // For now, we'll simulate it by getting all locations and filtering
            
            apiClient.getAllLocations(new Callback<ApiResponse<List<LocationPoint>>>() {
                @Override
                public void onResponse(Call<ApiResponse<List<LocationPoint>>> call, Response<ApiResponse<List<LocationPoint>>> response) {
                    if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                        List<LocationPoint> allLocations = response.body().getData();
                        if (allLocations != null) {
                            // Filter locations within bounds
                            List<LocationPoint> locationsInBounds = new ArrayList<>();
                            for (LocationPoint location : allLocations) {
                                LatLng latLng = new LatLng(location.getLatitude(), location.getLongitude());
                                if (bounds.contains(latLng)) {
                                    locationsInBounds.add(location);
                                }
                            }
                            
                            // Save to local database
                            executor.execute(() -> {
                                for (LocationPoint location : locationsInBounds) {
                                    locationRepository.insert(location);
                                }
                                Log.d(TAG, "Downloaded " + locationsInBounds.size() + " locations for region");
                            });
                        }
                    } else {
                        Log.e(TAG, "Error downloading locations: " + (response.body() != null ? response.body().getMessage() : "Unknown error"));
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<List<LocationPoint>>> call, Throwable t) {
                    Log.e(TAG, "Error downloading locations", t);
                }
            });
        });
    }

    /**
     * Get locations within a region
     * @param bounds The bounds of the region
     * @return LiveData list of locations
     */
    public LiveData<List<LocationPoint>> getLocationsInRegion(LatLngBounds bounds) {
        // This would typically be a custom query in the DAO
        // For now, we'll use a MediatorLiveData to filter the results
        
        MediatorLiveData<List<LocationPoint>> result = new MediatorLiveData<>();
        
        // Get all locations from the repository
        LiveData<List<LocationPoint>> allLocations = locationRepository.getAllLocations();
        
        // Add source and filter
        result.addSource(allLocations, locations -> {
            if (locations != null) {
                List<LocationPoint> locationsInBounds = new ArrayList<>();
                for (LocationPoint location : locations) {
                    LatLng latLng = new LatLng(location.getLatitude(), location.getLongitude());
                    if (bounds.contains(latLng)) {
                        locationsInBounds.add(location);
                    }
                }
                result.setValue(locationsInBounds);
            } else {
                result.setValue(new ArrayList<>());
            }
        });
        
        return result;
    }

    /**
     * Queue a change made while offline
     * @param location The location that was changed
     * @param type The type of change
     */
    public void queueOfflineChange(LocationPoint location, ChangeType type) {
        executor.execute(() -> {
            // Add to queue
            QueuedChange change = new QueuedChange(location, type);
            queuedChanges.add(change);
            
            // Update sync status
            SyncStatus status = syncStatus.getValue();
            if (status != null) {
                status.pendingChanges++;
                syncStatus.postValue(status);
            }
            
            Log.d(TAG, "Queued offline change: " + type + " for location " + location.getId());
            
            // If online, sync immediately
            if (connectivityMonitor.isOnline()) {
                syncQueuedChanges();
            }
        });
    }

    /**
     * Sync queued changes when online
     */
    public void syncQueuedChanges() {
        if (!connectivityMonitor.isOnline() || queuedChanges.isEmpty()) {
            return;
        }
        
        executor.execute(() -> {
            // Update sync status
            SyncStatus status = new SyncStatus(true, queuedChanges.size(), 0);
            syncStatus.postValue(status);
            
            Log.d(TAG, "Syncing " + queuedChanges.size() + " queued changes");
            
            // Process each change
            List<QueuedChange> processedChanges = new ArrayList<>();
            
            for (QueuedChange change : queuedChanges) {
                try {
                    switch (change.type) {
                        case ADD:
                            syncAddLocation(change.location);
                            break;
                        case UPDATE:
                            syncUpdateLocation(change.location);
                            break;
                        case DELETE:
                            syncDeleteLocation(change.location);
                            break;
                    }
                    
                    // Mark as processed
                    processedChanges.add(change);
                    
                    // Update sync status
                    status.completedChanges++;
                    syncStatus.postValue(status);
                } catch (Exception e) {
                    Log.e(TAG, "Error syncing change", e);
                }
            }
            
            // Remove processed changes
            queuedChanges.removeAll(processedChanges);
            
            // Update sync status
            status.isSync = false;
            status.pendingChanges = queuedChanges.size();
            syncStatus.postValue(status);
            
            Log.d(TAG, "Sync completed, " + processedChanges.size() + " changes processed, " + queuedChanges.size() + " changes remaining");
        });
    }

    /**
     * Get the sync status
     * @return LiveData with sync status
     */
    public LiveData<SyncStatus> getSyncStatus() {
        return syncStatus;
    }

    /**
     * Sync an added location
     * @param location The location to add
     */
    private void syncAddLocation(LocationPoint location) {
        // Send to API
        apiClient.addLocation(location, new Callback<ApiResponse<LocationPoint>>() {
            @Override
            public void onResponse(Call<ApiResponse<LocationPoint>> call, Response<ApiResponse<LocationPoint>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    LocationPoint updatedLocation = response.body().getData();
                    if (updatedLocation != null) {
                        // Update local database with server-generated ID and other fields
                        executor.execute(() -> locationRepository.update(updatedLocation));
                        Log.d(TAG, "Synced add location: " + updatedLocation.getId());
                    }
                } else {
                    Log.e(TAG, "Error syncing add location: " + (response.body() != null ? response.body().getMessage() : "Unknown error"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LocationPoint>> call, Throwable t) {
                Log.e(TAG, "Error syncing add location", t);
            }
        });
    }

    /**
     * Sync an updated location
     * @param location The location to update
     */
    private void syncUpdateLocation(LocationPoint location) {
        // Send to API
        apiClient.updateLocation(location.getId(), location, new Callback<ApiResponse<LocationPoint>>() {
            @Override
            public void onResponse(Call<ApiResponse<LocationPoint>> call, Response<ApiResponse<LocationPoint>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    LocationPoint updatedLocation = response.body().getData();
                    if (updatedLocation != null) {
                        // Update local database
                        executor.execute(() -> locationRepository.update(updatedLocation));
                        Log.d(TAG, "Synced update location: " + updatedLocation.getId());
                    }
                } else {
                    Log.e(TAG, "Error syncing update location: " + (response.body() != null ? response.body().getMessage() : "Unknown error"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LocationPoint>> call, Throwable t) {
                Log.e(TAG, "Error syncing update location", t);
            }
        });
    }

    /**
     * Sync a deleted location
     * @param location The location to delete
     */
    private void syncDeleteLocation(LocationPoint location) {
        // Send to API
        apiClient.deleteLocation(location.getId(), new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    Log.d(TAG, "Synced delete location: " + location.getId());
                } else {
                    Log.e(TAG, "Error syncing delete location: " + (response.body() != null ? response.body().getMessage() : "Unknown error"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                Log.e(TAG, "Error syncing delete location", t);
            }
        });
    }

    /**
     * Class representing a queued change
     */
    private static class QueuedChange {
        final LocationPoint location;
        final ChangeType type;

        QueuedChange(LocationPoint location, ChangeType type) {
            this.location = location;
            this.type = type;
        }
    }

    /**
     * Class representing sync status
     */
    public static class SyncStatus {
        public boolean isSync;
        public int pendingChanges;
        public int completedChanges;

        SyncStatus(boolean isSync, int pendingChanges, int completedChanges) {
            this.isSync = isSync;
            this.pendingChanges = pendingChanges;
            this.completedChanges = completedChanges;
        }
    }

    /**
     * Enum representing the type of change
     */
    public enum ChangeType {
        ADD,
        UPDATE,
        DELETE
    }
}

// سكريبت دمج البيانات من الأجهزة المتعددة
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

class DatabaseMerger {
    constructor() {
        // قاعدة البيانات الرئيسية (الجهاز الأول)
        this.mainPool = new Pool({
            user: 'yemen',
            host: 'localhost',
            database: 'yemen_gps',
            password: 'admin',
            port: 5432,
        });

        // قاعدة البيانات الثانوية (الجهاز الثاني)
        this.secondaryPool = new Pool({
            user: 'yemen',
            host: 'localhost', // أو IP الجهاز الثاني
            database: 'yemen_gps_device2',
            password: 'admin',
            port: 5432,
        });

        this.mergedCount = 0;
        this.duplicateCount = 0;
        this.imagesMerged = 0;
    }

    async mergeDatabases() {
        console.log('🔄 بدء عملية دمج قواعد البيانات...');

        try {
            // 1. دمج بيانات الأماكن
            await this.mergePlaces();

            // 2. دمج الصور
            await this.mergeImages();

            // 3. إنشاء تقرير نهائي
            await this.generateReport();

            console.log('✅ تم دمج قواعد البيانات بنجاح!');

        } catch (error) {
            console.error('❌ خطأ في عملية الدمج:', error);
        } finally {
            await this.mainPool.end();
            await this.secondaryPool.end();
        }
    }

    async mergePlaces() {
        console.log('\n📊 دمج بيانات الأماكن...');

        try {
            // جلب جميع الأماكن من قاعدة البيانات الثانوية
            const result = await this.secondaryPool.query('SELECT * FROM places ORDER BY created_at');
            const places = result.rows;

            console.log(`📍 تم العثور على ${places.length} مكان في قاعدة البيانات الثانوية`);

            for (const place of places) {
                try {
                    // التحقق من وجود المكان في قاعدة البيانات الرئيسية
                    const existingPlace = await this.mainPool.query(
                        'SELECT id FROM places WHERE google_place_id = $1',
                        [place.google_place_id]
                    );

                    if (existingPlace.rows.length > 0) {
                        // تحديث البيانات الموجودة
                        await this.updateExistingPlace(place);
                        this.duplicateCount++;
                        console.log(`   🔄 تم تحديث: ${place.name_ar}`);
                    } else {
                        // إدراج مكان جديد
                        await this.insertNewPlace(place);
                        this.mergedCount++;
                        console.log(`   ✅ تم إدراج: ${place.name_ar}`);
                    }

                } catch (error) {
                    console.log(`   ❌ خطأ في معالجة ${place.name_ar}: ${error.message}`);
                }
            }

            console.log(`✅ تم دمج ${this.mergedCount} مكان جديد`);
            console.log(`🔄 تم تحديث ${this.duplicateCount} مكان موجود`);

        } catch (error) {
            console.error('❌ خطأ في دمج الأماكن:', error);
        }
    }

    async insertNewPlace(place) {
        const query = `
            INSERT INTO places (
                google_place_id, name_ar, name_en, description_ar, description_en,
                latitude, longitude, address_ar, address_en, phone, website,
                rating, user_ratings_total, price_level, category_id,
                area_name, place_type, photos, opening_hours, reviews,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
                $16, $17, $18, $19, $20, $21, $22
            )
        `;

        const values = [
            place.google_place_id, place.name_ar, place.name_en,
            place.description_ar, place.description_en,
            place.latitude, place.longitude,
            place.address_ar, place.address_en,
            place.phone, place.website,
            place.rating, place.user_ratings_total, place.price_level,
            place.category_id, place.area_name, place.place_type,
            place.photos, place.opening_hours, place.reviews,
            place.created_at, place.updated_at
        ];

        await this.mainPool.query(query, values);
    }

    async updateExistingPlace(place) {
        const query = `
            UPDATE places SET
                name_ar = COALESCE($2, name_ar),
                name_en = COALESCE($3, name_en),
                description_ar = COALESCE($4, description_ar),
                description_en = COALESCE($5, description_en),
                phone = COALESCE($6, phone),
                website = COALESCE($7, website),
                rating = COALESCE($8, rating),
                user_ratings_total = COALESCE($9, user_ratings_total),
                price_level = COALESCE($10, price_level),
                photos = CASE 
                    WHEN $11::json IS NOT NULL AND $11::text != '[]' 
                    THEN $11 
                    ELSE photos 
                END,
                opening_hours = COALESCE($12, opening_hours),
                reviews = COALESCE($13, reviews),
                updated_at = NOW()
            WHERE google_place_id = $1
        `;

        const values = [
            place.google_place_id, place.name_ar, place.name_en,
            place.description_ar, place.description_en,
            place.phone, place.website,
            place.rating, place.user_ratings_total, place.price_level,
            place.photos, place.opening_hours, place.reviews
        ];

        await this.mainPool.query(query, values);
    }

    async mergeImages() {
        console.log('\n📷 دمج الصور...');

        const sourceDir = path.join('public', 'images', 'places');
        const targetDir = path.join('public', 'images', 'places');

        // إنشاء مجلد الهدف إذا لم يكن موجوداً
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
        }

        try {
            // في حالة الأجهزة المنفصلة، ستحتاج لنسخ الصور يدوياً أو عبر الشبكة
            console.log('📁 مجلد الصور الرئيسي:', targetDir);
            
            // عد الصور الموجودة
            const files = fs.readdirSync(sourceDir).filter(file => file.endsWith('.jpg'));
            this.imagesMerged = files.length;
            
            console.log(`📷 إجمالي الصور: ${this.imagesMerged}`);

        } catch (error) {
            console.log('⚠️ تحذير: لم يتم العثور على مجلد الصور');
        }
    }

    async generateReport() {
        console.log('\n📋 إنشاء التقرير النهائي...');

        try {
            // إحصائيات قاعدة البيانات الرئيسية
            const totalPlaces = await this.mainPool.query('SELECT COUNT(*) FROM places');
            const placesByCategory = await this.mainPool.query(`
                SELECT 
                    category_id,
                    COUNT(*) as count,
                    CASE category_id
                        WHEN 1 THEN 'معالم سياحية'
                        WHEN 2 THEN 'أماكن دينية'
                        WHEN 3 THEN 'مطاعم'
                        WHEN 4 THEN 'فنادق'
                        WHEN 5 THEN 'صحة'
                        WHEN 6 THEN 'تعليم'
                        WHEN 7 THEN 'خدمات'
                        WHEN 8 THEN 'تسوق'
                        WHEN 9 THEN 'نقل'
                        ELSE 'أخرى'
                    END as category_name
                FROM places 
                GROUP BY category_id 
                ORDER BY count DESC
            `);

            const placesByArea = await this.mainPool.query(`
                SELECT area_name, COUNT(*) as count 
                FROM places 
                GROUP BY area_name 
                ORDER BY count DESC
            `);

            // إنشاء التقرير
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalPlaces: parseInt(totalPlaces.rows[0].count),
                    mergedPlaces: this.mergedCount,
                    updatedPlaces: this.duplicateCount,
                    totalImages: this.imagesMerged
                },
                placesByCategory: placesByCategory.rows,
                placesByArea: placesByArea.rows
            };

            // حفظ التقرير
            const reportPath = path.join('reports', `merge-report-${Date.now()}.json`);
            if (!fs.existsSync('reports')) {
                fs.mkdirSync('reports');
            }
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

            // طباعة التقرير
            console.log('\n📊 تقرير الدمج النهائي:');
            console.log('================================');
            console.log(`📍 إجمالي الأماكن: ${report.summary.totalPlaces}`);
            console.log(`✅ أماكن جديدة: ${report.summary.mergedPlaces}`);
            console.log(`🔄 أماكن محدثة: ${report.summary.updatedPlaces}`);
            console.log(`📷 إجمالي الصور: ${report.summary.totalImages}`);
            
            console.log('\n📋 التوزيع حسب الفئة:');
            report.placesByCategory.forEach(cat => {
                console.log(`   ${cat.category_name}: ${cat.count} مكان`);
            });

            console.log('\n🗺️ التوزيع حسب المنطقة:');
            report.placesByArea.forEach(area => {
                console.log(`   ${area.area_name}: ${area.count} مكان`);
            });

            console.log(`\n💾 تم حفظ التقرير في: ${reportPath}`);

        } catch (error) {
            console.error('❌ خطأ في إنشاء التقرير:', error);
        }
    }
}

// دالة لنسخ قاعدة البيانات عبر الشبكة
async function exportDatabaseFromDevice2(deviceIP) {
    console.log(`📤 تصدير قاعدة البيانات من الجهاز ${deviceIP}...`);
    
    const exportCommand = `pg_dump -h ${deviceIP} -U yemen -d yemen_gps_device2 > device2_export.sql`;
    
    // تنفيذ الأمر (يحتاج تثبيت PostgreSQL client)
    const { exec } = require('child_process');
    
    return new Promise((resolve, reject) => {
        exec(exportCommand, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                console.log('✅ تم تصدير قاعدة البيانات بنجاح');
                resolve(stdout);
            }
        });
    });
}

// دالة لاستيراد قاعدة البيانات
async function importDatabaseToMain() {
    console.log('📥 استيراد قاعدة البيانات...');
    
    const importCommand = `psql -U yemen -d yemen_gps < device2_export.sql`;
    
    const { exec } = require('child_process');
    
    return new Promise((resolve, reject) => {
        exec(importCommand, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                console.log('✅ تم استيراد قاعدة البيانات بنجاح');
                resolve(stdout);
            }
        });
    });
}

// تشغيل عملية الدمج
if (require.main === module) {
    const merger = new DatabaseMerger();
    merger.mergeDatabases();
}

module.exports = { DatabaseMerger, exportDatabaseFromDevice2, importDatabaseToMain };

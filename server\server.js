const express = require('express');
const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3002;

// إعداد قاعدة البيانات
const db = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'yemen_gps',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// اختبار الاتصال بقاعدة البيانات
db.connect()
  .then(() => console.log('✅ تم الاتصال بقاعدة البيانات بنجاح'))
  .catch(err => console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err));

// المسارات الأساسية
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin.html'));
});

// API تسجيل الدخول
app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  
  try {
    console.log('محاولة تسجيل دخول للمستخدم:', username);
    
    const result = await db.query('SELECT * FROM users WHERE username = $1', [username]);
    
    if (result.rows.length === 0) {
      console.log('المستخدم غير موجود:', username);
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    const user = result.rows[0];
    console.log('تم العثور على المستخدم:', user.username);
    
    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      console.log('كلمة المرور غير صحيحة للمستخدم:', username);
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    console.log('تم تسجيل الدخول بنجاح للمستخدم:', username);
    
    // إرسال بيانات المستخدم (بدون كلمة المرور)
    const userData = {
      user_id: user.id,
      username: user.username,
      full_name: user.full_name,
      email: user.email,
      phone: user.phone,
      role_id: user.role_id,
      is_active: user.is_active
    };
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      user: userData,
      token: 'dummy-token-' + Date.now()
    });
    
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err);
    res.status(500).json({ message: 'حدث خطأ في الخادم' });
  }
});

// API المستخدمين
app.get('/api/users', async (req, res) => {
  try {
    console.log('جلب قائمة المستخدمين...');
    const result = await db.query(`
      SELECT id, username, full_name, email, phone, role_id, is_active, registration_date, last_login, type
      FROM users 
      ORDER BY id
    `);
    console.log('تم جلب', result.rows.length, 'مستخدم');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب المستخدمين:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// API التصنيفات
app.get('/api/categories', async (req, res) => {
  try {
    console.log('جلب قائمة التصنيفات...');
    const result = await db.query('SELECT * FROM categories ORDER BY id');
    console.log('تم جلب', result.rows.length, 'تصنيف');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب التصنيفات:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات التصنيفات' });
  }
});

// API المواقع
app.get('/api/admin/locations', async (req, res) => {
  try {
    console.log('جلب قائمة المواقع...');
    const result = await db.query(`
      SELECT l.*, c.name as category_name 
      FROM locations l 
      LEFT JOIN categories c ON l.category_id = c.id 
      ORDER BY l.id
    `);
    console.log('تم جلب', result.rows.length, 'موقع');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب المواقع:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المواقع' });
  }
});

// API العملاء
app.get('/api/clients', async (req, res) => {
  try {
    console.log('جلب قائمة العملاء...');
    const result = await db.query('SELECT * FROM clients ORDER BY id');
    console.log('تم جلب', result.rows.length, 'عميل');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في جلب العملاء:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات العملاء' });
  }
});

// API اختبار الاتصال
app.get('/api/test-connection', async (req, res) => {
  try {
    const result = await db.query('SELECT NOW() as current_time');
    res.json({ 
      message: 'الاتصال بقاعدة البيانات يعمل بنجاح', 
      time: result.rows[0].current_time 
    });
  } catch (err) {
    console.error('خطأ في اختبار الاتصال:', err);
    res.status(500).json({ message: 'فشل الاتصال بقاعدة البيانات' });
  }
});

// تشغيل الخادم
app.listen(port, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${port}`);
  console.log(`🌐 افتح المتصفح على: http://localhost:${port}`);
  console.log(`👨‍💼 لوحة التحكم: http://localhost:${port}/admin-login.html`);
});

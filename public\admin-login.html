<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة تحكم يمن GPS</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- <PERSON><PERSON>wal Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .card-body {
            padding: 30px;
        }

        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
            width: 100%;
            padding: 10px;
            font-weight: 500;
        }

        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }

        .form-control {
            padding: 12px;
            border-radius: 5px;
        }

        .form-label {
            font-weight: 500;
        }

        .alert {
            display: none;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 100px;
            margin-bottom: 15px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            visibility: visible;
            opacity: 1;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- مؤشر التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <div class="login-container">
        <div class="card">
            <div class="card-header">
                <!-- استخدام نص بدلاً من صورة -->
                <div class="logo-text">يمن GPS</div>
                <h4 class="mb-0">تسجيل الدخول إلى لوحة التحكم</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" id="loginAlert" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="alertMessage"></span>
                </div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" placeholder="أدخل اسم المستخدم" required>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" placeholder="أدخل كلمة المرور" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">تذكرني</label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                    </button>
                </form>

                <div class="mt-3 text-center">
                    <a href="/" class="text-decoration-none">العودة للرئيسية</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // التحقق من وجود مستخدم مسجل دخول بالفعل
        function checkExistingLogin() {
            const localUser = localStorage.getItem('adminUser');
            const sessionUser = sessionStorage.getItem('adminUser');

            if (localUser || sessionUser) {
                console.log('المستخدم مسجل دخول بالفعل، إعادة التوجيه إلى لوحة التحكم');
                window.location.href = 'admin.html';
                return true;
            }
            return false;
        }

        // تشغيل فحص تسجيل الدخول الموجود
        if (!checkExistingLogin()) {
            console.log('لا يوجد مستخدم مسجل دخول، عرض صفحة تسجيل الدخول');
        }

        // إظهار مؤشر التحميل
        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }

        // إخفاء مؤشر التحميل
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const alertElement = document.getElementById('loginAlert');
            const alertMessageElement = document.getElementById('alertMessage');

            alertMessageElement.textContent = message;
            alertElement.style.display = 'block';
        }

        // إخفاء رسالة الخطأ
        function hideError() {
            document.getElementById('loginAlert').style.display = 'none';
        }

        // تبديل عرض كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // معالجة تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            hideError();
            showLoading();

            // الحصول على بيانات النموذج
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // إرسال طلب تسجيل الدخول إلى الخادم
            console.log('محاولة تسجيل الدخول باستخدام:', { username });
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
                }
                return response.json();
            })
            .then(data => {
                console.log('استجابة الخادم:', data);

                // التحقق من نجاح تسجيل الدخول
                if (!data.success && !data.user) {
                    throw new Error(data.error || 'حدث خطأ أثناء تسجيل الدخول');
                }

                // تحديد بيانات المستخدم
                const user = data.user || data;
                const token = data.token || 'admin_session_' + Date.now();

                // تخزين بيانات المستخدم والتوكن
                if (rememberMe) {
                    localStorage.setItem('adminUser', JSON.stringify(user));
                    localStorage.setItem('yemenGpsToken', token);
                    console.log('تم حفظ بيانات المستخدم في التخزين المحلي');
                } else {
                    sessionStorage.setItem('adminUser', JSON.stringify(user));
                    sessionStorage.setItem('yemenGpsToken', token);
                    console.log('تم حفظ بيانات المستخدم في تخزين الجلسة');
                }

                // التوجيه إلى لوحة التحكم بعد تأخير قصير للتأكد من حفظ البيانات
                console.log('تم تسجيل الدخول بنجاح، جاري التوجيه إلى لوحة التحكم');
                setTimeout(() => {
                    window.location.href = 'admin.html';
                }, 100);
            })
            .catch(error => {
                console.error('خطأ في تسجيل الدخول:', error);
                showError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
                hideLoading();
            });
        });
    </script>
</body>
</html>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:background="@drawable/nav_header_background"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:contentDescription="@string/app_name"
        android:paddingTop="8dp"
        app:srcCompat="@mipmap/ic_launcher_round" />

    <TextView
        android:id="@+id/nav_header_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:text="@string/app_name"
        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/nav_header_email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/email" />

</LinearLayout>

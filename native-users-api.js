// خادم HTTP أساسي للتعامل مع واجهة برمجة التطبيقات لإدارة المستخدمين
const http = require('http');
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const url = require('url');

// إعداد اتصال قاعدة البيانات
const dbConfig = {
  host: 'localhost',
  port: '5432',
  database: 'yemen_nav',
  user: 'yemen',
  password: 'yemen123' // استخدم كلمة المرور الصحيحة هنا
};

console.log('جاري الاتصال بقاعدة البيانات:', {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  user: dbConfig.user
});

const pool = new Pool(dbConfig);

// اختبار الاتصال بقاعدة البيانات
pool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', err);
  } else {
    console.log('تم الاتصال بقاعدة البيانات بنجاح:', res.rows[0]);
  }
});

// إنشاء خادم HTTP
const server = http.createServer(async (req, res) => {
  // إعداد CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // التعامل مع طلبات OPTIONS
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // تحليل URL
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  // سجل الطلب
  console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
  
  // دالة لقراءة بيانات الطلب
  const readRequestBody = () => {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      req.on('end', () => {
        try {
          const data = body ? JSON.parse(body) : {};
          resolve(data);
        } catch (error) {
          reject(error);
        }
      });
      req.on('error', reject);
    });
  };
  
  // دالة للرد بـ JSON
  const sendJsonResponse = (statusCode, data) => {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(data));
  };
  
  // التعامل مع الطلبات حسب المسار والطريقة
  try {
    // نقطة نهاية API الرئيسية
    if (pathname === '/api' && req.method === 'GET') {
      sendJsonResponse(200, { message: 'Welcome to Yemen Nav API' });
      return;
    }
    
    // الحصول على جميع المستخدمين
    if (pathname === '/api/admin/users' && req.method === 'GET') {
      try {
        const result = await pool.query(`
          SELECT u.user_id, u.username, u.email, u.full_name, u.phone, u.profile_image, 
                  u.account_type, r.role_name, u.registration_date, u.last_login, 
                  u.is_active, u.is_verified 
           FROM users u 
           LEFT JOIN roles r ON u.role_id = r.role_id 
           ORDER BY u.registration_date DESC
        `);
        
        sendJsonResponse(200, result.rows);
      } catch (err) {
        console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
        sendJsonResponse(500, { message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
      }
      return;
    }
    
    // إضافة مستخدم جديد
    if (pathname === '/api/admin/users' && req.method === 'POST') {
      try {
        const data = await readRequestBody();
        const { username, password, email, fullName, accountType, roleId } = data;
        
        if (!username || !password) {
          sendJsonResponse(400, { message: 'اسم المستخدم وكلمة المرور مطلوبان' });
          return;
        }
        
        // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
        const userCheck = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
        if (userCheck.rows.length > 0) {
          sendJsonResponse(400, { message: 'اسم المستخدم موجود بالفعل' });
          return;
        }
        
        // إنشاء المستخدم الجديد
        const result = await pool.query(`
          INSERT INTO users (username, password, email, full_name, account_type, role_id, registration_date, is_active) 
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), true) 
          RETURNING user_id, username, email, full_name, account_type, role_id
        `, [username, password, email, fullName, accountType || 'local', roleId || 2]);
        
        const newUser = result.rows[0];
        
        sendJsonResponse(201, { 
          message: 'تم إنشاء المستخدم بنجاح',
          user: {
            userId: newUser.user_id,
            username: newUser.username,
            email: newUser.email,
            fullName: newUser.full_name,
            accountType: newUser.account_type,
            roleId: newUser.role_id
          }
        });
      } catch (err) {
        console.error('خطأ في إنشاء المستخدم:', err.message);
        sendJsonResponse(400, { message: err.message });
      }
      return;
    }
    
    // تحديث مستخدم
    if (pathname.match(/^\/api\/admin\/users\/\d+$/) && req.method === 'PUT') {
      try {
        const userId = pathname.split('/').pop();
        const data = await readRequestBody();
        const { username, email, fullName, password, roleId, isActive } = data;
        
        // التحقق من وجود المستخدم
        const userCheck = await pool.query('SELECT * FROM users WHERE user_id = $1', [userId]);
        if (userCheck.rows.length === 0) {
          sendJsonResponse(404, { message: 'المستخدم غير موجود' });
          return;
        }
        
        // بناء استعلام التحديث
        let query = 'UPDATE users SET ';
        const values = [];
        const updateFields = [];
        let paramIndex = 1;
        
        if (email) {
          updateFields.push(`email = $${paramIndex++}`);
          values.push(email);
        }
        
        if (fullName) {
          updateFields.push(`full_name = $${paramIndex++}`);
          values.push(fullName);
        }
        
        if (password) {
          updateFields.push(`password = $${paramIndex++}`);
          values.push(password);
        }
        
        if (roleId) {
          updateFields.push(`role_id = $${paramIndex++}`);
          values.push(roleId);
        }
        
        if (isActive !== undefined) {
          updateFields.push(`is_active = $${paramIndex++}`);
          values.push(isActive);
        }
        
        // إذا لم يكن هناك حقول للتحديث
        if (updateFields.length === 0) {
          sendJsonResponse(400, { message: 'لم يتم تحديد أي حقول للتحديث' });
          return;
        }
        
        query += updateFields.join(', ');
        query += ` WHERE user_id = $${paramIndex} RETURNING user_id, username, email, full_name, account_type, role_id, is_active`;
        values.push(userId);
        
        // تنفيذ الاستعلام
        const result = await pool.query(query, values);
        const updatedUser = result.rows[0];
        
        sendJsonResponse(200, {
          message: 'تم تحديث المستخدم بنجاح',
          user: {
            userId: updatedUser.user_id,
            username: updatedUser.username,
            email: updatedUser.email,
            fullName: updatedUser.full_name,
            accountType: updatedUser.account_type,
            roleId: updatedUser.role_id,
            isActive: updatedUser.is_active
          }
        });
      } catch (err) {
        console.error('خطأ في تحديث المستخدم:', err.message);
        sendJsonResponse(400, { message: err.message });
      }
      return;
    }
    
    // حذف مستخدم
    if (pathname.match(/^\/api\/admin\/users\/\d+$/) && req.method === 'DELETE') {
      try {
        const userId = pathname.split('/').pop();
        
        // التحقق من وجود المستخدم
        const userCheck = await pool.query('SELECT * FROM users WHERE user_id = $1', [userId]);
        if (userCheck.rows.length === 0) {
          sendJsonResponse(404, { message: 'المستخدم غير موجود' });
          return;
        }
        
        // حذف المستخدم
        await pool.query('DELETE FROM users WHERE user_id = $1', [userId]);
        
        sendJsonResponse(200, { message: 'تم حذف المستخدم بنجاح' });
      } catch (err) {
        console.error('خطأ في حذف المستخدم:', err.message);
        sendJsonResponse(400, { message: err.message });
      }
      return;
    }
    
    // الحصول على قائمة الأدوار
    if (pathname === '/api/admin/roles' && req.method === 'GET') {
      try {
        const result = await pool.query('SELECT * FROM roles ORDER BY role_id');
        sendJsonResponse(200, result.rows);
      } catch (err) {
        console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
        sendJsonResponse(500, { message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
      }
      return;
    }
    
    // تقديم الملفات الثابتة
    if (req.method === 'GET') {
      const filePath = path.join(__dirname, 'public', pathname === '/' ? 'index.html' : pathname);
      
      fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
          // إذا لم يتم العثور على الملف، قم بتقديم index.html
          const indexPath = path.join(__dirname, 'public', 'index.html');
          fs.readFile(indexPath, (err, data) => {
            if (err) {
              sendJsonResponse(500, { message: 'خطأ في قراءة الملف' });
              return;
            }
            
            const contentType = 'text/html';
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
          });
          return;
        }
        
        // قراءة الملف وتقديمه
        fs.readFile(filePath, (err, data) => {
          if (err) {
            sendJsonResponse(500, { message: 'خطأ في قراءة الملف' });
            return;
          }
          
          // تحديد نوع المحتوى بناءً على امتداد الملف
          const extname = path.extname(filePath);
          let contentType = 'text/html';
          
          switch (extname) {
            case '.js':
              contentType = 'text/javascript';
              break;
            case '.css':
              contentType = 'text/css';
              break;
            case '.json':
              contentType = 'application/json';
              break;
            case '.png':
              contentType = 'image/png';
              break;
            case '.jpg':
              contentType = 'image/jpg';
              break;
          }
          
          res.writeHead(200, { 'Content-Type': contentType });
          res.end(data);
        });
      });
      return;
    }
    
    // إذا لم يتم العثور على المسار
    sendJsonResponse(404, { message: 'المسار غير موجود' });
  } catch (error) {
    console.error('خطأ عام:', error);
    sendJsonResponse(500, { message: 'حدث خطأ في الخادم' });
  }
});

// إغلاق الاتصال بقاعدة البيانات عند إيقاف الخادم
function gracefulShutdown() {
  console.log('إغلاق الاتصال بقاعدة البيانات...');
  pool.end(() => {
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
    process.exit(0);
  });
}

// التقاط إشارات إيقاف التشغيل
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// تشغيل الخادم
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// إضافة سجل للتصحيح لمعرفة المسار الذي يتم طلبه
if (req.url.includes('login.html')) {
  console.log('تم طلب صفحة تسجيل الدخول من المسار:', req.url);
  console.log('المسار الكامل للملف:', path.join(__dirname, 'public', req.url.split('?')[0]));
}



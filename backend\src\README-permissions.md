# تعليمات إضافة صلاحيات المستخدمين

هذا الملف يحتوي على تعليمات لإضافة صلاحيات المستخدمين إلى قاعدة البيانات.

## الخطوات

1. تأكد من أن قاعدة البيانات `yemen_gps` موجودة ومتصلة.
2. قم بتنفيذ ملف SQL لإضافة أعمدة الصلاحيات.

## تنفيذ ملف SQL

يمكنك تنفيذ ملف SQL بإحدى الطرق التالية:

### باستخدام psql (سطر الأوامر)

```bash
psql -U yemen -d yemen_gps -f backend/src/add-permissions-columns.sql
```

### باستخدام pgAdmin

1. افتح pgAdmin
2. اتصل بقاعدة البيانات `yemen_gps`
3. افتح أداة Query Tool
4. قم باستيراد ملف `backend/src/add-permissions-columns.sql`
5. قم بتنفيذ الاستعلام

## التحقق من نجاح العملية

بعد تنفيذ الملف، يمكنك التحقق من نجاح العملية بتنفيذ الاستعلام التالي:

```sql
SELECT username, can_access_admin, permissions_json FROM users WHERE username = 'admin';
```

يجب أن ترى أن المستخدم `admin` لديه `can_access_admin = true` وقائمة من الصلاحيات في `permissions_json`.

## ملاحظات

- إذا كنت تستخدم وضع التطوير، فلن تحتاج إلى تنفيذ هذا الملف لأن التطبيق سيعمل بدون التحقق من الصلاحيات.
- في وضع الإنتاج، يجب تنفيذ هذا الملف لضمان عمل نظام الصلاحيات بشكل صحيح.
- كلمة مرور المستخدم `admin` الافتراضية هي: `yemen123`

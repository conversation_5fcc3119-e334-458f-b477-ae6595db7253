// سكريبت تحميل مكثف لجميع أماكن صنعاء من Google Maps
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// إعدادات قاعدة البيانات
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// مفتاح Google API
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';

// مجلد حفظ الصور
const IMAGES_DIR = path.join(__dirname, '..', 'public', 'images', 'places');

// إنشاء مجلد الصور إذا لم يكن موجوداً
if (!fs.existsSync(IMAGES_DIR)) {
    fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

// إحداثيات صنعاء مع مناطق متعددة للتغطية الشاملة
const SANAA_AREAS = [
    { name: 'صنعاء المركز', lat: 15.3547, lng: 44.2066, radius: 15000 },
    { name: 'صنعاء القديمة', lat: 15.3535, lng: 44.2139, radius: 5000 },
    { name: 'شارع الزبيري', lat: 15.3694, lng: 44.2111, radius: 8000 },
    { name: 'شارع الستين', lat: 15.3333, lng: 44.2167, radius: 10000 },
    { name: 'حدة', lat: 15.3889, lng: 44.1833, radius: 12000 },
    { name: 'شعوب', lat: 15.4167, lng: 44.2333, radius: 10000 },
    { name: 'الحصبة', lat: 15.3167, lng: 44.2500, radius: 8000 },
    { name: 'معين', lat: 15.2833, lng: 44.1833, radius: 8000 },
    { name: 'بني الحارث', lat: 15.4500, lng: 44.1500, radius: 15000 },
    { name: 'همدان', lat: 15.2500, lng: 44.3000, radius: 12000 },
    { name: 'أزال', lat: 15.4000, lng: 44.3000, radius: 10000 },
    { name: 'الثورة', lat: 15.3750, lng: 44.2250, radius: 8000 }
];

// جميع أنواع الأماكن في Google Maps
const ALL_PLACE_TYPES = [
    // الأساسية
    'restaurant', 'lodging', 'hospital', 'school', 'university', 'bank', 'atm',
    'gas_station', 'shopping_mall', 'supermarket', 'pharmacy', 'police',
    'fire_station', 'post_office', 'library', 'museum', 'park',

    // السياحة والترفيه
    'tourist_attraction', 'amusement_park', 'aquarium', 'art_gallery',
    'bowling_alley', 'casino', 'movie_theater', 'night_club', 'zoo',

    // الطعام والشراب
    'bakery', 'bar', 'cafe', 'meal_delivery', 'meal_takeaway',

    // الخدمات
    'beauty_salon', 'car_dealer', 'car_rental', 'car_repair', 'car_wash',
    'dentist', 'doctor', 'electrician', 'florist', 'funeral_home',
    'hair_care', 'hardware_store', 'insurance_agency', 'jewelry_store',
    'laundry', 'lawyer', 'locksmith', 'moving_company', 'painter',
    'pet_store', 'plumber', 'real_estate_agency', 'roofing_contractor',
    'shoe_store', 'spa', 'storage', 'store', 'taxi_stand', 'travel_agency',
    'veterinary_care',

    // الدينية
    'church', 'hindu_temple', 'mosque', 'synagogue',

    // النقل
    'airport', 'bus_station', 'light_rail_station', 'subway_station',
    'train_station', 'transit_station',

    // الحكومية
    'city_hall', 'courthouse', 'embassy', 'local_government_office',

    // الصحة
    'physiotherapist', 'gym', 'health',

    // التسوق
    'book_store', 'clothing_store', 'convenience_store', 'department_store',
    'electronics_store', 'furniture_store', 'grocery_or_supermarket',
    'home_goods_store', 'liquor_store', 'shoe_store', 'bicycle_store'
];

class IntensiveSanaaDownloader {
    constructor() {
        this.downloadedCount = 0;
        this.errorCount = 0;
        this.photosCount = 0;
        this.startTime = Date.now();
        this.processedPlaces = new Set(); // لتجنب التكرار
    }

    // البحث المكثف في منطقة معينة
    async intensiveSearch(area, placeType) {
        try {
            console.log(`🔍 البحث المكثف عن ${placeType} في ${area.name}...`);

            const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    location: `${area.lat},${area.lng}`,
                    radius: area.radius,
                    type: placeType,
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                console.log(`   ✅ تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            } else if (response.data.status === 'ZERO_RESULTS') {
                console.log(`   ⚪ لا توجد نتائج لـ ${placeType} في ${area.name}`);
                return [];
            } else {
                console.log(`   ❌ خطأ: ${response.data.status}`);
                return [];
            }
        } catch (error) {
            console.error(`   ❌ خطأ في البحث: ${error.message}`);
            return [];
        }
    }

    // البحث بالكلمات المفتاحية (Text Search)
    async textSearch(area, query) {
        try {
            console.log(`🔍 البحث النصي: "${query}" في ${area.name}...`);

            const url = 'https://maps.googleapis.com/maps/api/place/textsearch/json';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    query: `${query} in Sanaa Yemen`,
                    location: `${area.lat},${area.lng}`,
                    radius: area.radius,
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                console.log(`   ✅ البحث النصي: ${response.data.results.length} نتيجة`);
                return response.data.results;
            }
            return [];
        } catch (error) {
            console.error(`   ❌ خطأ في البحث النصي: ${error.message}`);
            return [];
        }
    }

    // الحصول على تفاصيل مكان
    async getPlaceDetails(placeId) {
        try {
            const url = 'https://maps.googleapis.com/maps/api/place/details/json';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    place_id: placeId,
                    fields: 'name,formatted_address,geometry,photos,rating,reviews,formatted_phone_number,website,opening_hours,types,price_level,user_ratings_total',
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                return response.data.result;
            }
            return null;
        } catch (error) {
            console.error(`   ❌ خطأ في تفاصيل المكان: ${error.message}`);
            return null;
        }
    }

    // تحميل صورة
    async downloadPhoto(photoRef, placeId, index = 0) {
        try {
            const response = await axios.get('https://maps.googleapis.com/maps/api/place/photo', {
                params: {
                    key: GOOGLE_API_KEY,
                    photoreference: photoRef,
                    maxwidth: 1200 // جودة عالية
                },
                responseType: 'stream'
            });

            const fileName = `${placeId.replace(/[^a-zA-Z0-9]/g, '_')}_${index}.jpg`;
            const filePath = path.join(IMAGES_DIR, fileName);

            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    this.photosCount++;
                    console.log(`   📷 صورة ${index + 1}: ${fileName}`);
                    resolve(`/images/places/${fileName}`);
                });
                writer.on('error', reject);
            });
        } catch (error) {
            console.log(`   ❌ فشل تحميل الصورة ${index + 1}: ${error.message}`);
            return null;
        }
    }

    // حفظ مكان في قاعدة البيانات
    async savePlace(place, photos = []) {
        try {
            const query = `
                INSERT INTO places (
                    name_ar, name_en, description_ar, description_en,
                    latitude, longitude, governorate_id, category_id,
                    phone, website, rating, google_place_id,
                    photos, opening_hours, price_level, place_types,
                    source, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW()
                )
                ON CONFLICT (google_place_id) DO UPDATE SET
                    name_ar = EXCLUDED.name_ar,
                    phone = EXCLUDED.phone,
                    website = EXCLUDED.website,
                    rating = EXCLUDED.rating,
                    photos = EXCLUDED.photos,
                    opening_hours = EXCLUDED.opening_hours,
                    price_level = EXCLUDED.price_level,
                    place_types = EXCLUDED.place_types,
                    updated_at = NOW()
                RETURNING id
            `;

            const values = [
                place.name || 'غير محدد',
                place.name || 'Unknown',
                place.formatted_address || '',
                place.formatted_address || '',
                place.geometry?.location?.lat || 0,
                place.geometry?.location?.lng || 0,
                1, // صنعاء
                this.determineCategoryId(place.types),
                place.formatted_phone_number || null,
                place.website || null,
                place.rating || null,
                place.place_id,
                JSON.stringify(photos),
                JSON.stringify(place.opening_hours?.weekday_text || []),
                place.price_level || null,
                JSON.stringify(place.types || []),
                'google_intensive'
            ];

            await pool.query(query, values);
            this.downloadedCount++;
            console.log(`   💾 تم حفظ: ${place.name}`);

        } catch (error) {
            this.errorCount++;
            console.error(`   ❌ خطأ في الحفظ: ${error.message}`);
        }
    }

    // تحديد فئة المكان
    determineCategoryId(types) {
        const categoryMap = {
            'tourist_attraction': 1, 'museum': 1, 'park': 1,
            'mosque': 2, 'church': 2, 'hindu_temple': 2,
            'restaurant': 3, 'cafe': 3, 'bakery': 3,
            'lodging': 4, 'hotel': 4,
            'hospital': 5, 'doctor': 5, 'pharmacy': 5,
            'school': 6, 'university': 6, 'library': 6,
            'bank': 7, 'atm': 7, 'post_office': 7,
            'shopping_mall': 8, 'store': 8, 'supermarket': 8,
            'airport': 9, 'bus_station': 9, 'gas_station': 9
        };

        for (const type of types || []) {
            if (categoryMap[type]) {
                return categoryMap[type];
            }
        }
        return 10; // أخرى
    }

    // معالجة مكان واحد
    async processPlace(place) {
        // تجنب التكرار
        if (this.processedPlaces.has(place.place_id)) {
            return;
        }
        this.processedPlaces.add(place.place_id);

        try {
            console.log(`\n📍 معالجة: ${place.name}`);

            // الحصول على التفاصيل
            const details = await this.getPlaceDetails(place.place_id);
            if (!details) {
                console.log(`   ⚠️ لا توجد تفاصيل إضافية`);
                await this.savePlace(place);
                return;
            }

            // تحميل الصور (حتى 5 صور لكل مكان)
            const photos = [];
            if (details.photos && details.photos.length > 0) {
                const maxPhotos = Math.min(details.photos.length, 5);
                console.log(`   📷 تحميل ${maxPhotos} صور...`);

                for (let i = 0; i < maxPhotos; i++) {
                    const photoPath = await this.downloadPhoto(
                        details.photos[i].photo_reference,
                        place.place_id,
                        i
                    );
                    if (photoPath) {
                        photos.push(photoPath);
                    }
                    await this.delay(200); // تأخير قصير
                }
            }

            // حفظ المكان مع التفاصيل والصور
            await this.savePlace({ ...place, ...details }, photos);

        } catch (error) {
            console.error(`   ❌ خطأ في معالجة المكان: ${error.message}`);
            this.errorCount++;
        }
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // تشغيل التحميل المكثف لصنعاء
    async downloadAllSanaaPlaces() {
        console.log('🚀 بدء التحميل المكثف لجميع أماكن صنعاء...\n');
        console.log(`📍 المناطق المستهدفة: ${SANAA_AREAS.length} منطقة`);
        console.log(`🏷️ أنواع الأماكن: ${ALL_PLACE_TYPES.length} نوع`);
        console.log(`🔍 إجمالي عمليات البحث: ${SANAA_AREAS.length * ALL_PLACE_TYPES.length}`);
        console.log('=' .repeat(60));

        try {
            // البحث حسب النوع في جميع المناطق
            for (const placeType of ALL_PLACE_TYPES) {
                console.log(`\n🏷️ البحث عن: ${placeType}`);

                for (const area of SANAA_AREAS) {
                    // البحث العادي
                    const places = await this.intensiveSearch(area, placeType);

                    // معالجة النتائج
                    for (const place of places) {
                        await this.processPlace(place);
                        await this.delay(300); // تأخير بين المعالجات
                    }

                    await this.delay(500); // تأخير بين المناطق
                }

                await this.delay(1000); // تأخير بين الأنواع
            }

            // البحث النصي الإضافي
            console.log('\n🔍 البحث النصي الإضافي...');
            const textQueries = [
                'مطعم', 'فندق', 'مستشفى', 'مدرسة', 'جامعة', 'بنك', 'صيدلية',
                'مسجد', 'متحف', 'حديقة', 'سوق', 'مول', 'مقهى', 'صالون',
                'ورشة', 'محطة وقود', 'مكتبة', 'عيادة', 'مختبر', 'استوديو'
            ];

            for (const query of textQueries) {
                for (const area of SANAA_AREAS.slice(0, 3)) { // أول 3 مناطق فقط
                    const places = await this.textSearch(area, query);
                    for (const place of places) {
                        await this.processPlace(place);
                        await this.delay(300);
                    }
                    await this.delay(500);
                }
            }

            this.printSummary();

        } catch (error) {
            console.error('❌ خطأ عام في التحميل:', error);
        } finally {
            await pool.end();
        }
    }

    // طباعة ملخص العملية
    printSummary() {
        const duration = Math.round((Date.now() - this.startTime) / 1000);
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor((duration % 3600) / 60);
        const seconds = duration % 60;

        console.log('\n' + '='.repeat(60));
        console.log('📊 ملخص التحميل المكثف لصنعاء');
        console.log('='.repeat(60));
        console.log(`⏱️  الوقت المستغرق: ${hours}س ${minutes}د ${seconds}ث`);
        console.log(`📍 إجمالي الأماكن المحملة: ${this.downloadedCount}`);
        console.log(`📷 إجمالي الصور المحملة: ${this.photosCount}`);
        console.log(`❌ فشل في التحميل: ${this.errorCount}`);
        console.log(`🎯 معدل النجاح: ${((this.downloadedCount / (this.downloadedCount + this.errorCount)) * 100).toFixed(1)}%`);
        console.log(`💾 قاعدة البيانات: yemen_gps`);
        console.log(`📂 مجلد الصور: ${IMAGES_DIR}`);
        console.log('='.repeat(60));
        console.log('🎉 تم إكمال التحميل المكثف لصنعاء بنجاح!');
    }
}

// تشغيل السكريبت
if (require.main === module) {
    const downloader = new IntensiveSanaaDownloader();
    downloader.downloadAllSanaaPlaces();
}

module.exports = IntensiveSanaaDownloader;

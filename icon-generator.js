// Icon Generator Script for Yemen GPS
const fs = require('fs');
const { createCanvas, loadImage } = require('canvas');
const path = require('path');

// Ensure the directory exists
const iconsDir = path.join(__dirname, 'public', 'images', 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Define the sizes required for PWA icons
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create a canvas and draw the icon
async function generateIcon(size) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Draw a green circle (theme color #4CAF50)
  ctx.fillStyle = '#4CAF50';
  ctx.beginPath();
  ctx.arc(size/2, size/2, size/2 - size/20, 0, Math.PI * 2);
  ctx.fill();
  
  // Draw a location crosshair in white
  ctx.strokeStyle = 'white';
  ctx.lineWidth = size / 12;
  ctx.lineCap = 'round';
  
  // Draw an X
  ctx.beginPath();
  // Top-left to bottom-right
  ctx.moveTo(size * 0.3, size * 0.3);
  ctx.lineTo(size * 0.7, size * 0.7);
  // Top-right to bottom-left
  ctx.moveTo(size * 0.7, size * 0.3);
  ctx.lineTo(size * 0.3, size * 0.7);
  ctx.stroke();
  
  // Save the image as PNG
  const buffer = canvas.toBuffer('image/png');
  const filePath = path.join(iconsDir, `icon-${size}x${size}.png`);
  fs.writeFileSync(filePath, buffer);
  console.log(`Generated: ${filePath}`);
}

// Generate a favicon.ico (typically 32x32)
async function generateFavicon() {
  const size = 32;
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Draw a green circle
  ctx.fillStyle = '#4CAF50';
  ctx.beginPath();
  ctx.arc(size/2, size/2, size/2 - 1, 0, Math.PI * 2);
  ctx.fill();
  
  // Draw a location crosshair in white
  ctx.strokeStyle = 'white';
  ctx.lineWidth = size / 12;
  ctx.lineCap = 'round';
  
  // Draw an X
  ctx.beginPath();
  ctx.moveTo(size * 0.3, size * 0.3);
  ctx.lineTo(size * 0.7, size * 0.7);
  ctx.moveTo(size * 0.7, size * 0.3);
  ctx.lineTo(size * 0.3, size * 0.7);
  ctx.stroke();
  
  // Save as favicon.ico in the public directory
  const buffer = canvas.toBuffer('image/png');
  const filePath = path.join(__dirname, 'public', 'favicon.ico');
  fs.writeFileSync(filePath, buffer);
  console.log(`Generated: ${filePath}`);
}

// Generate all the required icons
async function generateAllIcons() {
  console.log('Generating icons for Yemen GPS PWA...');
  
  // Generate all size variants
  for (const size of sizes) {
    await generateIcon(size);
  }
  
  // Generate favicon
  await generateFavicon();
  
  console.log('Icon generation completed!');
}

// Run the generator
generateAllIcons().catch(err => {
  console.error('Error generating icons:', err);
});

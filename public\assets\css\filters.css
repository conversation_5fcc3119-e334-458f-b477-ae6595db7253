/**
 * Yemen GPS - نظام خرائط اليمن
 * تنسيقات لوحة التصفية المتقدمة
 */

/* لوحة التصفية */
.filter-panel {
    position: fixed;
    top: 0;
    right: -360px;
    width: 350px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
    direction: rtl;
}

.filter-panel.active {
    right: 0;
}

/* رأس لوحة التصفية */
.filter-header {
    padding: 15px 20px;
    background-color: #4CAF50;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 1;
}

.filter-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.filter-header-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.filter-header-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* محتوى لوحة التصفية */
.filter-content {
    padding: 15px 20px;
}

.filter-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.filter-section:last-child {
    border-bottom: none;
}

.filter-section h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 500;
    color: #444;
}

/* مربعات اختيار التصنيف */
.category-filters {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.category-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    color: #555;
}

.category-checkbox input {
    margin-left: 8px;
}

/* شريط تمرير التقييم */
.rating-filter {
    padding: 10px 0;
}

input[type="range"] {
    width: 100%;
    height: 5px;
    -webkit-appearance: none;
    background: #e0e0e0;
    outline: none;
    border-radius: 5px;
    margin-bottom: 15px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: #4CAF50;
    border-radius: 50%;
    cursor: pointer;
}

.rating-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stars-display {
    color: #FFC107;
    font-size: 16px;
}

/* المفتاح المنزلق (للاختيار) */
.switch-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
}

.switch-slider {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background-color: #e0e0e0;
    border-radius: 20px;
    transition: all 0.3s;
    margin-left: 10px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    right: 3px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s;
}

input:checked + .switch-slider {
    background-color: #4CAF50;
}

input:checked + .switch-slider:before {
    transform: translateX(-20px);
}

.switch-checkbox input {
    display: none;
}

/* عرض المسافة */
.distance-display {
    text-align: center;
    font-size: 14px;
    color: #555;
    margin-top: -10px;
}

/* أزرار التصفية */
.filter-actions {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #f0f0f0;
    position: sticky;
    bottom: 0;
    background-color: #fff;
    z-index: 1;
}

.primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.primary-btn:hover {
    background-color: #45a049;
}

.secondary-btn {
    background-color: #f5f5f5;
    color: #444;
    border: 1px solid #ddd;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.secondary-btn:hover {
    background-color: #ebebeb;
}

/* زر التصفية في لوحة التحكم */
.filter-btn {
    background-color: white;
    color: #444;
    border: 1px solid #ddd;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    margin-bottom: 10px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
}

.filter-btn:hover {
    background-color: #f5f5f5;
}

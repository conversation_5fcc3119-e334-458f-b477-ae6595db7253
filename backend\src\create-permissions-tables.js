// يمن ناف - ملف لإنشاء جداول الصلاحيات
const { Pool } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: 'yemen',
    password: 'admin',
    host: 'localhost',
    port: 5432,
    database: 'yemen_gps'
};

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// الأوامر SQL لإنشاء الجداول
const createPermissionsTable = `
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
`;

const createRolePermissionsTable = `
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);
`;

const createUserPermissionsTable = `
CREATE TABLE IF NOT EXISTS user_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    is_granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, permission_id)
);
`;

const createTriggers = `
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_permissions_updated_at
BEFORE UPDATE ON permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_permissions_updated_at
BEFORE UPDATE ON user_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
`;

const insertPermissions = `
INSERT INTO permissions (name, code, description) VALUES
    ('إدارة المستخدمين', 'manage_users', 'إنشاء وتعديل وحذف المستخدمين'),
    ('إدارة العملاء', 'manage_clients', 'إنشاء وتعديل وحذف العملاء'),
    ('إدارة المواقع', 'manage_locations', 'إنشاء وتعديل وحذف المواقع'),
    ('إدارة الإعدادات', 'manage_settings', 'تعديل إعدادات النظام'),
    ('عرض لوحة التحكم', 'view_dashboard', 'الوصول إلى لوحة التحكم'),
    ('إدارة النسخ الاحتياطي', 'manage_backups', 'إنشاء واستعادة النسخ الاحتياطي'),
    ('إدارة التصنيفات', 'manage_categories', 'إنشاء وتعديل وحذف التصنيفات'),
    ('إدارة المسارات', 'manage_routes', 'إنشاء وتعديل وحذف المسارات'),
    ('إدارة التقارير', 'manage_reports', 'إنشاء وعرض التقارير'),
    ('إدارة الأدوار', 'manage_roles', 'إنشاء وتعديل وحذف الأدوار والصلاحيات')
ON CONFLICT (code) DO NOTHING;
`;

const assignPermissionsToAdmin = `
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions
ON CONFLICT DO NOTHING;
`;

const assignPermissionsToUser = `
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE code IN ('view_dashboard')
ON CONFLICT DO NOTHING;
`;

const assignPermissionsToDeveloper = `
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions
ON CONFLICT DO NOTHING;
`;

// تنفيذ الأوامر SQL
async function createTables() {
    const client = await pool.connect();
    
    try {
        await client.query('BEGIN');
        
        console.log('إنشاء جدول الصلاحيات...');
        await client.query(createPermissionsTable);
        
        console.log('إنشاء جدول ربط الأدوار بالصلاحيات...');
        await client.query(createRolePermissionsTable);
        
        console.log('إنشاء جدول صلاحيات المستخدمين...');
        await client.query(createUserPermissionsTable);
        
        console.log('إنشاء المحفزات...');
        await client.query(createTriggers);
        
        console.log('إدراج الصلاحيات الأساسية...');
        await client.query(insertPermissions);
        
        console.log('تعيين الصلاحيات للمدير...');
        await client.query(assignPermissionsToAdmin);
        
        console.log('تعيين الصلاحيات للمستخدم العادي...');
        await client.query(assignPermissionsToUser);
        
        console.log('تعيين الصلاحيات للمطور...');
        await client.query(assignPermissionsToDeveloper);
        
        await client.query('COMMIT');
        console.log('تم إنشاء جداول الصلاحيات بنجاح');
    } catch (err) {
        await client.query('ROLLBACK');
        console.error('حدث خطأ أثناء إنشاء جداول الصلاحيات:', err);
        throw err;
    } finally {
        client.release();
    }
}

// تنفيذ الدالة وإغلاق الاتصال
createTables()
    .then(() => {
        console.log('تم الانتهاء من إنشاء جداول الصلاحيات');
        pool.end();
    })
    .catch(err => {
        console.error('فشل إنشاء جداول الصلاحيات:', err);
        pool.end();
        process.exit(1);
    });

// يمن ناف - إنشاء جدول المستخدمين في قاعدة البيانات
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'progsql',
};

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// دالة لإنشاء جدول المستخدمين
async function createUsersTable() {
    try {
        console.log('جاري إنشاء جدول المستخدمين...');
        
        // إنشاء جدول الأدوار أولاً
        const createRolesTableQuery = `
            CREATE TABLE IF NOT EXISTS roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `;
        
        await pool.query(createRolesTableQuery);
        console.log('تم إنشاء جدول الأدوار بنجاح');
        
        // إنشاء جدول المستخدمين
        const createUsersTableQuery = `
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) NOT NULL UNIQUE,
                full_name VARCHAR(100),
                phone VARCHAR(20),
                profile_image VARCHAR(255),
                account_type VARCHAR(20) DEFAULT 'user',
                role_id INTEGER REFERENCES roles(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                is_verified BOOLEAN DEFAULT FALSE
            );
        `;
        
        await pool.query(createUsersTableQuery);
        console.log('تم إنشاء جدول المستخدمين بنجاح');
        
        // إضافة الأدوار الافتراضية
        const insertRolesQuery = `
            INSERT INTO roles (name, description)
            VALUES 
                ('admin', 'مدير النظام مع صلاحيات كاملة'),
                ('user', 'مستخدم عادي مع صلاحيات محدودة'),
                ('developer', 'مطور مع صلاحيات خاصة')
            ON CONFLICT (name) DO NOTHING;
        `;
        
        await pool.query(insertRolesQuery);
        console.log('تم إضافة الأدوار الافتراضية بنجاح');
        
        // إضافة مستخدم المسؤول الافتراضي
        const adminRoleIdQuery = `SELECT id FROM roles WHERE name = 'admin' LIMIT 1;`;
        const adminRoleResult = await pool.query(adminRoleIdQuery);
        const adminRoleId = adminRoleResult.rows[0].id;
        
        const insertAdminQuery = `
            INSERT INTO users (username, password, email, full_name, phone, account_type, role_id, is_active, is_verified)
            VALUES 
                ('admin', '$2b$10$rPQcLj0zHZ6BKX0Y5XG8/.6MQVVnb0yRHx4/4/6qdnCvGBK6o.Zay', '<EMAIL>', 'المسؤول الرئيسي', '*********', 'admin', $1, TRUE, TRUE)
            ON CONFLICT (username) DO NOTHING;
        `;
        
        await pool.query(insertAdminQuery, [adminRoleId]);
        console.log('تم إضافة مستخدم المسؤول الافتراضي بنجاح');
        
        // إضافة مستخدمين للاختبار
        const userRoleIdQuery = `SELECT id FROM roles WHERE name = 'user' LIMIT 1;`;
        const userRoleResult = await pool.query(userRoleIdQuery);
        const userRoleId = userRoleResult.rows[0].id;
        
        const insertTestUsersQuery = `
            INSERT INTO users (username, password, email, full_name, phone, account_type, role_id, is_active, is_verified)
            VALUES 
                ('ahmed2025', '$2b$10$rPQcLj0zHZ6BKX0Y5XG8/.6MQVVnb0yRHx4/4/6qdnCvGBK6o.Zay', '<EMAIL>', 'أحمد محمد', '*********', 'user', $1, TRUE, FALSE),
                ('mohammed123', '$2b$10$rPQcLj0zHZ6BKX0Y5XG8/.6MQVVnb0yRHx4/4/6qdnCvGBK6o.Zay', '<EMAIL>', 'محمد علي', '*********', 'user', $1, FALSE, FALSE)
            ON CONFLICT (username) DO NOTHING;
        `;
        
        await pool.query(insertTestUsersQuery, [userRoleId]);
        console.log('تم إضافة مستخدمين للاختبار بنجاح');
        
        console.log('تم إنشاء وتهيئة جداول المستخدمين بنجاح');
        
    } catch (err) {
        console.error('خطأ في إنشاء جدول المستخدمين:', err.message);
    } finally {
        // إغلاق الاتصال بقاعدة البيانات
        pool.end();
    }
}

// تنفيذ الدالة
createUsersTable();

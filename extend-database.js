/**
 * سكربت توسيع قاعدة بيانات Yemen GPS
 * 
 * هذا السكربت يقوم بإضافة جداول وبيانات إضافية لقاعدة بيانات Yemen GPS
 * يعتمد على وجود قاعدة البيانات الأساسية التي تم إنشاؤها بواسطة setup-database.js
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

// إعدادات الاتصال بقاعدة البيانات باستخدام المستخدم postgres
const postgresConfig = {
    user: 'postgres',
    password: 'yemen123',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

/**
 * دالة لإنشاء جداول إضافية في قاعدة البيانات
 */
async function createAdditionalTables() {
    // استخدام المستخدم postgres بدلاً من yemen للتغلب على مشكلة الصلاحيات
    const client = new Client(postgresConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء جداول إضافية...');
        await client.connect();
        
        console.log('جاري إنشاء الجداول الإضافية...');
        
        // إنشاء جدول إعدادات النظام
        await client.query(`
            CREATE TABLE IF NOT EXISTS system_settings (
                id SERIAL PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول إعدادات النظام.');
        
        // إنشاء جدول الإشعارات
        await client.query(`
            CREATE TABLE IF NOT EXISTS notifications (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(20) DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                related_entity VARCHAR(50),
                related_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الإشعارات.');
        
        // إنشاء جدول تاريخ المواقع (للتتبع)
        await client.query(`
            CREATE TABLE IF NOT EXISTS location_history (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                action VARCHAR(20) NOT NULL,
                old_data JSONB,
                new_data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول تاريخ المواقع.');
        
        // إنشاء جدول صور المواقع
        await client.query(`
            CREATE TABLE IF NOT EXISTS location_images (
                id SERIAL PRIMARY KEY,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                image_url VARCHAR(255) NOT NULL,
                caption TEXT,
                is_primary BOOLEAN DEFAULT FALSE,
                uploaded_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول صور المواقع.');
        
        // إنشاء جدول الأجهزة
        await client.query(`
            CREATE TABLE IF NOT EXISTS devices (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                device_id VARCHAR(100) NOT NULL,
                device_type VARCHAR(50),
                device_name VARCHAR(100),
                device_model VARCHAR(100),
                push_token VARCHAR(255),
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, device_id)
            );
        `);
        console.log('تم إنشاء جدول الأجهزة.');
        
        // إنشاء جدول الجلسات
        await client.query(`
            CREATE TABLE IF NOT EXISTS sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                token VARCHAR(255) UNIQUE NOT NULL,
                device_id VARCHAR(100),
                ip_address VARCHAR(45),
                user_agent TEXT,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الجلسات.');
        
        // إنشاء جدول المناطق الجغرافية
        await client.query(`
            CREATE TABLE IF NOT EXISTS geo_areas (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(20) NOT NULL, -- city, district, neighborhood
                parent_id INTEGER REFERENCES geo_areas(id) ON DELETE SET NULL,
                boundaries JSONB,
                center_lat DECIMAL(10, 8),
                center_lng DECIMAL(11, 8),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول المناطق الجغرافية.');
        
        // إنشاء جدول الإعلانات
        await client.query(`
            CREATE TABLE IF NOT EXISTS advertisements (
                id SERIAL PRIMARY KEY,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                image_url VARCHAR(255),
                link_url VARCHAR(255),
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                status VARCHAR(20) DEFAULT 'active',
                position VARCHAR(50),
                client_id INTEGER REFERENCES clients(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الإعلانات.');
        
        // إنشاء جدول الأحداث
        await client.query(`
            CREATE TABLE IF NOT EXISTS events (
                id SERIAL PRIMARY KEY,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL,
                custom_location TEXT,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP,
                organizer VARCHAR(100),
                contact_info TEXT,
                image_url VARCHAR(255),
                status VARCHAR(20) DEFAULT 'upcoming',
                created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الأحداث.');
        
        console.log('تم إنشاء جميع الجداول الإضافية بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الجداول الإضافية:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء الفهارس (Indexes) لتحسين أداء قاعدة البيانات
 */
async function createIndexes() {
    // استخدام المستخدم postgres بدلاً من yemen للتغلب على مشكلة الصلاحيات
    const client = new Client(postgresConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء الفهارس...');
        await client.connect();
        
        console.log('جاري إنشاء الفهارس لتحسين الأداء...');
        
        // فهارس لجدول المستخدمين
        await client.query(`CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);`);
        
        // فهارس لجدول المواقع
        await client.query(`CREATE INDEX IF NOT EXISTS idx_locations_category_id ON locations(category_id);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_locations_status ON locations(status);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_locations_added_by ON locations(added_by);`);
        
        // فهرس للبحث الجغرافي
        await client.query(`CREATE INDEX IF NOT EXISTS idx_locations_coords ON locations(lat, lng);`);
        
        // فهارس لجدول التقييمات
        await client.query(`CREATE INDEX IF NOT EXISTS idx_reviews_location_id ON reviews(location_id);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON reviews(user_id);`);
        
        // فهارس لجدول المفضلة
        await client.query(`CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites(user_id);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_favorites_location_id ON favorites(location_id);`);
        
        // فهارس لجدول الإشعارات
        await client.query(`CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);`);
        
        // فهارس لجدول الجلسات
        await client.query(`CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);`);
        await client.query(`CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);`);
        
        console.log('تم إنشاء جميع الفهارس بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الفهارس:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء الدوال والإجراءات المخزنة في قاعدة البيانات
 */
async function createStoredProcedures() {
    // استخدام المستخدم postgres بدلاً من yemen للتغلب على مشكلة الصلاحيات
    const client = new Client(postgresConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء الإجراءات المخزنة...');
        await client.connect();
        
        console.log('جاري إنشاء الدوال والإجراءات المخزنة...');
        
        // دالة لحساب متوسط التقييم لموقع معين
        await client.query(`
            CREATE OR REPLACE FUNCTION calculate_average_rating(location_id_param INTEGER)
            RETURNS DECIMAL AS $$
            DECLARE
                avg_rating DECIMAL;
            BEGIN
                SELECT COALESCE(AVG(rating), 0) INTO avg_rating
                FROM reviews
                WHERE location_id = location_id_param;
                
                RETURN avg_rating;
            END;
            $$ LANGUAGE plpgsql;
        `);
        console.log('تم إنشاء دالة حساب متوسط التقييم.');
        
        // دالة لتحديث تاريخ آخر تحديث للموقع
        await client.query(`
            CREATE OR REPLACE FUNCTION update_location_timestamp()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);
        console.log('تم إنشاء دالة تحديث تاريخ التحديث.');
        
        // إنشاء المحفز (Trigger) لتحديث تاريخ التحديث تلقائيًا
        await client.query(`
            DROP TRIGGER IF EXISTS update_location_timestamp_trigger ON locations;
            CREATE TRIGGER update_location_timestamp_trigger
            BEFORE UPDATE ON locations
            FOR EACH ROW
            EXECUTE FUNCTION update_location_timestamp();
        `);
        console.log('تم إنشاء محفز تحديث تاريخ التحديث.');
        
        // دالة لإضافة سجل في تاريخ المواقع عند التعديل
        await client.query(`
            CREATE OR REPLACE FUNCTION log_location_changes()
            RETURNS TRIGGER AS $$
            BEGIN
                INSERT INTO location_history (
                    user_id,
                    location_id,
                    action,
                    old_data,
                    new_data
                ) VALUES (
                    current_setting('app.current_user_id', true)::INTEGER,
                    NEW.id,
                    CASE
                        WHEN TG_OP = 'INSERT' THEN 'create'
                        WHEN TG_OP = 'UPDATE' THEN 'update'
                        WHEN TG_OP = 'DELETE' THEN 'delete'
                    END,
                    CASE
                        WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN row_to_json(OLD)
                        ELSE NULL
                    END,
                    CASE
                        WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN row_to_json(NEW)
                        ELSE NULL
                    END
                );
                
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;
        `);
        console.log('تم إنشاء دالة تسجيل تغييرات المواقع.');
        
        // إنشاء المحفزات (Triggers) لتسجيل تغييرات المواقع
        await client.query(`
            DROP TRIGGER IF EXISTS log_location_insert_trigger ON locations;
            CREATE TRIGGER log_location_insert_trigger
            AFTER INSERT ON locations
            FOR EACH ROW
            EXECUTE FUNCTION log_location_changes();
            
            DROP TRIGGER IF EXISTS log_location_update_trigger ON locations;
            CREATE TRIGGER log_location_update_trigger
            AFTER UPDATE ON locations
            FOR EACH ROW
            EXECUTE FUNCTION log_location_changes();
            
            DROP TRIGGER IF EXISTS log_location_delete_trigger ON locations;
            CREATE TRIGGER log_location_delete_trigger
            AFTER DELETE ON locations
            FOR EACH ROW
            EXECUTE FUNCTION log_location_changes();
        `);
        console.log('تم إنشاء محفزات تسجيل تغييرات المواقع.');
        
        console.log('تم إنشاء جميع الدوال والإجراءات المخزنة بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الدوال والإجراءات المخزنة:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإدخال بيانات إضافية في الجداول
 */
async function insertAdditionalData() {
    // استخدام المستخدم postgres بدلاً من yemen للتغلب على مشكلة الصلاحيات
    const client = new Client(postgresConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإدخال بيانات إضافية...');
        await client.connect();
        
        console.log('جاري إدخال البيانات الإضافية...');
        
        // إدخال إعدادات النظام الافتراضية
        await client.query(`
            INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public)
            VALUES 
                ('site_name', 'Yemen GPS', 'string', 'اسم الموقع', true),
                ('site_description', 'نظام تحديد المواقع اليمني', 'string', 'وصف الموقع', true),
                ('contact_email', '<EMAIL>', 'string', 'البريد الإلكتروني للتواصل', true),
                ('contact_phone', '+967 123456789', 'string', 'رقم الهاتف للتواصل', true),
                ('default_latitude', '15.3694', 'float', 'خط العرض الافتراضي (صنعاء)', true),
                ('default_longitude', '44.1910', 'float', 'خط الطول الافتراضي (صنعاء)', true),
                ('default_zoom', '12', 'integer', 'مستوى التكبير الافتراضي للخريطة', true),
                ('map_provider', 'google', 'string', 'مزود خدمة الخرائط (google, openstreetmap)', false),
                ('google_maps_api_key', '', 'string', 'مفتاح واجهة برمجة تطبيقات خرائط Google', false),
                ('enable_registration', 'true', 'boolean', 'تمكين تسجيل مستخدمين جدد', false),
                ('enable_reviews', 'true', 'boolean', 'تمكين التقييمات', true),
                ('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', false),
                ('items_per_page', '20', 'integer', 'عدد العناصر في كل صفحة', true)
            ON CONFLICT (setting_key) DO NOTHING;
        `);
        console.log('تم إدخال إعدادات النظام الافتراضية.');
        
        // إدخال بيانات المناطق الجغرافية الأساسية (المحافظات الرئيسية)
        await client.query(`
            INSERT INTO geo_areas (name, type, center_lat, center_lng)
            VALUES 
                ('صنعاء', 'city', 15.3694, 44.1910),
                ('عدن', 'city', 12.7797, 45.0095),
                ('تعز', 'city', 13.5789, 44.0178),
                ('الحديدة', 'city', 14.7979, 42.9532),
                ('المكلا', 'city', 14.5393, 49.1275),
                ('ذمار', 'city', 14.5426, 44.4018),
                ('إب', 'city', 13.9674, 44.1825),
                ('عمران', 'city', 15.6594, 43.9430),
                ('صعدة', 'city', 16.9398, 43.7602),
                ('حضرموت', 'city', 15.9266, 48.7911)
            ON CONFLICT DO NOTHING;
        `);
        console.log('تم إدخال بيانات المناطق الجغرافية الأساسية.');
        
        console.log('تم إدخال جميع البيانات الإضافية بنجاح!');
    } catch (err) {
        console.error('خطأ في إدخال البيانات الإضافية:', err.message);
}

/**
 * دالة لإنشاء نسخة احتياطية من قاعدة البيانات
 */
async function backupDatabase() {
// استخدام المستخدم postgres بدلاً من yemen للتغلب على مشكلة الصلاحيات
const client = new Client(postgresConfig);
    
try {
console.log('جاري إنشاء نسخة احتياطية من قاعدة البيانات...');
    try {
        console.log('جاري إنشاء نسخة احتياطية من قاعدة البيانات...');
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupDir = path.join(__dirname, 'backups');
        const backupFile = path.join(backupDir, `yemen_gps_backup_${timestamp}.sql`);
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }
        
        // إنشاء أمر pg_dump لعمل نسخة احتياطية
        const command = `pg_dump -h ${dbConfig.host} -p ${dbConfig.port} -U ${dbConfig.user} -F p -f "${backupFile}" ${dbConfig.database}`;
        
        console.log(`جاري تنفيذ أمر النسخ الاحتياطي: ${command}`);
        
        // تنفيذ الأمر باستخدام child_process
        const { exec } = require('child_process');
        
        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`خطأ في إنشاء النسخة الاحتياطية: ${error.message}`);
                return;
            }
            
            if (stderr) {
                console.error(`خطأ: ${stderr}`);
                return;
            }
            
            console.log(`تم إنشاء النسخة الاحتياطية بنجاح في: ${backupFile}`);
        });
    } catch (err) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', err.message);
        throw err;
    }
}

/**
 * الدالة الرئيسية لتنفيذ جميع الخطوات
 */
async function extendDatabase() {
    try {
        console.log('بدء توسيع قاعدة البيانات Yemen GPS...');
        
        // إنشاء الجداول الإضافية
        await createAdditionalTables();
        
        // إنشاء الفهارس
        await createIndexes();
        
        // إنشاء الدوال والإجراءات المخزنة
        await createStoredProcedures();
        
        // إدخال البيانات الإضافية
        await insertAdditionalData();
        
        // إنشاء نسخة احتياطية من قاعدة البيانات
        await backupDatabase();
        
        console.log('تم توسيع قاعدة البيانات Yemen GPS بنجاح!');
    } catch (err) {
        console.error('حدث خطأ أثناء توسيع قاعدة البيانات:', err);
        process.exit(1);
    }
}

// تنفيذ الدالة الرئيسية
extendDatabase();

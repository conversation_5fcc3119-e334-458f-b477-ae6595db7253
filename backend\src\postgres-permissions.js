// يمن ناف - ملف إدارة الصلاحيات باستخدام قاعدة بيانات PostgreSQL
const db = require('./postgres-db');

// دالة للحصول على جميع الصلاحيات
async function getAllPermissions() {
    try {
        const query = `
            SELECT 
                id,
                name,
                code,
                description,
                created_at,
                updated_at
            FROM permissions
            ORDER BY id
        `;
        
        const result = await db.query(query);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على الصلاحيات:', error);
        throw error;
    }
}

// دالة للحصول على صلاحيات دور معين
async function getRolePermissions(roleId) {
    try {
        const query = `
            SELECT 
                p.id,
                p.name,
                p.code,
                p.description
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
            ORDER BY p.id
        `;
        
        const result = await db.query(query, [roleId]);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على صلاحيات الدور:', error);
        throw error;
    }
}

// دالة للحصول على صلاحيات مستخدم معين (من الدور + الصلاحيات الخاصة)
async function getUserPermissions(userId) {
    try {
        // الحصول على معرف دور المستخدم
        const userQuery = `
            SELECT role_id FROM users WHERE id = $1
        `;
        const userResult = await db.query(userQuery, [userId]);
        
        if (userResult.rows.length === 0) {
            throw new Error('المستخدم غير موجود');
        }
        
        const roleId = userResult.rows[0].role_id;
        
        // الحصول على صلاحيات الدور
        const rolePermissionsQuery = `
            SELECT 
                p.id,
                p.name,
                p.code,
                p.description,
                TRUE as is_granted
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = $1
        `;
        
        const rolePermissionsResult = await db.query(rolePermissionsQuery, [roleId]);
        
        // الحصول على الصلاحيات الخاصة بالمستخدم
        const userPermissionsQuery = `
            SELECT 
                p.id,
                p.name,
                p.code,
                p.description,
                up.is_granted
            FROM permissions p
            JOIN user_permissions up ON p.id = up.permission_id
            WHERE up.user_id = $1
        `;
        
        const userPermissionsResult = await db.query(userPermissionsQuery, [userId]);
        
        // دمج الصلاحيات مع إعطاء الأولوية للصلاحيات الخاصة بالمستخدم
        const rolePermissions = rolePermissionsResult.rows;
        const userPermissions = userPermissionsResult.rows;
        
        // إنشاء قاموس للصلاحيات الخاصة بالمستخدم للبحث السريع
        const userPermissionsMap = {};
        userPermissions.forEach(permission => {
            userPermissionsMap[permission.code] = permission;
        });
        
        // دمج الصلاحيات
        const mergedPermissions = rolePermissions.map(permission => {
            // إذا كان هناك صلاحية خاصة بالمستخدم، استخدمها
            if (userPermissionsMap[permission.code]) {
                return userPermissionsMap[permission.code];
            }
            // وإلا استخدم صلاحية الدور
            return permission;
        });
        
        // إضافة أي صلاحيات خاصة بالمستخدم غير موجودة في صلاحيات الدور
        userPermissions.forEach(permission => {
            const exists = mergedPermissions.some(p => p.code === permission.code);
            if (!exists) {
                mergedPermissions.push(permission);
            }
        });
        
        return mergedPermissions.filter(permission => permission.is_granted);
    } catch (error) {
        console.error('خطأ في الحصول على صلاحيات المستخدم:', error);
        throw error;
    }
}

// دالة للتحقق مما إذا كان المستخدم يملك صلاحية معينة
async function hasPermission(userId, permissionCode) {
    try {
        const permissions = await getUserPermissions(userId);
        return permissions.some(permission => permission.code === permissionCode);
    } catch (error) {
        console.error('خطأ في التحقق من صلاحية المستخدم:', error);
        return false;
    }
}

// دالة لإضافة صلاحية جديدة
async function createPermission(permissionData) {
    try {
        const { name, code, description } = permissionData;
        
        const query = `
            INSERT INTO permissions (name, code, description)
            VALUES ($1, $2, $3)
            RETURNING id, name, code, description
        `;
        
        const result = await db.query(query, [name, code, description]);
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في إنشاء صلاحية جديدة:', error);
        throw error;
    }
}

// دالة لتحديث صلاحية
async function updatePermission(permissionId, permissionData) {
    try {
        const { name, description } = permissionData;
        
        const query = `
            UPDATE permissions
            SET name = $1, description = $2
            WHERE id = $3
            RETURNING id, name, code, description
        `;
        
        const result = await db.query(query, [name, description, permissionId]);
        
        if (result.rows.length === 0) {
            throw new Error('الصلاحية غير موجودة');
        }
        
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في تحديث الصلاحية:', error);
        throw error;
    }
}

// دالة لإضافة صلاحية لدور
async function addPermissionToRole(roleId, permissionId) {
    try {
        const query = `
            INSERT INTO role_permissions (role_id, permission_id)
            VALUES ($1, $2)
            ON CONFLICT (role_id, permission_id) DO NOTHING
            RETURNING id
        `;
        
        const result = await db.query(query, [roleId, permissionId]);
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في إضافة صلاحية للدور:', error);
        throw error;
    }
}

// دالة لإزالة صلاحية من دور
async function removePermissionFromRole(roleId, permissionId) {
    try {
        const query = `
            DELETE FROM role_permissions
            WHERE role_id = $1 AND permission_id = $2
            RETURNING id
        `;
        
        const result = await db.query(query, [roleId, permissionId]);
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في إزالة صلاحية من الدور:', error);
        throw error;
    }
}

// دالة لإضافة صلاحية خاصة لمستخدم
async function addPermissionToUser(userId, permissionId, isGranted = true) {
    try {
        const query = `
            INSERT INTO user_permissions (user_id, permission_id, is_granted)
            VALUES ($1, $2, $3)
            ON CONFLICT (user_id, permission_id) 
            DO UPDATE SET is_granted = $3
            RETURNING id
        `;
        
        const result = await db.query(query, [userId, permissionId, isGranted]);
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في إضافة صلاحية للمستخدم:', error);
        throw error;
    }
}

// دالة لإزالة صلاحية خاصة من مستخدم
async function removePermissionFromUser(userId, permissionId) {
    try {
        const query = `
            DELETE FROM user_permissions
            WHERE user_id = $1 AND permission_id = $2
            RETURNING id
        `;
        
        const result = await db.query(query, [userId, permissionId]);
        return result.rows[0];
    } catch (error) {
        console.error('خطأ في إزالة صلاحية من المستخدم:', error);
        throw error;
    }
}

module.exports = {
    getAllPermissions,
    getRolePermissions,
    getUserPermissions,
    hasPermission,
    createPermission,
    updatePermission,
    addPermissionToRole,
    removePermissionFromRole,
    addPermissionToUser,
    removePermissionFromUser
};

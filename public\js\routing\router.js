// public/js/routing/router.js
class Router {
  constructor(roadNetwork) {
    this.roadNetwork = roadNetwork;
    this.astar = new AStarRouter(roadNetwork);
    this.hazards = new Map(); // خريطة للمخاطر على الطرق
  }
  
  // حساب المسار بين نقطتين
  calculateRoute(startLat, startLng, endLat, endLng, routeType = 'car') {
    // البحث عن أقرب عقد إلى نقاط البداية والنهاية
    const startNode = this.roadNetwork.findNearestNode(startLat, startLng);
    const endNode = this.roadNetwork.findNearestNode(endLat, endLng);
    
    if (!startNode || !endNode) {
      throw new Error('لم يتم العثور على أقرب عقد');
    }
    
    // تعديل الشبكة بناءً على نوع المسار
    const modifiedNetwork = this.getModifiedNetworkForRouteType(routeType);
    const routeAstar = new AStarRouter(modifiedNetwork);
    
    // حساب المسار باستخدام خوارزمية A*
    const path = routeAstar.findPath(startNode, endNode);
    
    if (!path) {
      throw new Error('لم يتم العثور على مسار');
    }
    
    // تحويل المسار إلى تنسيق GeoJSON
    return this.pathToGeoJSON(path, routeType);
  }
  
  // الحصول على شبكة طرق معدلة بناءً على نوع المسار
  getModifiedNetworkForRouteType(routeType) {
    // إنشاء نسخة من شبكة الطرق
    const modifiedNetwork = new RoadNetwork();
    
    // نسخ العقد
    for (const [id, node] of this.roadNetwork.nodes.entries()) {
      modifiedNetwork.addNode(id, node.lat, node.lng);
    }
    
    // نسخ الحواف مع تعديل الأوزان بناءً على نوع المسار
    for (const [fromId, edges] of this.roadNetwork.edges.entries()) {
      for (const edge of edges) {
        let speedFactor = 1;
        let accessFactor = 1;
        
        // تعديل العوامل بناءً على نوع المسار ونوع الطريق
        if (routeType === 'walking') {
          // المشاة يتحركون بسرعة ثابتة تقريبًا على جميع أنواع الطرق
          speedFactor = 0.2; // سرعة المشي أبطأ من السيارات
          
          // المشاة يفضلون الطرق الفرعية والسكنية
          if (edge.roadType === 'primary') {
            accessFactor = 1.5; // الطرق الرئيسية أقل تفضيلاً للمشاة
          } else if (edge.roadType === 'secondary') {
            accessFactor = 1.2; // الطرق الثانوية متوسطة التفضيل للمشاة
          } else if (edge.roadType === 'tertiary' || edge.roadType === 'residential') {
            accessFactor = 0.8; // الطرق الفرعية والسكنية مفضلة للمشاة
          }
        } else { // car
          // السيارات تتحرك بسرعات مختلفة على أنواع الطرق المختلفة
          if (edge.roadType === 'primary') {
            speedFactor = 1.5; // الطرق الرئيسية أسرع للسيارات
          } else if (edge.roadType === 'secondary') {
            speedFactor = 1.2; // الطرق الثانوية متوسطة السرعة
          } else if (edge.roadType === 'tertiary') {
            speedFactor = 1.0; // الطرق الفرعية عادية السرعة
          } else if (edge.roadType === 'residential') {
            speedFactor = 0.8; // الطرق السكنية أبطأ للسيارات
          }
        }
        
        // حساب المسافة المعدلة بناءً على العوامل
        const adjustedDistance = edge.distance * accessFactor / speedFactor;
        
        // إضافة الحافة المعدلة
        modifiedNetwork.addEdge(edge.from, edge.to, {
          ...edge,
          distance: adjustedDistance,
          routeType: routeType
        });
      }
    }
    
    return modifiedNetwork;
  }
  
  // حساب مسارات بديلة
  calculateAlternativeRoutes(startLat, startLng, endLat, endLng, numAlternatives = 2, routeType = 'car') {
    const routes = [];
    
    // حساب المسار الرئيسي
    const mainRoute = this.calculateRoute(startLat, startLng, endLat, endLng, routeType);
    routes.push(mainRoute);
    
    // حساب المسارات البديلة
    for (let i = 0; i < numAlternatives; i++) {
      try {
        // تعديل شبكة الطرق مؤقتًا لتجنب بعض الطرق في المسار الرئيسي
        const modifiedNetwork = this.createModifiedNetwork(mainRoute, i);
        const modifiedRouter = new Router(modifiedNetwork);
        
        // حساب مسار بديل
        const alternativeRoute = modifiedRouter.calculateRoute(startLat, startLng, endLat, endLng);
        
        // التحقق من أن المسار البديل مختلف بما فيه الكفاية
        if (this.isRouteDifferentEnough(alternativeRoute, routes)) {
          routes.push(alternativeRoute);
        }
      } catch (error) {
        console.error('Error calculating alternative route:', error);
      }
    }
    
    return routes;
  }
  
  // إنشاء شبكة طرق معدلة لحساب المسارات البديلة
  createModifiedNetwork(mainRoute, alternativeIndex) {
    // إنشاء نسخة من شبكة الطرق
    const modifiedNetwork = new RoadNetwork();
    
    // نسخ العقد
    for (const [id, node] of this.roadNetwork.nodes.entries()) {
      modifiedNetwork.addNode(id, node.lat, node.lng);
    }
    
    // نسخ الحواف مع تعديل أوزان بعض الطرق
    for (const [fromId, edges] of this.roadNetwork.edges.entries()) {
      for (const edge of edges) {
        // التحقق مما إذا كانت الحافة جزءًا من المسار الرئيسي
        const isOnMainRoute = this.isEdgeOnRoute(edge, mainRoute);
        
        // إضافة الحافة مع تعديل الوزن إذا كانت جزءًا من المسار الرئيسي
        if (isOnMainRoute) {
          // زيادة الوزن بشكل مختلف لكل مسار بديل
          const penaltyFactor = 1.5 + alternativeIndex * 0.5;
          
          modifiedNetwork.addEdge(edge.from, edge.to, {
            ...edge,
            // زيادة المسافة لتجنب هذه الطريق
            distance: edge.distance * penaltyFactor
          });
        } else {
          modifiedNetwork.addEdge(edge.from, edge.to, edge);
        }
      }
    }
    
    return modifiedNetwork;
  }
  
  // التحقق مما إذا كانت الحافة جزءًا من المسار
  isEdgeOnRoute(edge, route) {
    const coordinates = route.geometry.coordinates;
    
    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lng1, lat1] = coordinates[i];
      const [lng2, lat2] = coordinates[i + 1];
      
      const fromNode = this.roadNetwork.findNearestNode(lat1, lng1);
      const toNode = this.roadNetwork.findNearestNode(lat2, lng2);
      
      if (fromNode && toNode && 
          ((edge.from === fromNode.id && edge.to === toNode.id) ||
           (edge.from === toNode.id && edge.to === fromNode.id))) {
        return true;
      }
    }
    
    return false;
  }
  
  // التحقق مما إذا كان المسار مختلفًا بما فيه الكفاية عن المسارات الأخرى
  isRouteDifferentEnough(route, existingRoutes) {
    const minDifferenceRatio = 0.3; // نسبة الاختلاف المطلوبة (30%)
    
    for (const existingRoute of existingRoutes) {
      const differenceRatio = this.calculateRouteDifference(route, existingRoute);
      
      if (differenceRatio < minDifferenceRatio) {
        return false;
      }
    }
    
    return true;
  }
  
  // حساب نسبة الاختلاف بين مسارين
  calculateRouteDifference(route1, route2) {
    const coords1 = route1.geometry.coordinates;
    const coords2 = route2.geometry.coordinates;
    
    // عدد النقاط المختلفة
    let differentPoints = 0;
    
    // مجموعة النقاط في المسار الثاني
    const coords2Set = new Set(coords2.map(coord => `${coord[0]},${coord[1]}`));
    
    // التحقق من كل نقطة في المسار الأول
    for (const coord of coords1) {
      const coordKey = `${coord[0]},${coord[1]}`;
      
      if (!coords2Set.has(coordKey)) {
        differentPoints++;
      }
    }
    
    // حساب نسبة الاختلاف
    return differentPoints / coords1.length;
  }
  
  // تحويل المسار إلى تنسيق GeoJSON
  pathToGeoJSON(path, routeType = 'car') {
    const coordinates = path.map(node => [node.x, node.y]);
    
    // حساب المسافة الإجمالية
    let totalDistance = 0;
    for (let i = 0; i < path.length - 1; i++) {
      totalDistance += this.roadNetwork.getDistance(path[i], path[i + 1]);
    }
    
    // تقدير الوقت بناءً على نوع المسار
    let estimatedTime;
    if (routeType === 'walking') {
      // المشي: 12 دقيقة لكل كيلومتر (5 كم/ساعة)
      estimatedTime = Math.round(totalDistance * 12);
    } else { // car
      // السيارة: دقيقتان لكل كيلومتر (30 كم/ساعة)
      estimatedTime = Math.round(totalDistance * 2);
    }
    
    // إنشاء خطوات المسار
    const steps = this.createRouteSteps(path, routeType);
    
    return {
      type: 'Feature',
      geometry: {
        type: 'LineString',
        coordinates
      },
      properties: {
        distance: totalDistance,
        duration: estimatedTime,
        steps
      }
    };
  }
  
  // إنشاء خطوات المسار
  createRouteSteps(path, routeType = 'car') {
    const steps = [];
    
    for (let i = 0; i < path.length - 1; i++) {
      const currentNode = path[i];
      const nextNode = path[i + 1];
      
      // حساب المسافة بين العقدتين
      const distance = this.roadNetwork.getDistance(currentNode, nextNode);
      
      // الحصول على معلومات الطريق
      const edges = this.roadNetwork.edges.get(currentNode.id);
      let edge = null;
      
      if (edges) {
        edge = edges.find(e => e.to === nextNode.id);
      }
      
      // حساب الاتجاه
      const bearing = this.calculateBearing(currentNode, nextNode);
      const direction = this.getBearingDirection(bearing);
      
      // حساب الوقت بناءً على نوع المسار
      let duration;
      if (routeType === 'walking') {
        // المشي: 12 دقيقة لكل كيلومتر (5 كم/ساعة)
        duration = distance * 12;
      } else { // car
        // السيارة: دقيقتان لكل كيلومتر (30 كم/ساعة)
        duration = distance * 2;
      }
      
      // إنشاء خطوة
      steps.push({
        distance: distance,
        duration: duration,
        instruction: this.createInstruction(direction, edge ? edge.name : '', routeType),
        name: edge ? (edge.name || '') : '',
        maneuver: direction
      });
    }
    
    // إضافة خطوة الوصول
    steps.push({
      distance: 0,
      duration: 0,
      instruction: 'لقد وصلت إلى وجهتك',
      name: '',
      maneuver: 'arrive'
    });
    
    return steps;
  }
  
  // حساب الاتجاه بين نقطتين
  calculateBearing(point1, point2) {
    // استخدام lat, lng إذا كانت موجودة، وإلا استخدام y, x
    const lat1 = (point1.lat !== undefined ? point1.lat : point1.y) * Math.PI / 180;
    const lat2 = (point2.lat !== undefined ? point2.lat : point2.y) * Math.PI / 180;
    const lon1 = (point1.lng !== undefined ? point1.lng : point1.x) * Math.PI / 180;
    const lon2 = (point2.lng !== undefined ? point2.lng : point2.x) * Math.PI / 180;
    
    const y = Math.sin(lon2 - lon1) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) -
              Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
    
    let bearing = Math.atan2(y, x) * 180 / Math.PI;
    bearing = (bearing + 360) % 360;
    
    return bearing;
  }
  
  // الحصول على اتجاه البوصلة
  getBearingDirection(bearing) {
    if (bearing >= 337.5 || bearing < 22.5) return 'straight';
    if (bearing >= 22.5 && bearing < 67.5) return 'turn-right';
    if (bearing >= 67.5 && bearing < 112.5) return 'turn-right';
    if (bearing >= 112.5 && bearing < 157.5) return 'turn-right';
    if (bearing >= 157.5 && bearing < 202.5) return 'straight';
    if (bearing >= 202.5 && bearing < 247.5) return 'turn-left';
    if (bearing >= 247.5 && bearing < 292.5) return 'turn-left';
    if (bearing >= 292.5 && bearing < 337.5) return 'turn-left';
    
    return 'straight';
  }
  
  // إنشاء تعليمات للخطوة
  createInstruction(direction, streetName, routeType = 'car') {
    const streetNameText = streetName ? ` إلى ${streetName}` : '';
    
    // تعليمات مختلفة بناءً على نوع المسار
    if (routeType === 'walking') {
      switch (direction) {
        case 'turn-right':
          return `اتجه يمينًا${streetNameText}`;
        case 'turn-left':
          return `اتجه يسارًا${streetNameText}`;
        case 'straight':
          return `استمر في المشي مباشرة${streetNameText}`;
        default:
          return `استمر في المشي${streetNameText}`;
      }
    } else { // car
      switch (direction) {
        case 'turn-right':
          return `انعطف يمينًا${streetNameText}`;
        case 'turn-left':
          return `انعطف يسارًا${streetNameText}`;
        case 'straight':
          return `استمر مباشرة${streetNameText}`;
        default:
          return `استمر${streetNameText}`;
      }
    }
  }
  
  // إضافة مخاطر على الطريق
  addHazard(id, lat, lng, type, description = '') {
    this.hazards.set(id, { id, lat, lng, type, description });
  }
  
  // الحصول على المخاطر القريبة من المسار
  getHazardsNearRoute(route, position, maxDistance = 1) {
    const nearHazards = [];
    
    for (const hazard of this.hazards.values()) {
      // حساب المسافة من الموقع الحالي إلى المخاطر
      const distance = this.calculateDistance(
        position.lat, position.lng,
        hazard.lat, hazard.lng
      );
      
      // التحقق مما إذا كانت المخاطر قريبة بما فيه الكفاية
      if (distance <= maxDistance) {
        // التحقق مما إذا كانت المخاطر على المسار
        if (this.isHazardOnRoute(hazard, route)) {
          nearHazards.push({
            ...hazard,
            distance
          });
        }
      }
    }
    
    // ترتيب المخاطر حسب المسافة
    nearHazards.sort((a, b) => a.distance - b.distance);
    
    return nearHazards;
  }
  
  // التحقق مما إذا كانت المخاطر على المسار
  isHazardOnRoute(hazard, route) {
    const coordinates = route.geometry.coordinates;
    const maxDistance = 0.1; // كيلومتر
    
    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lng1, lat1] = coordinates[i];
      const [lng2, lat2] = coordinates[i + 1];
      
      // حساب المسافة من المخاطر إلى الخط بين نقطتين
      const distance = this.distanceToLine(
        hazard.lat, hazard.lng,
        lat1, lng1,
        lat2, lng2
      );
      
      if (distance <= maxDistance) {
        return true;
      }
    }
    
    return false;
  }
  
  // حساب المسافة من نقطة إلى خط
  distanceToLine(lat, lng, lat1, lng1, lat2, lng2) {
    const A = lat - lat1;
    const B = lng - lng1;
    const C = lat2 - lat1;
    const D = lng2 - lng1;
    
    const dot = A * C + B * D;
    const len_sq = C * C + D * D;
    let param = -1;
    
    if (len_sq != 0) {
      param = dot / len_sq;
    }
    
    let xx, yy;
    
    if (param < 0) {
      xx = lat1;
      yy = lng1;
    } else if (param > 1) {
      xx = lat2;
      yy = lng2;
    } else {
      xx = lat1 + param * C;
      yy = lng1 + param * D;
    }
    
    const dx = lat - xx;
    const dy = lng - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  // حساب المسافة بين نقطتين (بالكيلومتر)
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}

// إضافة الوحدة إلى النافذة العالمية
window.Router = Router;

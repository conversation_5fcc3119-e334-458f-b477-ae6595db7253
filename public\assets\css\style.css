/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    background-color: #f5f5f5;
    overflow: hidden;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 32px;
    height: 32px;
}

.logo-text {
    font-size: 18px;
    font-weight: bold;
    color: #1a73e8;
}

/* Search Container */
.search-container {
    flex: 1;
    max-width: 500px;
    margin: 0 20px;
    position: relative;
}

.search-box {
    display: flex;
    align-items: center;
    background: #f1f3f4;
    border-radius: 24px;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.search-icon {
    color: #5f6368;
    margin-left: 12px;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 16px;
    outline: none;
    padding: 8px 0;
    direction: rtl;
}

.search-btn {
    background: none;
    border: none;
    color: #5f6368;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.search-btn:hover {
    background-color: rgba(0,0,0,0.1);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

/* Header Actions */
.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.3s ease;
}

.action-btn:hover {
    background-color: rgba(0,0,0,0.1);
}

/* Main Content */
.main-content {
    margin-top: 60px;
    height: calc(100vh - 60px);
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #fff;
    border-left: 1px solid #e0e0e0;
    position: fixed;
    top: 60px;
    right: -300px;
    height: calc(100vh - 60px);
    transition: right 0.3s ease;
    z-index: 999;
    overflow-y: auto;
}

.sidebar.open {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.sidebar-header h3 {
    font-size: 18px;
    color: #202124;
}

.close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #5f6368;
    padding: 8px;
    border-radius: 50%;
}

.close-btn:hover {
    background-color: rgba(0,0,0,0.1);
}

/* Map Container */
.map-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.map {
    width: 100%;
    height: 100%;
}

/* إخفاء عناصر Google Maps غير المرغوب فيها */
.gm-style-cc {
    display: none !important;
}

.gmnoprint {
    display: none !important;
}

.gm-style .gm-style-cc {
    display: none !important;
}

.gm-style .gmnoprint {
    display: none !important;
}

/* إخفاء رابط "عرض في خرائط Google" */
.gm-style a[href*="maps.google.com"] {
    display: none !important;
}

/* إخفاء معلومات الإحداثيات */
.gm-style .gm-style-cc span {
    display: none !important;
}

/* إخفاء جميع عناصر التحكم السفلية */
.gm-style .gm-style-cc,
.gm-style .gmnoprint,
.gm-style [title="عرض في خرائط Google"],
.gm-style [title="فتح هذه المنطقة في خرائط Google (في نافذة جديدة)"] {
    display: none !important;
}

/* إخفاء شعار Google */
.gm-style .gm-style-cc img {
    display: none !important;
}

/* إخفاء جميع الروابط في أسفل الخريطة */
.gm-style-cc a {
    display: none !important;
}

/* أنماط نافذة المعلومات */
.info-window-content {
    max-width: 300px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.place-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0 0 5px 0;
}

.place-title-en {
    font-size: 14px;
    color: #666;
    margin: 0 0 10px 0;
    font-style: italic;
}

.place-photos {
    display: flex;
    gap: 5px;
    margin: 10px 0;
    overflow-x: auto;
}

.place-photo {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.place-photo:hover {
    transform: scale(1.05);
}

.more-photos {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 60px;
    background: #f0f0f0;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
}

.place-details p {
    margin: 5px 0;
    font-size: 14px;
}

.place-phone, .place-website, .place-rating, .place-coordinates {
    display: flex;
    align-items: center;
    gap: 5px;
}

.place-phone i, .place-website i, .place-rating i, .place-coordinates i {
    color: #4285f4;
    width: 16px;
}

.place-actions {
    display: flex;
    gap: 8px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.directions-btn {
    background: #4285f4;
    color: white;
}

.directions-btn:hover {
    background: #3367d6;
}

.share-btn {
    background: #34a853;
    color: white;
}

.share-btn:hover {
    background: #2d8f47;
}

.save-btn {
    background: #fbbc04;
    color: #333;
}

.save-btn:hover {
    background: #f9ab00;
}

/* نافذة الصورة المنبثقة */
.photo-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.photo-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.photo-modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.photo-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 30px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* رسائل التنبيه */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.toast {
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    animation: slideIn 0.3s ease;
}

.toast-success {
    background: #4caf50;
}

.toast-error {
    background: #f44336;
}

.toast-warning {
    background: #ff9800;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات للبحث */
.search-result-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: #f5f5f5;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.result-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.result-coordinates {
    font-size: 11px;
    color: #999;
}

.no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Map Controls */
.map-controls {
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 999;
}

.zoom-controls,
.view-controls {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    overflow: hidden;
}

.control-btn {
    background: #fff;
    border: none;
    padding: 12px;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid #e0e0e0;
}

.control-btn:last-child {
    border-bottom: none;
}

.control-btn:hover {
    background-color: #f8f9fa;
}

.control-btn.active {
    background-color: #1a73e8;
    color: #fff;
}

.fullscreen-btn {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Info Window */
.info-window {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 350px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.info-window:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.info-header h4 {
    font-size: 16px;
    color: #202124;
}

.info-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    color: #202124;
}

.back-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.back-btn:hover {
    background-color: #f0f0f0;
}

.info-content {
    padding: 20px;
}

.info-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.info-btn {
    flex: 1;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #5f6368;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.info-btn:hover {
    background-color: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

/* Directions Form in Info Window */
.info-body .directions-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-body .direction-input {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-body .direction-input label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.info-body .direction-input input {
    padding: 12px 40px 12px 12px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    box-sizing: border-box;
}

.info-body .direction-input input:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.info-body .location-btn {
    position: absolute;
    right: 8px;
    top: 28px;
    background: none;
    border: none;
    color: #4285f4;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.info-body .location-btn:hover {
    background-color: #f0f0f0;
}

.info-body .swap-btn {
    align-self: center;
    background: #f8f9fa;
    border: 1px solid #dadce0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    margin: -5px 0;
}

.info-body .swap-btn:hover {
    background: #e8eaed;
    transform: rotate(180deg);
}

.info-body .transport-options {
    display: flex;
    gap: 8px;
    justify-content: space-between;
}

.info-body .transport-btn {
    flex: 1;
    padding: 12px 8px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.info-body .transport-btn:hover {
    background: #f8f9fa;
}

.info-body .transport-btn.active {
    background: #4285f4;
    color: white;
    border-color: #4285f4;
}

.info-body .transport-btn i {
    font-size: 16px;
}

.info-body .route-preferences {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-body .preference-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.info-body .preference-options {
    display: flex;
    gap: 15px;
}

.info-body .preference-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;
}

.info-body .preference-option input[type="radio"] {
    margin: 0;
}

.info-body .calculate-btn {
    padding: 12px;
    background: #4285f4;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.info-body .calculate-btn:hover {
    background: #3367d6;
}

.info-body .calculate-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.info-body .route-results {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.info-body .route-summary {
    margin-bottom: 15px;
}

.info-body .route-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
}

.info-body .route-info strong {
    color: #4285f4;
}

.info-body .route-instructions {
    max-height: 200px;
    overflow-y: auto;
}

.info-body .route-step {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-body .route-step:last-child {
    border-bottom: none;
}

.info-body .step-icon {
    width: 24px;
    height: 24px;
    background: #4285f4;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    flex-shrink: 0;
}

.info-body .step-content {
    flex: 1;
}

.info-body .step-instruction {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.info-body .step-distance {
    font-size: 12px;
    color: #666;
}

/* Layers Panel */
.layers-panel {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 280px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    transform: translateX(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.layers-panel:not(.hidden) {
    transform: translateX(0);
    opacity: 1;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.panel-header h4 {
    font-size: 16px;
    color: #202124;
}

.panel-content {
    padding: 20px;
}

.layer-group {
    margin-bottom: 20px;
}

.layer-group h5 {
    font-size: 14px;
    color: #5f6368;
    margin-bottom: 12px;
}

.layer-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    cursor: pointer;
    font-size: 14px;
    color: #202124;
}

.layer-option input {
    margin: 0;
}

/* Labels layer option - only show for satellite view */
.layer-option.labels-only {
    display: none;
}

.layer-option.labels-only.show {
    display: flex;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255,255,255,0.9);
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background: #323232;
    color: #fff;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 8px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Search Results Styles */
.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-title {
    font-weight: 500;
    color: #202124;
    margin-bottom: 4px;
}

.result-type {
    font-size: 12px;
    color: #5f6368;
}

.no-results {
    padding: 16px;
    text-align: center;
    color: #5f6368;
}

/* Saved Places Styles */
.saved-place {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.saved-place:last-child {
    border-bottom: none;
}

.place-name {
    font-weight: 500;
    color: #202124;
}

.place-date {
    font-size: 12px;
    color: #5f6368;
    margin-top: 2px;
}

.place-actions {
    display: flex;
    gap: 4px;
}

.place-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.3s ease;
}

.place-btn:hover {
    background-color: rgba(0,0,0,0.1);
}

.place-btn.delete:hover {
    background-color: rgba(244,67,54,0.1);
    color: #f44336;
}

/* Current Location Marker */
.current-location-marker {
    background: #4285f4;
    border: 3px solid #fff;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    width: 20px;
    height: 20px;
}

.current-location-marker i {
    font-size: 12px;
}

/* Custom Red Marker */
.custom-red-marker {
    background: transparent;
    border: none;
}

.marker-pin {
    position: relative;
    width: 30px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.marker-pin::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: #ea4335;
    border: 3px solid #fff;
    border-radius: 50% 50% 50% 0;
    transform: translateX(-50%) rotate(-45deg);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.marker-icon {
    position: relative;
    z-index: 2;
    color: #fff;
    font-size: 14px;
    margin-top: -5px;
    margin-left: -1px;
}

.marker-pin .marker-icon i {
    transform: rotate(45deg);
}

/* Location Info Styles */
.location-info p {
    margin: 4px 0;
    font-size: 14px;
    color: #5f6368;
}

.location-info strong {
    color: #202124;
}

/* Location Actions */
.location-actions {
    margin-top: 16px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 80px;
    padding: 8px 12px;
    border: 1px solid #dadce0;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;
    color: #5f6368;
}

.action-btn:hover {
    background-color: #f8f9fa;
    border-color: #4285f4;
    color: #4285f4;
}

.action-btn.primary {
    background-color: #4285f4;
    border-color: #4285f4;
    color: white;
}

.action-btn.primary:hover {
    background-color: #3367d6;
    border-color: #3367d6;
    color: white;
}

.action-btn i {
    font-size: 14px;
}

/* Toast Styles */
.toast.error {
    background: #f44336;
}

.toast.success {
    background: #4caf50;
}

/* Directions Panel */
.directions-panel {
    position: fixed;
    top: 80px;
    left: 20px;
    width: 350px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.directions-panel.hidden {
    transform: translateX(-100%);
}

.directions-form {
    padding: 0;
}

.direction-input {
    position: relative;
    margin-bottom: 15px;
}

.direction-input label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-size: 14px;
    font-weight: 500;
}

.direction-input input {
    width: 100%;
    padding: 12px 40px 12px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.direction-input input:focus {
    outline: none;
    border-color: #4285f4;
}

.direction-input input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.location-btn, .swap-btn {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.location-btn:hover, .swap-btn:hover {
    background-color: #f0f0f0;
    color: #333;
}

.transport-options {
    display: flex;
    gap: 8px;
    margin: 20px 0;
}

.transport-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 12px;
}

.transport-btn:hover {
    border-color: #4285f4;
    background-color: #f8f9ff;
}

.transport-btn.active {
    border-color: #4285f4;
    background-color: #4285f4;
    color: white;
}

.transport-btn i {
    font-size: 18px;
    margin-bottom: 4px;
}

/* Route Preferences */
.route-preferences {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

.preference-label {
    display: block;
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.preference-options {
    display: flex;
    gap: 15px;
}

.preference-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #555;
}

.preference-option input[type="radio"] {
    margin-left: 8px;
    margin-right: 0;
    accent-color: #4285f4;
}

.preference-option span {
    user-select: none;
}

.calculate-btn {
    width: 100%;
    padding: 12px;
    background: #4285f4;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.calculate-btn:hover {
    background: #3367d6;
}

.calculate-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.route-results {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.route-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.route-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.route-info span {
    font-size: 14px;
    color: #666;
}

.route-info strong {
    color: #333;
    font-weight: 600;
}

.route-instructions {
    max-height: 300px;
    overflow-y: auto;
}

.route-step {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.route-step:last-child {
    border-bottom: none;
}

.step-icon {
    width: 24px;
    height: 24px;
    background: #4285f4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    margin-left: 12px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-instruction {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.4;
}

.step-distance {
    font-size: 12px;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 10px;
    }

    .search-container {
        margin: 0 10px;
    }

    .sidebar {
        width: 280px;
    }

    .info-window {
        width: calc(100% - 40px);
        right: 20px;
    }

    .layers-panel {
        width: calc(100% - 40px);
        left: 20px;
    }

    .directions-panel {
        width: calc(100% - 40px);
        left: 20px;
    }

    .map-controls {
        bottom: 10px;
        left: 10px;
    }

    .info-actions {
        flex-direction: column;
    }

    .info-btn {
        justify-content: flex-start;
    }
}

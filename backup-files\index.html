<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>يمن ناف - نظام الملاحة اليمني بتصميم Google Maps</title>
    <link rel="icon" href="/images/icons/icon.svg" type="image/svg+xml">
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#4285F4">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- منع التخزين المؤقت للصفحة -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- نظام إعادة التحميل الإجباري -->
    <script src="/js/force-reload.js?nocache=true"></script>
    
    <!-- الخطوط والمكتبات الأساسية -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/map-fix.css">
    
    <style>
        :root {
            --primary-color: #4285F4;
            --secondary-color: #EA4335;
            --accent-color: #FBBC05;
            --success-color: #34A853;
            --dark-gray: #5F6368;
            --light-gray: #DADCE0;
            --background-white: #FFFFFF;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Roboto', 'Arial', sans-serif;
        }
        
        body {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background-color: var(--background-white);
            color: #3c4043;
        }
        
        /* إخفاء عناصر Leaflet الافتراضية */
        .leaflet-control-zoom, 
        .leaflet-control-attribution {
            display: none !important;
        }
        
        /* الهيكل الرئيسي للتطبيق */
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            position: relative;
        }
        
        /* شريط البحث الرئيسي */
        .search-container {
            position: absolute;
            top: 16px;
            right: 16px;
            left: 16px;
            max-width: 480px;
            margin: 0 auto;
            z-index: 1000;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            background-color: var(--background-white);
            border-radius: 8px;
            box-shadow: 0 2px 6px var(--shadow-color);
            padding: 8px 16px;
            height: 48px;
            width: 100%;
        }
        
        .search-icon {
            color: var(--dark-gray);
            margin-left: 12px;
            font-size: 18px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 8px 0;
            width: 100%;
        }
        
        .search-actions {
            display: flex;
            align-items: center;
        }
        
        .search-action-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            color: var(--dark-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
        }
        
        .search-action-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        /* زر الإعدادات والقائمة الجانبية */
        .menu-button {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 40px;
            height: 40px;
            background-color: var(--background-white);
            border-radius: 50%;
            box-shadow: 0 2px 6px var(--shadow-color);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            cursor: pointer;
            border: none;
            color: var(--dark-gray);
        }
        
        .sidebar {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 320px;
            background-color: var(--background-white);
            box-shadow: 0 0 10px var(--shadow-color);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
        }
        
        .sidebar.active {
            transform: translateX(0);
        }
        
        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-close {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: var(--dark-gray);
        }
        
        .sidebar-content {
            padding: 16px;
        }
        
        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .sidebar-menu-item i {
            margin-left: 16px;
            font-size: 20px;
            width: 24px;
            color: var(--dark-gray);
        }
        
        .sidebar-menu-item span {
            font-size: 16px;
        }
        
        /* كونتينر الخريطة */
        .map-container {
            flex: 1;
            position: relative;
            z-index: 1;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        /* أزرار التحكم بالخريطة */
        .map-controls {
            position: absolute;
            bottom: 24px;
            left: 24px;
            display: flex;
            flex-direction: column;
            z-index: 999;
        }
        
        .map-control-group {
            background-color: var(--background-white);
            border-radius: 8px;
            box-shadow: 0 2px 6px var(--shadow-color);
            margin-top: 8px;
        }
        
        .map-control-button {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--dark-gray);
            font-size: 18px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .map-control-button:last-child {
            border-bottom: none;
        }
        
        .map-control-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .map-control-button.active {
            color: var(--primary-color);
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        /* قائمة طبقات الخريطة */
        .map-layers-menu {
            position: absolute;
            bottom: 24px;
            right: 24px;
            background-color: var(--background-white);
            border-radius: 8px;
            box-shadow: 0 2px 6px var(--shadow-color);
            padding: 8px 0;
            z-index: 999;
            display: none;
        }
        
        .map-layers-menu.active {
            display: block;
        }
        
        .map-layer-option {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .map-layer-option:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .map-layer-option.active {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        .map-layer-icon {
            width: 24px;
            height: 24px;
            margin-left: 12px;
            background-size: cover;
            background-position: center;
            border-radius: 4px;
        }
        
        .streets-icon {
            background-color: #e0e0e0;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234285F4'%3E%3Cpath d='M20.5,3l-0.16,0.03L15,5.1L9,3L3.36,4.9C3.15,4.97,3,5.15,3,5.38V20.5c0,0.27,0.22,0.5,0.5,0.5l0.16-0.03L9,18.9l6,2.1l5.64-1.9c0.21-0.07,0.36-0.25,0.36-0.48V3.5C21,3.23,20.78,3,20.5,3z M10,5.47l4,1.4v11.66l-4-1.4V5.47z M5,6.46l3-1.01v11.7l-3,1.16V6.46z M19,17.54l-3,1.01V6.86l3-1.16V17.54z'/%3E%3C/svg%3E");
        }
        
        .satellite-icon {
            background-image: url("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAQABADASIAAhEBAxEB/8QAFgABAQEAAAAAAAAAAAAAAAAABQYH/8QAIRAAAgICAQQDAAAAAAAAAAAAAQIDBAURAAYSITETMlH/xAAUAQEAAAAAAAAAAAAAAAAAAAAD/8QAGREAAgMBAAAAAAAAAAAAAAAAAQIAAxES/9oADAMBAAIRAxEAPwDSWfz/AFpT6ixUmvctY3EXiiVRKsKaZu5m16A3vQ9nxrM5jKdL5bE0qEb3wgkKzMCu1JBI7T65+avO1vtW1XkrWa8yyQyqHRx7BFSuYKrb8UpA7PBI+tGr5mWSJHQ7VhsHRrACqilAP//Z");
        }
        
        .terrain-icon {
            background-image: url("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAQABADASIAAhEBAxEB/8QAFwAAAwEAAAAAAAAAAAAAAAAABAUGB//EACQQAAIBAwMDBQAAAAAAAAAAAAECAwQFEQAGIRITMSIyQVFx/8QAFAEBAAAAAAAAAAAAAAAAAAAABP/EABwRAQACAgMAAAAAAAAAAAAAAAEAAgMEERIhUf/aAAwDAQACEQMRAD8AX7Zr6js1HU1SxmaapMMKFgu7BGAQfGcnzjOPjW27apILTuKiudQVeW6VgMNAOO2qseOXPJ8jgAedU6GpqrtRfz2aCK0xQQwrE7kS1EhQZbr5IGTgDAyTyT8aul7FKtRVW6tp6pTSSNC0svuQsckgeMZ5BGNNDdrcyZPQ8/gUn8eSQsOH/9k=");
        }
        
        /* نافذة معلومات الموقع */
        .location-info-card {
            position: absolute;
            bottom: 24px;
            right: 50%;
            transform: translateX(50%);
            background-color: var(--background-white);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-color);
            width: 360px;
            max-width: 90%;
            z-index: 1000;
            overflow: hidden;
            display: none;
        }
        
        .location-info-card.active {
            display: block;
        }
        
        .location-info-header {
            padding: 16px;
            border-bottom: 1px solid var(--light-gray);
            position: relative;
        }
        
        .location-close-button {
            position: absolute;
            top: 12px;
            left: 12px;
            background: none;
            border: none;
            font-size: 18px;
            color: var(--dark-gray);
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        .location-close-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .location-info-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .location-info-subtitle {
            font-size: 14px;
            color: var(--dark-gray);
        }
        
        .location-info-content {
            padding: 16px;
        }
        
        .location-info-row {
            display: flex;
            margin-bottom: 12px;
        }
        
        .location-info-icon {
            margin-left: 12px;
            color: var(--dark-gray);
            font-size: 20px;
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .location-info-text {
            flex: 1;
        }
        
        .location-info-actions {
            display: flex;
            justify-content: space-between;
            padding: 8px 16px 16px;
            border-top: 1px solid var(--light-gray);
        }
        
        .location-action-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: var(--primary-color);
            border-radius: 4px;
        }
        
        .location-action-button:hover {
            background-color: rgba(66, 133, 244, 0.1);
        }
        
        .location-action-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .location-action-text {
            font-size: 12px;
        }
        
        /* نظام الإشعارات */
        .notification {
            position: fixed;
            top: 80px;
            right: 50%;
            transform: translateX(50%);
            background-color: var(--background-white);
            border-radius: 4px;
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 12px 16px;
            z-index: 9999;
            display: flex;
            align-items: center;
            max-width: 80%;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .notification.active {
            opacity: 1;
            transform: translateX(50%) translateY(0);
        }
        
        .notification-icon {
            margin-left: 12px;
            font-size: 20px;
        }
        
        .notification-text {
            font-size: 14px;
        }
        
        .notification-success .notification-icon {
            color: var(--success-color);
        }
        
        .notification-error .notification-icon {
            color: var(--secondary-color);
        }
        
        .notification-info .notification-icon {
            color: var(--primary-color);
        }
        
        /* مؤشر التحميل */
        .loading-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 10px var(--shadow-color);
            display: none;
        }
        
        .loading-indicator.active {
            display: flex;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid var(--light-gray);
            border-top: 3px solid var(--primary-color);
            animation: spin 1s linear infinite;
            margin-bottom: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 14px;
            margin-top: 8px;
        }
        
        /* تأثيرات الأيقونات المتحركة */
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
            }
            70% {
                transform: scale(1.1);
                box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
            }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .search-container {
                max-width: calc(100% - 32px);
            }
            
            .sidebar {
                width: 85%;
            }
            
            .location-info-card {
                width: 90%;
            }
        }
    </style>
</head>
<body>
    <!-- هيكل التطبيق الرئيسي -->
    <div class="app-container">
        <!-- شريط البحث -->
        <div class="search-container">
            <div class="search-box">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
                <input type="text" class="search-input" id="search-input" placeholder="ابحث في يمن ناف" autocomplete="off">
                <div class="search-actions">
                    <button class="search-action-button" id="clear-search" title="مسح البحث">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="search-action-button" id="voice-search" title="بحث صوتي">
                        <i class="fas fa-microphone"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- زر القائمة -->
        <button id="menu-button" class="menu-button">
            <i class="fas fa-bars"></i>
        </button>

        <!-- كونتينر الخريطة -->
        <div class="map-container">
            <div id="map"></div>
        </div>

        <!-- أزرار التحكم بالخريطة -->
        <div class="map-controls">
            <!-- زر طبقات الخريطة -->
            <div class="map-control-group">
                <button id="map-layers-button" class="map-control-button" title="طبقات الخريطة">
                    <i class="fas fa-layer-group"></i>
                </button>
            </div>

            <!-- أزرار التكبير والتصغير -->
            <div class="map-control-group">
                <button id="zoom-in" class="map-control-button" title="تكبير">
                    <i class="fas fa-plus"></i>
                </button>
                <button id="zoom-out" class="map-control-button" title="تصغير">
                    <i class="fas fa-minus"></i>
                </button>
            </div>

            <!-- زر تحديد الموقع الحالي -->
            <div class="map-control-group">
                <button id="my-location" class="map-control-button" title="تحديد موقعي الحالي">
                    <i class="fas fa-location-arrow"></i>
                </button>
            </div>
        </div>

        <!-- قائمة طبقات الخريطة -->
        <div id="map-layers-menu" class="map-layers-menu">
            <div id="streets-layer" class="map-layer-option active">
                <div class="map-layer-icon streets-icon"></div>
                <span>خريطة شوارع فائقة الدقة</span>
            </div>
            <div id="satellite-layer" class="map-layer-option">
                <div class="map-layer-icon satellite-icon"></div>
                <span>طبقة القمر الصناعي</span>
            </div>
            <div id="terrain-layer" class="map-layer-option">
                <div class="map-layer-icon terrain-icon"></div>
                <span>طبقة التضاريس</span>
            </div>
        </div>

        <!-- القائمة الجانبية -->
        <div id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">يمن ناف</h2>
                <button id="sidebar-close" class="sidebar-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="sidebar-content">
                <div class="sidebar-menu-item">
                    <i class="fas fa-star"></i>
                    <span>المواقع المحفوظة</span>
                </div>
                <div class="sidebar-menu-item">
                    <i class="fas fa-history"></i>
                    <span>سجل البحث</span>
                </div>
                <div class="sidebar-menu-item">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>خرائط دون اتصال</span>
                </div>
                <div class="sidebar-menu-item">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </div>
                <div class="sidebar-menu-item">
                    <i class="fas fa-question-circle"></i>
                    <span>المساعدة والتعليقات</span>
                </div>
                <div class="sidebar-menu-item">
                    <i class="fas fa-info-circle"></i>
                    <span>عن التطبيق</span>
                </div>
            </div>
        </div>

        <!-- كارت معلومات الموقع -->
        <div id="location-info-card" class="location-info-card">
            <div class="location-info-header">
                <h3 id="location-info-title" class="location-info-title">عنوان الموقع</h3>
                <p id="location-info-subtitle" class="location-info-subtitle">وصف الموقع</p>
                <button id="close-location-info" class="location-close-button" title="إغلاق">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="location-info-content">
                <div class="location-info-row">
                    <div class="location-info-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div id="location-address" class="location-info-text">
                        عنوان الموقع التفصيلي
                    </div>
                </div>
                <div class="location-info-row">
                    <div class="location-info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div id="location-details" class="location-info-text">
                        تفاصيل إضافية عن الموقع
                    </div>
                </div>
                <div class="location-info-row">
                    <div class="location-info-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div id="location-coordinates" class="location-info-text">
                        الإحداثيات: 15.3694, 44.191
                    </div>
                </div>
            </div>
            <div class="location-info-actions">
                <button id="set-destination" class="location-action-button" title="تعيين كوجهة">
                    <div class="location-action-icon">
                        <i class="fas fa-directions"></i>
                    </div>
                    <span class="location-action-text">تعيين كوجهة</span>
                </button>
                <button id="share-location" class="location-action-button" title="مشاركة الموقع">
                    <div class="location-action-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <span class="location-action-text">مشاركة</span>
                </button>
                <button id="save-location" class="location-action-button" title="حفظ الموقع">
                    <div class="location-action-icon">
                        <i class="far fa-bookmark"></i>
                    </div>
                    <span class="location-action-text">حفظ</span>
                </button>
            </div>
        </div>

        <!-- مؤشر التحميل -->
        <div id="loading-indicator" class="loading-indicator">
            <div class="spinner"></div>
            <div id="loading-text" class="loading-text">جاري التحميل...</div>
        </div>

        <!-- الإشعارات -->
        <div id="notification" class="notification">
            <div class="notification-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div id="notification-text" class="notification-text">
                نص الإشعار
            </div>
        </div>
    </div>

    <!-- المكتبات الأساسية -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.min.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js"></script>
    
    <!-- مزود الخرائط المحلي -->
    <script src="/js/local-maps-provider.js"></script>
    
    <!-- كود JavaScript الخاص بالتطبيق -->
    <script>
        // التأكد من عدم وجود أي نسخة مخزنة للخريطة القديمة
        if (window.map || window.appMap) {
            // إزالة أي خرائط موجودة مسبقًا
            window.map = null;
            window.appMap = null;
        }
        
        // تحميل ملف JavaScript مباشرة للتأكد من ظهور الخريطة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة بارامتر عشوائي لمنع التخزين المؤقت
            const preventCache = new Date().getTime();
            const originalSrc = '/js/google-maps-style.js';
            const script = document.createElement('script');
            script.src = originalSrc + '?v=' + preventCache;
            script.onload = function() {
                console.log('تم تحميل ملف google-maps-style.js بنجاح');
                
                // استدعاء وظيفة بدء النظام بعد تأخير قصير
                setTimeout(function() {
                    if (typeof window.startYemenNav === 'function') {
                        console.log('جاري تهيئة الخريطة باستخدام startYemenNav...');
                        window.startYemenNav();
                    } else if (typeof initMap === 'function') {
                        console.log('جاري تهيئة الخريطة باستخدام الوظائف المنفصلة...');
                        initMap();
                        initUI();
                        checkSharedLocation();
                    } else {
                        console.error('لم يتم العثور على وظائف تهيئة الخريطة');
                        // محاولة أخيرة لتهيئة الخريطة باستخدام Leaflet مباشرة
                        var map = L.map('map').setView([15.3694, 44.1910], 12);
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Yemen Nav',
                            maxZoom: 19
                        }).addTo(map);
                    }
                }, 500);
            };
            script.onerror = function() {
                console.error('حدث خطأ أثناء تحميل ملف google-maps-style.js');
                // في حالة فشل التحميل، تهيئة خريطة بسيطة
                var map = L.map('map').setView([15.3694, 44.1910], 12);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Yemen Nav',
                    maxZoom: 19
                }).addTo(map);
            };
            document.body.appendChild(script);
        });
    </script>

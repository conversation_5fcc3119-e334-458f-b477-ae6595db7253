# تنظيف مجلد node_modules

هذا الملف يشرح كيفية تنظيف مجلد `node_modules` وإزالة المكتبات غير الضرورية للمشروع.

## الملفات المتوفرة

1. **essential-packages.txt**: قائمة بالمكتبات الضرورية للمشروع.
2. **cleanup-node-modules.bat**: سكريبت لتنظيف مجلد node_modules تلقائيًا.
3. **package.json.new**: ملف package.json جديد يحتوي فقط على المكتبات الضرورية.

## طريقة التنظيف

### الطريقة الأولى: استخدام السكريبت التلقائي

1. قم بتشغيل ملف `cleanup-node-modules.bat` بالنقر المزدوج عليه.
2. انتظر حتى تكتمل عملية التنظيف.
3. سيتم الاحتفاظ فقط بالمكتبات الضرورية في مجلد `node_modules`.

### الطريقة الثانية: إعادة تثبيت المكتبات

1. قم بحذف مجلد `node_modules` الحالي.
2. قم بنسخ محتوى ملف `package.json.new` إلى ملف `package.json` الأصلي.
3. قم بتنفيذ الأمر التالي:
   ```
   npm install
   ```

## المكتبات الضرورية

المكتبات التالية هي الضرورية للمشروع:

- express
- cors
- body-parser
- dotenv
- compression
- helmet
- express-rate-limit
- node-cache
- jsonwebtoken
- pg
- mysql2
- bcrypt
- axios
- leaflet
- canvas
- admin-bro
- admin-bro-expressjs

## ملاحظات هامة

- قبل تنفيذ عملية التنظيف، يُفضل عمل نسخة احتياطية من مجلد `node_modules` الأصلي.
- في حالة حدوث أي مشاكل بعد التنظيف، يمكنك دائمًا إعادة تثبيت جميع المكتبات باستخدام الأمر `npm install`.
- تأكد من أن المشروع يعمل بشكل صحيح بعد عملية التنظيف.

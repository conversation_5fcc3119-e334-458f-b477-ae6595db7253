<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن - Yemen GPS</title>
    <link rel="stylesheet" href="assets/css/leaflet.css" />
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/icons.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        body {
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
        }
        #map {
            flex: 1 1 auto;
            height: calc(100vh - 60px); /* header height */
            width: 100vw;
            margin: 0;
            padding: 0;
            min-height: 400px;
        }
        .leaflet-control-layers-expanded { direction: rtl; }
        .custom-popup {
            min-width: 220px;
            text-align: right;
        }
        .custom-popup img { width: 100%; border-radius: 8px; margin-bottom: 5px; }
        .custom-popup .popup-actions { margin-top: 8px; display: flex; gap: 10px; justify-content: flex-end; }
        .custom-popup .popup-actions button { border: none; background: none; cursor: pointer; font-size: 1.2em; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img src="assets/images/logo.svg" alt="Yemen GPS" class="logo-img">
                <span class="logo-text">خرائط اليمن</span>
            </div>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="البحث عن موقع أو عنوان..." class="search-input">
                <button id="searchBtn" class="search-btn"><i class="fas fa-search"></i></button>
                <div id="searchResults" class="search-results"></div>
            </div>
            <div class="header-actions">
                <button id="shareBtn" class="action-btn" title="مشاركة الموقع"><i class="fas fa-share-alt"></i></button>
                <button id="locationBtn" class="action-btn" title="موقعي الحالي"><i class="fas fa-crosshairs"></i></button>
                <button id="layersBtn" class="action-btn" title="طبقات الخريطة"><i class="fas fa-layers"></i></button>
            </div>
        </div>
    </header>
    <!-- نهاية الهيدر -->

    <!-- الخريطة -->
    <div id="map"></div>
    <!-- نهاية الخريطة -->



    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner hidden">
        <div class="spinner"></div>
        <p>جاري التحميل...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>

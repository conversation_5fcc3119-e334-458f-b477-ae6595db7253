<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.AddLocationActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:title="@string/add_location_title" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Map Preview -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <fragment
                    android:id="@+id/map_preview"
                    android:name="com.google.android.gms.maps.SupportMapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <TextView
                    android:id="@+id/map_instructions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:background="#80000000"
                    android:padding="8dp"
                    android:text="Tap to select location"
                    android:textColor="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <!-- Location Coordinates -->
            <TextView
                android:id="@+id/location_coordinates"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:gravity="center"
                android:text="Latitude: 0.0, Longitude: 0.0"
                android:textStyle="italic" />

            <!-- Location Name -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/InputField"
                android:hint="@string/location_name">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_location_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Location Type -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="8dp"
                android:text="@string/location_type"
                android:textSize="16sp"
                android:textStyle="bold" />

            <RadioGroup
                android:id="@+id/radio_location_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/radio_traffic"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/type_traffic" />

                <RadioButton
                    android:id="@+id/radio_speedbump"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/type_speedbump" />

                <RadioButton
                    android:id="@+id/radio_pothole"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/type_pothole" />

                <RadioButton
                    android:id="@+id/radio_dirt_road"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/type_dirt_road" />

                <RadioButton
                    android:id="@+id/radio_checkpoint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/type_checkpoint" />

            </RadioGroup>

            <!-- Description -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/InputField"
                android:layout_marginTop="16dp"
                android:hint="@string/location_description">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:inputType="textMultiLine"
                    android:lines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Submit Button -->
            <Button
                android:id="@+id/btn_submit"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/submit_location" />

            <!-- Disclaimer -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="All submissions require admin approval before appearing on the map"
                android:textSize="12sp"
                android:textStyle="italic" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

# تصحيح أمر الاتصال بقاعدة بيانات PostgreSQL

## الخطأ الذي ظهر

```
Microsoft Windows [Version 10.0.26100.3775]
(c) Microsoft Corporation. All rights reserved.

C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime>"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" "host=localhost port=5432 dbname='yemen_nav" ' user=postgres sslmode=prefer connect_timeout=10" 2>>&1
psql: warning: extra command-line argument "user=postgres" ignored
psql: warning: extra command-line argument "sslmode=prefer" ignored
psql: warning: extra command-line argument "connect_timeout=10 2>>&1" ignored
psql: error: unterminated quoted string in connection info string

C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime>
```

## سبب الخطأ

هناك مشكلة في علامات الاقتباس في أمر الاتصال. المشكلة الرئيسية هي:

1. هناك علامة اقتباس مفردة (`'`) قبل `yemen_nav` ولكن علامة الإغلاق موجودة خارج علامات الاقتباس المزدوجة (`"`) التي تحيط بسلسلة الاتصال
2. المعلمات `user` و `sslmode` و `connect_timeout` موجودة خارج علامات الاقتباس المزدوجة، لذلك يتم تجاهلها كمعلمات إضافية

## الأمر الصحيح

### الطريقة 1: استخدام معلمات منفصلة

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U yemen
```

حيث:
- `-h` تحدد المضيف (host)
- `-p` تحدد المنفذ (port)
- `-d` تحدد اسم قاعدة البيانات (database name)
- `-U` تحدد اسم المستخدم (user)

### الطريقة 2: استخدام سلسلة اتصال واحدة

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" "host=localhost port=5432 dbname=yemen_nav user=postgres"
```

### الطريقة 3: استخدام متغير بيئي PGPASSWORD لتحديد كلمة المرور

```
set PGPASSWORD=your_secure_password
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres
```

## ملاحظات مهمة

1. **كلمة المرور**: في الأمثلة أعلاه، سيطلب منك psql إدخال كلمة المرور. إذا كنت تريد تحديد كلمة المرور في الأمر نفسه، يمكنك استخدام المعلمة `-W` أو تعيين متغير البيئة `PGPASSWORD` كما هو موضح في الطريقة 3.

2. **مسار psql**: تأكد من أن المسار إلى psql.exe صحيح. قد يختلف المسار حسب إصدار PostgreSQL وطريقة التثبيت.

3. **اسم قاعدة البيانات**: تأكد من أن قاعدة البيانات `yemen_nav` موجودة بالفعل. إذا لم تكن موجودة، فستحتاج إلى إنشائها أولاً.

4. **إعادة توجيه الخطأ**: إذا كنت تريد إعادة توجيه الخطأ، تأكد من وضع `2>&1` خارج علامات الاقتباس:
   ```
   "C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres 2>&1
   ```

## أمثلة إضافية

### الاتصال وتنفيذ استعلام SQL

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres -c "SELECT * FROM users LIMIT 5;"
```

### الاتصال وتنفيذ ملف SQL

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres -f path\to\your\sqlfile.sql
```

### إنشاء قاعدة بيانات جديدة

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -U postgres -c "CREATE DATABASE yemen_nav;"
```

## إنشاء قاعدة بيانات yemen_nav وإعدادها

إذا كنت تريد إنشاء قاعدة بيانات `yemen_nav` وإعدادها، يمكنك استخدام الأوامر التالية:

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -U postgres -c "CREATE DATABASE yemen_nav;"
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres -c "CREATE EXTENSION postgis; CREATE EXTENSION postgis_topology;"
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -U postgres -c "CREATE USER yemen WITH ENCRYPTED PASSWORD 'admin';"
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE yemen_nav TO yemen;"
```

## التحقق من الاتصال

للتحقق من أن الاتصال يعمل بشكل صحيح، يمكنك استخدام الأمر التالي:

```
"C:\Program Files\PostgreSQL\14\pgAdmin 4\runtime\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres -c "SELECT version();"
```

إذا كان كل شيء يعمل بشكل صحيح، فسترى إصدار PostgreSQL المثبت على جهازك.

-- Add permissions_json column
ALTER TABLE users ADD COLUMN IF NOT EXISTS permissions_json JSONB DEFAULT '[]';

-- Add can_access_admin column
ALTER TABLE users ADD COLUMN IF NOT EXISTS can_access_admin BOOLEAN DEFAULT FALSE;

-- Update admin user
UPDATE users SET can_access_admin = TRUE WHERE username = 'admin';

-- Add default permissions for admin user
UPDATE users SET permissions_json = '[
    {"code": "view_dashboard", "name": "View Dashboard"},
    {"code": "manage_users", "name": "Manage Users"},
    {"code": "manage_clients", "name": "Manage Clients"},
    {"code": "manage_locations", "name": "Manage Locations"},
    {"code": "manage_settings", "name": "Manage Settings"},
    {"code": "manage_categories", "name": "Manage Categories"}
]'::jsonb WHERE username = 'admin';

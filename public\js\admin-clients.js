/**
 * Yemen GPS - إدارة العملاء
 * ملف JavaScript لإدارة العملاء في لوحة التحكم
 */

// المتغيرات العامة
const API_BASE_URL = 'http://localhost:3000/api';
let allClients = [];
let filteredClients = [];

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات
    loadClients();
    
    // إضافة مستمعي الأحداث
    document.getElementById('client-search').addEventListener('input', filterClients);
    document.getElementById('client-form').addEventListener('submit', saveClient);
    
    // تهيئة الوضع المتجاوب للشاشات الصغيرة
    initResponsiveLayout();
});

/**
 * تهيئة الوضع المتجاوب للشاشات الصغيرة
 */
function initResponsiveLayout() {
    const toggleButton = document.querySelector('.toggle-sidebar');
    
    if (window.innerWidth <= 768) {
        document.querySelector('.sidebar').classList.remove('active');
        document.querySelector('.main-content').style.marginRight = '0';
        toggleButton.style.display = 'block';
    } else {
        document.querySelector('.sidebar').classList.add('active');
        document.querySelector('.main-content').style.marginRight = '250px';
        toggleButton.style.display = 'none';
    }
    
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar').classList.remove('active');
            document.querySelector('.main-content').style.marginRight = '0';
            toggleButton.style.display = 'block';
        } else {
            document.querySelector('.sidebar').classList.add('active');
            document.querySelector('.main-content').style.marginRight = '250px';
            toggleButton.style.display = 'none';
        }
    });
}

/**
 * تبديل حالة القائمة الجانبية
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('active');
    
    if (sidebar.classList.contains('active')) {
        mainContent.style.marginRight = '250px';
    } else {
        mainContent.style.marginRight = '0';
    }
}

/**
 * تحميل العملاء
 */
function loadClients() {
    showLoading();
    
    apiRequest('/admin/clients')
        .then(data => {
            allClients = data;
            filteredClients = [...allClients];
            displayClients(filteredClients);
            updateClientStats();
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل العملاء:', error);
            document.getElementById('clients-error').textContent = 'حدث خطأ أثناء تحميل بيانات العملاء: ' + error.message;
            document.getElementById('clients-error').style.display = 'block';
            hideLoading();
        });
}

/**
 * عرض العملاء في الجدول
 */
function displayClients(clients) {
    const tableBody = document.getElementById('clients-table-body');
    tableBody.innerHTML = '';
    
    if (clients.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="no-data">لا يوجد عملاء للعرض</td></tr>';
        return;
    }
    
    clients.forEach(client => {
        const row = document.createElement('tr');
        
        // تحديد حالة العميل
        let statusText = 'نشط';
        let statusClass = 'status-active';
        
        if (client.status === 'inactive') {
            statusText = 'غير نشط';
            statusClass = 'status-inactive';
        }
        
        // تنسيق تاريخ الإنشاء
        const createdDate = new Date(client.created_at);
        const formattedDate = createdDate.toLocaleDateString('ar-SA');
        
        row.innerHTML = `
            <td>${client.id}</td>
            <td>${client.name}</td>
            <td>${client.email || '-'}</td>
            <td>${client.phone || '-'}</td>
            <td>${client.address || '-'}</td>
            <td>${client.devicesn || '-'}</td>
            <td>${client.licensen || '-'}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="editClient(${client.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteClient(${client.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    updateClientStats();
}

/**
 * تحديث إحصائيات العملاء
 */
function updateClientStats() {
    document.getElementById('total-clients').textContent = allClients.length;
    document.getElementById('displayed-clients').textContent = filteredClients.length;
}

/**
 * تصفية العملاء حسب المعايير المحددة
 */
function filterClients() {
    const searchTerm = document.getElementById('client-search').value.trim().toLowerCase();
    const statusFilter = document.getElementById('client-status-filter').value;
    
    // إظهار/إخفاء زر مسح البحث
    document.getElementById('clear-client-search').style.display = searchTerm ? 'block' : 'none';
    
    filteredClients = allClients.filter(client => {
        // تصفية حسب نص البحث
        const matchesSearch = 
            client.name.toLowerCase().includes(searchTerm) ||
            (client.email && client.email.toLowerCase().includes(searchTerm)) ||
            (client.phone && client.phone.includes(searchTerm)) ||
            (client.devicesn && client.devicesn.includes(searchTerm)) ||
            (client.licensen && client.licensen.includes(searchTerm));
        
        // تصفية حسب الحالة
        const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    displayClients(filteredClients);
}

/**
 * مسح البحث
 */
function clearClientSearch() {
    document.getElementById('client-search').value = '';
    filterClients();
}

/**
 * عرض نافذة إضافة عميل جديد
 */
function showAddClientModal() {
    // تغيير عنوان النافذة
    document.getElementById('client-modal-title').textContent = 'إضافة عميل جديد';
    
    // إعادة تعيين النموذج
    document.getElementById('client-form').reset();
    document.getElementById('client-id').value = '';
    
    // تعيين الحالة الافتراضية
    document.getElementById('client-status').value = 'active';
    
    // عرض النافذة
    document.getElementById('client-modal').style.display = 'block';
}

/**
 * عرض نافذة تعديل عميل
 */
function editClient(clientId) {
    // البحث عن العميل
    const client = allClients.find(c => c.id === clientId);
    
    if (!client) {
        alert('لم يتم العثور على العميل');
        return;
    }
    
    // تغيير عنوان النافذة
    document.getElementById('client-modal-title').textContent = 'تعديل العميل';
    
    // ملء النموذج ببيانات العميل
    document.getElementById('client-id').value = client.id;
    document.getElementById('client-name').value = client.name;
    document.getElementById('client-email').value = client.email || '';
    document.getElementById('client-phone').value = client.phone || '';
    document.getElementById('client-address').value = client.address || '';
    document.getElementById('client-devicesn').value = client.devicesn || '';
    document.getElementById('client-licensen').value = client.licensen || '';
    document.getElementById('client-status').value = client.status || 'active';
    
    // عرض النافذة
    document.getElementById('client-modal').style.display = 'block';
}

/**
 * إغلاق نافذة إضافة/تعديل العميل
 */
function closeClientModal() {
    document.getElementById('client-modal').style.display = 'none';
}

/**
 * حفظ بيانات العميل (إضافة/تعديل)
 */
function saveClient(event) {
    event.preventDefault();
    
    // جمع بيانات النموذج
    const clientId = document.getElementById('client-id').value;
    const clientData = {
        name: document.getElementById('client-name').value,
        email: document.getElementById('client-email').value || null,
        phone: document.getElementById('client-phone').value || null,
        address: document.getElementById('client-address').value || null,
        devicesn: document.getElementById('client-devicesn').value || null,
        licensen: document.getElementById('client-licensen').value || null,
        status: document.getElementById('client-status').value
    };
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // تحديد ما إذا كانت عملية إضافة أو تعديل
    const isUpdate = clientId !== '';
    const url = isUpdate ? `/admin/clients/${clientId}` : '/admin/clients';
    const method = isUpdate ? 'PUT' : 'POST';
    
    // إرسال البيانات إلى الخادم
    apiRequest(url, {
        method: method,
        body: JSON.stringify(clientData)
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النافذة
        closeClientModal();
        
        // إظهار رسالة نجاح
        alert(isUpdate ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح');
        
        // إعادة تحميل العملاء
        loadClients();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حفظ العميل: ' + error.message);
    });
}

/**
 * حذف عميل
 */
function deleteClient(clientId) {
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا العميل؟')) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال طلب الحذف إلى الخادم
    apiRequest(`/admin/clients/${clientId}`, {
        method: 'DELETE'
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة نجاح
        alert('تم حذف العميل بنجاح');
        
        // إعادة تحميل العملاء
        loadClients();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حذف العميل: ' + error.message);
    });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    document.getElementById('clients-loading').style.display = 'flex';
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    document.getElementById('clients-loading').style.display = 'none';
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات الجلسة من تخزين الجلسة
    sessionStorage.removeItem('yemenNavToken');
    sessionStorage.removeItem('yemenNavCurrentUser');
    sessionStorage.removeItem('yemenNavLoggedIn');
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + sessionStorage.getItem('yemenNavToken')
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = `${API_BASE_URL}${endpoint}`;
    }
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        });
}

/**
 * ملف CSS مخصص للوحة تحكم يمن GPS
 */

/* تعريف الخط */
@font-face {
    font-family: '<PERSON><PERSON><PERSON>';
    src: url('../fonts/<PERSON>-Art-bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

/* تطبيق الخط على كل العناصر */
body, h1, h2, h3, h4, h5, h6, p, a, button, input, select, textarea, table, .btn, .nav-link, .dropdown-item {
    font-family: '<PERSON>-<PERSON>', 'Tajawal', sans-serif !important;
}

/* تحسين الشكل العام */
body {
    background-color: #f8f9fa;
    color: #343a40;
}

/* تحسين شريط التنقل */
.navbar {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff !important;
}

.navbar-brand img {
    height: 35px;
    margin-left: 10px;
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

.nav-link {
    font-size: 1.1rem;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2) !important;
    font-weight: bold;
}

/* تحسين البطاقات */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    color: white;
    font-weight: bold;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.card-body {
    padding: 1.5rem;
}

/* تحسين الجداول */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    border-top: none;
    border-bottom: 2px solid #dee2e6;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تحسين الأزرار */
.btn {
    border-radius: 5px;
    font-weight: bold;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    border: none;
}

.btn-action {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 3px;
}

/* تحسين علامات التبويب */
.nav-tabs {
    border-bottom: none;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border-radius: 10px 10px 0 0;
    font-weight: bold;
    padding: 0.75rem 1.5rem;
    margin-right: 5px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background-color: rgba(52, 58, 64, 0.05);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    color: white;
    border-color: transparent;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* تحسين النماذج */
.form-control {
    border-radius: 5px;
    padding: 0.75rem 1rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

.form-label {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: none;
}

.modal-header {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
}

/* تحسين مؤشر التحميل */
.loading-overlay {
    background-color: rgba(255, 255, 255, 0.9);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0, 123, 255, 0.1);
    border-top: 5px solid #007bff;
    border-radius: 50%;
}

/* تحسين الإشعارات */
.notification {
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    background-color: white;
}

.notification.success {
    border-left: 5px solid #28a745;
}

.notification.error {
    border-left: 5px solid #dc3545;
}

/* تحسين حقول البحث */
.form-control-sm {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    min-width: 200px;
}

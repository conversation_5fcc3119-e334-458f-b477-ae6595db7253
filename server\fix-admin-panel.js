// إصلاح مشكلة عدم ظهور بيانات المستخدمين في لوحة التحكم
const fs = require('fs');
const path = require('path');

// تعديل ملف admin-db.js للتأكد من أن واجهة المستخدم تعرض بيانات قاعدة البيانات

// مسار ملف admin-db.js
const adminDbFilePath = path.join(__dirname, '..', 'public', 'js', 'admin-db.js');

// قراءة الملف
fs.readFile(adminDbFilePath, 'utf8', (err, data) => {
  if (err) {
    console.error('حدث خطأ أثناء قراءة الملف:', err);
    return;
  }

  // إضافة معلومات التصحيح في دالة loadUsersFromDatabase
  const modifiedData = data.replace(
    /function loadUsersFromDatabase\(\) \{[\s\S]*?fetch\('\/api\/admin\/users'\)/g,
    `function loadUsersFromDatabase() {
    // إظهار مؤشر التحميل
    showLoading();
    console.log('جاري تحميل المستخدمين من قاعدة البيانات...');
    
    // تحميل المستخدمين من API
    fetch('/api/admin/users')`
  );
  
  // إضافة تصحيح في دالة displayUsers
  const finalData = modifiedData.replace(
    /console\.log\('User data from API:', users\[0\]\);/g,
    `console.log('User data from API:', users);
    // التأكد من استلام بيانات صحيحة
    if (!Array.isArray(users)) {
      console.error('البيانات المستلمة ليست مصفوفة:', users);
      showNotification('تنسيق البيانات غير صحيح', 'error');
      return;
    }`
  );

  // كتابة الملف المعدل
  fs.writeFile(adminDbFilePath, finalData, 'utf8', (err) => {
    if (err) {
      console.error('حدث خطأ أثناء كتابة الملف:', err);
      return;
    }
    console.log('تم تحديث ملف admin-db.js بنجاح');
  });
});

// فحص ملف الخادم
const serverFilePath = path.join(__dirname, 'server.js');
fs.readFile(serverFilePath, 'utf8', (err, data) => {
  if (err) {
    console.error('حدث خطأ أثناء قراءة ملف الخادم:', err);
    return;
  }

  // إضافة تصحيح لمعالجة CORS في ملف الخادم
  if (!data.includes('res.header')) {
    const updatedServerData = data.replace(
      /app\.use\(cors\(\)\);/,
      `app.use(cors());
// تمكين CORS لجميع الطلبات
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    return res.status(200).json({});
  }
  console.log(\`طلب \${req.method} إلى \${req.path}\`);
  next();
});`
    );

    fs.writeFile(serverFilePath, updatedServerData, 'utf8', (err) => {
      if (err) {
        console.error('حدث خطأ أثناء تحديث ملف الخادم:', err);
        return;
      }
      console.log('تم تحديث ملف server.js بنجاح');
    });
  } else {
    console.log('ملف الخادم يحتوي بالفعل على إعدادات CORS المناسبة');
  }
});

console.log('تم بدء عملية إصلاح مشكلة عدم ظهور بيانات المستخدمين في لوحة التحكم');
console.log('بعد اكتمال العملية، قم بإعادة تشغيل الخادم باستخدام الأمر: node server/server.js');

/* public/css/offline-mode.css */
:root {
  --primary-color: #4CAF50;
  --primary-dark: #388E3C;
  --primary-light: #A5D6A7;
  --accent-color: #FFC107;
  --text-on-primary: #FFFFFF;
  --error-color: #F44336;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --info-color: #2196F3;
  --dark-text: #212121;
  --medium-text: #757575;
  --light-text: #BDBDBD;
  --div-border: #E0E0E0;
  --card-bg: #FFFFFF;
  --page-bg: #F5F5F5;
}

/* الإعدادات العامة */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Tajawal', Arial, sans-serif;
  direction: rtl;
  background-color: var(--page-bg);
  color: var(--dark-text);
  line-height: 1.6;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: var(--primary-dark);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--dark-text);
}

/* رسالة عدم الاتصال */
.offline-banner {
  background-color: var(--warning-color);
  color: white;
  text-align: center;
  padding: 10px;
  position: relative;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: none;
}

.offline-banner.visible {
  display: block;
  animation: slideDown 0.5s forwards;
}

/* مؤشر حالة الاتصال */
.connection-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  margin-right: 10px;
}

.connection-indicator-online {
  background-color: rgba(76, 175, 80, 0.15);
  color: var(--success-color);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.connection-indicator-offline {
  background-color: rgba(244, 67, 54, 0.15);
  color: var(--error-color);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.connection-indicator-syncing {
  background-color: rgba(255, 193, 7, 0.15);
  color: var(--warning-color);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.connection-dot-online {
  background-color: var(--success-color);
  box-shadow: 0 0 0 rgba(76, 175, 80, 0.4);
  animation: none;
}

.connection-dot-offline {
  background-color: var(--error-color);
  box-shadow: 0 0 0 rgba(244, 67, 54, 0.4);
  animation: pulse 2s infinite;
}

.connection-dot-syncing {
  background-color: var(--warning-color);
  box-shadow: 0 0 0 rgba(255, 193, 7, 0.4);
  animation: pulse 1s infinite;
}

/* البطاقات */
.card {
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--div-border);
}

.card-title {
  font-size: 1.25rem;
  color: var(--dark-text);
  margin: 0;
}

.card-subtitle {
  font-size: 0.875rem;
  color: var(--medium-text);
  margin-top: 5px;
}

.card-body {
  padding: 5px 0;
}

.card-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid var(--div-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* الأزرار */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn:focus, .btn:hover {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-on-primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  color: var(--text-on-primary);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: #212121;
}

.btn-warning:hover {
  background-color: #FFA000;
  border-color: #FFA000;
}

.btn-danger {
  background-color: var(--error-color);
  border-color: var(--error-color);
  color: var(--text-on-primary);
}

.btn-danger:hover {
  background-color: #D32F2F;
  border-color: #D32F2F;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-block {
  display: block;
  width: 100%;
}

/* بطاقات الخرائط المحفوظة */
.saved-map-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
  background-color: var(--card-bg);
}

.saved-map-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.saved-map-thumbnail {
  height: 120px;
  background-color: #e9e9e9;
  background-size: cover;
  background-position: center;
  position: relative;
}

.saved-map-size {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.saved-map-info {
  padding: 15px;
}

.saved-map-title {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 1rem;
}

.saved-map-meta {
  display: flex;
  justify-content: space-between;
  color: var(--medium-text);
  font-size: 0.75rem;
  margin-bottom: 10px;
}

.saved-map-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

/* مؤشر التقدم */
.progress {
  height: 0.5rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--primary-color);
  transition: width 0.6s ease;
}

.progress-sm {
  height: 0.25rem;
}

.progress-lg {
  height: 1rem;
}

/* الإشعارات */
.toast-container {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 9999;
}

.toast {
  min-width: 250px;
  max-width: 350px;
  background-color: var(--card-bg);
  color: var(--dark-text);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin-bottom: 10px;
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s, transform 0.3s;
  overflow: hidden;
}

.toast.show {
  opacity: 1;
  transform: translateY(0);
}

.toast-header {
  position: relative;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--div-border);
}

.toast-header::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 4px;
}

.toast-title {
  font-weight: bold;
  flex-grow: 1;
}

.toast-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: var(--medium-text);
}

.toast-body {
  padding: 12px 15px;
}

.toast-progress {
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
}

.toast-progress-bar {
  height: 100%;
  width: 100%;
  transform-origin: right;
  animation: toast-progress 5s linear forwards;
}

.toast-success .toast-header::before {
  background-color: var(--success-color);
}

.toast-error .toast-header::before {
  background-color: var(--error-color);
}

.toast-warning .toast-header::before {
  background-color: var(--warning-color);
}

.toast-info .toast-header::before {
  background-color: var(--info-color);
}

.toast-success .toast-progress-bar {
  background-color: var(--success-color);
}

.toast-error .toast-progress-bar {
  background-color: var(--error-color);
}

.toast-warning .toast-progress-bar {
  background-color: var(--warning-color);
}

.toast-info .toast-progress-bar {
  background-color: var(--info-color);
}

/* الرسوم المتحركة */
@keyframes toast-progress {
  from { transform: scaleX(1); }
  to { transform: scaleX(0); }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* واجهة مستخدم الخريطة دون اتصال */
.offline-zoom-controls {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  margin: 10px;
  overflow: hidden;
}

.offline-zoom-control {
  padding: 8px;
  text-align: center;
  cursor: pointer;
  font-size: 18px;
  user-select: none;
}

.offline-zoom-control:hover {
  background-color: #f5f5f5;
}

.offline-zoom-control:active {
  background-color: #eeeeee;
}

.offline-zoom-divider {
  height: 1px;
  background-color: #e0e0e0;
}

.offline-status-bar {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 8px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  font-size: 14px;
}

.offline-save-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  box-shadow: 0 3px 5px rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.offline-save-button:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  transform: translateY(-2px);
}

.offline-save-icon {
  font-size: 24px;
}

/* المحتوى المتحرك */
.skeleton-loader {
  background: linear-gradient(90deg, 
    rgba(0,0,0,0.06) 25%, 
    rgba(0,0,0,0.15) 37%, 
    rgba(0,0,0,0.06) 63%
  );
  background-size: 400% 100%;
  animation: shimmer 1.4s infinite;
  border-radius: 4px;
}

.skeleton-title {
  height: 20px;
  margin-bottom: 15px;
  width: 50%;
}

.skeleton-text {
  height: 16px;
  margin-bottom: 10px;
}

.skeleton-text:last-child {
  width: 80%;
}

/* الفواصل العائمة */
.float-divider {
  width: 100%;
  height: 1px;
  background-color: var(--div-border);
  margin: 20px 0;
  position: relative;
}

.float-divider::after {
  content: attr(data-content);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 0 10px;
  color: var(--medium-text);
  font-size: 0.875rem;
}

/* وضع الشاشات الصغيرة */
@media (max-width: 576px) {
  .card {
    padding: 15px;
  }
  
  .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
  }
  
  .saved-map-thumbnail {
    height: 100px;
  }
  
  .toast-container {
    left: 10px;
    right: 10px;
    top: 10px;
  }
  
  .toast {
    max-width: 100%;
  }
  
  .offline-banner {
    font-size: 0.875rem;
    padding: 8px;
  }
}

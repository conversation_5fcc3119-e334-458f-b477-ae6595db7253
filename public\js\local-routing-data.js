/**
 * نظام إدارة بيانات المسارات المحلية
 * يقوم بتخزين واسترجاع بيانات المسارات المحلية
 */
(function() {
  'use strict';
  
  /**
   * كائن إدارة بيانات المسارات المحلية
   */
  const LocalRoutingData = {
    /**
     * اسم مفتاح التخزين المحلي
     */
    STORAGE_KEY: 'yemen_nav_routes',
    
    /**
     * الحد الأقصى لعدد المسارات المخزنة
     */
    MAX_STORED_ROUTES: 100,
    
    /**
     * مدة صلاحية المسارات المخزنة (بالأيام)
     */
    ROUTES_EXPIRY_DAYS: 30,
    
    /**
     * الحصول على المسارات المخزنة
     * @return {Object} كائن يحتوي على المسارات المخزنة
     */
    getStoredRoutes: function() {
      try {
        const storedData = localStorage.getItem(this.STORAGE_KEY);
        return storedData ? JSON.parse(storedData) : {};
      } catch (error) {
        console.error('خطأ في قراءة المسارات المخزنة:', error);
        return {};
      }
    },
    
    /**
     * حفظ المسارات في التخزين المحلي
     * @param {Object} routes كائن يحتوي على المسارات
     */
    saveStoredRoutes: function(routes) {
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(routes));
      } catch (error) {
        console.error('خطأ في حفظ المسارات:', error);
        
        // في حالة امتلاء التخزين المحلي، قم بإزالة المسارات القديمة
        if (error.name === 'QuotaExceededError' || error.code === 22) {
          this.cleanupOldRoutes();
          try {
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(routes));
          } catch (retryError) {
            console.error('فشل في حفظ المسارات حتى بعد التنظيف:', retryError);
          }
        }
      }
    },
    
    /**
     * تنظيف المسارات القديمة
     */
    cleanupOldRoutes: function() {
      const routes = this.getStoredRoutes();
      const now = new Date().getTime();
      const expiryTime = now - (this.ROUTES_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
      
      // إزالة المسارات القديمة
      Object.keys(routes).forEach(key => {
        if (routes[key].timestamp < expiryTime) {
          delete routes[key];
        }
      });
      
      // إذا كان لا يزال هناك الكثير من المسارات، قم بإزالة الأقدم
      const routeKeys = Object.keys(routes);
      if (routeKeys.length > this.MAX_STORED_ROUTES) {
        // ترتيب المسارات حسب الطابع الزمني (الأقدم أولاً)
        routeKeys.sort((a, b) => routes[a].timestamp - routes[b].timestamp);
        
        // إزالة المسارات الأقدم
        const keysToRemove = routeKeys.slice(0, routeKeys.length - this.MAX_STORED_ROUTES);
        keysToRemove.forEach(key => {
          delete routes[key];
        });
      }
      
      // حفظ المسارات المتبقية
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(routes));
      } catch (error) {
        console.error('فشل في حفظ المسارات بعد التنظيف:', error);
        
        // إذا فشل الحفظ مرة أخرى، قم بمسح جميع المسارات
        localStorage.removeItem(this.STORAGE_KEY);
      }
    },
    
    /**
     * إنشاء مفتاح للمسار
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @return {String} مفتاح المسار
     */
    createRouteKey: function(startLatLng, endLatLng, mode) {
      // تقريب الإحداثيات إلى 4 أرقام عشرية للسماح بالتطابق التقريبي
      const startLat = parseFloat(startLatLng.lat).toFixed(4);
      const startLng = parseFloat(startLatLng.lng).toFixed(4);
      const endLat = parseFloat(endLatLng.lat).toFixed(4);
      const endLng = parseFloat(endLatLng.lng).toFixed(4);
      
      return `${startLat},${startLng}|${endLat},${endLng}|${mode}`;
    },
    
    /**
     * البحث عن مسار قريب
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @param {Number} maxDistance المسافة القصوى للبحث (بالكيلومتر)
     * @return {Object|null} المسار إذا وجد، أو null إذا لم يوجد
     */
    findNearbyRoute: function(startLatLng, endLatLng, mode, maxDistance = 0.5) {
      const routes = this.getStoredRoutes();
      let bestMatch = null;
      let minDistance = maxDistance;
      
      Object.keys(routes).forEach(key => {
        // التحقق من تطابق وضع التنقل
        if (!key.endsWith(`|${mode}`)) return;
        
        const routeData = routes[key];
        const routeCoords = key.split('|');
        const routeStart = routeCoords[0].split(',');
        const routeEnd = routeCoords[1].split(',');
        
        // حساب المسافة بين نقاط البداية
        const startDistance = this.calculateDistance(
          startLatLng.lat, startLatLng.lng,
          parseFloat(routeStart[0]), parseFloat(routeStart[1])
        );
        
        // حساب المسافة بين نقاط النهاية
        const endDistance = this.calculateDistance(
          endLatLng.lat, endLatLng.lng,
          parseFloat(routeEnd[0]), parseFloat(routeEnd[1])
        );
        
        // إذا كانت المسافة أقل من الحد الأقصى، استخدم هذا المسار
        const totalDistance = startDistance + endDistance;
        if (totalDistance < minDistance) {
          minDistance = totalDistance;
          bestMatch = routeData;
        }
      });
      
      return bestMatch;
    },
    
    /**
     * حساب المسافة بين نقطتين بالكيلومتر
     * @param {Number} lat1 خط عرض النقطة الأولى
     * @param {Number} lon1 خط طول النقطة الأولى
     * @param {Number} lat2 خط عرض النقطة الثانية
     * @param {Number} lon2 خط طول النقطة الثانية
     * @return {Number} المسافة بالكيلومتر
     */
    calculateDistance: function(lat1, lon1, lat2, lon2) {
      const R = 6371; // نصف قطر الأرض بالكيلومتر
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    },
    
    /**
     * تحويل من درجات إلى راديان
     * @param {Number} deg زاوية بالدرجات
     * @return {Number} الزاوية بالراديان
     */
    deg2rad: function(deg) {
      return deg * (Math.PI/180);
    },
    
    /**
     * الحصول على المسارات
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @return {Array|null} مصفوفة من المسارات، أو null إذا لم توجد
     */
    getRoutes: function(startLatLng, endLatLng, mode) {
      // البحث عن المسار المطابق تماماً
      const routeKey = this.createRouteKey(startLatLng, endLatLng, mode);
      const routes = this.getStoredRoutes();
      
      if (routes[routeKey]) {
        // التحقق من صلاحية المسار
        const now = new Date().getTime();
        if (routes[routeKey].timestamp + (this.ROUTES_EXPIRY_DAYS * 24 * 60 * 60 * 1000) > now) {
          return routes[routeKey].routes;
        } else {
          // إزالة المسار منتهي الصلاحية
          delete routes[routeKey];
          this.saveStoredRoutes(routes);
        }
      }
      
      // البحث عن مسار قريب
      const nearbyRoute = this.findNearbyRoute(startLatLng, endLatLng, mode);
      if (nearbyRoute) {
        return nearbyRoute.routes;
      }
      
      return null;
    },
    
    /**
     * حفظ المسارات
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @param {Array} routes مصفوفة من المسارات
     */
    saveRoutes: function(startLatLng, endLatLng, mode, routes) {
      const routeKey = this.createRouteKey(startLatLng, endLatLng, mode);
      const storedRoutes = this.getStoredRoutes();
      
      // حفظ المسارات مع الطابع الزمني
      storedRoutes[routeKey] = {
        timestamp: new Date().getTime(),
        routes: routes
      };
      
      this.saveStoredRoutes(storedRoutes);
    }
  };
  
  // تصدير الكائن للاستخدام العام
  window.localRoutingData = LocalRoutingData;
})();

@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 تشغيل نظام يمن GPS على الخادم الخارجي
echo ========================================
echo.

echo 📋 فحص المتطلبات...

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

:: التحقق من وجود الملفات المطلوبة
if not exist "production-server.js" (
    echo ❌ ملف production-server.js غير موجود
    pause
    exit /b 1
)
echo ✅ ملف الخادم موجود

if not exist "public\index.html" (
    echo ❌ ملف public\index.html غير موجود
    pause
    exit /b 1
)
echo ✅ ملفات الواجهة موجودة

echo.
echo 🔧 إعدادات الخادم:
echo    البورت: 5000
echo    المضيف: 0.0.0.0
echo    الرابط الخارجي: http://185.11.8.26:5000/
echo.

echo 🛑 إيقاف أي خوادم سابقة...
taskkill /IM node.exe /F >nul 2>&1

echo ⏳ انتظار 3 ثوان...
timeout /t 3 /nobreak >nul

echo.
echo 🚀 بدء تشغيل الخادم...
echo ========================================
echo.

:: تشغيل الخادم
node production-server.js

echo.
echo ========================================
echo 🛑 تم إيقاف الخادم
echo ========================================
pause

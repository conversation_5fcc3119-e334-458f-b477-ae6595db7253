<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇾🇪 خرائط اليمن الشاملة - Yemen Maps Complete</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 350px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .sidebar.open {
            transform: translateX(0);
        }
        
        .sidebar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .sidebar-content {
            padding: 20px;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 20px;
        }
        
        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .search-box i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        
        .category-filters {
            margin-bottom: 20px;
        }
        
        .category-btn {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            color: #495057;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .category-btn:hover,
        .category-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .place-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .place-card:hover {
            transform: translateY(-2px);
        }
        
        .place-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .place-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .place-category {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .place-rating {
            color: #ffc107;
        }
        
        .toggle-sidebar {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .toggle-sidebar:hover {
            transform: scale(1.1);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        .stats-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.95);
            padding: 10px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 999;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-weight: 700;
            font-size: 18px;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .popup-content {
            text-align: center;
            min-width: 200px;
        }
        
        .popup-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .popup-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .popup-category {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .popup-rating {
            color: #ffc107;
            margin-bottom: 10px;
        }
        
        .popup-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- Toggle Sidebar Button -->
    <button class="toggle-sidebar" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Map Container -->
    <div id="map"></div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="fas fa-map-marked-alt"></i> خرائط اليمن</h4>
            <p class="mb-0">اكتشف الأماكن من حولك</p>
        </div>
        
        <div class="sidebar-content">
            <!-- Search Box -->
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="ابحث عن مكان...">
                <i class="fas fa-search"></i>
            </div>
            
            <!-- Category Filters -->
            <div class="category-filters">
                <h6>الفئات</h6>
                <div id="categoryButtons">
                    <a href="#" class="category-btn active" data-category="">الكل</a>
                    <a href="#" class="category-btn" data-category="restaurant">مطاعم</a>
                    <a href="#" class="category-btn" data-category="hospital">مستشفيات</a>
                    <a href="#" class="category-btn" data-category="school">مدارس</a>
                    <a href="#" class="category-btn" data-category="mosque">مساجد</a>
                    <a href="#" class="category-btn" data-category="bank">بنوك</a>
                    <a href="#" class="category-btn" data-category="gas_station">محطات وقود</a>
                </div>
            </div>
            
            <!-- Places List -->
            <div id="placesList">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>جاري تحميل الأماكن...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stats Bar -->
    <div class="stats-bar">
        <div class="stat-item">
            <div class="stat-number" id="totalPlaces">0</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="visiblePlaces">0</div>
            <div class="stat-label">الأماكن المعروضة</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="currentGovernorate">صنعاء</div>
            <div class="stat-label">المحافظة الحالية</div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let map;
        let placesLayer;
        let currentPlaces = [];
        let currentFilter = '';
        let currentSearch = '';
        
        // Initialize map
        function initMap() {
            // Create map centered on Sanaa
            map = L.map('map').setView([15.3694, 44.1910], 12);
            
            // Add tile layer (using OpenStreetMap for now)
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Initialize places layer
            placesLayer = L.layerGroup().addTo(map);
            
            // Load places
            loadPlaces();
            
            // Load stats
            loadStats();
        }
        
        // Load places from API
        async function loadPlaces() {
            try {
                const response = await fetch('/api/places?limit=500');
                const data = await response.json();
                
                if (data.success) {
                    currentPlaces = data.places;
                    displayPlaces(currentPlaces);
                    updateStats();
                } else {
                    console.error('Error loading places:', data.error);
                }
            } catch (error) {
                console.error('Error loading places:', error);
                document.getElementById('placesList').innerHTML = 
                    '<div class="loading"><i class="fas fa-exclamation-triangle"></i><p>خطأ في تحميل البيانات</p></div>';
            }
        }
        
        // Display places on map and sidebar
        function displayPlaces(places) {
            // Clear existing markers
            placesLayer.clearLayers();
            
            // Clear sidebar list
            const placesList = document.getElementById('placesList');
            placesList.innerHTML = '';
            
            if (places.length === 0) {
                placesList.innerHTML = '<div class="loading"><p>لا توجد أماكن مطابقة للبحث</p></div>';
                return;
            }
            
            places.forEach(place => {
                // Add marker to map
                const marker = L.marker([place.latitude, place.longitude])
                    .bindPopup(createPopupContent(place))
                    .addTo(placesLayer);
                
                // Add to sidebar list
                const placeCard = createPlaceCard(place);
                placesList.appendChild(placeCard);
            });
            
            // Update visible places count
            document.getElementById('visiblePlaces').textContent = places.length;
        }
        
        // Create popup content for markers
        function createPopupContent(place) {
            return `
                <div class="popup-content">
                    ${place.primary_photo ? `<img src="${place.primary_photo}" class="popup-image" alt="${place.name}">` : ''}
                    <div class="popup-name">${place.name || place.name_ar}</div>
                    <div class="popup-category">${getCategoryName(place.category)}</div>
                    ${place.rating ? `<div class="popup-rating">${'★'.repeat(Math.floor(place.rating))} ${place.rating}</div>` : ''}
                    <button class="popup-btn" onclick="showPlaceDetails('${place.place_id}')">
                        عرض التفاصيل
                    </button>
                </div>
            `;
        }
        
        // Create place card for sidebar
        function createPlaceCard(place) {
            const card = document.createElement('div');
            card.className = 'place-card';
            card.onclick = () => {
                map.setView([place.latitude, place.longitude], 16);
                showPlaceDetails(place.place_id);
            };
            
            card.innerHTML = `
                ${place.primary_photo ? `<img src="${place.primary_photo}" class="place-image" alt="${place.name}">` : ''}
                <div class="place-name">${place.name || place.name_ar}</div>
                <div class="place-category">${getCategoryName(place.category)}</div>
                ${place.rating ? `<div class="place-rating">${'★'.repeat(Math.floor(place.rating))} ${place.rating}</div>` : ''}
                ${place.address ? `<div class="text-muted small">${place.address}</div>` : ''}
            `;
            
            return card;
        }
        
        // Get category name in Arabic
        function getCategoryName(category) {
            const categories = {
                'restaurant': 'مطعم',
                'hospital': 'مستشفى',
                'school': 'مدرسة',
                'mosque': 'مسجد',
                'bank': 'بنك',
                'gas_station': 'محطة وقود',
                'shopping_mall': 'مركز تجاري',
                'hotel': 'فندق',
                'pharmacy': 'صيدلية',
                'university': 'جامعة'
            };
            return categories[category] || category;
        }
        
        // Show place details
        async function showPlaceDetails(placeId) {
            try {
                const response = await fetch(`/api/place/${placeId}`);
                const data = await response.json();
                
                if (data.success) {
                    // Here you can show a modal or detailed view
                    console.log('Place details:', data.place);
                    alert(`تفاصيل ${data.place.name}\nالعنوان: ${data.place.address || 'غير محدد'}\nالهاتف: ${data.place.phone || 'غير محدد'}`);
                }
            } catch (error) {
                console.error('Error loading place details:', error);
            }
        }
        
        // Filter places by category
        function filterByCategory(category) {
            currentFilter = category;
            const filtered = category ? 
                currentPlaces.filter(place => place.category === category) : 
                currentPlaces;
            
            displayPlaces(filtered);
            
            // Update active button
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-category="${category}"]`).classList.add('active');
        }
        
        // Search places
        function searchPlaces(query) {
            currentSearch = query.toLowerCase();
            const filtered = currentPlaces.filter(place => 
                (place.name && place.name.toLowerCase().includes(currentSearch)) ||
                (place.name_ar && place.name_ar.includes(currentSearch)) ||
                (place.address && place.address.toLowerCase().includes(currentSearch))
            );
            
            displayPlaces(filtered);
        }
        
        // Load and update stats
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('totalPlaces').textContent = data.stats.total_places;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        // Update stats
        function updateStats() {
            document.getElementById('totalPlaces').textContent = currentPlaces.length;
            document.getElementById('visiblePlaces').textContent = currentPlaces.length;
        }
        
        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            
            // Search input
            document.getElementById('searchInput').addEventListener('input', function(e) {
                searchPlaces(e.target.value);
            });
            
            // Category buttons
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    filterByCategory(this.dataset.category);
                });
            });
        });
    </script>
</body>
</html>

/**
 * وحدة API للمواقع والطقس والأماكن القريبة
 */

const express = require('express');
const fetch = require('node-fetch');
const router = express.Router();
const { pool } = require('./db-config');

// الحصول على المواقع
router.get('/locations', async (req, res) => {
  try {
    // الحصول على معلمات البحث
    const { south, west, north, east, category } = req.query;
    
    let query = 'SELECT * FROM locations';
    const params = [];
    
    // إضافة شروط البحث
    if (south && west && north && east) {
      query += ' WHERE lat BETWEEN $1 AND $2 AND lng BETWEEN $3 AND $4';
      params.push(south, north, west, east);
      
      if (category) {
        query += ' AND category = $5';
        params.push(category);
      }
    } else if (category) {
      query += ' WHERE category = $1';
      params.push(category);
    }
    
    // تنفيذ الاستعلام
    const result = await pool.query(query, params);
    
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في الحصول على المواقع:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء الحصول على المواقع' });
  }
});

// الحصول على معلومات الطقس
router.get('/weather', async (req, res) => {
  try {
    const { lat, lng } = req.query;
    
    if (!lat || !lng) {
      return res.status(400).json({ error: 'يجب توفير خط العرض وخط الطول' });
    }
    
    // استخدام واجهة برمجة تطبيقات OpenWeatherMap
    const apiKey = process.env.OPENWEATHERMAP_API_KEY || 'YOUR_API_KEY';
    const url = `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lng}&appid=${apiKey}&units=metric&lang=ar`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`خطأ في الاستجابة: ${response.status}`);
    }
    
    const data = await response.json();
    
    // تنسيق البيانات
    const weatherData = {
      temperature: Math.round(data.main.temp),
      description: data.weather[0].description,
      icon: `https://openweathermap.org/img/wn/${data.weather[0].icon}@2x.png`,
      humidity: data.main.humidity,
      windSpeed: data.wind.speed,
      pressure: data.main.pressure,
      sunrise: new Date(data.sys.sunrise * 1000).toLocaleTimeString('ar-YE'),
      sunset: new Date(data.sys.sunset * 1000).toLocaleTimeString('ar-YE')
    };
    
    res.json(weatherData);
  } catch (error) {
    console.error('خطأ في الحصول على معلومات الطقس:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء الحصول على معلومات الطقس' });
  }
});

// الحصول على الأماكن القريبة
router.get('/nearby', async (req, res) => {
  try {
    const { lat, lng, radius, category } = req.query;
    
    if (!lat || !lng) {
      return res.status(400).json({ error: 'يجب توفير خط العرض وخط الطول' });
    }
    
    const radiusValue = radius || 500; // المسافة الافتراضية 500 متر
    
    // استعلام لإيجاد الأماكن القريبة
    let query = `
      SELECT *,
        (6371 * acos(cos(radians($1)) * cos(radians(lat)) * cos(radians(lng) - radians($2)) + sin(radians($1)) * sin(radians(lat)))) AS distance
      FROM locations
      WHERE (6371 * acos(cos(radians($1)) * cos(radians(lat)) * cos(radians(lng) - radians($2)) + sin(radians($1)) * sin(radians(lat)))) < $3
    `;
    
    const params = [lat, lng, radiusValue / 1000]; // تحويل المسافة إلى كيلومترات
    
    if (category) {
      query += ' AND category = $4';
      params.push(category);
    }
    
    query += ' ORDER BY distance LIMIT 10';
    
    const result = await pool.query(query, params);
    
    // تنسيق النتائج
    const places = result.rows.map(place => {
      // حساب المسافة بالمتر أو الكيلومتر
      const distanceInKm = place.distance;
      let formattedDistance;
      
      if (distanceInKm < 1) {
        formattedDistance = `${Math.round(distanceInKm * 1000)} متر`;
      } else {
        formattedDistance = `${distanceInKm.toFixed(1)} كم`;
      }
      
      return {
        ...place,
        distance: formattedDistance
      };
    });
    
    res.json(places);
  } catch (error) {
    console.error('خطأ في الحصول على الأماكن القريبة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء الحصول على الأماكن القريبة' });
  }
});

// الحصول على نقاط الاهتمام
router.get('/pois', async (req, res) => {
  try {
    const { south, west, north, east, category } = req.query;
    
    if (!south || !west || !north || !east) {
      return res.status(400).json({ error: 'يجب توفير حدود المنطقة' });
    }
    
    let query = `
      SELECT * FROM locations
      WHERE lat BETWEEN $1 AND $2 AND lng BETWEEN $3 AND $4
    `;
    
    const params = [south, north, west, east];
    
    if (category) {
      query += ' AND category = $5';
      params.push(category);
    }
    
    const result = await pool.query(query, params);
    
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في الحصول على نقاط الاهتمام:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء الحصول على نقاط الاهتمام' });
  }
});

// الحصول على الموقع بناءً على عنوان IP
router.get('/ip-location', async (req, res) => {
  try {
    // تحسين استخراج عنوان IP
    const ip = req.headers['x-forwarded-for'] || 
              req.connection.remoteAddress || 
              req.socket.remoteAddress || 
              (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
              '127.0.0.1';
    
    // تنظيف عنوان IP (إزالة ::ffff: إذا كان موجوداً)
    const cleanIp = ip.includes('::ffff:') ? ip.split('::ffff:')[1] : ip;
    
    console.log(`محاولة تحديد الموقع لعنوان IP: ${cleanIp}`);
    
    // التحقق مما إذا كان عنوان IP محلياً
    const isLocalIp = ['127.0.0.1', 'localhost', '::1'].includes(cleanIp) || 
                     cleanIp.startsWith('192.168.') || 
                     cleanIp.startsWith('10.') || 
                     cleanIp.startsWith('172.16.');
    
    if (isLocalIp) {
      console.log('تم اكتشاف عنوان IP محلي، استخدام موقع افتراضي (صنعاء)');
      return res.json({
        lat: 15.3694,
        lng: 44.1910,
        city: 'صنعاء',
        country: 'اليمن',
        provider: 'default-local'
      });
    }
    
    // محاولة استخدام الخدمات المختلفة بالتتابع
    try {
      // إضافة مهلة زمنية للطلبات
      const fetchWithTimeout = async (url, options = {}, timeout = 5000) => {
        const controller = new AbortController();
        const id = setTimeout(() => controller.abort(), timeout);
        
        try {
          const response = await fetch(url, {
            ...options,
            signal: controller.signal
          });
          clearTimeout(id);
          return response;
        } catch (error) {
          clearTimeout(id);
          throw error;
        }
      };
      
      // محاولة أولى باستخدام ipapi.co
      console.log('محاولة استخدام خدمة ipapi.co...');
      const response = await fetchWithTimeout(`https://ipapi.co/${cleanIp}/json/`, {}, 3000);
      
      if (response.ok) {
        const data = await response.json();
        
        if (data && data.latitude && data.longitude && !data.error) {
          console.log('تم الحصول على الموقع بنجاح من ipapi.co');
          return res.json({
            lat: data.latitude,
            lng: data.longitude,
            city: data.city || 'غير معروف',
            country: data.country_name || 'اليمن',
            provider: 'ipapi.co'
          });
        }
      }
      
      // محاولة ثانية باستخدام ipwho.is
      console.log('محاولة استخدام خدمة ipwho.is...');
      const backupResponse = await fetchWithTimeout(`https://ipwho.is/${cleanIp}`, {}, 3000);
      
      if (backupResponse.ok) {
        const backupData = await backupResponse.json();
        
        if (backupData && backupData.latitude && backupData.longitude && backupData.success !== false) {
          console.log('تم الحصول على الموقع بنجاح من ipwho.is');
          return res.json({
            lat: backupData.latitude,
            lng: backupData.longitude,
            city: backupData.city || 'غير معروف',
            country: backupData.country || 'اليمن',
            provider: 'ipwho.is'
          });
        }
      }
      
      // محاولة ثالثة باستخدام ip-api.com
      console.log('محاولة استخدام خدمة ip-api.com...');
      const thirdResponse = await fetchWithTimeout(`http://ip-api.com/json/${cleanIp}?fields=status,lat,lon,city,country`, {}, 3000);
      
      if (thirdResponse.ok) {
        const thirdData = await thirdResponse.json();
        
        if (thirdData && thirdData.lat && thirdData.lon && thirdData.status === 'success') {
          console.log('تم الحصول على الموقع بنجاح من ip-api.com');
          return res.json({
            lat: thirdData.lat,
            lng: thirdData.lon,
            city: thirdData.city || 'غير معروف',
            country: thirdData.country || 'اليمن',
            provider: 'ip-api.com'
          });
        }
      }
      
      // إذا فشلت جميع المحاولات، استخدام موقع افتراضي
      console.log('فشلت جميع محاولات تحديد الموقع، استخدام موقع افتراضي');
      return res.json({
        lat: 15.3694,
        lng: 44.1910,
        city: 'صنعاء',
        country: 'اليمن',
        provider: 'default-fallback'
      });
    } catch (serviceError) {
      console.error('خطأ في خدمات تحديد الموقع:', serviceError);
      
      // استخدام موقع افتراضي في حالة الخطأ
      return res.json({
        lat: 15.3694,
        lng: 44.1910,
        city: 'صنعاء',
        country: 'اليمن',
        provider: 'default-error'
      });
    }
  } catch (error) {
    console.error('خطأ في الحصول على الموقع بناءً على IP:', error);
    
    // في حالة حدوث أي خطأ، نعيد موقع افتراضي (صنعاء)
    res.json({
      lat: 15.3694,
      lng: 44.1910,
      city: 'صنعاء',
      country: 'اليمن',
      provider: 'default'
    });
  }
});

// الحصول على موقع المدينة بناءً على الاسم
router.get('/city-location', async (req, res) => {
  try {
    const { city } = req.query;
    
    if (!city) {
      return res.status(400).json({ error: 'يجب توفير اسم المدينة' });
    }
    
    // قائمة المدن اليمنية الرئيسية مع إحداثياتها
    const yemenCities = {
      'صنعاء': { lat: 15.3694, lng: 44.1910 },
      'عدن': { lat: 12.7797, lng: 45.0095 },
      'الحديدة': { lat: 14.7979, lng: 42.9542 },
      'تعز': { lat: 13.9789, lng: 44.1754 },
      'ذمار': { lat: 15.4542, lng: 44.2055 },
      'إب': { lat: 14.0783, lng: 44.2464 },
      'صعدة': { lat: 16.9398, lng: 43.5895 },
      'مأرب': { lat: 15.4248, lng: 45.3292 },
      'البيضاء': { lat: 13.9815, lng: 45.5746 },
      'حضرموت': { lat: 15.9266, lng: 48.7910 },
      'المكلا': { lat: 14.5365, lng: 49.1295 },
      'عمران': { lat: 15.6594, lng: 43.9430 },
      'حجة': { lat: 15.6917, lng: 43.6021 },
      'لحج': { lat: 13.0582, lng: 44.8814 },
      'أبين': { lat: 13.5800, lng: 45.9500 },
      'الضالع': { lat: 13.6932, lng: 44.7291 },
      'شبوة': { lat: 14.5300, lng: 47.0000 },
      'المحويت': { lat: 15.4700, lng: 43.5600 },
      'ريمة': { lat: 14.7000, lng: 43.4000 }
    };
    
    // البحث عن المدينة في القائمة
    const cityName = city.trim();
    const cityLocation = yemenCities[cityName];
    
    if (cityLocation) {
      res.json({
        ...cityLocation,
        city: cityName,
        country: 'اليمن'
      });
    } else {
      // إذا لم يتم العثور على المدينة، نستخدم صنعاء كموقع افتراضي
      res.json({
        lat: 15.3694,
        lng: 44.1910,
        city: 'صنعاء',
        country: 'اليمن',
        note: 'تم استخدام موقع افتراضي لأن المدينة المطلوبة غير موجودة في قاعدة البيانات'
      });
    }
  } catch (error) {
    console.error('خطأ في الحصول على موقع المدينة:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء الحصول على موقع المدينة' });
  }
});

module.exports = router;

# دليل تثبيت وتشغيل نظام "يمن ناف" على ويندوز

## مقدمة

هذا الدليل يشرح كيفية تثبيت وإعداد نظام "يمن ناف" على جهاز ويندوز الحالي واستخدامه كخادم ويب. سنقوم بتثبيت جميع المتطلبات اللازمة وتكوين النظام للعمل محليًا.

## متطلبات النظام

- نظام تشغيل: Windows 10 أو Windows 11
- وحدة المعالجة المركزية: 4 أنوية على الأقل
- الذاكرة: 8 جيجابايت على الأقل
- مساحة التخزين: 100 جيجابايت على الأقل (للخرائط والبيانات)
- اتصال إنترنت: 10 ميجابت/ثانية على الأقل

## خطوات التثبيت

### 1. تثبيت Node.js

1. قم بتنزيل Node.js من الموقع الرسمي: https://nodejs.org/
2. اختر الإصدار LTS (الدعم طويل الأمد) - الإصدار 16.x أو أحدث
3. قم بتشغيل ملف التثبيت واتبع التعليمات
4. تأكد من تحديد خيار "Add to PATH" أثناء التثبيت
5. تحقق من التثبيت بفتح موجه الأوامر (Command Prompt) وكتابة:
   ```
   node --version
   npm --version
   ```

### 2. تثبيت PostgreSQL وPostGIS

1. قم بتنزيل PostgreSQL من الموقع الرسمي: https://www.postgresql.org/download/windows/
2. قم بتشغيل ملف التثبيت واتبع التعليمات
3. حدد المكونات التالية أثناء التثبيت:
   - PostgreSQL Server
   - pgAdmin 4
   - Stack Builder
   - Command Line Tools
4. اختر كلمة مرور آمنة لمستخدم postgres واحتفظ بها
5. اترك منفذ الاتصال الافتراضي (5432)
6. بعد اكتمال التثبيت، قم بتشغيل Stack Builder
7. اختر الخادم المثبت من القائمة المنسدلة
8. في قسم "Spatial Extensions"، اختر "PostGIS Bundle"
9. انقر على "Next" واتبع التعليمات لتثبيت PostGIS

### 3. إنشاء قاعدة البيانات

1. افتح pgAdmin 4 من قائمة ابدأ
2. قم بتوصيل الخادم المحلي (أدخل كلمة المرور التي حددتها أثناء التثبيت)
3. انقر بزر الماوس الأيمن على "Databases" واختر "Create" > "Database"
4. أدخل "yemen_nav" كاسم لقاعدة البيانات وانقر على "Save"
5. انقر بزر الماوس الأيمن على قاعدة البيانات الجديدة واختر "Query Tool"
6. أدخل الأوامر التالية وانقر على "Execute":
   ```sql
   CREATE EXTENSION postgis;
   CREATE EXTENSION postgis_topology;
   
   CREATE USER yemen_nav_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE yemen_nav TO yemen_nav_user;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yemen_nav_user;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yemen_nav_user;
   ```

### 4. تثبيت Git

1. قم بتنزيل Git من الموقع الرسمي: https://git-scm.com/download/win
2. قم بتشغيل ملف التثبيت واتبع التعليمات
3. اختر الإعدادات الافتراضية في معظم الخطوات
4. في خطوة "Adjusting your PATH environment"، اختر "Git from the command line and also from 3rd-party software"
5. تحقق من التثبيت بفتح موجه الأوامر وكتابة:
   ```
   git --version
   ```

### 5. إنشاء مجلدات المشروع

1. افتح موجه الأوامر بصلاحيات المسؤول
2. قم بإنشاء مجلد للمشروع:
   ```
   mkdir C:\yemen-nav
   cd C:\yemen-nav
   mkdir backend
   mkdir frontend
   mkdir storage
   ```

### 6. إعداد الواجهة الخلفية (Backend)

#### تنزيل وإعداد الكود المصدري

1. افتح موجه الأوامر وانتقل إلى مجلد المشروع:
   ```
   cd C:\yemen-nav\backend
   ```

2. قم بإنشاء مشروع Node.js جديد:
   ```
   npm init -y
   ```

3. قم بتثبيت الحزم اللازمة:
   ```
   npm install express cors body-parser pg pg-hstore sequelize dotenv jsonwebtoken bcrypt multer mapbox-gl @turf/turf
   npm install nodemon morgan winston --save-dev
   ```

4. قم بإنشاء ملف `.env` في المجلد:
   ```
   echo DB_HOST=localhost > .env
   echo DB_PORT=5432 >> .env
   echo DB_NAME=yemen_nav >> .env
   echo DB_USER=yemen_nav_user >> .env
   echo DB_PASSWORD=your_secure_password >> .env
   echo PORT=3000 >> .env
   echo NODE_ENV=development >> .env
   echo JWT_SECRET=your_jwt_secret_key >> .env
   echo STORAGE_PATH=C:/yemen-nav/storage >> .env
   ```

5. قم بإنشاء ملف `server.js`:
   ```
   echo // Yemen Nav Backend Server > server.js
   echo const express = require('express'); >> server.js
   echo const cors = require('cors'); >> server.js
   echo const bodyParser = require('body-parser'); >> server.js
   echo const dotenv = require('dotenv'); >> server.js
   echo >> server.js
   echo // Load environment variables >> server.js
   echo dotenv.config(); >> server.js
   echo >> server.js
   echo const app = express(); >> server.js
   echo >> server.js
   echo // Middleware >> server.js
   echo app.use(cors()); >> server.js
   echo app.use(bodyParser.json()); >> server.js
   echo app.use(bodyParser.urlencoded({ extended: true })); >> server.js
   echo >> server.js
   echo // Routes >> server.js
   echo app.get('/', (req, res) => { >> server.js
   echo   res.json({ message: 'Welcome to Yemen Nav API' }); >> server.js
   echo }); >> server.js
   echo >> server.js
   echo // Start server >> server.js
   echo const PORT = process.env.PORT || 3000; >> server.js
   echo app.listen(PORT, () => { >> server.js
   echo   console.log(`Server is running on port ${PORT}`); >> server.js
   echo }); >> server.js
   ```

6. قم بتعديل ملف `package.json` لإضافة سكربتات التشغيل:
   ```
   npm pkg set scripts.start="node server.js"
   npm pkg set scripts.dev="nodemon server.js"
   ```

### 7. إعداد الواجهة الأمامية (Frontend)

#### تثبيت Create React App

1. افتح موجه الأوامر وانتقل إلى مجلد المشروع:
   ```
   cd C:\yemen-nav
   npm install -g create-react-app
   ```

#### إنشاء تطبيق React

1. قم بإنشاء تطبيق React جديد:
   ```
   cd C:\yemen-nav\frontend
   npx create-react-app .
   ```

2. قم بتثبيت الحزم اللازمة:
   ```
   npm install react-router-dom axios mapbox-gl @turf/turf styled-components material-ui-core material-ui-icons
   ```

3. قم بإنشاء ملف `.env` في المجلد:
   ```
   echo REACT_APP_API_URL=http://localhost:3000 > .env
   echo REACT_APP_MAPBOX_TOKEN=your_mapbox_token >> .env
   ```

### 8. تثبيت وإعداد خادم الويب (IIS)

#### تثبيت IIS

1. افتح "Control Panel" > "Programs" > "Programs and Features" > "Turn Windows features on or off"
2. حدد "Internet Information Services" وتأكد من تحديد الميزات التالية:
   - Web Management Tools
   - World Wide Web Services
     - Application Development Features
       - .NET Extensibility
       - ASP.NET
       - CGI
       - ISAPI Extensions
       - ISAPI Filters
     - Common HTTP Features (all)
     - Health and Diagnostics (all)
     - Performance Features (all)
     - Security (all)
3. انقر على "OK" وانتظر حتى يتم تثبيت الميزات

#### تثبيت URL Rewrite Module

1. قم بتنزيل وتثبيت URL Rewrite Module من الموقع الرسمي:
   https://www.iis.net/downloads/microsoft/url-rewrite

#### إعداد IIS لاستضافة التطبيق

1. افتح "Internet Information Services (IIS) Manager" من قائمة ابدأ
2. انقر بزر الماوس الأيمن على "Sites" واختر "Add Website"
3. أدخل المعلومات التالية:
   - Site name: Yemen Nav
   - Physical path: C:\yemen-nav\frontend\build
   - Binding: Port 80, Host name: yemen-nav.local
4. انقر على "OK"

5. أضف السجل التالي إلى ملف hosts (C:\Windows\System32\drivers\etc\hosts):
   ```
   127.0.0.1 yemen-nav.local
   ```

### 9. تثبيت PM2 لإدارة العمليات

1. افتح موجه الأوامر وقم بتثبيت PM2 عالميًا:
   ```
   npm install -g pm2
   ```

2. قم بإنشاء ملف تكوين PM2:
   ```
   cd C:\yemen-nav
   echo module.exports = { > ecosystem.config.js
   echo   apps: [{ >> ecosystem.config.js
   echo     name: 'yemen-nav-backend', >> ecosystem.config.js
   echo     script: './backend/server.js', >> ecosystem.config.js
   echo     env: { >> ecosystem.config.js
   echo       NODE_ENV: 'production', >> ecosystem.config.js
   echo     }, >> ecosystem.config.js
   echo   }], >> ecosystem.config.js
   echo }; >> ecosystem.config.js
   ```

3. قم بتثبيت PM2-Windows-Startup لتشغيل PM2 تلقائيًا عند بدء تشغيل Windows:
   ```
   npm install -g pm2-windows-startup
   pm2-startup install
   ```

### 10. بناء وتشغيل النظام

#### بناء الواجهة الأمامية

1. افتح موجه الأوامر وانتقل إلى مجلد الواجهة الأمامية:
   ```
   cd C:\yemen-nav\frontend
   npm run build
   ```

#### تشغيل الواجهة الخلفية

1. افتح موجه الأوامر وانتقل إلى مجلد المشروع:
   ```
   cd C:\yemen-nav
   pm2 start ecosystem.config.js
   pm2 save
   ```

#### تكوين إعادة توجيه IIS

1. قم بإنشاء ملف `web.config` في مجلد `C:\yemen-nav\frontend\build`:
   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <configuration>
     <system.webServer>
       <rewrite>
         <rules>
           <rule name="React Routes" stopProcessing="true">
             <match url=".*" />
             <conditions logicalGrouping="MatchAll">
               <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
               <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
               <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
             </conditions>
             <action type="Rewrite" url="/" />
           </rule>
           <rule name="API Proxy" stopProcessing="true">
             <match url="^api/(.*)" />
             <action type="Rewrite" url="http://localhost:3000/{R:1}" />
           </rule>
         </rules>
       </rewrite>
       <httpErrors errorMode="Custom" existingResponse="Replace">
         <remove statusCode="404" />
         <error statusCode="404" path="/" responseMode="ExecuteURL" />
       </httpErrors>
     </system.webServer>
   </configuration>
   ```

2. أعد تشغيل موقع IIS:
   - افتح "Internet Information Services (IIS) Manager"
   - انقر بزر الماوس الأيمن على موقع "Yemen Nav" واختر "Manage Website" > "Restart"

## الوصول إلى النظام

بعد إكمال جميع خطوات التثبيت والإعداد، يمكنك الوصول إلى نظام "يمن ناف" من خلال المتصفح:

1. افتح متصفح الويب (Chrome، Firefox، Edge، إلخ)
2. انتقل إلى العنوان التالي:
   ```
   http://yemen-nav.local
   ```
   أو
   ```
   http://localhost
   ```

## استكشاف الأخطاء وإصلاحها

### مشاكل الواجهة الخلفية

1. تحقق من حالة خدمة الواجهة الخلفية:
   ```
   pm2 status
   ```

2. تحقق من سجلات الواجهة الخلفية:
   ```
   pm2 logs yemen-nav-backend
   ```

3. تأكد من أن منفذ 3000 غير مستخدم بالفعل:
   ```
   netstat -ano | findstr :3000
   ```

### مشاكل قاعدة البيانات

1. تحقق من حالة خدمة PostgreSQL:
   - افتح "Services" من قائمة ابدأ
   - ابحث عن "postgresql" وتأكد من أنها قيد التشغيل

2. تحقق من الاتصال بقاعدة البيانات:
   - افتح pgAdmin 4
   - تأكد من أنه يمكنك الاتصال بالخادم المحلي
   - تأكد من وجود قاعدة البيانات "yemen_nav"

### مشاكل IIS

1. تحقق من حالة موقع IIS:
   - افتح "Internet Information Services (IIS) Manager"
   - تأكد من أن موقع "Yemen Nav" قيد التشغيل

2. تحقق من سجلات IIS:
   - افتح "Internet Information Services (IIS) Manager"
   - انقر على موقع "Yemen Nav"
   - انقر مزدوجًا على "Logging"
   - افتح مجلد السجلات وتحقق من الأخطاء

## تحديث النظام

### تحديث الواجهة الخلفية

1. افتح موجه الأوامر وانتقل إلى مجلد الواجهة الخلفية:
   ```
   cd C:\yemen-nav\backend
   git pull
   npm install
   pm2 restart yemen-nav-backend
   ```

### تحديث الواجهة الأمامية

1. افتح موجه الأوامر وانتقل إلى مجلد الواجهة الأمامية:
   ```
   cd C:\yemen-nav\frontend
   git pull
   npm install
   npm run build
   ```

## النسخ الاحتياطي واستعادة النظام

### نسخ احتياطي لقاعدة البيانات

1. افتح موجه الأوامر وقم بتنفيذ الأمر التالي:
   ```
   "C:\Program Files\PostgreSQL\13\bin\pg_dump.exe" -U postgres -F c -b -v -f "C:\yemen-nav\backups\yemen_nav_backup.backup" yemen_nav
   ```

### استعادة قاعدة البيانات

1. افتح موجه الأوامر وقم بتنفيذ الأمر التالي:
   ```
   "C:\Program Files\PostgreSQL\13\bin\pg_restore.exe" -U postgres -d yemen_nav -v "C:\yemen-nav\backups\yemen_nav_backup.backup"
   ```

## الخلاصة

باتباع هذا الدليل، قمت بتثبيت وإعداد نظام "يمن ناف" على جهاز ويندوز الحالي واستخدامه كخادم ويب. يمكنك الآن الوصول إلى النظام من خلال المتصفح وإضافة نقاط وعلامات جديدة على الخريطة.

للحصول على مساعدة إضافية، يرجى الاتصال بفريق الدعم الفني أو مراجعة الوثائق الإضافية المتوفرة في مجلد `docs`.

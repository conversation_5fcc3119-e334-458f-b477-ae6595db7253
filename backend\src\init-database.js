// يمن ناف - ملف تهيئة قاعدة البيانات
const db = require('./postgres-db');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// دالة لإنشاء جدول المستخدمين
async function createUsersTable() {
    try {
        const query = `
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                full_name VARCHAR(100),
                phone VARCHAR(20),
                profile_image VARCHAR(255),
                role_id INTEGER DEFAULT 2,
                account_type VARCHAR(20) DEFAULT 'personal',
                is_active BOOLEAN DEFAULT TRUE,
                is_verified BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                permissions_json JSONB DEFAULT '[]',
                can_access_admin BOOLEAN DEFAULT FALSE
            )
        `;

        await db.query(query);
        console.log('تم إنشاء جدول المستخدمين بنجاح');

        return true;
    } catch (error) {
        console.error('خطأ في إنشاء جدول المستخدمين:', error);
        throw error;
    }
}

// دالة لإنشاء جدول الأدوار
async function createRolesTable() {
    try {
        const query = `
            CREATE TABLE IF NOT EXISTS roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;

        await db.query(query);
        console.log('تم إنشاء جدول الأدوار بنجاح');

        // إدراج الأدوار الافتراضية
        const insertRolesQuery = `
            INSERT INTO roles (name, description)
            VALUES
                ('admin', 'مدير النظام مع كامل الصلاحيات'),
                ('user', 'مستخدم عادي'),
                ('developer', 'مطور النظام')
            ON CONFLICT (name) DO NOTHING
        `;

        await db.query(insertRolesQuery);
        console.log('تم إدراج الأدوار الافتراضية بنجاح');

        return true;
    } catch (error) {
        console.error('خطأ في إنشاء جدول الأدوار:', error);
        throw error;
    }
}

// دالة لإنشاء مستخدم المدير الافتراضي
async function createAdminUser() {
    try {
        const bcrypt = require('bcrypt');

        // تشفير كلمة المرور
        const hashedPassword = await bcrypt.hash('admin', 10);

        // إدراج مستخدم المدير
        const query = `
            INSERT INTO users (
                username,
                password,
                email,
                full_name,
                role_id,
                is_active,
                is_verified,
                can_access_admin,
                permissions_json
            )
            VALUES (
                'admin',
                $1,
                '<EMAIL>',
                'مدير النظام',
                1,
                TRUE,
                TRUE,
                TRUE,
                '[
                    {"code": "view_dashboard", "name": "عرض لوحة التحكم"},
                    {"code": "manage_users", "name": "إدارة المستخدمين"},
                    {"code": "manage_clients", "name": "إدارة العملاء"},
                    {"code": "manage_locations", "name": "إدارة المواقع"},
                    {"code": "manage_settings", "name": "إدارة الإعدادات"},
                    {"code": "manage_categories", "name": "إدارة التصنيفات"}
                ]'::jsonb
            )
            ON CONFLICT (username) DO NOTHING
            RETURNING id
        `;

        const result = await db.query(query, [hashedPassword]);

        if (result.rows.length > 0) {
            console.log('تم إنشاء مستخدم المدير بنجاح');
        } else {
            console.log('مستخدم المدير موجود بالفعل');
        }

        return true;
    } catch (error) {
        console.error('خطأ في إنشاء مستخدم المدير:', error);
        throw error;
    }
}

// دالة لإنشاء جدول العملاء
async function createClientsTable() {
    try {
        const query = `
            CREATE TABLE IF NOT EXISTS clients (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                address TEXT,
                deviceSN VARCHAR(50),
                licenseN VARCHAR(50),
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;

        await db.query(query);
        console.log('تم إنشاء جدول العملاء بنجاح');

        return true;
    } catch (error) {
        console.error('خطأ في إنشاء جدول العملاء:', error);
        throw error;
    }
}

// دالة رئيسية لتهيئة قاعدة البيانات
async function initDatabase() {
    try {
        console.log('بدء تهيئة قاعدة البيانات...');

        // طباعة معلومات الاتصال للتصحيح
        console.log('معلومات الاتصال بقاعدة البيانات:');
        console.log('DB_USER:', process.env.DB_USER || 'postgres');
        console.log('DB_HOST:', process.env.DB_HOST || 'localhost');
        console.log('DB_NAME:', process.env.DB_NAME || 'yemen_gps');
        console.log('DB_PORT:', process.env.DB_PORT || 5432);

        // التحقق من الاتصال بقاعدة البيانات
        const isConnected = await db.checkConnection();

        if (!isConnected) {
            console.error('فشل الاتصال بقاعدة البيانات');
            console.log('محاولة إعادة الاتصال...');

            // محاولة ثانية بعد انتظار قصير
            await new Promise(resolve => setTimeout(resolve, 2000));
            const retryConnection = await db.checkConnection();

            if (!retryConnection) {
                console.error('فشل الاتصال بقاعدة البيانات بعد المحاولة الثانية');
                process.exit(1);
            }
        }

        // إنشاء الجداول
        await createRolesTable();
        await createUsersTable();
        await createClientsTable();

        // إنشاء مستخدم المدير
        await createAdminUser();

        console.log('تم تهيئة قاعدة البيانات بنجاح');
        process.exit(0);
    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
        process.exit(1);
    }
}

// تنفيذ الدالة الرئيسية
initDatabase();

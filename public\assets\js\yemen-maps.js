/**
 * Yemen Maps - نظام خرائط اليمن
 * تطبيق خرائط متكامل يحاكي Google Maps ويعمل بدون اتصال بالإنترنت
 */

// تهيئة التطبيق
class YemenMaps {
    constructor() {
        // المتغيرات الأساسية
        this.map = null;
        this.currentMarker = null;
        this.markers = [];
        this.currentLocation = null;
        this.directionsLayer = null;
        this.currentLayer = 'streets';
        this.baseLayers = {};
        this.savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
        this.offlineMode = false;

        // تحقق من الاتصال بالإنترنت
        this.checkOnlineStatus();

        // تهيئة الخريطة
        this.initMap();

        // ربط الأحداث
        this.bindEvents();



        // تحقق من وجود موقع مشارك
        this.handleSharedLocation();

        // تحميل الإعدادات المحفوظة
        this.loadSavedSettings();

        // تحميل المواقع من الخادم
        this.loadLocations();

        // الحصول على الموقع الحالي تلقائياً عند فتح الخريطة
        setTimeout(() => {
            this.getCurrentLocation(false); // false = لا تحرك الخريطة تلقائياً
        }, 1000);

        // تحميل آخر موقع معروف
        this.loadLastKnownLocation();
    }

    // تحقق من حالة الاتصال بالإنترنت
    checkOnlineStatus() {
        this.offlineMode = !navigator.onLine;

        // إضافة مؤشر وضع عدم الاتصال
        if (this.offlineMode) {
            this.showOfflineIndicator();
        }

        window.addEventListener('online', () => {
            this.offlineMode = false;
            this.hideOfflineIndicator();
            this.showNotification('أنت متصل بالإنترنت الآن', 'success');
        });

        window.addEventListener('offline', () => {
            this.offlineMode = true;
            this.showOfflineIndicator();
            this.showNotification('أنت الآن في وضع عدم الاتصال بالإنترنت', 'warning');
        });
    }

    // إظهار مؤشر وضع عدم الاتصال
    showOfflineIndicator() {
        const offlineIndicator = document.createElement('div');
        offlineIndicator.id = 'offline-indicator';
        offlineIndicator.textContent = 'وضع عدم الاتصال بالإنترنت';
        document.body.appendChild(offlineIndicator);
    }

    // إخفاء مؤشر وضع عدم الاتصال
    hideOfflineIndicator() {
        const offlineIndicator = document.getElementById('offline-indicator');
        if (offlineIndicator) {
            offlineIndicator.remove();
        }
    }

    // إظهار إشعار للمستخدم
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }

    // ربط أحداث واجهة المستخدم
    bindEvents() {

        // ربط حدث البحث
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');

        if (searchInput && searchBtn) {
            searchBtn.addEventListener('click', () => {
                const query = searchInput.value.trim();
                if (query) {
                    this.searchLocation(query);
                }
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const query = searchInput.value.trim();
                    if (query) {
                        this.searchLocation(query);
                    }
                }
            });
        }

        // ربط أحداث أزرار التحكم
        const zoomInBtn = document.getElementById('zoom-in-btn');
        const zoomOutBtn = document.getElementById('zoom-out-btn');
        const myLocationBtn = document.getElementById('my-location-btn');

        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', () => {
                if (this.map) {
                    this.map.zoomIn();
                }
            });
        }

        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', () => {
                if (this.map) {
                    this.map.zoomOut();
                }
            });
        }

        if (myLocationBtn) {
            myLocationBtn.addEventListener('click', () => {
                this.getCurrentLocation(true);
            });
        }

        // ربط أحداث الأزرار الجديدة
        const nightModeBtn = document.getElementById('night-mode-btn');
        const fullscreenBtn = document.getElementById('fullscreen-btn');

        if (nightModeBtn) {
            nightModeBtn.addEventListener('click', () => {
                this.toggleNightMode();
            });
        }

        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });
        }

        // ربط زر تحديد الموقع يدوياً
        const manualLocationBtn = document.getElementById('manual-location-btn');
        if (manualLocationBtn) {
            manualLocationBtn.addEventListener('click', () => {
                this.showManualLocationModal();
            });
        }

        // ربط أحداث أزرار طبقات الخريطة
        const mapLayerBtns = document.querySelectorAll('.map-layer-btn');
        console.log('عدد أزرار طبقات الخريطة الموجودة:', mapLayerBtns.length);

        if (mapLayerBtns && mapLayerBtns.length > 0) {
            mapLayerBtns.forEach((btn, index) => {
                const layer = btn.getAttribute('data-layer');
                console.log(`زر ${index + 1}: طبقة ${layer}`);

                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log(`تم النقر على زر طبقة: ${layer}`);
                    if (layer) {
                        this.switchMapLayer(layer);
                    }
                });
            });
        } else {
            console.error('لم يتم العثور على أزرار طبقات الخريطة');
        }
    }

    // تهيئة الخريطة
    initMap() {
        // إظهار مؤشر التحميل
        this.showLoading(true);

        // حل مشكلة الخريطة في أندرويد - تأخير تهيئة الخريطة
        setTimeout(() => {
            try {
                // إنشاء الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.map = L.map('map', {
                    center: [15.3694, 44.1910], // مركز اليمن (صنعاء)
                    zoom: 7,
                    zoomControl: false,
                    attributionControl: true,
                    maxBounds: [
                        [10.0, 40.0], // الجنوب الغربي
                        [20.0, 55.0]  // الشمال الشرقي
                    ],
                    minZoom: 6,
                    maxZoom: 18,
                    tap: true,            // تمكين النقر للأجهزة اللمسية
                    tapTolerance: 30,     // زيادة مساحة النقر للأجهزة اللمسية
                    bounceAtZoomLimits: false // منع الارتداد عند حدود التكبير
                });

                // إضافة طبقات الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.baseLayers = {
                    streets: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19,
                        subdomains: 'abc',
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,     // تحديث الطبقة فقط عند التوقف لتحسين الأداء
                        updateWhenZooming: false   // لا تحدث أثناء التكبير/التصغير
                    }),

                    satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                        maxZoom: 18,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    }),

                    terrain: L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://opentopomap.org">OpenTopoMap</a> contributors',
                        maxZoom: 17,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    })
                };

                // إضافة الطبقة الافتراضية
                this.baseLayers[this.currentLayer].addTo(this.map);

                // إعداد طبقة العلامات
                this.markersLayer = L.layerGroup().addTo(this.map);

                // إضافة طبقة المسارات
                this.directionsLayer = L.layerGroup().addTo(this.map);

                // التعامل مع النقر على الخريطة
                this.map.on('click', (e) => {
                    this.handleMapClick(e);
                });

                // إضافة معالجة خاصة لأحداث اللمس على الأجهزة المحمولة
                if (L.Browser.mobile) {
                    // لمعالجة مشكلة النقر على الأجهزة المحمولة
                    document.querySelector('#map').addEventListener('touchend', function(e) {
                        // منع التمرير بعد النقر
                        if (e.touches && e.touches.length === 0) {
                            e.preventDefault();
                        }
                    }, false);
                }

                // إنهاء التحميل
                this.map.whenReady(() => {
                    this.showLoading(false);
                    console.log('تم تحميل الخريطة بنجاح');

                    // إعادة ضبط حجم الخريطة بعد التحميل لضمان العرض الصحيح
                    setTimeout(() => {
                        this.map.invalidateSize();
                    }, 200);
                });
            } catch (error) {
                console.error('خطأ في تهيئة الخريطة:', error);
                this.showLoading(false);
            }
        }, 500);
    }

    // إظهار أو إخفاء مؤشر التحميل
    showLoading(show) {
        const loader = document.getElementById('loading-indicator');
        if (loader) {
            loader.style.display = show ? 'flex' : 'none';
        }
    }

    // عرض تفاصيل المكان المحدد
    showPlaceDetails(location) {
        // التحقق من وجود مدير تفاصيل المكان
        if (this.placeDetailsManager) {
            this.placeDetailsManager.showPlaceDetails(location);
        } else if (window.placeDetailsManager) {
            window.placeDetailsManager.showPlaceDetails(location);
        } else {
            console.error('مدير تفاصيل المكان غير متوفر');
        }
    }

    // عرض علامة الموقع الافتراضي
    showFallbackLocationMarker(location, centerMap = false) {
        // إزالة العلامة السابقة إن وجدت
        if (this.currentMarker) {
            this.map.removeLayer(this.currentMarker);
        }

        const lat = location.lat;
        const lng = location.lng;
        this.currentLocation = { lat, lng };

        // إنشاء علامة مميزة للموقع الافتراضي
        const fallbackIcon = L.divIcon({
            className: 'fallback-location-marker',
            html: '<div class="fallback-marker-inner"><i class="fas fa-map-marker-alt"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 30]
        });

        this.currentMarker = L.marker([lat, lng], { icon: fallbackIcon }).addTo(this.map);

        // إضافة نافذة منبثقة لتوضيح أن هذا موقع افتراضي
        this.currentMarker.bindPopup('موقع افتراضي - تعذر تحديد موقعك الحقيقي').openPopup();

        // تحريك الخريطة إلى الموقع الافتراضي إذا كان مطلوبًا
        if (centerMap) {
            this.map.setView([lat, lng], 15);
        }
    }

    // تبديل نوع الخريطة
    switchMapLayer(layer) {
        console.log(`محاولة تبديل الطبقة إلى: ${layer}`);
        console.log('الطبقة الحالية:', this.currentLayer);
        console.log('الطبقات المتاحة:', Object.keys(this.baseLayers || {}));

        if (!this.map) {
            console.error('الخريطة غير مهيأة');
            return;
        }

        if (!this.baseLayers) {
            console.error('طبقات الخريطة غير مهيأة');
            return;
        }

        if (!this.baseLayers[layer]) {
            console.error(`نوع الخريطة غير موجود: ${layer}`);
            console.log('الطبقات المتاحة:', Object.keys(this.baseLayers));
            return;
        }

        // إزالة الطبقة الحالية
        if (this.currentLayer && this.baseLayers[this.currentLayer]) {
            console.log(`إزالة الطبقة الحالية: ${this.currentLayer}`);
            this.map.removeLayer(this.baseLayers[this.currentLayer]);
        }

        // إضافة الطبقة الجديدة
        console.log(`إضافة الطبقة الجديدة: ${layer}`);
        this.baseLayers[layer].addTo(this.map);
        this.currentLayer = layer;

        // تحديث حالة الأزرار
        const mapLayerBtns = document.querySelectorAll('.map-layer-btn');
        console.log('تحديث حالة الأزرار، عدد الأزرار:', mapLayerBtns.length);

        if (mapLayerBtns && mapLayerBtns.length > 0) {
            mapLayerBtns.forEach(btn => {
                const btnLayer = btn.getAttribute('data-layer');
                if (btnLayer === layer) {
                    btn.classList.add('active');
                    console.log(`تفعيل زر: ${btnLayer}`);
                } else {
                    btn.classList.remove('active');
                    console.log(`إلغاء تفعيل زر: ${btnLayer}`);
                }
            });
        }

        // تخزين الاختيار المفضل للمستخدم
        localStorage.setItem('preferredMapLayer', layer);

        console.log(`تم تغيير نوع الخريطة إلى: ${layer} بنجاح`);
        this.showNotification(`تم تغيير الخريطة إلى: ${this.getLayerDisplayName(layer)}`, 'success');
    }

    // الحصول على اسم الطبقة للعرض
    getLayerDisplayName(layer) {
        const names = {
            'streets': 'خريطة الشوارع',
            'satellite': 'صور الأقمار الصناعية',
            'terrain': 'خريطة التضاريس'
        };
        return names[layer] || layer;
    }

    // الحصول على الموقع الحالي للمستخدم
    getCurrentLocation(centerMap = false) {
        if (!navigator.geolocation) {
            this.showNotification('جهازك لا يدعم تحديد الموقع الجغرافي', 'error');
            this.useDefaultLocation(centerMap);
            return;
        }

        // محاولة تحديد الموقع بدون قيود صارمة
        console.log('محاولة تحديد الموقع الجغرافي...');

        // إظهار إشعار للمستخدم
        if (centerMap) {
            this.showNotification('جاري تحديد موقعك...', 'info');
        }

        // إظهار مؤشر التحميل فقط إذا كان centerMap = true
        if (centerMap) {
            this.showLoading(true);
        }

        // خيارات تحديد الموقع المحسنة
        const options = {
            enableHighAccuracy: false,  // تقليل الدقة لتحسين السرعة
            timeout: 8000,             // مهلة زمنية أقل
            maximumAge: 300000         // قبول موقع محفوظ لمدة 5 دقائق
        };

        navigator.geolocation.getCurrentPosition(
            (position) => {
                // إخفاء مؤشر التحميل
                this.showLoading(false);

                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                this.currentLocation = { lat, lng };

                console.log(`تم تحديد الموقع: ${lat}, ${lng}`);

                // إزالة العلامة السابقة إن وجدت
                if (this.currentMarker) {
                    this.map.removeLayer(this.currentMarker);
                }

                // إنشاء علامة للموقع الحالي
                const currentIcon = L.divIcon({
                    className: 'current-location-marker',
                    html: '<div class="current-marker-inner"><div class="current-marker-pulse"></div></div>',
                    iconSize: [20, 20],
                    iconAnchor: [10, 10]
                });

                this.currentMarker = L.marker([lat, lng], { icon: currentIcon }).addTo(this.map);

                // تحريك الخريطة إلى الموقع الحالي إذا كان مطلوبًا
                if (centerMap) {
                    this.map.setView([lat, lng], 15);
                    this.showNotification('تم تحديد موقعك الحالي', 'success');
                } else {
                    // إشعار خفيف بدون تحريك الخريطة
                    this.showNotification('تم العثور على موقعك', 'success');
                }

                // حفظ الموقع للاستخدام المستقبلي
                localStorage.setItem('lastKnownLocation', JSON.stringify({
                    lat: lat,
                    lng: lng,
                    name: 'موقعك الحالي',
                    timestamp: Date.now()
                }));
            },
            (error) => {
                // إخفاء مؤشر التحميل
                this.showLoading(false);

                console.warn('فشل تحديد الموقع:', error.code, error.message);

                let errorMessage = '';
                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = centerMap ?
                            'تم رفض طلب تحديد الموقع. يمكنك استخدام زر "تحديد الموقع يدوياً".' :
                            'لم يتم السماح بتحديد الموقع';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = centerMap ?
                            'معلومات الموقع غير متوفرة. جرب "تحديد الموقع يدوياً".' :
                            'الموقع غير متوفر حالياً';
                        break;
                    case error.TIMEOUT:
                        errorMessage = centerMap ?
                            'انتهى وقت تحديد الموقع. جرب "تحديد الموقع يدوياً".' :
                            'انتهت مهلة تحديد الموقع';
                        break;
                    default:
                        errorMessage = centerMap ?
                            'لا يمكن تحديد موقعك. استخدم "تحديد الموقع يدوياً".' :
                            'فشل تحديد الموقع';
                }

                // إظهار رسالة مناسبة
                if (centerMap) {
                    this.showNotification(errorMessage, 'warning');
                    // استخدام موقع افتراضي
                    this.useDefaultLocation(centerMap);
                } else {
                    // إشعار خفيف للتحديد التلقائي
                    console.log('تعذر تحديد الموقع تلقائياً:', errorMessage);
                }
            },
            options
        );
    }

    // استخدام موقع افتراضي
    useDefaultLocation(centerMap = false) {
        const defaultLocation = {lat: 15.3694, lng: 44.1910}; // صنعاء

        console.log('استخدام موقع افتراضي في صنعاء');

        // تحديث الموقع الحالي
        this.currentLocation = defaultLocation;

        // إزالة العلامة السابقة إن وجدت
        if (this.currentMarker) {
            this.map.removeLayer(this.currentMarker);
        }

        // إنشاء علامة للموقع الافتراضي
        const fallbackIcon = L.divIcon({
            className: 'fallback-location-marker',
            html: '<div class="fallback-marker-inner"><i class="fas fa-map-marker-alt"></i></div>',
            iconSize: [30, 30],
            iconAnchor: [15, 30]
        });

        this.currentMarker = L.marker([defaultLocation.lat, defaultLocation.lng], {
            icon: fallbackIcon
        }).addTo(this.map);

        // إضافة نافذة منبثقة
        this.currentMarker.bindPopup(`
            <div style="text-align: center; direction: rtl;">
                <h4>موقع افتراضي - صنعاء</h4>
                <p>لم يتمكن النظام من تحديد موقعك الحقيقي</p>
                <p>يمكنك استخدام زر "تحديد الموقع يدوياً" 📍</p>
            </div>
        `);

        // تحريك الخريطة إلى الموقع الافتراضي إذا كان مطلوبًا
        if (centerMap) {
            this.map.setView([defaultLocation.lat, defaultLocation.lng], 12);
            this.showNotification('تم استخدام موقع افتراضي في صنعاء', 'info');
        }

        // حفظ الموقع الافتراضي
        localStorage.setItem('lastKnownLocation', JSON.stringify({
            lat: defaultLocation.lat,
            lng: defaultLocation.lng,
            name: 'صنعاء (افتراضي)',
            timestamp: Date.now(),
            isDefault: true
        }));
    }

    // تبديل الوضع الليلي
    toggleNightMode() {
        const body = document.body;
        const nightModeBtn = document.getElementById('night-mode-btn');

        body.classList.toggle('night-mode');

        // تحديث أيقونة الزر
        const icon = nightModeBtn.querySelector('i');
        if (body.classList.contains('night-mode')) {
            icon.className = 'fas fa-sun';
            nightModeBtn.title = 'الوضع النهاري';
            this.showNotification('تم تفعيل الوضع الليلي', 'success');
            localStorage.setItem('nightMode', 'true');
        } else {
            icon.className = 'fas fa-moon';
            nightModeBtn.title = 'الوضع الليلي';
            this.showNotification('تم تفعيل الوضع النهاري', 'success');
            localStorage.setItem('nightMode', 'false');
        }
    }

    // تبديل ملء الشاشة
    toggleFullscreen() {
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const icon = fullscreenBtn.querySelector('i');

        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().then(() => {
                icon.className = 'fas fa-compress';
                fullscreenBtn.title = 'إنهاء ملء الشاشة';
                this.showNotification('تم تفعيل ملء الشاشة', 'success');
            }).catch(err => {
                this.showNotification('فشل في تفعيل ملء الشاشة', 'error');
            });
        } else {
            document.exitFullscreen().then(() => {
                icon.className = 'fas fa-expand';
                fullscreenBtn.title = 'ملء الشاشة';
                this.showNotification('تم إنهاء ملء الشاشة', 'success');
            }).catch(err => {
                this.showNotification('فشل في إنهاء ملء الشاشة', 'error');
            });
        }
    }

    // تحميل الإعدادات المحفوظة
    loadSavedSettings() {
        // تحميل الوضع الليلي المحفوظ
        const savedNightMode = localStorage.getItem('nightMode');
        if (savedNightMode === 'true') {
            document.body.classList.add('night-mode');
            const nightModeBtn = document.getElementById('night-mode-btn');
            if (nightModeBtn) {
                const icon = nightModeBtn.querySelector('i');
                icon.className = 'fas fa-sun';
                nightModeBtn.title = 'الوضع النهاري';
            }
        }

        // تحميل نوع الخريطة المفضل
        const savedLayer = localStorage.getItem('preferredMapLayer');
        if (savedLayer && this.baseLayers && this.baseLayers[savedLayer]) {
            this.switchMapLayer(savedLayer);
        }
    }

    // البحث عن موقع
    async searchLocation(query) {
        this.showLoading(true);

        try {
            // محاولة البحث في البيانات المحلية أولاً
            const localResults = await this.searchLocalPlaces(query);

            if (localResults && localResults.length > 0) {
                this.showSearchResults(localResults);
                this.showLoading(false);
                return;
            }

            // إذا لم توجد نتائج محلية، استخدم خدمة البحث الخارجية
            if (!this.offlineMode) {
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=ye&limit=5`);
                const results = await response.json();

                if (results && results.length > 0) {
                    this.showSearchResults(results);
                } else {
                    this.showNotification('لم يتم العثور على نتائج', 'warning');
                }
            } else {
                this.showNotification('البحث غير متاح في الوضع الأوفلاين', 'warning');
            }
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showNotification('حدث خطأ أثناء البحث', 'error');
        }

        this.showLoading(false);
    }

    // البحث في البيانات المحلية
    async searchLocalPlaces(query) {
        // محاولة جلب البيانات من الخادم المحلي
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            if (response.ok) {
                const data = await response.json();
                return data.results || [];
            }
        } catch (error) {
            console.log('البحث المحلي غير متاح، استخدام البيانات المخزنة');
        }

        // البحث في البيانات المخزنة محلياً (إن وجدت)
        const savedPlaces = JSON.parse(localStorage.getItem('cachedPlaces') || '[]');
        return savedPlaces.filter(place =>
            place.name_ar?.includes(query) ||
            place.name_en?.toLowerCase().includes(query.toLowerCase()) ||
            place.description_ar?.includes(query)
        );
    }

    // عرض نتائج البحث
    showSearchResults(results) {
        if (results.length === 0) {
            this.showNotification('لم يتم العثور على نتائج', 'warning');
            return;
        }

        // الانتقال إلى أول نتيجة
        const firstResult = results[0];
        let lat, lng;

        if (firstResult.lat && firstResult.lon) {
            // نتائج من Nominatim
            lat = parseFloat(firstResult.lat);
            lng = parseFloat(firstResult.lon);
        } else if (firstResult.latitude && firstResult.longitude) {
            // نتائج من قاعدة البيانات المحلية
            lat = parseFloat(firstResult.latitude);
            lng = parseFloat(firstResult.longitude);
        }

        if (lat && lng) {
            this.map.setView([lat, lng], 15);

            // إضافة علامة للنتيجة
            if (this.searchMarker) {
                this.map.removeLayer(this.searchMarker);
            }

            this.searchMarker = L.marker([lat, lng]).addTo(this.map);
            this.searchMarker.bindPopup(firstResult.display_name || firstResult.name_ar || 'نتيجة البحث').openPopup();

            this.showNotification(`تم العثور على ${results.length} نتيجة`, 'success');
        }
    }

    // معالجة النقر على الخريطة
    handleMapClick(e) {
        const lat = e.latlng.lat;
        const lng = e.latlng.lng;

        // إضافة علامة مؤقتة
        if (this.tempMarker) {
            this.map.removeLayer(this.tempMarker);
        }

        this.tempMarker = L.marker([lat, lng]).addTo(this.map);
        this.tempMarker.bindPopup(`
            <div style="text-align: center; direction: rtl;">
                <h4>موقع مخصص</h4>
                <p>خط العرض: ${lat.toFixed(6)}</p>
                <p>خط الطول: ${lng.toFixed(6)}</p>
                <button onclick="yemenMaps.saveCustomLocation(${lat}, ${lng})" style="margin: 5px; padding: 5px 10px; background: #1a73e8; color: white; border: none; border-radius: 4px; cursor: pointer;">حفظ الموقع</button>
                <button onclick="yemenMaps.getDirectionsTo(${lat}, ${lng})" style="margin: 5px; padding: 5px 10px; background: #34a853; color: white; border: none; border-radius: 4px; cursor: pointer;">المسار</button>
            </div>
        `).openPopup();
    }

    // حفظ موقع مخصص
    saveCustomLocation(lat, lng) {
        const name = prompt('أدخل اسم الموقع:');
        if (name) {
            const customLocation = {
                id: Date.now(),
                name_ar: name,
                latitude: lat,
                longitude: lng,
                type: 'custom',
                created_at: new Date().toISOString()
            };

            // حفظ في التخزين المحلي
            const savedLocations = JSON.parse(localStorage.getItem('customLocations') || '[]');
            savedLocations.push(customLocation);
            localStorage.setItem('customLocations', JSON.stringify(savedLocations));

            this.showNotification(`تم حفظ الموقع: ${name}`, 'success');

            // إزالة العلامة المؤقتة
            if (this.tempMarker) {
                this.map.removeLayer(this.tempMarker);
            }
        }
    }

    // الحصول على اتجاهات إلى موقع
    getDirectionsTo(lat, lng) {
        if (!this.currentLocation) {
            this.showNotification('يرجى تحديد موقعك الحالي أولاً', 'warning');
            return;
        }

        // حساب المسافة التقريبية
        const distance = this.calculateDistance(
            this.currentLocation.lat,
            this.currentLocation.lng,
            lat,
            lng
        );

        // رسم خط تقريبي للمسار
        if (this.routeLine) {
            this.map.removeLayer(this.routeLine);
        }

        this.routeLine = L.polyline([
            [this.currentLocation.lat, this.currentLocation.lng],
            [lat, lng]
        ], {
            color: '#1a73e8',
            weight: 4,
            opacity: 0.7
        }).addTo(this.map);

        // عرض معلومات المسار
        this.showNotification(`المسافة التقريبية: ${distance.toFixed(2)} كم`, 'success');

        // تكبير الخريطة لإظهار المسار كاملاً
        const group = new L.featureGroup([this.routeLine]);
        this.map.fitBounds(group.getBounds().pad(0.1));
    }

    // حساب المسافة بين نقطتين
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // نصف قطر الأرض بالكيلومتر
        const dLat = this.deg2rad(lat2 - lat1);
        const dLng = this.deg2rad(lng2 - lng1);
        const a =
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    // معالجة الموقع المشارك
    handleSharedLocation() {
        const urlParams = new URLSearchParams(window.location.search);
        const sharedLat = urlParams.get('lat');
        const sharedLng = urlParams.get('lng');

        if (sharedLat && sharedLng) {
            const lat = parseFloat(sharedLat);
            const lng = parseFloat(sharedLng);

            setTimeout(() => {
                this.map.setView([lat, lng], 15);
                L.marker([lat, lng]).addTo(this.map)
                    .bindPopup('موقع مشارك')
                    .openPopup();
                this.showNotification('تم فتح الموقع المشارك', 'success');
            }, 1000);
        }
    }

    // تحميل المواقع من الخادم
    async loadLocations() {
        console.log('محاولة تحميل المواقع من الخادم...');

        try {
            const response = await fetch('/api/places');
            console.log('استجابة الخادم:', response.status, response.statusText);

            if (response.ok) {
                const data = await response.json();
                console.log('بيانات المواقع المستلمة:', data);

                if (data.success && data.data) {
                    this.addMarkersToMap(data.data);
                    // حفظ البيانات محلياً للاستخدام الأوفلاين
                    localStorage.setItem('cachedPlaces', JSON.stringify(data.data));
                    console.log(`تم تحميل ${data.data.length} موقع من الخادم`);
                } else {
                    console.warn('البيانات المستلمة غير صالحة:', data);
                    this.loadCachedPlaces();
                }
            } else {
                console.warn(`فشل تحميل البيانات: ${response.status} ${response.statusText}`);
                this.loadCachedPlaces();
            }
        } catch (error) {
            console.warn('خطأ في الاتصال بالخادم:', error.message);
            this.loadCachedPlaces();
        }
    }

    // تحميل البيانات المحفوظة محلياً
    loadCachedPlaces() {
        console.log('تحميل البيانات المحفوظة محلياً...');

        const cachedPlaces = JSON.parse(localStorage.getItem('cachedPlaces') || '[]');
        if (cachedPlaces.length > 0) {
            this.addMarkersToMap(cachedPlaces);
            console.log(`تم تحميل ${cachedPlaces.length} موقع من التخزين المحلي`);
        } else {
            console.log('لا توجد بيانات محفوظة محلياً');
            // إضافة بعض المواقع الافتراضية
            this.addDefaultPlaces();
        }
    }

    // إضافة مواقع افتراضية
    addDefaultPlaces() {
        console.log('إضافة مواقع افتراضية...');

        const defaultPlaces = [
            {
                id: 1,
                name_ar: 'صنعاء القديمة',
                name_en: 'Old Sana\'a',
                latitude: 15.3547,
                longitude: 44.2066,
                description_ar: 'المدينة القديمة في صنعاء - موقع تراث عالمي',
                type: 'heritage'
            },
            {
                id: 2,
                name_ar: 'جامع الصالح',
                name_en: 'Al Saleh Mosque',
                latitude: 15.3719,
                longitude: 44.1961,
                description_ar: 'أكبر مسجد في اليمن',
                type: 'mosque'
            },
            {
                id: 3,
                name_ar: 'مطار صنعاء الدولي',
                name_en: 'Sana\'a International Airport',
                latitude: 15.4762,
                longitude: 44.2194,
                description_ar: 'المطار الرئيسي في العاصمة صنعاء',
                type: 'airport'
            }
        ];

        this.addMarkersToMap(defaultPlaces);
        // حفظ المواقع الافتراضية
        localStorage.setItem('cachedPlaces', JSON.stringify(defaultPlaces));
        console.log('تم إضافة المواقع الافتراضية');
    }

    // إضافة العلامات للخريطة
    addMarkersToMap(places) {
        places.forEach(place => {
            if (place.latitude && place.longitude) {
                const marker = L.marker([place.latitude, place.longitude]).addTo(this.map);

                const popupContent = `
                    <div class="custom-popup" style="direction: rtl;">
                        <h3>${place.name_ar || place.name}</h3>
                        <p>${place.description_ar || place.description || ''}</p>
                        <div class="popup-actions">
                            <button class="popup-action-btn" onclick="yemenMaps.getDirectionsTo(${place.latitude}, ${place.longitude})">
                                <i class="fas fa-route"></i>
                                المسار
                            </button>
                            <button class="popup-action-btn" onclick="yemenMaps.shareLocation(${place.latitude}, ${place.longitude}, '${place.name_ar || place.name}')">
                                <i class="fas fa-share-alt"></i>
                                مشاركة
                            </button>
                        </div>
                    </div>
                `;

                marker.bindPopup(popupContent);
            }
        });

        console.log(`تم إضافة ${places.length} موقع للخريطة`);
    }

    // مشاركة موقع
    shareLocation(lat, lng, name) {
        const url = `${window.location.origin}${window.location.pathname}?lat=${lat}&lng=${lng}`;

        if (navigator.share) {
            navigator.share({
                title: name || 'موقع مشارك',
                text: `شاهد هذا الموقع على خرائط اليمن`,
                url: url
            });
        } else {
            // نسخ الرابط للحافظة
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('تم نسخ رابط الموقع', 'success');
            }).catch(() => {
                // عرض الرابط في نافذة منبثقة
                prompt('انسخ هذا الرابط:', url);
            });
        }
    }

    // عرض نافذة تحديد الموقع يدوياً
    showManualLocationModal() {
        const modal = document.getElementById('manual-location-modal');
        modal.classList.remove('hidden');
    }

    // البحث عن مدينة
    searchForCity() {
        const cityName = document.getElementById('city-search').value.trim();
        if (!cityName) {
            this.showNotification('يرجى إدخال اسم المدينة', 'warning');
            return;
        }

        // قائمة المدن اليمنية مع إحداثياتها
        const yemeniCities = {
            'صنعاء': {lat: 15.3694, lng: 44.1910},
            'sanaa': {lat: 15.3694, lng: 44.1910},
            'عدن': {lat: 12.7855, lng: 45.0187},
            'aden': {lat: 12.7855, lng: 45.0187},
            'تعز': {lat: 13.5795, lng: 44.2075},
            'taiz': {lat: 13.5795, lng: 44.2075},
            'الحديدة': {lat: 14.7978, lng: 42.9545},
            'hodeidah': {lat: 14.7978, lng: 42.9545},
            'إب': {lat: 13.9670, lng: 44.1830},
            'ibb': {lat: 13.9670, lng: 44.1830},
            'المكلا': {lat: 14.5995, lng: 49.1244},
            'mukalla': {lat: 14.5995, lng: 49.1244},
            'صعدة': {lat: 16.9402, lng: 43.7445},
            'saada': {lat: 16.9402, lng: 43.7445},
            'سيئون': {lat: 14.2681, lng: 49.5243},
            'seiyun': {lat: 14.2681, lng: 49.5243},
            'ذمار': {lat: 14.5425, lng: 44.4009},
            'dhamar': {lat: 14.5425, lng: 44.4009},
            'مأرب': {lat: 15.4694, lng: 45.3222},
            'marib': {lat: 15.4694, lng: 45.3222}
        };

        const searchKey = cityName.toLowerCase();
        const city = yemeniCities[searchKey] || yemeniCities[cityName];

        if (city) {
            this.goToQuickLocation(city.lat, city.lng, cityName);
            document.getElementById('manual-location-modal').classList.add('hidden');
        } else {
            this.showNotification('لم يتم العثور على المدينة. جرب: صنعاء، عدن، تعز، الحديدة، إب', 'warning');
        }
    }

    // تحديد الموقع بالإحداثيات
    setManualLocation() {
        const lat = parseFloat(document.getElementById('manual-lat').value);
        const lng = parseFloat(document.getElementById('manual-lng').value);

        if (isNaN(lat) || isNaN(lng)) {
            this.showNotification('يرجى إدخال إحداثيات صحيحة', 'warning');
            return;
        }

        // التحقق من أن الإحداثيات ضمن نطاق اليمن تقريباً
        if (lat < 12 || lat > 19 || lng < 42 || lng > 54) {
            const confirm = window.confirm('الإحداثيات المدخلة خارج نطاق اليمن. هل تريد المتابعة؟');
            if (!confirm) return;
        }

        this.goToQuickLocation(lat, lng, 'موقع مخصص');
        document.getElementById('manual-location-modal').classList.add('hidden');
    }

    // تفعيل وضع النقر على الخريطة
    enableMapClickMode() {
        document.getElementById('manual-location-modal').classList.add('hidden');
        this.showNotification('انقر على الخريطة لتحديد موقعك', 'info');

        // تفعيل وضع النقر
        this.mapClickMode = true;
        document.body.style.cursor = 'crosshair';

        // إزالة وضع النقر بعد 30 ثانية
        setTimeout(() => {
            if (this.mapClickMode) {
                this.mapClickMode = false;
                document.body.style.cursor = 'default';
                this.showNotification('انتهى وضع النقر', 'info');
            }
        }, 30000);
    }

    // الانتقال إلى موقع سريع
    goToQuickLocation(lat, lng, name) {
        // تحديث الموقع الحالي
        this.currentLocation = {lat: lat, lng: lng};

        // الانتقال إلى الموقع
        this.map.setView([lat, lng], 15);

        // إضافة علامة للموقع
        if (this.currentLocationMarker) {
            this.map.removeLayer(this.currentLocationMarker);
        }

        this.currentLocationMarker = L.marker([lat, lng], {
            icon: L.divIcon({
                className: 'current-location-marker',
                html: '<div class="current-marker-inner"><div class="current-marker-pulse"></div></div>',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            })
        }).addTo(this.map);

        this.currentLocationMarker.bindPopup(`
            <div style="text-align: center; direction: rtl;">
                <h4>${name}</h4>
                <p>موقعك الحالي</p>
                <p>خط العرض: ${lat.toFixed(6)}</p>
                <p>خط الطول: ${lng.toFixed(6)}</p>
            </div>
        `).openPopup();

        this.showNotification(`تم تحديد موقعك في ${name}`, 'success');

        // حفظ الموقع في التخزين المحلي
        localStorage.setItem('lastKnownLocation', JSON.stringify({lat, lng, name}));
    }

    // تحسين معالجة النقر على الخريطة
    handleMapClick(e) {
        const lat = e.latlng.lat;
        const lng = e.latlng.lng;

        // إذا كان وضع النقر مفعل، استخدم هذا كموقع حالي
        if (this.mapClickMode) {
            this.mapClickMode = false;
            document.body.style.cursor = 'default';
            this.goToQuickLocation(lat, lng, 'موقع محدد بالنقر');
            return;
        }

        // السلوك العادي للنقر
        if (this.tempMarker) {
            this.map.removeLayer(this.tempMarker);
        }

        this.tempMarker = L.marker([lat, lng]).addTo(this.map);
        this.tempMarker.bindPopup(`
            <div style="text-align: center; direction: rtl;">
                <h4>موقع مخصص</h4>
                <p>خط العرض: ${lat.toFixed(6)}</p>
                <p>خط الطول: ${lng.toFixed(6)}</p>
                <button onclick="yemenMaps.saveCustomLocation(${lat}, ${lng})" style="margin: 5px; padding: 5px 10px; background: #1a73e8; color: white; border: none; border-radius: 4px; cursor: pointer;">حفظ الموقع</button>
                <button onclick="yemenMaps.getDirectionsTo(${lat}, ${lng})" style="margin: 5px; padding: 5px 10px; background: #34a853; color: white; border: none; border-radius: 4px; cursor: pointer;">المسار</button>
                <button onclick="yemenMaps.setAsCurrentLocation(${lat}, ${lng})" style="margin: 5px; padding: 5px 10px; background: #ff9800; color: white; border: none; border-radius: 4px; cursor: pointer;">تحديد كموقعي</button>
            </div>
        `).openPopup();
    }

    // تحديد موقع كموقع حالي
    setAsCurrentLocation(lat, lng) {
        this.goToQuickLocation(lat, lng, 'موقع محدد يدوياً');

        // إزالة العلامة المؤقتة
        if (this.tempMarker) {
            this.map.removeLayer(this.tempMarker);
        }
    }

    // تحميل آخر موقع معروف
    loadLastKnownLocation() {
        const savedLocation = localStorage.getItem('lastKnownLocation');
        if (savedLocation) {
            try {
                const location = JSON.parse(savedLocation);
                this.currentLocation = {lat: location.lat, lng: location.lng};

                // عرض إشعار للمستخدم
                setTimeout(() => {
                    const useLastLocation = confirm(`هل تريد استخدام آخر موقع محفوظ: ${location.name}؟`);
                    if (useLastLocation) {
                        this.goToQuickLocation(location.lat, location.lng, location.name);
                    }
                }, 2000);
            } catch (error) {
                console.log('خطأ في تحميل آخر موقع معروف');
            }
        }
    }
}

// إنشاء كائن YemenMaps عند تحميل الصفحة
let yemenMaps;
document.addEventListener('DOMContentLoaded', () => {
    yemenMaps = new YemenMaps();

    // ربط كائن عرض تفاصيل المكان بالخريطة
    if (window.placeDetailsManager) {
        yemenMaps.placeDetailsManager = window.placeDetailsManager;
    }
});

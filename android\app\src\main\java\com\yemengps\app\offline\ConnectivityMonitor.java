package com.yemengps.app.offline;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.List;

/**
 * Monitor for network connectivity changes
 */
public class ConnectivityMonitor {

    private static final String TAG = "ConnectivityMonitor";

    private final Context context;
    private final ConnectivityManager connectivityManager;
    private final List<ConnectivityCallback> callbacks = new ArrayList<>();
    private boolean isConnected = false;

    /**
     * Constructor
     * @param context The application context
     */
    public ConnectivityMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        // Initialize current state
        isConnected = isNetworkAvailable();
        
        // Register for network callbacks
        registerNetworkCallback();
    }

    /**
     * Check if the device is currently online
     * @return True if the device has network connectivity
     */
    public boolean isOnline() {
        return isConnected;
    }

    /**
     * Register a callback for connectivity changes
     * @param callback The callback to register
     */
    public void registerConnectivityCallback(ConnectivityCallback callback) {
        if (!callbacks.contains(callback)) {
            callbacks.add(callback);
            // Immediately notify of current state
            callback.onConnectivityChanged(isConnected);
        }
    }

    /**
     * Unregister a callback for connectivity changes
     * @param callback The callback to unregister
     */
    public void unregisterConnectivityCallback(ConnectivityCallback callback) {
        callbacks.remove(callback);
    }

    /**
     * Register for network callbacks
     */
    private void registerNetworkCallback() {
        if (connectivityManager == null) {
            return;
        }

        NetworkRequest.Builder builder = new NetworkRequest.Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        
        ConnectivityManager.NetworkCallback networkCallback = new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(@NonNull Network network) {
                Log.d(TAG, "Network available");
                updateConnectivity(true);
            }

            @Override
            public void onLost(@NonNull Network network) {
                Log.d(TAG, "Network lost");
                updateConnectivity(false);
            }

            @Override
            public void onCapabilitiesChanged(@NonNull Network network, @NonNull NetworkCapabilities networkCapabilities) {
                boolean hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                                     networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                Log.d(TAG, "Network capabilities changed, has internet: " + hasInternet);
                updateConnectivity(hasInternet);
            }
        };

        connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
    }

    /**
     * Check if network is available
     * @return True if network is available
     */
    private boolean isNetworkAvailable() {
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(connectivityManager.getActiveNetwork());
            return capabilities != null && 
                   capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                   capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
        } else {
            // For older devices
            android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
    }

    /**
     * Update connectivity status and notify callbacks
     * @param connected Whether the device is connected
     */
    private void updateConnectivity(boolean connected) {
        if (isConnected != connected) {
            isConnected = connected;
            notifyCallbacks();
        }
    }

    /**
     * Notify all callbacks of the current connectivity state
     */
    private void notifyCallbacks() {
        for (ConnectivityCallback callback : new ArrayList<>(callbacks)) {
            callback.onConnectivityChanged(isConnected);
        }
    }

    /**
     * Interface for connectivity callbacks
     */
    public interface ConnectivityCallback {
        /**
         * Called when connectivity changes
         * @param isConnected Whether the device is connected
         */
        void onConnectivityChanged(boolean isConnected);
    }
}

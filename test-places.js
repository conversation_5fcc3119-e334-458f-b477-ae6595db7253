// اختبار عرض الأماكن الجديدة
const http = require('http');

function testPlaces() {
    console.log('🔍 اختبار عرض الأماكن الجديدة...\n');
    
    http.get('http://localhost:3000/api/places/places?limit=10', (res) => {
        let data = '';
        
        res.on('data', chunk => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const result = JSON.parse(data);
                
                if (result.success) {
                    console.log(`✅ تم العثور على ${result.data.length} مكان\n`);
                    
                    console.log('📋 قائمة الأماكن:');
                    console.log('================');
                    
                    result.data.forEach((place, i) => {
                        console.log(`${i + 1}. ${place.name_ar}`);
                        console.log(`   التقييم: ${place.rating} نجمة (${place.reviews_count} تقييم)`);
                        console.log(`   الفئة: ${place.category_name_ar}`);
                        console.log(`   المحافظة: ${place.governorate_name_ar}`);
                        if (place.main_image) {
                            console.log(`   الصورة: متوفرة`);
                        }
                        console.log('');
                    });
                    
                } else {
                    console.log('❌ خطأ في الحصول على البيانات:', result.message);
                }
                
            } catch (error) {
                console.log('❌ خطأ في تحليل البيانات:', error.message);
                console.log('البيانات الخام:', data);
            }
        });
        
    }).on('error', (error) => {
        console.log('❌ خطأ في الطلب:', error.message);
    });
}

testPlaces();

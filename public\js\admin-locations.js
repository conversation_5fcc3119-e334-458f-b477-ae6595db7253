/**
 * Yemen GPS - إدارة المواقع
 * ملف JavaScript لإدارة المواقع في لوحة التحكم
 */

// المتغيرات العامة
const API_BASE_URL = 'http://localhost:3000/api';
let allLocations = [];
let allCategories = [];
let filteredLocations = [];

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات
    loadLocations();
    loadCategories();
    
    // إضافة مستمعي الأحداث
    document.getElementById('location-search').addEventListener('input', filterLocations);
    document.getElementById('location-form').addEventListener('submit', saveLocation);
    
    // تهيئة الوضع المتجاوب للشاشات الصغيرة
    initResponsiveLayout();
});

/**
 * تهيئة الوضع المتجاوب للشاشات الصغيرة
 */
function initResponsiveLayout() {
    const toggleButton = document.querySelector('.toggle-sidebar');
    
    if (window.innerWidth <= 768) {
        document.querySelector('.sidebar').classList.remove('active');
        document.querySelector('.main-content').style.marginRight = '0';
        toggleButton.style.display = 'block';
    } else {
        document.querySelector('.sidebar').classList.add('active');
        document.querySelector('.main-content').style.marginRight = '250px';
        toggleButton.style.display = 'none';
    }
    
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar').classList.remove('active');
            document.querySelector('.main-content').style.marginRight = '0';
            toggleButton.style.display = 'block';
        } else {
            document.querySelector('.sidebar').classList.add('active');
            document.querySelector('.main-content').style.marginRight = '250px';
            toggleButton.style.display = 'none';
        }
    });
}

/**
 * تبديل حالة القائمة الجانبية
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('active');
    
    if (sidebar.classList.contains('active')) {
        mainContent.style.marginRight = '250px';
    } else {
        mainContent.style.marginRight = '0';
    }
}

/**
 * تحميل المواقع
 */
function loadLocations() {
    showLoading();
    
    apiRequest('/admin/locations')
        .then(data => {
            allLocations = data;
            filteredLocations = [...allLocations];
            displayLocations(filteredLocations);
            updateLocationStats();
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل المواقع:', error);
            document.getElementById('locations-error').textContent = 'حدث خطأ أثناء تحميل بيانات المواقع: ' + error.message;
            document.getElementById('locations-error').style.display = 'block';
            hideLoading();
        });
}

/**
 * تحميل التصنيفات
 */
function loadCategories() {
    apiRequest('/admin/categories')
        .then(data => {
            allCategories = data;
            
            // ملء قائمة تصفية التصنيفات
            const categoryFilter = document.getElementById('location-category-filter');
            categoryFilter.innerHTML = '<option value="all">الكل</option>';
            
            // ملء قائمة التصنيفات في نموذج إضافة/تعديل الموقع
            const categorySelect = document.getElementById('location-category');
            categorySelect.innerHTML = '<option value="">-- اختر التصنيف --</option>';
            
            allCategories.forEach(category => {
                // إضافة خيار إلى قائمة التصفية
                const filterOption = document.createElement('option');
                filterOption.value = category.id;
                filterOption.textContent = category.name;
                categoryFilter.appendChild(filterOption);
                
                // إضافة خيار إلى قائمة النموذج
                const formOption = document.createElement('option');
                formOption.value = category.id;
                formOption.textContent = category.name;
                categorySelect.appendChild(formOption);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل التصنيفات:', error);
        });
}

/**
 * عرض المواقع في الجدول
 */
function displayLocations(locations) {
    const tableBody = document.getElementById('locations-table-body');
    tableBody.innerHTML = '';
    
    if (locations.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="no-data">لا توجد مواقع للعرض</td></tr>';
        return;
    }
    
    locations.forEach(location => {
        const row = document.createElement('tr');
        
        // تحديد حالة الموقع
        let statusText = 'نشط';
        let statusClass = 'status-active';
        
        if (location.status === 'pending') {
            statusText = 'قيد المراجعة';
            statusClass = 'status-pending';
        } else if (location.status === 'inactive') {
            statusText = 'غير نشط';
            statusClass = 'status-inactive';
        }
        
        // العثور على اسم التصنيف
        const category = allCategories.find(c => c.id === location.category_id);
        const categoryName = category ? category.name : '-';
        
        // تنسيق تاريخ الإضافة
        const createdDate = new Date(location.created_at);
        const formattedDate = createdDate.toLocaleDateString('ar-SA');
        
        row.innerHTML = `
            <td>${location.id}</td>
            <td>${location.name}</td>
            <td>${location.address || '-'}</td>
            <td>${categoryName}</td>
            <td>${location.lat}, ${location.lng}</td>
            <td>${location.phone || '-'}</td>
            <td>${formattedDate}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="editLocation(${location.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteLocation(${location.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    updateLocationStats();
}

/**
 * تحديث إحصائيات المواقع
 */
function updateLocationStats() {
    document.getElementById('total-locations').textContent = allLocations.length;
    document.getElementById('displayed-locations').textContent = filteredLocations.length;
}

/**
 * تصفية المواقع حسب المعايير المحددة
 */
function filterLocations() {
    const searchTerm = document.getElementById('location-search').value.trim().toLowerCase();
    const categoryFilter = document.getElementById('location-category-filter').value;
    const statusFilter = document.getElementById('location-status-filter').value;
    
    // إظهار/إخفاء زر مسح البحث
    document.getElementById('clear-location-search').style.display = searchTerm ? 'block' : 'none';
    
    filteredLocations = allLocations.filter(location => {
        // تصفية حسب نص البحث
        const matchesSearch = 
            location.name.toLowerCase().includes(searchTerm) ||
            (location.address && location.address.toLowerCase().includes(searchTerm)) ||
            (location.phone && location.phone.includes(searchTerm));
        
        // تصفية حسب التصنيف
        const matchesCategory = categoryFilter === 'all' || location.category_id == categoryFilter;
        
        // تصفية حسب الحالة
        const matchesStatus = statusFilter === 'all' || location.status === statusFilter;
        
        return matchesSearch && matchesCategory && matchesStatus;
    });
    
    displayLocations(filteredLocations);
}

/**
 * مسح البحث
 */
function clearLocationSearch() {
    document.getElementById('location-search').value = '';
    filterLocations();
}

/**
 * عرض نافذة إضافة موقع جديد
 */
function showAddLocationModal() {
    // تغيير عنوان النافذة
    document.getElementById('location-modal-title').textContent = 'إضافة موقع جديد';
    
    // إعادة تعيين النموذج
    document.getElementById('location-form').reset();
    document.getElementById('location-id').value = '';
    
    // تعيين الحالة الافتراضية
    document.getElementById('location-status').value = 'active';
    
    // عرض النافذة
    document.getElementById('location-modal').style.display = 'block';
}

/**
 * عرض نافذة تعديل موقع
 */
function editLocation(locationId) {
    // البحث عن الموقع
    const location = allLocations.find(loc => loc.id === locationId);
    
    if (!location) {
        alert('لم يتم العثور على الموقع');
        return;
    }
    
    // تغيير عنوان النافذة
    document.getElementById('location-modal-title').textContent = 'تعديل الموقع';
    
    // ملء النموذج ببيانات الموقع
    document.getElementById('location-id').value = location.id;
    document.getElementById('location-name').value = location.name;
    document.getElementById('location-description').value = location.description || '';
    document.getElementById('location-category').value = location.category_id || '';
    document.getElementById('location-address').value = location.address || '';
    document.getElementById('location-lat').value = location.lat;
    document.getElementById('location-lng').value = location.lng;
    document.getElementById('location-phone').value = location.phone || '';
    document.getElementById('location-website').value = location.website || '';
    document.getElementById('location-status').value = location.status || 'active';
    
    // عرض النافذة
    document.getElementById('location-modal').style.display = 'block';
}

/**
 * إغلاق نافذة إضافة/تعديل الموقع
 */
function closeLocationModal() {
    document.getElementById('location-modal').style.display = 'none';
}

/**
 * حفظ بيانات الموقع (إضافة/تعديل)
 */
function saveLocation(event) {
    event.preventDefault();
    
    // جمع بيانات النموذج
    const locationId = document.getElementById('location-id').value;
    const locationData = {
        name: document.getElementById('location-name').value,
        description: document.getElementById('location-description').value,
        category_id: document.getElementById('location-category').value,
        address: document.getElementById('location-address').value,
        lat: parseFloat(document.getElementById('location-lat').value),
        lng: parseFloat(document.getElementById('location-lng').value),
        phone: document.getElementById('location-phone').value,
        website: document.getElementById('location-website').value,
        status: document.getElementById('location-status').value
    };
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // تحديد ما إذا كانت عملية إضافة أو تعديل
    const isUpdate = locationId !== '';
    const url = isUpdate ? `/admin/locations/${locationId}` : '/admin/locations';
    const method = isUpdate ? 'PUT' : 'POST';
    
    // إرسال البيانات إلى الخادم
    apiRequest(url, {
        method: method,
        body: JSON.stringify(locationData)
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النافذة
        closeLocationModal();
        
        // إظهار رسالة نجاح
        alert(isUpdate ? 'تم تحديث الموقع بنجاح' : 'تم إضافة الموقع بنجاح');
        
        // إعادة تحميل المواقع
        loadLocations();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حفظ الموقع: ' + error.message);
    });
}

/**
 * حذف موقع
 */
function deleteLocation(locationId) {
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الموقع؟')) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال طلب الحذف إلى الخادم
    apiRequest(`/admin/locations/${locationId}`, {
        method: 'DELETE'
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة نجاح
        alert('تم حذف الموقع بنجاح');
        
        // إعادة تحميل المواقع
        loadLocations();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حذف الموقع: ' + error.message);
    });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    document.getElementById('locations-loading').style.display = 'flex';
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    document.getElementById('locations-loading').style.display = 'none';
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات الجلسة من تخزين الجلسة
    sessionStorage.removeItem('yemenNavToken');
    sessionStorage.removeItem('yemenNavCurrentUser');
    sessionStorage.removeItem('yemenNavLoggedIn');
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + sessionStorage.getItem('yemenNavToken')
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = `${API_BASE_URL}${endpoint}`;
    }
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        });
}

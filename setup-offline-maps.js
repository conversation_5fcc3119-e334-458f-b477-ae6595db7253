// إعداد نظام الخرائط المستقل مع التحديثات التلقائية
const fs = require('fs');
const path = require('path');
const https = require('https');
const { Pool } = require('pg');

class OfflineMapSetup {
    constructor() {
        this.config = {
            // Mapbox Configuration
            mapbox: {
                accessToken: 'YOUR_MAPBOX_TOKEN', // يجب الحصول عليه من mapbox.com
                styleUrl: 'mapbox://styles/mapbox/streets-v11',
                downloadUrl: 'https://api.mapbox.com/styles/v1/mapbox/streets-v11/tiles'
            },
            
            // HERE Maps Configuration
            here: {
                apiKey: 'YOUR_HERE_API_KEY', // يجب الحصول عليه من developer.here.com
                baseUrl: 'https://base.maps.ls.hereapi.com/maptile/2.1'
            },
            
            // OpenMapTiles Configuration
            openMapTiles: {
                serverUrl: 'https://api.maptiler.com/tiles/v3',
                apiKey: 'YOUR_MAPTILER_KEY' // يجب الحصول عليه من maptiler.com
            },
            
            // Yemen specific bounds
            yemenBounds: {
                north: 19.0,
                south: 12.0,
                east: 54.0,
                west: 42.0
            },
            
            // Zoom levels to download
            zoomLevels: {
                min: 5,  // عرض عام لليمن
                max: 16  // تفاصيل الشوارع
            },
            
            // Local storage paths
            paths: {
                tiles: './offline-maps/tiles',
                styles: './offline-maps/styles',
                data: './offline-maps/data',
                backup: './offline-maps/backup'
            }
        };
        
        this.pool = new Pool({
            user: 'yemen',
            host: 'localhost',
            database: 'yemen_gps',
            password: 'admin',
            port: 5432,
        });
        
        this.init();
    }
    
    async init() {
        console.log('🚀 بدء إعداد نظام الخرائط المستقل...');
        
        // إنشاء المجلدات المطلوبة
        this.createDirectories();
        
        // تحديد أفضل مزود خرائط
        await this.selectBestProvider();
        
        // تحميل خرائط اليمن
        await this.downloadYemenMaps();
        
        // إعداد الخادم المحلي
        await this.setupLocalServer();
        
        // إعداد التحديثات التلقائية
        this.setupAutoUpdates();
        
        console.log('✅ تم إعداد نظام الخرائط المستقل بنجاح!');
    }
    
    createDirectories() {
        console.log('📁 إنشاء مجلدات التخزين...');
        
        Object.values(this.config.paths).forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`   ✅ تم إنشاء: ${dir}`);
            }
        });
    }
    
    async selectBestProvider() {
        console.log('🔍 اختبار مزودي الخرائط...');
        
        const providers = [
            { name: 'Mapbox', test: () => this.testMapbox() },
            { name: 'HERE', test: () => this.testHere() },
            { name: 'OpenMapTiles', test: () => this.testOpenMapTiles() }
        ];
        
        for (const provider of providers) {
            try {
                const isAvailable = await provider.test();
                if (isAvailable) {
                    console.log(`   ✅ ${provider.name} متاح ويعمل`);
                    this.selectedProvider = provider.name.toLowerCase();
                    return;
                }
            } catch (error) {
                console.log(`   ❌ ${provider.name} غير متاح: ${error.message}`);
            }
        }
        
        // استخدام OpenStreetMap كبديل مجاني
        console.log('   ⚠️ استخدام OpenStreetMap كبديل مجاني');
        this.selectedProvider = 'openstreetmap';
    }
    
    async testMapbox() {
        // اختبار Mapbox API
        const testUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/sanaa.json?access_token=${this.config.mapbox.accessToken}`;
        return this.testUrl(testUrl);
    }
    
    async testHere() {
        // اختبار HERE API
        const testUrl = `https://geocode.search.hereapi.com/v1/geocode?q=Sanaa&apikey=${this.config.here.apiKey}`;
        return this.testUrl(testUrl);
    }
    
    async testOpenMapTiles() {
        // اختبار OpenMapTiles API
        const testUrl = `https://api.maptiler.com/geocoding/sanaa.json?key=${this.config.openMapTiles.apiKey}`;
        return this.testUrl(testUrl);
    }
    
    async testUrl(url) {
        return new Promise((resolve) => {
            const request = https.get(url, (response) => {
                resolve(response.statusCode === 200);
            });
            
            request.on('error', () => resolve(false));
            request.setTimeout(5000, () => {
                request.destroy();
                resolve(false);
            });
        });
    }
    
    async downloadYemenMaps() {
        console.log('🗺️ تحميل خرائط اليمن...');
        
        const { north, south, east, west } = this.config.yemenBounds;
        const { min, max } = this.config.zoomLevels;
        
        let totalTiles = 0;
        let downloadedTiles = 0;
        
        // حساب إجمالي البلاطات المطلوبة
        for (let z = min; z <= max; z++) {
            const tilesAtZoom = this.calculateTilesInBounds(north, south, east, west, z);
            totalTiles += tilesAtZoom;
        }
        
        console.log(`   📊 إجمالي البلاطات المطلوبة: ${totalTiles}`);
        
        // تحميل البلاطات
        for (let z = min; z <= max; z++) {
            console.log(`   🔄 تحميل المستوى ${z}...`);
            
            const bounds = this.getBoundsForZoom(north, south, east, west, z);
            
            for (let x = bounds.minX; x <= bounds.maxX; x++) {
                for (let y = bounds.minY; y <= bounds.maxY; y++) {
                    try {
                        await this.downloadTile(z, x, y);
                        downloadedTiles++;
                        
                        if (downloadedTiles % 100 === 0) {
                            const progress = ((downloadedTiles / totalTiles) * 100).toFixed(1);
                            console.log(`   📈 التقدم: ${progress}% (${downloadedTiles}/${totalTiles})`);
                        }
                        
                        // تأخير قصير لتجنب تجاوز حدود API
                        await this.delay(100);
                        
                    } catch (error) {
                        console.log(`   ⚠️ فشل تحميل البلاطة ${z}/${x}/${y}: ${error.message}`);
                    }
                }
            }
        }
        
        console.log(`   ✅ تم تحميل ${downloadedTiles} بلاطة من أصل ${totalTiles}`);
    }
    
    calculateTilesInBounds(north, south, east, west, zoom) {
        const bounds = this.getBoundsForZoom(north, south, east, west, zoom);
        return (bounds.maxX - bounds.minX + 1) * (bounds.maxY - bounds.minY + 1);
    }
    
    getBoundsForZoom(north, south, east, west, zoom) {
        const minX = Math.floor(this.lonToTileX(west, zoom));
        const maxX = Math.floor(this.lonToTileX(east, zoom));
        const minY = Math.floor(this.latToTileY(north, zoom));
        const maxY = Math.floor(this.latToTileY(south, zoom));
        
        return { minX, maxX, minY, maxY };
    }
    
    lonToTileX(lon, zoom) {
        return (lon + 180) / 360 * Math.pow(2, zoom);
    }
    
    latToTileY(lat, zoom) {
        return (1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom);
    }
    
    async downloadTile(z, x, y) {
        const tileUrl = this.getTileUrl(z, x, y);
        const tilePath = path.join(this.config.paths.tiles, `${z}`, `${x}`);
        const tileFile = path.join(tilePath, `${y}.png`);
        
        // إنشاء المجلد إذا لم يكن موجوداً
        if (!fs.existsSync(tilePath)) {
            fs.mkdirSync(tilePath, { recursive: true });
        }
        
        // تخطي إذا كانت البلاطة موجودة
        if (fs.existsSync(tileFile)) {
            return;
        }
        
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(tileFile);
            
            https.get(tileUrl, (response) => {
                if (response.statusCode === 200) {
                    response.pipe(file);
                    file.on('finish', () => {
                        file.close();
                        resolve();
                    });
                } else {
                    reject(new Error(`HTTP ${response.statusCode}`));
                }
            }).on('error', reject);
        });
    }
    
    getTileUrl(z, x, y) {
        switch (this.selectedProvider) {
            case 'mapbox':
                return `${this.config.mapbox.downloadUrl}/${z}/${x}/${y}@2x?access_token=${this.config.mapbox.accessToken}`;
            
            case 'here':
                return `${this.config.here.baseUrl}/maptile/newest/normal.day/${z}/${x}/${y}/256/png8?apikey=${this.config.here.apiKey}`;
            
            case 'openmaptiles':
                return `${this.config.openMapTiles.serverUrl}/${z}/${x}/${y}.png?key=${this.config.openMapTiles.apiKey}`;
            
            default: // OpenStreetMap
                return `https://tile.openstreetmap.org/${z}/${x}/${y}.png`;
        }
    }
    
    async setupLocalServer() {
        console.log('🖥️ إعداد الخادم المحلي...');
        
        const serverCode = `
const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 8000;

// خدمة البلاطات المحلية
app.get('/tiles/:z/:x/:y.png', (req, res) => {
    const { z, x, y } = req.params;
    const tilePath = path.join(__dirname, 'offline-maps', 'tiles', z, x, \`\${y}.png\`);
    
    if (fs.existsSync(tilePath)) {
        res.sendFile(tilePath);
    } else {
        // إرجاع بلاطة فارغة إذا لم توجد
        res.status(404).send('Tile not found');
    }
});

// خدمة الملفات الثابتة
app.use(express.static('.'));

// API للأماكن
app.get('/api/places', async (req, res) => {
    try {
        const { Pool } = require('pg');
        const pool = new Pool({
            user: 'yemen',
            host: 'localhost',
            database: 'yemen_gps',
            password: 'admin',
            port: 5432,
        });
        
        const result = await pool.query('SELECT * FROM places ORDER BY created_at DESC');
        res.json(result.rows);
        pool.end();
    } catch (error) {
        console.error('خطأ في تحميل الأماكن:', error);
        res.status(500).json({ error: 'خطأ في تحميل البيانات' });
    }
});

app.listen(PORT, () => {
    console.log(\`🌐 الخادم المحلي يعمل على المنفذ \${PORT}\`);
    console.log(\`🗺️ الخرائط المحلية: http://localhost:\${PORT}/tiles/{z}/{x}/{y}.png\`);
    console.log(\`📍 الخريطة الرئيسية: http://localhost:\${PORT}/\`);
});
        `;
        
        fs.writeFileSync('offline-server.js', serverCode);
        console.log('   ✅ تم إنشاء offline-server.js');
    }
    
    setupAutoUpdates() {
        console.log('🔄 إعداد التحديثات التلقائية...');
        
        // إنشاء مهمة تحديث أسبوعية
        const updateScript = `
const cron = require('node-cron');
const { OfflineMapSetup } = require('./setup-offline-maps');

// تحديث أسبوعي كل يوم جمعة في الساعة 2 صباح<|im_start|>
cron.schedule('0 2 * * 5', async () => {
    console.log('🔄 بدء التحديث الأسبوعي للخرائط...');
    
    try {
        const mapSetup = new OfflineMapSetup();
        await mapSetup.downloadYemenMaps();
        console.log('✅ تم تحديث الخرائط بنجاح');
    } catch (error) {
        console.error('❌ فشل في تحديث الخرائط:', error);
    }
});

console.log('⏰ تم تفعيل التحديثات التلقائية (كل جمعة 2 ص)');
        `;
        
        fs.writeFileSync('auto-update.js', updateScript);
        console.log('   ✅ تم إنشاء auto-update.js');
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تشغيل الإعداد
if (require.main === module) {
    new OfflineMapSetup();
}

module.exports = OfflineMapSetup;

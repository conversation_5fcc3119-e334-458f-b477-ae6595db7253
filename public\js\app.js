// public/js/app.js

// تهيئة مدير الخرائط دون اتصال
let offlineMapManager;

// عند تحميل الخريطة
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing offline maps and night mode');
  
  // التحقق إذا كان زر الخرائط دون اتصال موجود بالفعل
  const controlsDiv = document.querySelector('.controls');
  console.log('Controls div found:', controlsDiv);
  
  if (controlsDiv && !document.getElementById('offline-maps-btn')) {
    const offlineMapsBtn = document.createElement('button');
    offlineMapsBtn.className = 'control-button';
    offlineMapsBtn.id = 'offline-maps-btn';
    offlineMapsBtn.title = 'الخرائط دون اتصال';
    offlineMapsBtn.innerHTML = '&#128190;'; // رمز القرص
    controlsDiv.appendChild(offlineMapsBtn);
  }

  // إضافة واجهة مستخدم لإدارة الخرائط دون اتصال
  const offlineMapsPanel = document.createElement('div');
  offlineMapsPanel.className = 'offline-maps-panel';
  offlineMapsPanel.style.cssText = 'display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 2000; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); width: 80%; max-width: 400px;';
  offlineMapsPanel.innerHTML = `
    <h3 style="margin-top: 0; text-align: center;">تنزيل الخرائط للاستخدام دون اتصال</h3>
    <p>حدد المنطقة التي تريد تنزيلها للاستخدام دون اتصال بالإنترنت.</p>
    
    <div style="margin-bottom: 15px;">
      <label for="min-zoom">مستوى التكبير الأدنى:</label>
      <input type="range" id="min-zoom" min="5" max="15" value="10" style="width: 100%;">
      <span id="min-zoom-value">10</span>
    </div>
    
    <div style="margin-bottom: 15px;">
      <label for="max-zoom">مستوى التكبير الأقصى:</label>
      <input type="range" id="max-zoom" min="10" max="18" value="16" style="width: 100%;">
      <span id="max-zoom-value">16</span>
    </div>
    
    <div style="margin-bottom: 15px;">
      <label for="region-name">اسم المنطقة:</label>
      <input type="text" id="region-name" placeholder="أدخل اسم المنطقة" style="width: 100%; padding: 5px;">
    </div>
    
    <div style="display: flex; justify-content: space-between;">
      <button id="select-area-btn" style="background-color: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">تحديد المنطقة</button>
      <button id="download-region" style="background-color: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">تنزيل المنطقة</button>
      <button id="close-offline-maps-panel" style="background-color: #f44336; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">إغلاق</button>
    </div>
    
    <div id="download-progress" style="margin-top: 15px; display: none;">
      <div style="height: 20px; background-color: #f1f1f1; border-radius: 5px; overflow: hidden;">
        <div id="progress-bar" style="height: 100%; width: 0%; background-color: #4CAF50; transition: width 0.3s;"></div>
      </div>
      <div id="progress-text" style="text-align: center; margin-top: 5px;">0%</div>
    </div>

    <div id="offline-regions-list" style="margin-top: 15px; max-height: 200px; overflow-y: auto; display: none;">
      <h4 style="margin-top: 0;">المناطق المحفوظة</h4>
      <ul id="regions-list" style="padding-right: 20px;"></ul>
    </div>
    <div id="selected-area-info" style="display: none;"></div>
  `;
  document.body.appendChild(offlineMapsPanel);

  // إضافة زر إدارة الخرائط دون اتصال
  function addOfflineMapManagerButton() {
    const controlDiv = document.createElement('div');
    controlDiv.className = 'leaflet-control-offline-manager leaflet-bar leaflet-control';
    controlDiv.innerHTML = `
      <a href="#" title="إدارة الخرائط دون اتصال" role="button" aria-label="إدارة الخرائط دون اتصال">
        <i class="fas fa-save"></i>
      </a>
    `;
    
    controlDiv.onclick = function(e) {
      e.preventDefault();
      showOfflineMapManager();
    };
    
    document.querySelector('.leaflet-top.leaflet-right').appendChild(controlDiv);
  }

  // عرض مدير الخرائط دون اتصال
  function showOfflineMapManager() {
    // إنشاء نافذة منبثقة لإدارة الخرائط دون اتصال
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'offlineMapManager';
    
    // الحصول على المناطق المحفوظة
    offlineMaps.getRegions().then(regions => {
      // تنسيق المناطق وإضافة معلومات إضافية
      const regionsWithDetails = regions.map(region => {
        const estimatedSizeMB = (region.tileCount * 15 / 1024).toFixed(2);
        const date = new Date(region.timestamp);
        const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
        
        return {
          ...region,
          formattedDate,
          estimatedSizeMB
        };
      });
      
      // إنشاء محتوى النافذة المنبثقة
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h2>إدارة الخرائط دون اتصال</h2>
            <span class="close">&times;</span>
          </div>
          <div class="modal-body">
            <div class="regions-list">
              <h3>المناطق المحفوظة (${regionsWithDetails.length})</h3>
              ${regionsWithDetails.length === 0 ? '<p>لا توجد مناطق محفوظة</p>' : ''}
              <div class="region-cards">
                ${regionsWithDetails.map(region => `
                  <div class="region-card" data-region-id="${region.id}">
                    <div class="region-header">
                      <h4>${region.name}</h4>
                      <div class="region-actions">
                        <button class="btn-use-region" title="استخدام هذه المنطقة"><i class="fas fa-map-marker-alt"></i></button>
                        <button class="btn-share-region" title="مشاركة هذه المنطقة"><i class="fas fa-share-alt"></i></button>
                        <button class="btn-delete-region" title="حذف هذه المنطقة"><i class="fas fa-trash"></i></button>
                      </div>
                    </div>
                    <div class="region-info">
                      <p>الحجم: ${region.estimatedSizeMB} ميجابايت (${region.tileCount} بلاطة)<br>
                      تاريخ التنزيل: ${region.formattedDate}</p>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
            
            <div class="offline-actions">
              <button id="downloadNewRegionBtn" class="btn btn-primary">تنزيل منطقة جديدة</button>
            </div>
          </div>
        </div>
      `;
      
      // إضافة النافذة المنبثقة إلى الصفحة
      document.body.appendChild(modal);
      
      // إضافة الأنماط إذا لم تكن موجودة
      if (!document.getElementById('offline-manager-styles')) {
        const styles = document.createElement('style');
        styles.id = 'offline-manager-styles';
        styles.textContent = `
          .modal {
            display: block;
            position: fixed;
            z-index: 9999;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
          }
          
          .modal-content {
            background-color: #f8f9fa;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            direction: rtl;
          }
          
          .modal-header {
            padding: 15px 20px;
            background-color: #4285f4;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .modal-body { padding: 20px; }
          
          .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
          }
          
          .region-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
          }
          
          .region-card {
            background-color: white;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          
          .region-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
          
          .region-actions {
            display: flex;
            gap: 5px;
          }
          
          .region-actions button {
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
          }
          
          .btn-use-region:hover { color: #4285f4; }
          .btn-share-region:hover { color: #4CAF50; }
          .btn-delete-region:hover { color: #ea4335; }
        `;
        document.head.appendChild(styles);
      }
      
      // إضافة مستمعي الأحداث
      const closeBtn = modal.querySelector('.close');
      closeBtn.onclick = function() {
        document.body.removeChild(modal);
      };
      
      // إغلاق النافذة المنبثقة عند النقر خارجها
      window.onclick = function(event) {
        if (event.target === modal) {
          document.body.removeChild(modal);
        }
      };
      
      // زر تنزيل منطقة جديدة
      const downloadNewRegionBtn = document.getElementById('downloadNewRegionBtn');
      downloadNewRegionBtn.onclick = function() {
        document.body.removeChild(modal);
        enableAreaSelectMode();
      };
      
      // أزرار استخدام المنطقة
      const useRegionBtns = modal.querySelectorAll('.btn-use-region');
      useRegionBtns.forEach(btn => {
        btn.onclick = function() {
          const regionId = btn.closest('.region-card').dataset.regionId;
          const region = regions.find(r => r.id === regionId);
          
          if (region) {
            // الانتقال إلى المنطقة على الخريطة
            const bounds = L.latLngBounds(
              L.latLng(region.bounds.south, region.bounds.west),
              L.latLng(region.bounds.north, region.bounds.east)
            );
            
            map.fitBounds(bounds);
            showNotification(`تم الانتقال إلى منطقة "${region.name}"`, 'success');
            document.body.removeChild(modal);
          }
        };
      });
      
      // أزرار مشاركة المنطقة
      const shareRegionBtns = modal.querySelectorAll('.btn-share-region');
      shareRegionBtns.forEach(btn => {
        btn.onclick = function() {
          const regionId = btn.closest('.region-card').dataset.regionId;
          const region = regions.find(r => r.id === regionId);
          
          if (region) {
            showShareRegionDialog(region);
          }
        };
      });
      
      // أزرار حذف المنطقة
      const deleteRegionBtns = modal.querySelectorAll('.btn-delete-region');
      deleteRegionBtns.forEach(btn => {
        btn.onclick = function() {
          const regionId = btn.closest('.region-card').dataset.regionId;
          const region = regions.find(r => r.id === regionId);
          
          if (region && confirm(`هل أنت متأكد من رغبتك في حذف منطقة "${region.name}"؟`)) {
            offlineMaps.deleteRegion(regionId).then(() => {
              document.body.removeChild(modal);
              showOfflineMapManager();
              showNotification(`تم حذف منطقة "${region.name}" بنجاح`, 'success');
            }).catch(error => {
              alert(`فشل في حذف المنطقة: ${error.message}`);
            });
          }
        };
      });
    }).catch(error => {
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h2>إدارة الخرائط دون اتصال</h2>
            <span class="close">&times;</span>
          </div>
          <div class="modal-body">
            <p class="error-message">فشل في تحميل المناطق المحفوظة: ${error.message}</p>
          </div>
        </div>
      `;
      
      document.body.appendChild(modal);
      
      const closeBtn = modal.querySelector('.close');
      closeBtn.onclick = function() {
        document.body.removeChild(modal);
      };
    });
  }

  // تحديث وظيفة التهيئة لإضافة زر مدير الخرائط دون اتصال
  addOfflineMapManagerButton();

  // تهيئة مدير الخرائط دون اتصال بعد تحميل الخريطة
  if (window.map) {
    offlineMapManager = new OfflineMapManager(window.map);
    
    // عرض المناطق المحفوظة
    displaySavedRegions();
  }
  
  // إضافة حدث النقر على زر الخرائط دون اتصال
  document.getElementById('offline-maps-btn')?.addEventListener('click', () => {
    document.querySelector('.offline-maps-panel').style.display = 'block';
    
    // عرض المناطق المحفوظة
    displaySavedRegions();
  });
  
  // إضافة حدث النقر على زر إغلاق لوحة الخرائط دون اتصال
  document.getElementById('close-offline-maps-panel')?.addEventListener('click', () => {
    document.querySelector('.offline-maps-panel').style.display = 'none';
    
    // إيقاف وضع تحديد المنطقة إذا كان نشطًا
    if (window.areaSelectMode) {
      disableAreaSelectMode();
    }
  });
  
  // إضافة حدث تغيير مستوى التكبير الأدنى
  document.getElementById('min-zoom')?.addEventListener('input', function() {
    document.getElementById('min-zoom-value').textContent = this.value;
  });
  
  // إضافة حدث تغيير مستوى التكبير الأقصى
  document.getElementById('max-zoom')?.addEventListener('input', function() {
    document.getElementById('max-zoom-value').textContent = this.value;
  });

  // متغيرات عالمية لتحديد المنطقة
  window.areaSelectMode = false;
  window.areaSelectRectangle = null;
  window.areaSelectControl = null;

  // تفعيل وضع تحديد المنطقة
  function enableAreaSelectMode() {
    if (window.areaSelectMode) return;
    
    window.areaSelectMode = true;
    
    // تغيير مؤشر الفأرة ليدل على وضع التحديد
    document.getElementById('map').style.cursor = 'crosshair';
    
    // إضافة رسالة إرشادية
    const helpMsg = document.createElement('div');
    helpMsg.id = 'area-select-help';
    helpMsg.className = 'area-select-help';
    helpMsg.textContent = 'انقر واسحب لتحديد المنطقة المطلوب تنزيلها';
    document.body.appendChild(helpMsg);
    
    // إضافة أداة الرسم للخريطة
    window.areaSelectControl = new L.Draw.Rectangle(window.map, {
      shapeOptions: {
        color: '#3388ff',
        weight: 2,
        opacity: 0.7,
        fillOpacity: 0.2
      }
    });
    
    window.areaSelectControl.enable();
    
    // الاستماع لحدث إنشاء المستطيل
    window.map.on('draw:created', function(e) {
      // إزالة المستطيل السابق إذا وجد
      if (window.areaSelectRectangle) {
        window.map.removeLayer(window.areaSelectRectangle);
      }
      
      // حفظ المستطيل الجديد
      window.areaSelectRectangle = e.layer;
      window.map.addLayer(window.areaSelectRectangle);
      
      // عرض معلومات المنطقة المحددة
      const bounds = window.areaSelectRectangle.getBounds();
      const area = L.GeometryUtil.geodesicArea(window.areaSelectRectangle.getLatLngs()[0]);
      const areaInKm = (area / 1000000).toFixed(2);
      
      const selectedAreaInfo = document.getElementById('selected-area-info');
      selectedAreaInfo.textContent = `المساحة المحددة: ${areaInKm} كم² | الإحداثيات: شمال ${bounds.getNorth().toFixed(4)}، جنوب ${bounds.getSouth().toFixed(4)}، شرق ${bounds.getEast().toFixed(4)}، غرب ${bounds.getWest().toFixed(4)}`;
      selectedAreaInfo.style.display = 'block';
      selectedAreaInfo.style.backgroundColor = 'rgba(0, 128, 0, 0.1)';
      selectedAreaInfo.style.padding = '8px';
      selectedAreaInfo.style.borderRadius = '4px';
      selectedAreaInfo.style.marginTop = '10px';
      selectedAreaInfo.style.marginBottom = '10px';
      selectedAreaInfo.style.direction = 'rtl';
      selectedAreaInfo.style.fontSize = '14px';
      
      // تقدير حجم البيانات التي سيتم تنزيلها
      const minZoom = parseInt(document.getElementById('min-zoom').value);
      const maxZoom = parseInt(document.getElementById('max-zoom').value);
      
      // حساب عدد البلاطات التقريبي
      let estimatedTiles = 0;
      for (let z = minZoom; z <= maxZoom; z++) {
        // في كل مستوى تكبير، يتضاعف عدد البلاطات 4 مرات
        const tilesAtZoom = Math.ceil(areaInKm * 0.15 * Math.pow(4, z - minZoom));
        estimatedTiles += tilesAtZoom;
      }
      
      // تقدير الحجم (متوسط حجم البلاطة حوالي 15 كيلوبايت)
      const estimatedSizeKB = estimatedTiles * 15;
      let estimatedSizeText = '';
      
      if (estimatedSizeKB > 1024) {
        const estimatedSizeMB = (estimatedSizeKB / 1024).toFixed(1);
        estimatedSizeText = `${estimatedSizeMB} ميجابايت`;
      } else {
        estimatedSizeText = `${estimatedSizeKB.toFixed(0)} كيلوبايت`;
      }
      
      // إضافة معلومات الحجم المقدر
      const estimatedSizeInfo = document.createElement('div');
      estimatedSizeInfo.textContent = `الحجم التقريبي: ${estimatedSizeText} (حوالي ${estimatedTiles} بلاطة)`;
      estimatedSizeInfo.style.marginTop = '5px';
      estimatedSizeInfo.style.color = '#666';
      selectedAreaInfo.appendChild(estimatedSizeInfo);
      
      // إضافة زر لتعديل المنطقة المحددة
      const editAreaButton = document.createElement('button');
      editAreaButton.textContent = 'تعديل المنطقة';
      editAreaButton.style.marginTop = '10px';
      editAreaButton.style.padding = '5px 10px';
      editAreaButton.style.backgroundColor = '#4CAF50';
      editAreaButton.style.color = 'white';
      editAreaButton.style.border = 'none';
      editAreaButton.style.borderRadius = '3px';
      editAreaButton.style.cursor = 'pointer';
      editAreaButton.style.marginRight = '5px';
      
      editAreaButton.addEventListener('click', function() {
        // إخفاء نافذة التنزيل مؤقتاً
        document.querySelector('.offline-maps-panel').style.display = 'none';
        
        // إزالة المستطيل الحالي
        if (window.areaSelectRectangle) {
          window.map.removeLayer(window.areaSelectRectangle);
        }
        
        // تفعيل وضع تحديد المنطقة مرة أخرى
        enableAreaSelectMode();
      });
      
      selectedAreaInfo.appendChild(editAreaButton);
      
      // تعطيل وضع التحديد بعد الانتهاء
      disableAreaSelectMode();
      
      // تفعيل زر التنزيل
      document.getElementById('download-region').disabled = false;
    });
  }

  // تعطيل وضع تحديد المنطقة
  function disableAreaSelectMode() {
    if (!window.areaSelectMode) return;
    
    window.areaSelectMode = false;
    
    // إعادة مؤشر الفأرة للوضع الطبيعي
    document.getElementById('map').style.cursor = '';
    
    // إزالة الرسالة الإرشادية
    const helpMsg = document.getElementById('area-select-help');
    if (helpMsg) {
      helpMsg.remove();
    }
    
    // تعطيل أداة الرسم
    if (window.areaSelectControl) {
      window.areaSelectControl.disable();
    }
    
    // إزالة مستمع الحدث
    window.map.off('draw:created');
  }

  // إضافة حدث النقر على زر تحديد المنطقة
  document.getElementById('select-area-btn')?.addEventListener('click', () => {
    // إخفاء لوحة الخرائط دون اتصال مؤقتًا
    document.querySelector('.offline-maps-panel').style.display = 'none';
    
    // تفعيل وضع تحديد المنطقة
    enableAreaSelectMode();
  });

  // إضافة حدث النقر على زر تنزيل المنطقة
  document.getElementById('download-region')?.addEventListener('click', () => {
    if (!offlineMapManager) {
      alert('لم يتم تهيئة مدير الخرائط دون اتصال بعد.');
      return;
    }
    
    const minZoom = parseInt(document.getElementById('min-zoom').value);
    const maxZoom = parseInt(document.getElementById('max-zoom').value);
    const regionName = document.getElementById('region-name').value.trim();
    
    if (!regionName) {
      alert('يرجى إدخال اسم المنطقة.');
      return;
    }
    
    // الحصول على حدود المنطقة المحددة أو حدود الخريطة الحالية
    let bounds;
    if (window.areaSelectRectangle) {
      bounds = window.areaSelectRectangle.getBounds();
    } else {
      bounds = window.map.getBounds();
    }
    
    // عرض شريط التقدم
    document.getElementById('download-progress').style.display = 'block';
    document.getElementById('progress-bar').style.width = '0%';
    document.getElementById('progress-text').textContent = '0%';
    
    // تنزيل المنطقة
    offlineMapManager.downloadRegion(bounds, regionName, { min: minZoom, max: maxZoom })
      .then(() => {
        // تحديث شريط التقدم
        document.getElementById('progress-bar').style.width = '100%';
        document.getElementById('progress-text').textContent = '100%';
        
        // حفظ معلومات المنطقة في التخزين المحلي
        const offlineRegions = JSON.parse(localStorage.getItem('offlineRegions') || '[]');
        offlineRegions.push({
          name: regionName,
          bounds: {
            north: bounds.getNorth(),
            south: bounds.getSouth(),
            east: bounds.getEast(),
            west: bounds.getWest()
          },
          minZoom,
          maxZoom,
          date: new Date().toISOString()
        });
        localStorage.setItem('offlineRegions', JSON.stringify(offlineRegions));
        
        // عرض المناطق المحفوظة
        displaySavedRegions();
        
        // إظهار رسالة نجاح
        alert(`تم تنزيل المنطقة "${regionName}" بنجاح للاستخدام دون اتصال.`);
      })
      .catch(error => {
        console.error('Error downloading region:', error);
        alert('حدث خطأ أثناء تنزيل المنطقة. يرجى المحاولة مرة أخرى.');
      });
  });

  // إضافة وضع القيادة الليلية
  addNightMode();
});

// عرض المناطق المحفوظة
function displaySavedRegions() {
  const regionsListContainer = document.getElementById('offline-regions-list');
  const regionsList = document.getElementById('regions-list');
  
  if (!regionsListContainer || !regionsList) return;
  
  // الحصول على المناطق المحفوظة
  const offlineRegions = JSON.parse(localStorage.getItem('offlineRegions') || '[]');
  
  // إذا كانت هناك مناطق محفوظة، عرضها
  if (offlineRegions.length > 0) {
    regionsListContainer.style.display = 'block';
    regionsList.innerHTML = '';
    
    offlineRegions.forEach((region, index) => {
      const regionItem = document.createElement('li');
      regionItem.style.margin = '10px 0';
      
      // تنسيق التاريخ
      const date = new Date(region.date);
      const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
      
      regionItem.innerHTML = `
        <div><strong>${region.name}</strong></div>
        <div>التاريخ: ${formattedDate}</div>
        <div>مستوى التكبير: ${region.minZoom} - ${region.maxZoom}</div>
        <button class="view-region" data-index="${index}" style="background-color: #4CAF50; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer; margin-left: 5px;">عرض</button>
        <button class="delete-region" data-index="${index}" style="background-color: #f44336; color: white; border: none; padding: 3px 8px; border-radius: 3px; cursor: pointer;">حذف</button>
      `;
      
      regionsList.appendChild(regionItem);
    });
    
    // إضافة أحداث النقر على أزرار العرض والحذف
    document.querySelectorAll('.view-region').forEach(button => {
      button.addEventListener('click', function() {
        const index = parseInt(this.getAttribute('data-index'));
        const region = offlineRegions[index];
        
        // إنشاء كائن حدود من البيانات المحفوظة
        const bounds = L.latLngBounds(
          L.latLng(region.bounds.south, region.bounds.west),
          L.latLng(region.bounds.north, region.bounds.east)
        );
        
        // تحريك الخريطة إلى المنطقة
        window.map.fitBounds(bounds);
      });
    });
    
    document.querySelectorAll('.delete-region').forEach(button => {
      button.addEventListener('click', function() {
        const index = parseInt(this.getAttribute('data-index'));
        
        if (confirm(`هل أنت متأكد من حذف المنطقة "${offlineRegions[index].name}"؟`)) {
          // حذف المنطقة من المصفوفة
          offlineRegions.splice(index, 1);
          
          // حفظ المصفوفة المحدثة
          localStorage.setItem('offlineRegions', JSON.stringify(offlineRegions));
          
          // تحديث العرض
          displaySavedRegions();
        }
      });
    });
  } else {
    regionsListContainer.style.display = 'none';
  }
}

// عرض نافذة مشاركة المنطقة
function showShareRegionDialog(region) {
  // إنشاء كود مشاركة فريد للمنطقة
  const shareCode = generateShareCode(region);
  
  // إنشاء رابط مشاركة
  const shareUrl = `${window.location.origin}${window.location.pathname}?share=${shareCode}`;
  
  // إنشاء نافذة منبثقة للمشاركة
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 500px;">
      <div class="modal-header">
        <h2>مشاركة منطقة "${region.name}"</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <p>يمكنك مشاركة هذه المنطقة مع الآخرين باستخدام الرابط أو الكود أدناه:</p>
        
        <div class="share-url-container">
          <label for="shareUrl">رابط المشاركة:</label>
          <div class="input-group">
            <input type="text" id="shareUrl" value="${shareUrl}" readonly class="form-control">
            <button id="copyUrlBtn" class="btn btn-outline-secondary">نسخ</button>
          </div>
        </div>
        
        <div class="share-code-container" style="margin-top: 15px;">
          <label for="shareCode">كود المشاركة:</label>
          <div class="input-group">
            <input type="text" id="shareCode" value="${shareCode}" readonly class="form-control">
            <button id="copyCodeBtn" class="btn btn-outline-secondary">نسخ</button>
          </div>
        </div>
        
        <div class="share-buttons" style="margin-top: 20px; text-align: center;">
          <button id="shareWhatsappBtn" class="btn btn-success">
            <i class="fab fa-whatsapp"></i> مشاركة عبر واتساب
          </button>
        </div>
      </div>
    </div>
  `;
  
  // إضافة النافذة المنبثقة إلى الصفحة
  document.body.appendChild(modal);
  
  // إضافة مستمعي الأحداث
  const closeBtn = modal.querySelector('.close');
  closeBtn.onclick = function() {
    document.body.removeChild(modal);
  };
  
  // إغلاق النافذة المنبثقة عند النقر خارجها
  window.onclick = function(event) {
    if (event.target === modal) {
      document.body.removeChild(modal);
    }
  };
  
  // نسخ رابط المشاركة
  const copyUrlBtn = document.getElementById('copyUrlBtn');
  copyUrlBtn.onclick = function() {
    const shareUrlInput = document.getElementById('shareUrl');
    shareUrlInput.select();
    document.execCommand('copy');
    copyUrlBtn.textContent = 'تم النسخ!';
    setTimeout(() => {
      copyUrlBtn.textContent = 'نسخ';
    }, 2000);
  };
  
  // نسخ كود المشاركة
  const copyCodeBtn = document.getElementById('copyCodeBtn');
  copyCodeBtn.onclick = function() {
    const shareCodeInput = document.getElementById('shareCode');
    shareCodeInput.select();
    document.execCommand('copy');
    copyCodeBtn.textContent = 'تم النسخ!';
    setTimeout(() => {
      copyCodeBtn.textContent = 'نسخ';
    }, 2000);
  };
  
  // مشاركة عبر واتساب
  const shareWhatsappBtn = document.getElementById('shareWhatsappBtn');
  shareWhatsappBtn.onclick = function() {
    const text = `منطقة خريطة "${region.name}" للاستخدام دون اتصال: ${shareUrl}`;
    window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
  };
}

// إنشاء كود مشاركة للمنطقة
function generateShareCode(region) {
  // إنشاء كائن يحتوي على المعلومات الأساسية للمنطقة
  const shareData = {
    n: region.name,
    b: {
      n: region.bounds.north,
      e: region.bounds.east,
      s: region.bounds.south,
      w: region.bounds.west
    },
    z: {
      min: region.zoomLevels.min,
      max: region.zoomLevels.max
    },
    t: region.timestamp
  };
  
  // تحويل البيانات إلى سلسلة JSON
  const jsonString = JSON.stringify(shareData);
  
  // تشفير السلسلة بترميز Base64
  return btoa(jsonString);
}

// معالجة كود المشاركة من الرابط
function processShareCode() {
  const urlParams = new URLSearchParams(window.location.search);
  const shareCode = urlParams.get('share');
  
  if (shareCode) {
    try {
      // فك تشفير الكود
      const jsonString = atob(shareCode);
      const shareData = JSON.parse(jsonString);
      
      // إنشاء كائن المنطقة
      const region = {
        name: shareData.n,
        bounds: {
          north: shareData.b.n,
          east: shareData.b.e,
          south: shareData.b.s,
          west: shareData.b.w
        },
        zoomLevels: {
          min: shareData.z.min,
          max: shareData.z.max
        },
        timestamp: shareData.t
      };
      
      // إظهار نافذة تأكيد التنزيل
      showSharedRegionConfirmation(region);
      
      // إزالة معلمة المشاركة من الرابط
      const newUrl = window.location.pathname + window.location.hash;
      history.replaceState(null, '', newUrl);
    } catch (error) {
      console.error('فشل في معالجة كود المشاركة:', error);
      showNotification('كود المشاركة غير صالح', 'error');
    }
  }
}

// إظهار نافذة تأكيد تنزيل المنطقة المشتركة
function showSharedRegionConfirmation(region) {
  // إنشاء حدود المنطقة
  const bounds = L.latLngBounds(
    L.latLng(region.bounds.south, region.bounds.west),
    L.latLng(region.bounds.north, region.bounds.east)
  );
  
  // حساب الحجم التقريبي
  const tileUrls = offlineMaps.getTileUrls(bounds, region.zoomLevels.min, region.zoomLevels.max);
  const estimatedSizeMB = ((tileUrls.length * 15) / 1024).toFixed(2);
  
  // إنشاء نافذة منبثقة للتأكيد
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 500px;">
      <div class="modal-header">
        <h2>تنزيل منطقة مشتركة</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <p>تمت مشاركة منطقة خريطة معك. هل ترغب في تنزيلها للاستخدام دون اتصال؟</p>
        
        <div class="region-details">
          <p><strong>اسم المنطقة:</strong> ${region.name}</p>
          <p><strong>الحجم التقريبي:</strong> ${estimatedSizeMB} ميجابايت (${tileUrls.length} بلاطة)</p>
        </div>
        
        <div class="confirmation-buttons" style="margin-top: 20px; text-align: center;">
          <button id="viewOnMapBtn" class="btn btn-info">عرض على الخريطة</button>
          <button id="downloadRegionBtn" class="btn btn-primary">تنزيل المنطقة</button>
          <button id="cancelDownloadBtn" class="btn btn-secondary">إلغاء</button>
        </div>
      </div>
    </div>
  `;
  
  // إضافة النافذة المنبثقة إلى الصفحة
  document.body.appendChild(modal);
  
  // إضافة مستمعي الأحداث
  const closeBtn = modal.querySelector('.close');
  closeBtn.onclick = function() {
    document.body.removeChild(modal);
  };
  
  // عرض المنطقة على الخريطة
  const viewOnMapBtn = document.getElementById('viewOnMapBtn');
  viewOnMapBtn.onclick = function() {
    map.fitBounds(bounds);
    
    // إضافة مستطيل مؤقت لإظهار المنطقة
    const rectangle = L.rectangle(bounds, {
      color: '#4285f4',
      weight: 2,
      fillOpacity: 0.2
    }).addTo(map);
    
    // إزالة المستطيل بعد 5 ثوانٍ
    setTimeout(() => {
      map.removeLayer(rectangle);
    }, 5000);
    
    document.body.removeChild(modal);
  };
  
  // تنزيل المنطقة
  const downloadRegionBtn = document.getElementById('downloadRegionBtn');
  downloadRegionBtn.onclick = function() {
    document.body.removeChild(modal);
    
    // عرض مربع حوار لتأكيد الاسم
    const regionName = prompt('أدخل اسمًا للمنطقة:', region.name);
    if (regionName) {
      // إظهار شريط التقدم
      showProgressBar();
      
      // تنزيل المنطقة
      offlineMaps.downloadRegion(bounds, regionName, region.zoomLevels)
        .then(result => {
          hideProgressBar();
          if (result.success) {
            showNotification(`تم تنزيل منطقة "${regionName}" بنجاح`, 'success');
          } else {
            showNotification(`فشل في تنزيل المنطقة: ${result.error}`, 'error');
          }
        })
        .catch(error => {
          hideProgressBar();
          showNotification(`فشل في تنزيل المنطقة: ${error.message}`, 'error');
        });
    }
  };
  
  // إلغاء التنزيل
  const cancelDownloadBtn = document.getElementById('cancelDownloadBtn');
  cancelDownloadBtn.onclick = function() {
    document.body.removeChild(modal);
  };
}

// إضافة وضع القيادة الليلية
function addNightMode() {
  // التحقق إذا كان زر وضع القيادة الليلية موجود بالفعل
  if (!document.getElementById('night-mode-toggle')) {
    const nightModeToggle = document.querySelector('#night-mode-toggle');
    if (!nightModeToggle) {
      const controlsDiv = document.querySelector('.controls');
      if (controlsDiv) {
        const nightModeBtn = document.createElement('button');
        nightModeBtn.className = 'control-button';
        nightModeBtn.id = 'night-mode-toggle';
        nightModeBtn.title = 'وضع القيادة الليلية';
        nightModeBtn.innerHTML = '&#127769;'; // رمز القمر
        controlsDiv.appendChild(nightModeBtn);
        
        // إضافة حدث النقر على زر الوضع الليلي
        nightModeBtn.addEventListener('click', toggleNightMode);
      }
    }
  }

  // إضافة ملف CSS للوضع الليلي
  const nightModeStyle = document.createElement('style');
  nightModeStyle.textContent = `
    body.night-mode {
      background-color: #121212;
      color: #ffffff;
    }

    /* الخريطة في الوضع الليلي */
    body.night-mode .leaflet-container {
      background-color: #242424;
    }

    /* العناصر في الوضع الليلي */
    body.night-mode .header,
    body.night-mode .controls .control-button,
    body.night-mode .device-info,
    body.night-mode .location-info,
    body.night-mode .search-popup,
    body.night-mode .status-bar,
    body.night-mode .layers-control,
    body.night-mode .offline-maps-panel,
    body.night-mode #route-info-box {
      background-color: #242424;
      color: #ffffff;
      border-color: #444444;
    }

    /* الأزرار في الوضع الليلي */
    body.night-mode button:not(.control-button) {
      background-color: #444444;
      color: #ffffff;
      border-color: #666666;
    }

    /* حقول الإدخال في الوضع الليلي */
    body.night-mode input,
    body.night-mode select {
      background-color: #333333;
      color: #ffffff;
      border-color: #666666;
    }

    /* تنبيهات الصوت في الوضع الليلي */
    body.night-mode #audio-notification {
      background-color: rgba(36, 36, 36, 0.9);
    }

    /* تعديل ألوان المسارات في الوضع الليلي */
    body.night-mode .main-route {
      stroke: #4CAF50;
      stroke-opacity: 0.9;
    }

    body.night-mode .alternative-route {
      stroke: #ff9800;
      stroke-opacity: 0.7;
    }
  `;
  document.head.appendChild(nightModeStyle);

  // إضافة حدث النقر على زر الوضع الليلي
  document.getElementById('night-mode-toggle')?.addEventListener('click', toggleNightMode);

  // التحقق من حالة الوضع الليلي المحفوظة
  const isNightMode = localStorage.getItem('nightMode') === 'true';
  if (isNightMode) {
    document.body.classList.add('night-mode');
  }
}

// تبديل الوضع الليلي
function toggleNightMode() {
  const isNightMode = document.body.classList.toggle('night-mode');
  localStorage.setItem('nightMode', isNightMode);

  // تغيير نمط الخريطة إلى الوضع الليلي/العادي
  if (window.map && window.baseMaps) {
    // إزالة جميع الطبقات
    Object.values(window.baseMaps).forEach(layer => {
      if (window.map.hasLayer(layer)) {
        window.map.removeLayer(layer);
      }
    });
    
    // إضافة طبقة الخريطة المناسبة
    if (isNightMode) {
      // إضافة طبقة الخريطة الليلية
      if (window.baseMaps['خريطة الطرق عالية الدقة']) {
        window.baseMaps['خريطة الطرق عالية الدقة'].addTo(window.map);
      } else if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    } else {
      // إضافة طبقة الخريطة العادية
      if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    }
  }
}

// تحديث وظيفة عرض مدير الخرائط دون اتصال لإضافة زر المشاركة
function showOfflineMapManager() {
  // إنشاء نافذة منبثقة لإدارة الخرائط دون اتصال
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'offlineMapManager';
  
  // الحصول على المناطق المحفوظة
  offlineMaps.getRegions().then(regions => {
    // تنسيق المناطق وإضافة معلومات إضافية
    const regionsWithDetails = regions.map(region => {
      const estimatedSizeMB = (region.tileCount * 15 / 1024).toFixed(2);
      const date = new Date(region.timestamp);
      const formattedDate = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
      
      return {
        ...region,
        formattedDate,
        estimatedSizeMB
      };
    });
    
    // إنشاء محتوى النافذة المنبثقة
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2>إدارة الخرائط دون اتصال</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <div class="regions-list">
            <h3>المناطق المحفوظة (${regionsWithDetails.length})</h3>
            ${regionsWithDetails.length === 0 ? '<p>لا توجد مناطق محفوظة</p>' : ''}
            <div class="region-cards">
              ${regionsWithDetails.map(region => `
                <div class="region-card" data-region-id="${region.id}">
                  <div class="region-header">
                    <h4>${region.name}</h4>
                    <div class="region-actions">
                      <button class="btn-use-region" title="استخدام هذه المنطقة"><i class="fas fa-map-marker-alt"></i></button>
                      <button class="btn-share-region" title="مشاركة هذه المنطقة"><i class="fas fa-share-alt"></i></button>
                      <button class="btn-delete-region" title="حذف هذه المنطقة"><i class="fas fa-trash"></i></button>
                    </div>
                  </div>
                  <div class="region-info">
                    <p>الحجم: ${region.estimatedSizeMB} ميجابايت (${region.tileCount} بلاطة)<br>
                    تاريخ التنزيل: ${region.formattedDate}</p>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="offline-actions">
            <button id="downloadNewRegionBtn" class="btn btn-primary">تنزيل منطقة جديدة</button>
          </div>
        </div>
      </div>
    `;
    
    // إضافة النافذة المنبثقة إلى الصفحة
    document.body.appendChild(modal);
    
    // إضافة الأنماط إذا لم تكن موجودة
    if (!document.getElementById('offline-manager-styles')) {
      const styles = document.createElement('style');
      styles.id = 'offline-manager-styles';
      styles.textContent = `
        .modal {
          display: block;
          position: fixed;
          z-index: 9999;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0,0,0,0.7);
        }
        
        .modal-content {
          background-color: #f8f9fa;
          margin: 10% auto;
          padding: 0;
          border-radius: 8px;
          width: 80%;
          max-width: 600px;
          direction: rtl;
        }
        
        .modal-header {
          padding: 15px 20px;
          background-color: #4285f4;
          color: white;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .modal-body { padding: 20px; }
        
        .close {
          color: white;
          font-size: 28px;
          font-weight: bold;
          cursor: pointer;
        }
        
        .region-cards {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 15px;
          margin-top: 15px;
        }
        
        .region-card {
          background-color: white;
          border-radius: 5px;
          padding: 15px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .region-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        
        .region-actions {
          display: flex;
          gap: 5px;
        }
        
        .region-actions button {
          background: none;
          border: none;
          cursor: pointer;
          color: #666;
        }
        
        .btn-use-region:hover { color: #4285f4; }
        .btn-share-region:hover { color: #4CAF50; }
        .btn-delete-region:hover { color: #ea4335; }
      `;
      document.head.appendChild(styles);
    }
    
    // إضافة مستمعي الأحداث
    const closeBtn = modal.querySelector('.close');
    closeBtn.onclick = function() {
      document.body.removeChild(modal);
    };
    
    // إغلاق النافذة المنبثقة عند النقر خارجها
    window.onclick = function(event) {
      if (event.target === modal) {
        document.body.removeChild(modal);
      }
    };
    
    // زر تنزيل منطقة جديدة
    const downloadNewRegionBtn = document.getElementById('downloadNewRegionBtn');
    downloadNewRegionBtn.onclick = function() {
      document.body.removeChild(modal);
      enableAreaSelectMode();
    };
    
    // أزرار استخدام المنطقة
    const useRegionBtns = modal.querySelectorAll('.btn-use-region');
    useRegionBtns.forEach(btn => {
      btn.onclick = function() {
        const regionId = btn.closest('.region-card').dataset.regionId;
        const region = regions.find(r => r.id === regionId);
        
        if (region) {
          // الانتقال إلى المنطقة على الخريطة
          const bounds = L.latLngBounds(
            L.latLng(region.bounds.south, region.bounds.west),
            L.latLng(region.bounds.north, region.bounds.east)
          );
          
          map.fitBounds(bounds);
          showNotification(`تم الانتقال إلى منطقة "${region.name}"`, 'success');
          document.body.removeChild(modal);
        }
      };
    });
    
    // أزرار مشاركة المنطقة
    const shareRegionBtns = modal.querySelectorAll('.btn-share-region');
    shareRegionBtns.forEach(btn => {
      btn.onclick = function() {
        const regionId = btn.closest('.region-card').dataset.regionId;
        const region = regions.find(r => r.id === regionId);
        
        if (region) {
          showShareRegionDialog(region);
        }
      };
    });
    
    // أزرار حذف المنطقة
    const deleteRegionBtns = modal.querySelectorAll('.btn-delete-region');
    deleteRegionBtns.forEach(btn => {
      btn.onclick = function() {
        const regionId = btn.closest('.region-card').dataset.regionId;
        const region = regions.find(r => r.id === regionId);
        
        if (region && confirm(`هل أنت متأكد من رغبتك في حذف منطقة "${region.name}"؟`)) {
          offlineMaps.deleteRegion(regionId).then(() => {
            document.body.removeChild(modal);
            showOfflineMapManager();
            showNotification(`تم حذف منطقة "${region.name}" بنجاح`, 'success');
          }).catch(error => {
            alert(`فشل في حذف المنطقة: ${error.message}`);
          });
        }
      };
    });
  }).catch(error => {
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2>إدارة الخرائط دون اتصال</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          <p class="error-message">فشل في تحميل المناطق المحفوظة: ${error.message}</p>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    const closeBtn = modal.querySelector('.close');
    closeBtn.onclick = function() {
      document.body.removeChild(modal);
    };
  });
}

// تحديث قالب بطاقة المنطقة لإضافة زر المشاركة
// تعريف المتغير regionsWithDetails لتجنب الخطأ
let regionsWithDetails = [];

// إنشاء عنصر modal قبل محاولة تعيين محتواه
const modal = document.createElement('div');
modal.className = 'modal';
modal.innerHTML = `
  <div class="modal-content">
    <div class="modal-header">
      <h2>إدارة الخرائط دون اتصال</h2>
      <span class="close">&times;</span>
    </div>
    <div class="modal-body">
      <div class="regions-list">
        <h3>المناطق المحفوظة (${regionsWithDetails?.length || 0})</h3>
        ${regionsWithDetails?.length === 0 ? '<p>لا توجد مناطق محفوظة</p>' : ''}
        <div class="region-cards">
          ${regionsWithDetails?.map(region => `
            <div class="region-card" data-region-id="${region.id}">
              <div class="region-header">
                <h4>${region.name}</h4>
                <div class="region-actions">
                  <button class="btn-use-region" title="استخدام هذه المنطقة"><i class="fas fa-map-marker-alt"></i></button>
                  <button class="btn-share-region" title="مشاركة هذه المنطقة"><i class="fas fa-share-alt"></i></button>
                  <button class="btn-delete-region" title="حذف هذه المنطقة"><i class="fas fa-trash"></i></button>
                </div>
              </div>
              <div class="region-info">
                <p>الحجم: ${region.estimatedSizeMB} ميجابايت (${region.tileCount} بلاطة)<br>
                تاريخ التنزيل: ${region.formattedDate}</p>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      
      <div class="offline-actions">
        <button id="downloadNewRegionBtn" class="btn btn-primary">تنزيل منطقة جديدة</button>
      </div>
    </div>
  </div>
`;

// تحديث وظيفة التهيئة لمعالجة كود المشاركة
document.addEventListener('DOMContentLoaded', function() {
  // إضافة معالج حدث لزر إضافة نقطة جديدة
  const addLocationBtn = document.getElementById('add-location-btn');
  if (addLocationBtn) {
    addLocationBtn.addEventListener('click', function() {
      // إنشاء نموذج إضافة نقطة جديدة
      showAddLocationForm();
    });
  }

  // معالجة كود المشاركة من الرابط
  processShareCode();
});

// دالة لعرض نموذج إضافة نقطة جديدة
function showAddLocationForm() {
  // إنشاء نافذة منبثقة للنموذج
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 500px;">
      <div class="modal-header">
        <h2>إضافة نقطة جديدة</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="add-point-form">
          <div style="margin-bottom: 15px;">
            <label for="point-name" style="display: block; margin-bottom: 5px;">اسم النقطة:</label>
            <input type="text" id="point-name" required style="width: 100%; padding: 8px; border-radius: 3px; border: 1px solid #ccc;">
          </div>
          
          <div style="margin-bottom: 15px;">
            <label for="point-type" style="display: block; margin-bottom: 5px;">نوع النقطة:</label>
            <select id="point-type" required style="width: 100%; padding: 8px; border-radius: 3px; border: 1px solid #ccc;">
              <option value="مطعم">مطعم</option>
              <option value="محطة وقود">محطة وقود</option>
              <option value="معلم سياحي">معلم سياحي</option>
              <option value="مسجد">مسجد</option>
              <option value="فندق">فندق</option>
              <option value="مركز تسوق">مركز تسوق</option>
              <option value="مستشفى">مستشفى</option>
              <option value="مدرسة">مدرسة</option>
              <option value="أخرى">أخرى</option>
            </select>
          </div>
          
          <div style="margin-bottom: 15px;">
            <label for="point-description" style="display: block; margin-bottom: 5px;">وصف النقطة (اختياري):</label>
            <textarea id="point-description" style="width: 100%; padding: 8px; border-radius: 3px; border: 1px solid #ccc; height: 80px;"></textarea>
          </div>

          <div style="text-align: center; margin-top: 20px;">
            <button type="button" id="add-current-location" class="btn" style="background-color: #2196F3; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer; margin-right: 10px;">إضافة موقعي الحالي</button>
            <button type="button" id="add-map-center" class="btn" style="background-color: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">إضافة مركز الخريطة</button>
          </div>
        </form>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // إضافة مستمع لزر الإغلاق
  const closeBtn = modal.querySelector('.close');
  closeBtn.onclick = function() {
    document.body.removeChild(modal);
  };
  
  // إغلاق النافذة المنبثقة عند النقر خارجها
  window.onclick = function(event) {
    if (event.target === modal) {
      document.body.removeChild(modal);
    }
  };
  
  // إضافة مستمعين للأزرار
  const addCurrentLocationBtn = modal.querySelector('#add-current-location');
  addCurrentLocationBtn.onclick = function() {
    navigator.geolocation.getCurrentPosition(
      function(position) {
        saveNewLocation({
          name: document.getElementById('point-name').value,
          type: document.getElementById('point-type').value,
          description: document.getElementById('point-description').value,
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      function(error) {
        alert('فشل في الحصول على الموقع الحالي: ' + error.message);
      }
    );
  };
  
  const addMapCenterBtn = modal.querySelector('#add-map-center');
  addMapCenterBtn.onclick = function() {
    const center = map.getCenter();
    saveNewLocation({
      name: document.getElementById('point-name').value,
      type: document.getElementById('point-type').value,
      description: document.getElementById('point-description').value,
      latitude: center.lat,
      longitude: center.lng
    });
  };
}

// دالة لحفظ نقطة جديدة
function saveNewLocation(locationData) {
  // التحقق من البيانات المطلوبة
  if (!locationData.name) {
    alert('يرجى إدخال اسم النقطة');
    return;
  }
  
  // إنشاء علامة على الخريطة
  const marker = L.marker([locationData.latitude, locationData.longitude], {
    icon: L.divIcon({
      className: 'custom-marker',
      html: `<div class="marker-pin" style="background-color: ${getMarkerColor(locationData.type)};"></div>`,
      iconSize: [30, 42],
      iconAnchor: [15, 42]
    })
  }).addTo(map);
  
  // إضافة نافذة منبثقة للعلامة
  marker.bindPopup(`
    <div class="popup-content">
      <h3>${locationData.name}</h3>
      <p><strong>النوع:</strong> ${locationData.type}</p>
      ${locationData.description ? `<p><strong>الوصف:</strong> ${locationData.description}</p>` : ''}
      <p><strong>الإحداثيات:</strong> ${locationData.latitude.toFixed(6)}, ${locationData.longitude.toFixed(6)}</p>
    </div>
  `);
  
  // تخزين النقطة في IndexedDB للاستخدام دون اتصال
  try {
    const db = window.openDatabase || window.indexedDB;
    if (db) {
      // الكود الخاص بتخزين النقطة في IndexedDB
      storeLocation(locationData);
    }
  } catch (error) {
    console.error('فشل في تخزين النقطة:', error);
  }
  
  // إغلاق نافذة إضافة النقطة
  const modal = document.querySelector('.modal');
  if (modal) {
    document.body.removeChild(modal);
  }
  
  // عرض رسالة نجاح
  showNotification(`تمت إضافة نقطة "${locationData.name}" بنجاح`, 'success');
}

// دالة لتخزين النقطة في IndexedDB
function storeLocation(locationData) {
  // إضافة الطابع الزمني ومعرف فريد
  const location = {
    ...locationData,
    id: Date.now().toString(),
    timestamp: Date.now(),
    synced: false  // حالة مزامنة مع الخادم
  };
  
  // استخدام دالة من offline-utils.js لتخزين النقطة
  if (typeof saveLocationToIndexedDB === 'function') {
    saveLocationToIndexedDB(location);
  } else {
    // إذا لم تكن الدالة موجودة، ننشئ المستودع هنا
    const request = indexedDB.open('yemen-nav-db', 1);
    
    request.onupgradeneeded = function(event) {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('locations')) {
        db.createObjectStore('locations', { keyPath: 'id' });
      }
    };
    
    request.onsuccess = function(event) {
      const db = event.target.result;
      const transaction = db.transaction(['locations'], 'readwrite');
      const store = transaction.objectStore('locations');
      
      store.add(location);
    };
    
    request.onerror = function(event) {
      console.error('فشل في فتح قاعدة البيانات:', event.target.error);
    };
  }
}

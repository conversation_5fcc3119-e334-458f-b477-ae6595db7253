// فحص الفئات المكررة في قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function checkCategories() {
    try {
        console.log('🔍 فحص فئات الأماكن...\n');
        
        // الحصول على جميع الفئات
        const result = await pool.query(`
            SELECT id, name_ar, name_en, parent_id, icon, color 
            FROM place_categories 
            ORDER BY parent_id NULLS FIRST, name_ar
        `);
        
        console.log(`📊 إجمالي الفئات: ${result.rows.length}\n`);
        
        // تجميع الفئات حسب الاسم العربي للبحث عن التكرار
        const categoryGroups = {};
        
        result.rows.forEach(category => {
            const key = category.name_ar.trim().toLowerCase();
            if (!categoryGroups[key]) {
                categoryGroups[key] = [];
            }
            categoryGroups[key].push(category);
        });
        
        // البحث عن الفئات المكررة
        const duplicates = Object.keys(categoryGroups).filter(key => categoryGroups[key].length > 1);
        
        if (duplicates.length > 0) {
            console.log('❌ تم العثور على فئات مكررة:\n');
            
            duplicates.forEach(duplicateName => {
                console.log(`🔄 "${duplicateName}":`);
                categoryGroups[duplicateName].forEach(cat => {
                    console.log(`   - ID: ${cat.id}, الاسم: ${cat.name_ar}, الإنجليزي: ${cat.name_en}, الأب: ${cat.parent_id || 'لا يوجد'}`);
                });
                console.log('');
            });
            
            // اقتراح حل
            console.log('💡 الحلول المقترحة:');
            console.log('1. حذف الفئات المكررة');
            console.log('2. دمج الفئات المكررة');
            console.log('3. إعادة تسمية الفئات المكررة\n');
            
        } else {
            console.log('✅ لا توجد فئات مكررة\n');
        }
        
        // عرض جميع الفئات مرتبة
        console.log('📋 جميع الفئات:');
        console.log('================');
        
        const mainCategories = result.rows.filter(cat => !cat.parent_id);
        const subCategories = result.rows.filter(cat => cat.parent_id);
        
        console.log('\n🏷️ الفئات الرئيسية:');
        mainCategories.forEach(cat => {
            console.log(`   ${cat.id}. ${cat.name_ar} (${cat.name_en})`);
            
            // عرض الفئات الفرعية
            const subs = subCategories.filter(sub => sub.parent_id === cat.id);
            if (subs.length > 0) {
                subs.forEach(sub => {
                    console.log(`      └── ${sub.id}. ${sub.name_ar} (${sub.name_en})`);
                });
            }
        });
        
        // الفئات الفرعية بدون أب
        const orphanCategories = subCategories.filter(cat => 
            !mainCategories.find(main => main.id === cat.parent_id)
        );
        
        if (orphanCategories.length > 0) {
            console.log('\n⚠️ فئات فرعية بدون فئة أب:');
            orphanCategories.forEach(cat => {
                console.log(`   ${cat.id}. ${cat.name_ar} (parent_id: ${cat.parent_id})`);
            });
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص الفئات:', error);
    } finally {
        await pool.end();
    }
}

checkCategories();

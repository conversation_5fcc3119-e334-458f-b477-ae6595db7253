#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps Complete - Data Import Tool
استيراد البيانات الموجودة من المشروع القديم
"""

import os
import sys
import json
import psycopg2
import psycopg2.extras
from datetime import datetime
import shutil
import uuid
from pathlib import Path

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_maps_complete',
    'user': 'yemen_maps_user',
    'password': 'YemenMaps2024!',
    'port': 5432
}

# مسارات المشروع
OLD_PROJECT_PATH = r"E:\yemen gps"
NEW_PROJECT_PATH = r"E:\yemen-maps-complete"
OLD_IMAGES_PATH = r"E:\yemen gps\public\images\places"
NEW_IMAGES_PATH = r"E:\yemen-maps-complete\images\imported"

class DataImporter:
    def __init__(self):
        self.conn = None
        self.cursor = None
        self.imported_places = 0
        self.imported_photos = 0
        self.errors = []
        
    def connect_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def connect_old_database(self):
        """الاتصال بقاعدة البيانات القديمة"""
        try:
            # محاولة الاتصال بقاعدة البيانات القديمة
            # يجب تعديل هذه المعلومات حسب إعدادات قاعدة البيانات القديمة
            old_db_config = {
                'host': 'localhost',
                'database': 'yemen_gps',  # اسم قاعدة البيانات القديمة
                'user': 'postgres',       # المستخدم
                'password': 'password',   # كلمة المرور
                'port': 5432
            }
            
            old_conn = psycopg2.connect(**old_db_config)
            old_cursor = old_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print("✅ تم الاتصال بقاعدة البيانات القديمة")
            return old_conn, old_cursor
        except Exception as e:
            print(f"⚠️ لم يتم العثور على قاعدة البيانات القديمة: {e}")
            return None, None
    
    def import_from_old_database(self):
        """استيراد البيانات من قاعدة البيانات القديمة"""
        old_conn, old_cursor = self.connect_old_database()
        
        if not old_conn:
            print("⚠️ سيتم تخطي استيراد البيانات من قاعدة البيانات القديمة")
            return
        
        try:
            # استعلام لجلب الأماكن من قاعدة البيانات القديمة
            # يجب تعديل هذا الاستعلام حسب هيكل قاعدة البيانات القديمة
            old_cursor.execute("""
                SELECT * FROM places 
                WHERE city LIKE '%صنعاء%' OR city LIKE '%Sanaa%'
                ORDER BY id
            """)
            
            old_places = old_cursor.fetchall()
            print(f"📊 تم العثور على {len(old_places)} مكان في قاعدة البيانات القديمة")
            
            for place in old_places:
                self.import_single_place(place, source='imported')
                
        except Exception as e:
            print(f"❌ خطأ في استيراد البيانات: {e}")
            self.errors.append(f"Database import error: {e}")
        finally:
            if old_conn:
                old_conn.close()
    
    def import_single_place(self, place_data, source='imported'):
        """استيراد مكان واحد"""
        try:
            # إنشاء معرف فريد للمكان
            place_id = f"imported_{uuid.uuid4().hex[:12]}"
            
            # تحضير البيانات
            insert_query = """
                INSERT INTO places (
                    place_id, name, name_ar, latitude, longitude, location_point,
                    governorate_id, city, address, category, subcategory,
                    phone, rating, source, is_verified, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            # تحديد معرف محافظة صنعاء
            sanaa_gov_id = 1  # أمانة العاصمة
            
            # تنظيف وتحضير البيانات
            name = place_data.get('name', '').strip()
            name_ar = place_data.get('name_ar', name).strip()
            latitude = float(place_data.get('latitude', 0))
            longitude = float(place_data.get('longitude', 0))
            city = place_data.get('city', 'صنعاء').strip()
            address = place_data.get('address', '').strip()
            category = place_data.get('category', 'general').strip()
            subcategory = place_data.get('subcategory', '').strip()
            phone = place_data.get('phone', '').strip()
            rating = place_data.get('rating', None)
            
            # التحقق من صحة الإحداثيات
            if latitude == 0 or longitude == 0:
                print(f"⚠️ تم تخطي {name} - إحداثيات غير صحيحة")
                return False
            
            # تنفيذ الاستعلام
            self.cursor.execute(insert_query, (
                place_id, name, name_ar, latitude, longitude, longitude, latitude,
                sanaa_gov_id, city, address, category, subcategory,
                phone, rating, source, True, datetime.now()
            ))
            
            self.imported_places += 1
            print(f"✅ تم استيراد: {name}")
            
            # البحث عن صور المكان
            self.import_place_photos(place_data, place_id)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في استيراد {place_data.get('name', 'Unknown')}: {e}")
            self.errors.append(f"Place import error: {e}")
            return False
    
    def import_place_photos(self, place_data, place_id):
        """استيراد صور المكان"""
        try:
            # البحث عن صور المكان في المجلد القديم
            old_place_id = place_data.get('id', '')
            photo_patterns = [
                f"{old_place_id}_*.jpg",
                f"{old_place_id}_*.png",
                f"{old_place_id}_*.jpeg"
            ]
            
            photo_count = 0
            for pattern in photo_patterns:
                photo_files = list(Path(OLD_IMAGES_PATH).glob(pattern))
                
                for photo_file in photo_files:
                    if self.copy_and_import_photo(photo_file, place_id, photo_count):
                        photo_count += 1
                        self.imported_photos += 1
            
            if photo_count > 0:
                print(f"  📸 تم استيراد {photo_count} صورة")
                
        except Exception as e:
            print(f"⚠️ خطأ في استيراد صور {place_id}: {e}")
    
    def copy_and_import_photo(self, photo_file, place_id, photo_index):
        """نسخ وتسجيل صورة واحدة"""
        try:
            # إنشاء اسم ملف جديد
            file_extension = photo_file.suffix
            new_filename = f"{place_id}_{photo_index}{file_extension}"
            new_file_path = Path(NEW_IMAGES_PATH) / new_filename
            
            # نسخ الملف
            shutil.copy2(photo_file, new_file_path)
            
            # تسجيل في قاعدة البيانات
            insert_photo_query = """
                INSERT INTO place_photos (
                    place_id, photo_path, width, height, file_format,
                    photo_type, is_primary, display_order, source, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            # تحديد إذا كانت الصورة الأولى (رئيسية)
            is_primary = photo_index == 0
            
            self.cursor.execute(insert_photo_query, (
                place_id, str(new_file_path), None, None, file_extension[1:],
                'general', is_primary, photo_index, 'imported', datetime.now()
            ))
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في نسخ الصورة {photo_file}: {e}")
            return False
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية لصنعاء"""
        sample_places = [
            {
                'name': 'مطعم الشاهي الشعبي',
                'name_ar': 'مطعم الشاهي الشعبي',
                'latitude': 15.3547,
                'longitude': 44.2066,
                'category': 'restaurant',
                'subcategory': 'traditional',
                'city': 'صنعاء',
                'address': 'شارع الزبيري، صنعاء القديمة',
                'phone': '+967 1 274856',
                'rating': 4.2
            },
            {
                'name': 'مستشفى الثورة العام',
                'name_ar': 'مستشفى الثورة العام',
                'latitude': 15.3515,
                'longitude': 44.2139,
                'category': 'hospital',
                'subcategory': 'public',
                'city': 'صنعاء',
                'address': 'شارع الثورة، صنعاء',
                'phone': '+967 1 252000',
                'rating': 3.8
            },
            {
                'name': 'جامعة صنعاء',
                'name_ar': 'جامعة صنعاء',
                'latitude': 15.3729,
                'longitude': 44.1901,
                'category': 'university',
                'subcategory': 'public',
                'city': 'صنعاء',
                'address': 'شارع جمال عبد الناصر، صنعاء',
                'phone': '+967 1 250553',
                'rating': 4.0
            }
        ]
        
        print("📝 إنشاء بيانات تجريبية...")
        for place in sample_places:
            self.import_single_place(place, source='sample')
    
    def run_import(self):
        """تشغيل عملية الاستيراد الكاملة"""
        print("🚀 بدء عملية استيراد البيانات...")
        print("=" * 50)
        
        if not self.connect_database():
            return False
        
        try:
            # استيراد من قاعدة البيانات القديمة
            self.import_from_old_database()
            
            # إنشاء بيانات تجريبية إذا لم يتم العثور على بيانات
            if self.imported_places == 0:
                print("📝 لم يتم العثور على بيانات، سيتم إنشاء بيانات تجريبية...")
                self.create_sample_data()
            
            # حفظ التغييرات
            self.conn.commit()
            
            # طباعة النتائج
            print("=" * 50)
            print("📊 نتائج الاستيراد:")
            print(f"✅ الأماكن المستوردة: {self.imported_places}")
            print(f"📸 الصور المستوردة: {self.imported_photos}")
            
            if self.errors:
                print(f"⚠️ الأخطاء: {len(self.errors)}")
                for error in self.errors[:5]:  # عرض أول 5 أخطاء فقط
                    print(f"   - {error}")
            
            print("✅ تم الانتهاء من عملية الاستيراد بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ عام في عملية الاستيراد: {e}")
            self.conn.rollback()
            return False
        finally:
            if self.conn:
                self.conn.close()

def main():
    """الدالة الرئيسية"""
    print("🇾🇪 Yemen Maps Complete - Data Import Tool")
    print("=" * 50)
    
    # التحقق من وجود المجلدات
    if not os.path.exists(NEW_PROJECT_PATH):
        print(f"❌ مجلد المشروع الجديد غير موجود: {NEW_PROJECT_PATH}")
        print("يرجى تشغيل setup_project.bat أولاً")
        return
    
    # إنشاء مجلد الصور إذا لم يكن موجوداً
    os.makedirs(NEW_IMAGES_PATH, exist_ok=True)
    
    # تشغيل عملية الاستيراد
    importer = DataImporter()
    success = importer.run_import()
    
    if success:
        print("\n🎉 تم الانتهاء بنجاح! يمكنك الآن المتابعة للخطوة التالية.")
    else:
        print("\n❌ فشلت عملية الاستيراد. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()

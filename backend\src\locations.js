// وحدة إدارة المواقع لتطبيق Yemen GPS
const db = require('./database');

// الحصول على قائمة التصنيفات
async function getCategories() {
  try {
    const result = await db.query(
      'SELECT * FROM location_categories WHERE is_active = true ORDER BY display_order, name'
    );
    return result.rows;
  } catch (error) {
    console.error('خطأ في الحصول على قائمة التصنيفات:', error);
    throw error;
  }
}

// الحصول على قائمة المواقع
async function getLocations(limit = 50, offset = 0, categoryId = null) {
  try {
    let query = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color,
      u.username as added_by_username
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      LEFT JOIN users u ON l.added_by = u.user_id
      WHERE l.is_public = true
    `;
    
    const queryParams = [];
    
    if (categoryId) {
      query += ` AND l.category_id = $1`;
      queryParams.push(categoryId);
    }
    
    query += ` ORDER BY l.rating DESC, l.views DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    
    queryParams.push(limit, offset);
    
    const result = await db.query(query, queryParams);
    return result.rows;
  } catch (error) {
    console.error('خطأ في الحصول على قائمة المواقع:', error);
    throw error;
  }
}

// البحث عن المواقع
async function searchLocations(searchTerm, limit = 20) {
  try {
    const query = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      WHERE l.is_public = true AND (
        l.name ILIKE $1 OR
        l.name_ar ILIKE $1 OR
        l.description ILIKE $1 OR
        l.address ILIKE $1 OR
        c.name ILIKE $1 OR
        c.name_ar ILIKE $1
      )
      ORDER BY l.rating DESC, l.views DESC
      LIMIT $2
    `;
    
    const result = await db.query(query, [`%${searchTerm}%`, limit]);
    
    // تسجيل البحث إذا كان المستخدم مسجل الدخول
    // (هذه الوظيفة ستكون في واجهة API وليس هنا)
    
    return result.rows;
  } catch (error) {
    console.error('خطأ في البحث عن المواقع:', error);
    throw error;
  }
}

// الحصول على المواقع القريبة
async function getNearbyLocations(lat, lng, radius = 5000, categoryId = null, limit = 20) {
  try {
    let query = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color,
      ST_Distance(l.geom, ST_SetSRID(ST_MakePoint($1, $2), 4326)) as distance
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      WHERE l.is_public = true AND ST_DWithin(l.geom, ST_SetSRID(ST_MakePoint($1, $2), 4326), $3)
    `;
    
    const queryParams = [lng, lat, radius]; // PostGIS expects longitude first
    
    if (categoryId) {
      query += ` AND l.category_id = $4`;
      queryParams.push(categoryId);
    }
    
    query += ` ORDER BY distance ASC LIMIT $${queryParams.length + 1}`;
    queryParams.push(limit);
    
    const result = await db.query(query, queryParams);
    return result.rows;
  } catch (error) {
    console.error('خطأ في الحصول على المواقع القريبة:', error);
    throw error;
  }
}

// الحصول على تفاصيل موقع
async function getLocationDetails(locationId) {
  try {
    // زيادة عدد المشاهدات
    await db.query(
      'UPDATE locations SET views = views + 1 WHERE location_id = $1',
      [locationId]
    );
    
    // الحصول على تفاصيل الموقع
    const locationQuery = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color,
      u.username as added_by_username, ST_X(l.geom) as longitude, ST_Y(l.geom) as latitude
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      LEFT JOIN users u ON l.added_by = u.user_id
      WHERE l.location_id = $1
    `;
    
    const locationResult = await db.query(locationQuery, [locationId]);
    
    if (locationResult.rows.length === 0) {
      throw new Error('لم يتم العثور على الموقع');
    }
    
    const location = locationResult.rows[0];
    
    // الحصول على صور الموقع
    const imagesQuery = `
      SELECT * FROM location_images 
      WHERE location_id = $1 
      ORDER BY is_primary DESC, upload_date DESC
    `;
    
    const imagesResult = await db.query(imagesQuery, [locationId]);
    location.images = imagesResult.rows;
    
    // الحصول على تقييمات الموقع
    const reviewsQuery = `
      SELECT r.*, u.username, u.profile_image
      FROM location_reviews r
      JOIN users u ON r.user_id = u.user_id
      WHERE r.location_id = $1 AND r.is_approved = true
      ORDER BY r.review_date DESC
    `;
    
    const reviewsResult = await db.query(reviewsQuery, [locationId]);
    location.reviews = reviewsResult.rows;
    
    return location;
  } catch (error) {
    console.error('خطأ في الحصول على تفاصيل الموقع:', error);
    throw error;
  }
}

// إضافة موقع جديد
async function addLocation(locationData, userId) {
  try {
    const { 
      name, name_ar, category_id, latitude, longitude, 
      address, description, phone, website, opening_hours 
    } = locationData;
    
    const query = `
      INSERT INTO locations (
        name, name_ar, category_id, geom, address, description, 
        phone, website, opening_hours, is_verified, added_by
      ) VALUES (
        $1, $2, $3, ST_SetSRID(ST_MakePoint($5, $4), 4326), $6, $7, $8, $9, $10, false, $11
      )
      RETURNING *
    `;
    
    const result = await db.query(query, [
      name, name_ar, category_id, latitude, longitude, 
      address, description, phone, website, opening_hours, userId
    ]);
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في إضافة موقع جديد:', error);
    throw error;
  }
}

// تحديث موقع
async function updateLocation(locationId, locationData, userId) {
  try {
    // التحقق من صلاحية التعديل
    const checkQuery = `
      SELECT * FROM locations WHERE location_id = $1 AND (added_by = $2 OR $2 IN (
        SELECT u.user_id FROM users u
        JOIN role_permissions rp ON u.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.permission_id
        WHERE p.permission_name = 'edit_locations' AND u.user_id = $2
      ))
    `;
    
    const checkResult = await db.query(checkQuery, [locationId, userId]);
    
    if (checkResult.rows.length === 0) {
      throw new Error('ليس لديك صلاحية لتعديل هذا الموقع');
    }
    
    const { 
      name, name_ar, category_id, latitude, longitude, 
      address, description, phone, website, opening_hours 
    } = locationData;
    
    let updateQuery = `
      UPDATE locations SET 
        name = COALESCE($1, name),
        name_ar = COALESCE($2, name_ar),
        category_id = COALESCE($3, category_id),
    `;
    
    const queryParams = [name, name_ar, category_id];
    
    // تحديث الإحداثيات فقط إذا تم توفيرها
    if (latitude && longitude) {
      updateQuery += ` geom = ST_SetSRID(ST_MakePoint($5, $4), 4326),`;
      queryParams.push(latitude, longitude);
    }
    
    updateQuery += `
        address = COALESCE($6, address),
        description = COALESCE($7, description),
        phone = COALESCE($8, phone),
        website = COALESCE($9, website),
        opening_hours = COALESCE($10, opening_hours),
        updated_at = CURRENT_TIMESTAMP
      WHERE location_id = $11
      RETURNING *
    `;
    
    queryParams.push(address, description, phone, website, opening_hours, locationId);
    
    const result = await db.query(updateQuery, queryParams);
    
    return result.rows[0];
  } catch (error) {
    console.error('خطأ في تحديث الموقع:', error);
    throw error;
  }
}

// إضافة تقييم لموقع
async function addReview(locationId, userId, rating, comment) {
  try {
    // التحقق من عدم وجود تقييم سابق
    const checkQuery = `
      SELECT * FROM location_reviews 
      WHERE location_id = $1 AND user_id = $2
    `;
    
    const checkResult = await db.query(checkQuery, [locationId, userId]);
    
    if (checkResult.rows.length > 0) {
      // تحديث التقييم الموجود
      const updateQuery = `
        UPDATE location_reviews 
        SET rating = $3, comment = $4, review_date = CURRENT_TIMESTAMP
        WHERE location_id = $1 AND user_id = $2
        RETURNING *
      `;
      
      const updateResult = await db.query(updateQuery, [locationId, userId, rating, comment]);
      
      // تحديث متوسط التقييم
      await updateLocationRating(locationId);
      
      return updateResult.rows[0];
    } else {
      // إضافة تقييم جديد
      const insertQuery = `
        INSERT INTO location_reviews (location_id, user_id, rating, comment)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `;
      
      const insertResult = await db.query(insertQuery, [locationId, userId, rating, comment]);
      
      // تحديث متوسط التقييم
      await updateLocationRating(locationId);
      
      return insertResult.rows[0];
    }
  } catch (error) {
    console.error('خطأ في إضافة تقييم:', error);
    throw error;
  }
}

// تحديث متوسط تقييم الموقع
async function updateLocationRating(locationId) {
  try {
    const query = `
      UPDATE locations l
      SET rating = (
        SELECT COALESCE(AVG(rating), 0)
        FROM location_reviews
        WHERE location_id = $1 AND is_approved = true
      ),
      rating_count = (
        SELECT COUNT(*)
        FROM location_reviews
        WHERE location_id = $1 AND is_approved = true
      )
      WHERE l.location_id = $1
    `;
    
    await db.query(query, [locationId]);
  } catch (error) {
    console.error('خطأ في تحديث متوسط التقييم:', error);
    throw error;
  }
}

// تصدير الوظائف
module.exports = {
  getCategories,
  getLocations,
  searchLocations,
  getNearbyLocations,
  getLocationDetails,
  addLocation,
  updateLocation,
  addReview
};

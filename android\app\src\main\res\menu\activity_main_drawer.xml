<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_map"
            android:icon="@android:drawable/ic_dialog_map"
            android:title="Map" />
        <item
            android:id="@+id/nav_saved_locations"
            android:icon="@android:drawable/ic_menu_save"
            android:title="Saved Locations" />
        <item
            android:id="@+id/nav_my_contributions"
            android:icon="@android:drawable/ic_menu_edit"
            android:title="My Contributions" />
        <item
            android:id="@+id/nav_offline_maps"
            android:icon="@android:drawable/ic_menu_save"
            android:title="Offline Maps" />
    </group>

    <item android:title="Settings">
        <menu>
            <item
                android:id="@+id/nav_settings"
                android:icon="@android:drawable/ic_menu_preferences"
                android:title="@string/settings" />
            <item
                android:id="@+id/nav_help"
                android:icon="@android:drawable/ic_menu_help"
                android:title="Help &amp; Feedback" />
            <item
                android:id="@+id/nav_about"
                android:icon="@android:drawable/ic_menu_info_details"
                android:title="@string/about" />
        </menu>
    </item>

    <item android:title="Account">
        <menu>
            <item
                android:id="@+id/nav_profile"
                android:icon="@android:drawable/ic_menu_myplaces"
                android:title="Profile" />
            <item
                android:id="@+id/nav_logout"
                android:icon="@android:drawable/ic_menu_close_clear_cancel"
                android:title="@string/logout" />
        </menu>
    </item>

</menu>

// فحص الإحداثيات في قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function checkCoordinates() {
    try {
        console.log('🔍 فحص الإحداثيات في قاعدة البيانات...\n');
        
        // فحص الأماكن بدون إحداثيات
        const missingCoords = await pool.query(`
            SELECT id, name_ar, name_en, latitude, longitude 
            FROM places 
            WHERE latitude IS NULL OR longitude IS NULL OR latitude = 0 OR longitude = 0
            ORDER BY id
        `);
        
        console.log(`❌ أماكن بدون إحداثيات: ${missingCoords.rows.length}`);
        if (missingCoords.rows.length > 0) {
            console.log('الأماكن المفقودة الإحداثيات:');
            missingCoords.rows.forEach(place => {
                console.log(`   ${place.id}. ${place.name_ar} - lat: ${place.latitude}, lng: ${place.longitude}`);
            });
        }
        
        console.log('');
        
        // فحص الأماكن مع الإحداثيات
        const withCoords = await pool.query(`
            SELECT id, name_ar, name_en, latitude, longitude 
            FROM places 
            WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND latitude != 0 AND longitude != 0
            ORDER BY id
            LIMIT 10
        `);
        
        console.log(`✅ أماكن مع إحداثيات: ${withCoords.rows.length}`);
        if (withCoords.rows.length > 0) {
            console.log('عينة من الأماكن مع الإحداثيات:');
            withCoords.rows.forEach(place => {
                console.log(`   ${place.id}. ${place.name_ar} - lat: ${place.latitude}, lng: ${place.longitude}`);
            });
        }
        
        console.log('');
        
        // إحصائيات عامة
        const stats = await pool.query(`
            SELECT 
                COUNT(*) as total_places,
                COUNT(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL AND latitude != 0 AND longitude != 0 THEN 1 END) as with_coords,
                COUNT(CASE WHEN latitude IS NULL OR longitude IS NULL OR latitude = 0 OR longitude = 0 THEN 1 END) as without_coords
            FROM places
        `);
        
        const stat = stats.rows[0];
        console.log('📊 إحصائيات الإحداثيات:');
        console.log(`   إجمالي الأماكن: ${stat.total_places}`);
        console.log(`   مع إحداثيات: ${stat.with_coords} (${Math.round((stat.with_coords / stat.total_places) * 100)}%)`);
        console.log(`   بدون إحداثيات: ${stat.without_coords} (${Math.round((stat.without_coords / stat.total_places) * 100)}%)`);
        
        // فحص الأماكن المستوردة من Google Places API
        const googlePlaces = await pool.query(`
            SELECT id, name_ar, name_en, latitude, longitude, description_ar
            FROM places 
            WHERE description_ar LIKE '%Google Places API%'
            ORDER BY id
        `);
        
        console.log(`\n🌐 أماكن من Google Places API: ${googlePlaces.rows.length}`);
        if (googlePlaces.rows.length > 0) {
            console.log('الأماكن المستوردة من Google:');
            googlePlaces.rows.forEach(place => {
                const hasCoords = place.latitude && place.longitude && place.latitude != 0 && place.longitude != 0;
                const status = hasCoords ? '✅' : '❌';
                console.log(`   ${status} ${place.id}. ${place.name_ar} - lat: ${place.latitude}, lng: ${place.longitude}`);
            });
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص الإحداثيات:', error);
    } finally {
        await pool.end();
    }
}

checkCoordinates();

<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Main theme colors -->
    <color name="colorPrimary">#2196F3</color>
    <color name="colorPrimaryDark">#1976D2</color>
    <color name="colorAccent">#FF9800</color>
    <color name="colorBackground">#FFFFFF</color>
    
    <!-- Text colors -->
    <color name="textColorPrimary">#212121</color>
    <color name="textColorSecondary">#757575</color>
    <color name="textColorTertiary">#9E9E9E</color>
    <color name="textColorLight">#FFFFFF</color>
    
    <!-- Location type colors -->
    <color name="colorTraffic">#F44336</color>        <!-- Red for traffic congestion -->
    <color name="colorSpeedbump">#FFC107</color>      <!-- Amber for speed bumps -->
    <color name="colorPothole">#FF5722</color>        <!-- Deep Orange for potholes -->
    <color name="colorDirtRoad">#795548</color>       <!-- Brown for dirt roads -->
    <color name="colorCheckpoint">#3F51B5</color>     <!-- Indigo for military checkpoints -->
    
    <!-- Status colors -->
    <color name="colorSuccess">#4CAF50</color>        <!-- Green for success -->
    <color name="colorWarning">#FFC107</color>        <!-- Amber for warnings -->
    <color name="colorError">#F44336</color>          <!-- Red for errors -->
    <color name="colorInfo">#2196F3</color>           <!-- Blue for information -->
    
    <!-- Map colors -->
    <color name="mapRoute">#2196F3</color>            <!-- Blue for routes -->
    <color name="mapUserLocation">#4CAF50</color>     <!-- Green for user location -->
    <color name="mapSelectedLocation">#FF9800</color> <!-- Orange for selected location -->
    
    <!-- UI element colors -->
    <color name="dividerColor">#E0E0E0</color>
    <color name="rippleColor">#33000000</color>
    <color name="shadowColor">#33000000</color>
    <color name="overlayColor">#66000000</color>
    
    <!-- Gradient colors -->
    <color name="gradientStart">#2196F3</color>
    <color name="gradientCenter">#03A9F4</color>
    <color name="gradientEnd">#00BCD4</color>
    
    <!-- Transparent colors for maps -->
    <color name="colorPrimaryTransparent">#332196F3</color>
    <color name="colorAccentTransparent">#33FF9800</color>
</resources>

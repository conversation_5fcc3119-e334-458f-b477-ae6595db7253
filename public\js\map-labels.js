// وحدة تحسين تسميات المواقع في الخريطة
// تضيف تسميات مشابهة لخرائط Google

(function() {
    // تخزين مرجع للخريطة
    let map;
    
    // تخزين طبقة التسميات
    let labelsLayer;
    
    // تخزين قائمة المدن والمواقع الرئيسية
    const majorCities = [
        { name: "صنعاء", lat: 15.3694, lng: 44.1910, importance: 10, lang: "ar" },
        { name: "عدن", lat: 12.7797, lng: 45.0095, importance: 9, lang: "ar" },
        { name: "تعز", lat: 13.5789, lng: 44.0178, importance: 8, lang: "ar" },
        { name: "الحديدة", lat: 14.7979, lng: 42.9532, importance: 8, lang: "ar" },
        { name: "المكلا", lat: 14.5393, lng: 49.1312, importance: 7, lang: "ar" },
        { name: "ذمار", lat: 14.5543, lng: 44.3919, importance: 7, lang: "ar" },
        { name: "إب", lat: 13.9673, lng: 44.1784, importance: 7, lang: "ar" },
        { name: "عمران", lat: 15.6594, lng: 43.9440, importance: 6, lang: "ar" },
        { name: "سيئون", lat: 15.9427, lng: 48.7878, importance: 6, lang: "ar" },
        { name: "صعدة", lat: 16.9398, lng: 43.7583, importance: 6, lang: "ar" },
        { name: "البيضاء", lat: 13.9853, lng: 45.5732, importance: 6, lang: "ar" },
        { name: "حجة", lat: 15.7008, lng: 43.6005, importance: 6, lang: "ar" },
        { name: "زبيد", lat: 14.1951, lng: 43.3152, importance: 5, lang: "ar" },
        { name: "مأرب", lat: 15.4711, lng: 45.3224, importance: 7, lang: "ar" },
        { name: "الغيضة", lat: 16.2087, lng: 52.1823, importance: 5, lang: "ar" },
        { name: "لحج", lat: 13.0582, lng: 44.8813, importance: 5, lang: "ar" },
        { name: "رداع", lat: 14.4128, lng: 44.8854, importance: 5, lang: "ar" },
        { name: "المخا", lat: 13.3103, lng: 43.2460, importance: 5, lang: "ar" },
        { name: "بيت الفقيه", lat: 14.4511, lng: 43.1771, importance: 4, lang: "ar" },
        { name: "باجل", lat: 15.0654, lng: 43.2851, importance: 4, lang: "ar" },
        { name: "حيس", lat: 13.8760, lng: 43.5066, importance: 4, lang: "ar" },
        { name: "الضالع", lat: 13.6941, lng: 44.7291, importance: 5, lang: "ar" },
        { name: "شبوة", lat: 14.5292, lng: 47.0095, importance: 5, lang: "ar" },
        { name: "عتق", lat: 14.5367, lng: 46.8292, importance: 5, lang: "ar" },
        { name: "الجوف", lat: 16.6122, lng: 45.5061, importance: 5, lang: "ar" },
        { name: "الحزم", lat: 16.1639, lng: 44.7769, importance: 4, lang: "ar" },
        { name: "أبين", lat: 13.5800, lng: 46.0800, importance: 5, lang: "ar" },
        { name: "زنجبار", lat: 13.1288, lng: 45.3800, importance: 5, lang: "ar" },
        { name: "المهرة", lat: 16.6122, lng: 51.8301, importance: 5, lang: "ar" },
        { name: "سقطرى", lat: 12.5000, lng: 53.8333, importance: 6, lang: "ar" },
        { name: "حضرموت", lat: 15.9385, lng: 48.7885, importance: 7, lang: "ar" },
        { name: "ريمة", lat: 14.6548, lng: 43.3662, importance: 4, lang: "ar" }
    ];
    
    // تهيئة وحدة تسميات الخريطة
    function init(mapInstance) {
        map = mapInstance;
        
        // إنشاء طبقة التسميات
        labelsLayer = L.layerGroup().addTo(map);
        
        // إضافة مستمع لتغيير مستوى التكبير
        map.on('zoomend', updateLabels);
        map.on('moveend', updateLabels);
        
        // تحديث التسميات عند التهيئة
        updateLabels();
        
        // إضافة طبقة التسميات إلى عناصر التحكم في الطبقات
        if (window.baseMaps) {
            const overlayMaps = {
                "أسماء المدن": labelsLayer
            };
            
            // تحديث عناصر التحكم في الطبقات
            L.control.layers(window.baseMaps, overlayMaps, {
                position: 'topright',
                collapsed: true
            }).addTo(map);
        }
        
        // تحميل بيانات إضافية من خدمة Nominatim
        loadAdditionalPlaces();
    }
    
    // تحديث التسميات بناءً على مستوى التكبير الحالي
    function updateLabels() {
        // مسح التسميات الحالية
        labelsLayer.clearLayers();
        
        // الحصول على مستوى التكبير الحالي
        const zoom = map.getZoom();
        
        // الحصول على حدود الخريطة المرئية
        const bounds = map.getBounds();
        
        // إضافة تسميات المدن الرئيسية
        majorCities.forEach(city => {
            // التحقق مما إذا كانت المدينة ضمن الحدود المرئية
            if (bounds.contains([city.lat, city.lng])) {
                // تحديد ما إذا كان يجب عرض المدينة بناءً على مستوى التكبير وأهميتها
                if (shouldShowLabel(city, zoom)) {
                    addCityLabel(city);
                }
            }
        });
    }
    
    // تحديد ما إذا كان يجب عرض تسمية المدينة بناءً على مستوى التكبير وأهميتها
    function shouldShowLabel(city, zoom) {
        // المدن الكبيرة تظهر في مستويات تكبير أقل
        if (city.importance >= 9 && zoom >= 7) return true;
        if (city.importance >= 8 && zoom >= 8) return true;
        if (city.importance >= 7 && zoom >= 9) return true;
        if (city.importance >= 6 && zoom >= 10) return true;
        if (city.importance >= 5 && zoom >= 11) return true;
        if (city.importance >= 4 && zoom >= 12) return true;
        if (zoom >= 13) return true;
        
        return false;
    }
    
    // إضافة تسمية مدينة إلى الخريطة
    function addCityLabel(city) {
        // إنشاء عنصر تسمية مخصص
        const labelIcon = L.divIcon({
            className: 'map-label',
            html: `<div class="map-label-content" dir="rtl">${city.name}</div>`,
            iconSize: [100, 20],
            iconAnchor: [50, 10]
        });
        
        // إضافة علامة باستخدام أيقونة التسمية
        const marker = L.marker([city.lat, city.lng], {
            icon: labelIcon,
            interactive: false, // لا تستجيب للنقر
            zIndexOffset: -1000 // وضعها خلف العلامات الأخرى
        }).addTo(labelsLayer);
    }
    
    // تحميل بيانات إضافية من خدمة Nominatim
    function loadAdditionalPlaces() {
        // تحميل المدن والقرى الإضافية من خدمة Nominatim
        fetch('https://nominatim.openstreetmap.org/search?country=Yemen&format=json&limit=100&featureType=settlement')
            .then(response => response.json())
            .then(data => {
                // إضافة المدن والقرى إلى القائمة
                data.forEach(place => {
                    // تجنب التكرار
                    const exists = majorCities.some(city => 
                        Math.abs(city.lat - parseFloat(place.lat)) < 0.01 && 
                        Math.abs(city.lng - parseFloat(place.lon)) < 0.01
                    );
                    
                    if (!exists) {
                        majorCities.push({
                            name: place.name,
                            lat: parseFloat(place.lat),
                            lng: parseFloat(place.lon),
                            importance: calculateImportance(place),
                            lang: place.namedetails && place.namedetails['name:ar'] ? 'ar' : 'en'
                        });
                    }
                });
                
                // تحديث التسميات
                updateLabels();
            })
            .catch(error => {
                console.error('خطأ في تحميل البيانات الإضافية:', error);
            });
    }
    
    // حساب أهمية المكان بناءً على بيانات Nominatim
    function calculateImportance(place) {
        // استخدام قيمة الأهمية من Nominatim إذا كانت متوفرة
        if (place.importance) {
            return place.importance * 10;
        }
        
        // تقدير الأهمية بناءً على نوع المكان
        if (place.type === 'city') return 7;
        if (place.type === 'town') return 6;
        if (place.type === 'village') return 4;
        if (place.type === 'hamlet') return 3;
        
        return 3; // قيمة افتراضية
    }
    
    // تصدير الوظائف العامة
    window.MapLabels = {
        init: init,
        updateLabels: updateLabels
    };
})();

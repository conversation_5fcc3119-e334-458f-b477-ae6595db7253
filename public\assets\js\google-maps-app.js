// Yemen GPS Google Maps Application
let map;
let infoWindow;
let markers = [];
let placesData = [];
let currentLocation = null;

// Initialize Google Maps
function initMap() {
    console.log('🗺️ تهيئة خرائط Google...');
    
    // إنشاء الخريطة
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 7,
        center: { lat: 15.3547, lng: 44.2066 }, // صنعاء
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        language: 'ar',
        region: 'YE',
        gestureHandling: 'greedy',
        zoomControl: false,
        mapTypeControl: false,
        scaleControl: false,
        streetViewControl: false,
        rotateControl: false,
        fullscreenControl: false
    });

    // إنشاء نافذة المعلومات
    infoWindow = new google.maps.InfoWindow();

    // تحميل البيانات من قاعدة البيانات
    loadPlacesFromDatabase();

    // ربط الأحداث
    bindEvents();

    // إخفاء عناصر Google Maps غير المرغوب فيها
    hideGoogleMapsElements();

    console.log('✅ تم تهيئة الخريطة بنجاح');
}

// تحميل الأماكن من قاعدة البيانات
async function loadPlacesFromDatabase() {
    try {
        console.log('📡 تحميل الأماكن من قاعدة البيانات...');
        
        const response = await fetch('/api/places');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        placesData = data;
        
        console.log(`📍 تم تحميل ${placesData.length} مكان`);
        
        // إضافة العلامات للخريطة
        addMarkersToMap();
        
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        showToast('خطأ في تحميل البيانات من الخادم', 'error');
    }
}

// إضافة العلامات للخريطة
function addMarkersToMap() {
    // مسح العلامات السابقة
    clearMarkers();
    
    placesData.forEach(place => {
        if (place.latitude && place.longitude) {
            const marker = new google.maps.Marker({
                position: { lat: parseFloat(place.latitude), lng: parseFloat(place.longitude) },
                map: map,
                title: place.name_ar || place.name_en,
                icon: {
                    url: getMarkerIcon(place.category_id),
                    scaledSize: new google.maps.Size(32, 32),
                    origin: new google.maps.Point(0, 0),
                    anchor: new google.maps.Point(16, 32)
                }
            });

            // إضافة حدث النقر
            marker.addListener('click', () => {
                showPlaceInfo(place, marker);
            });

            markers.push(marker);
        }
    });
    
    console.log(`🎯 تم إضافة ${markers.length} علامة للخريطة`);
}

// الحصول على أيقونة العلامة حسب الفئة
function getMarkerIcon(categoryId) {
    const icons = {
        1: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',      // سياحة
        2: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',     // دينية
        3: 'https://maps.google.com/mapfiles/ms/icons/orange-dot.png',   // مطاعم
        4: 'https://maps.google.com/mapfiles/ms/icons/purple-dot.png',   // فنادق
        5: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',    // صحة
        6: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png',   // تعليم
        7: 'https://maps.google.com/mapfiles/ms/icons/pink-dot.png',     // خدمات
        8: 'https://maps.google.com/mapfiles/ms/icons/ltblue-dot.png',   // تسوق
        9: 'https://maps.google.com/mapfiles/ms/icons/brown-dot.png',    // نقل
        10: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'      // أخرى
    };
    
    return icons[categoryId] || icons[10];
}

// عرض معلومات المكان
function showPlaceInfo(place, marker) {
    const photos = place.photos ? JSON.parse(place.photos) : [];
    const photosHtml = photos.length > 0 ? 
        `<div class="place-photos">
            ${photos.slice(0, 3).map(photo => 
                `<img src="${photo}" alt="${place.name_ar}" class="place-photo" onclick="openPhotoModal('${photo}')">`
            ).join('')}
            ${photos.length > 3 ? `<div class="more-photos">+${photos.length - 3} صور أخرى</div>` : ''}
        </div>` : '';

    const content = `
        <div class="info-window-content">
            <h3 class="place-title">${place.name_ar || place.name_en}</h3>
            ${place.name_en && place.name_ar !== place.name_en ? `<p class="place-title-en">${place.name_en}</p>` : ''}
            ${photosHtml}
            <div class="place-details">
                ${place.description_ar ? `<p class="place-description">${place.description_ar}</p>` : ''}
                ${place.phone ? `<p class="place-phone"><i class="fas fa-phone"></i> ${place.phone}</p>` : ''}
                ${place.website ? `<p class="place-website"><i class="fas fa-globe"></i> <a href="${place.website}" target="_blank">الموقع الإلكتروني</a></p>` : ''}
                ${place.rating ? `<p class="place-rating"><i class="fas fa-star"></i> ${place.rating}/5</p>` : ''}
                <p class="place-coordinates"><i class="fas fa-map-marker-alt"></i> ${place.latitude}, ${place.longitude}</p>
            </div>
            <div class="place-actions">
                <button onclick="getDirections(${place.latitude}, ${place.longitude})" class="action-btn directions-btn">
                    <i class="fas fa-directions"></i> الاتجاهات
                </button>
                <button onclick="sharePlace('${place.name_ar}', ${place.latitude}, ${place.longitude})" class="action-btn share-btn">
                    <i class="fas fa-share"></i> مشاركة
                </button>
                <button onclick="savePlace(${place.id})" class="action-btn save-btn">
                    <i class="fas fa-bookmark"></i> حفظ
                </button>
            </div>
        </div>
    `;

    infoWindow.setContent(content);
    infoWindow.open(map, marker);
}

// البحث في الأماكن
function searchPlaces(query) {
    if (!query || query.length < 2) {
        hideSearchResults();
        return;
    }

    const results = placesData.filter(place => 
        (place.name_ar && place.name_ar.includes(query)) ||
        (place.name_en && place.name_en.toLowerCase().includes(query.toLowerCase())) ||
        (place.description_ar && place.description_ar.includes(query))
    ).slice(0, 10);

    displaySearchResults(results);
}

// عرض نتائج البحث
function displaySearchResults(results) {
    const resultsContainer = document.getElementById('searchResults');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
        resultsContainer.style.display = 'block';
        return;
    }

    resultsContainer.innerHTML = results.map(place => `
        <div class="search-result-item" onclick="selectPlace(${place.id})">
            <div class="result-title">${place.name_ar || place.name_en}</div>
            <div class="result-description">${place.description_ar || ''}</div>
            <div class="result-coordinates">${place.latitude}, ${place.longitude}</div>
        </div>
    `).join('');
    
    resultsContainer.style.display = 'block';
}

// اختيار مكان من نتائج البحث
function selectPlace(placeId) {
    const place = placesData.find(p => p.id === placeId);
    if (place) {
        const position = { lat: parseFloat(place.latitude), lng: parseFloat(place.longitude) };
        map.setCenter(position);
        map.setZoom(15);
        
        // العثور على العلامة المقابلة
        const marker = markers.find(m => 
            m.getPosition().lat() === position.lat && 
            m.getPosition().lng() === position.lng
        );
        
        if (marker) {
            showPlaceInfo(place, marker);
        }
        
        hideSearchResults();
        document.getElementById('searchInput').value = place.name_ar || place.name_en;
    }
}

// إخفاء نتائج البحث
function hideSearchResults() {
    document.getElementById('searchResults').style.display = 'none';
}

// مسح العلامات
function clearMarkers() {
    markers.forEach(marker => marker.setMap(null));
    markers = [];
}

// ربط الأحداث
function bindEvents() {
    // البحث
    document.getElementById('searchBtn').addEventListener('click', () => {
        const query = document.getElementById('searchInput').value.trim();
        searchPlaces(query);
    });

    document.getElementById('searchInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = e.target.value.trim();
            searchPlaces(query);
        }
    });

    document.getElementById('searchInput').addEventListener('input', (e) => {
        searchPlaces(e.target.value.trim());
    });

    // الموقع الحالي
    document.getElementById('locationBtn').addEventListener('click', getCurrentLocation);

    // التحكم في التكبير
    document.getElementById('zoomIn').addEventListener('click', () => {
        map.setZoom(map.getZoom() + 1);
    });

    document.getElementById('zoomOut').addEventListener('click', () => {
        map.setZoom(map.getZoom() - 1);
    });

    // أنواع الخرائط
    document.getElementById('streetView').addEventListener('click', () => {
        map.setMapTypeId(google.maps.MapTypeId.ROADMAP);
        updateActiveViewButton('streetView');
    });

    document.getElementById('satelliteView').addEventListener('click', () => {
        map.setMapTypeId(google.maps.MapTypeId.SATELLITE);
        updateActiveViewButton('satelliteView');
    });

    document.getElementById('terrainView').addEventListener('click', () => {
        map.setMapTypeId(google.maps.MapTypeId.TERRAIN);
        updateActiveViewButton('terrainView');
    });

    // إغلاق نوافذ المعلومات
    document.getElementById('closeInfo').addEventListener('click', () => {
        document.getElementById('infoWindow').classList.add('hidden');
    });

    // إخفاء نتائج البحث عند النقر خارجها
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.search-container')) {
            hideSearchResults();
        }
    });
}

// تحديث زر العرض النشط
function updateActiveViewButton(activeId) {
    document.querySelectorAll('.view-controls .control-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(activeId).classList.add('active');
}

// الحصول على الموقع الحالي
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                currentLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                map.setCenter(currentLocation);
                map.setZoom(15);
                
                // إضافة علامة للموقع الحالي
                new google.maps.Marker({
                    position: currentLocation,
                    map: map,
                    title: 'موقعك الحالي',
                    icon: {
                        url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
                        scaledSize: new google.maps.Size(32, 32)
                    }
                });
                
                showToast('تم تحديد موقعك الحالي', 'success');
            },
            (error) => {
                console.error('خطأ في تحديد الموقع:', error);
                showToast('لا يمكن تحديد موقعك الحالي', 'error');
            }
        );
    } else {
        showToast('المتصفح لا يدعم تحديد الموقع', 'error');
    }
}

// الحصول على الاتجاهات
function getDirections(lat, lng) {
    if (currentLocation) {
        const url = `https://www.google.com/maps/dir/${currentLocation.lat},${currentLocation.lng}/${lat},${lng}`;
        window.open(url, '_blank');
    } else {
        const url = `https://www.google.com/maps/dir//${lat},${lng}`;
        window.open(url, '_blank');
    }
}

// مشاركة مكان
function sharePlace(name, lat, lng) {
    const url = `${window.location.origin}/?lat=${lat}&lng=${lng}&place=${encodeURIComponent(name)}`;
    
    if (navigator.share) {
        navigator.share({
            title: name,
            text: `موقع ${name} على خرائط اليمن`,
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            showToast('تم نسخ الرابط', 'success');
        });
    }
}

// حفظ مكان
function savePlace(placeId) {
    // يمكن تطوير هذه الوظيفة لاحقاً
    showToast('تم حفظ المكان', 'success');
}

// عرض رسالة
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.getElementById('toastContainer').appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// إخفاء عناصر Google Maps غير المرغوب فيها
function hideGoogleMapsElements() {
    const style = document.createElement('style');
    style.textContent = `
        .gm-style-cc,
        .gmnoprint,
        .gm-style .gm-style-cc,
        .gm-style .gmnoprint,
        .gm-style a[href*="maps.google.com"],
        .gm-style [title*="Google"],
        .gm-style [title*="خرائط Google"] {
            display: none !important;
        }
    `;
    document.head.appendChild(style);
}

// فتح صورة في نافذة منبثقة
function openPhotoModal(photoUrl) {
    const modal = document.createElement('div');
    modal.className = 'photo-modal';
    modal.innerHTML = `
        <div class="photo-modal-content">
            <span class="photo-modal-close">&times;</span>
            <img src="${photoUrl}" alt="صورة المكان">
        </div>
    `;
    
    document.body.appendChild(modal);
    
    modal.querySelector('.photo-modal-close').addEventListener('click', () => {
        modal.remove();
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 تحميل تطبيق خرائط اليمن...');
});

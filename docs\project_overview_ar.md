# نظام "يمن ناف" (Yemen Nav) - نظرة عامة

## نبذة عن المشروع

نظام "يمن ناف" هو تطبيق ملاحة تفاعلي مصمم خصيصًا لليمن، يعمل دون الحاجة للإنترنت لتوجيه السائقين، مع تنبيهات صوتية عن الازدحامات، المطبات، الحفر، والطرق الخطرة. يدعم النظام إدارة المستخدمين والمواقع المُضافة من خلال لوحة تحكم إدارية متكاملة.

## المتطلبات الأساسية

### 1. الخرائط والبيانات الجغرافية

- **خرائط تفاعلية**: تعمل دون اتصال بالإنترنت (Offline Maps)
- **دقة تحديد المواقع**: استخدام بيانات GPS لتحديد المواقع بدقة
- **دعم اللغة العربية**: تسميات باللغة العربية (أسماء المدن، الشوارع، المعالم)
- **تصنيف الطرق**: تمييز أنواع الطرق (رئيسي، فرعي، ترابي، مُعَبَّد)

### 2. وظائف الملاحة الأساسية

- **توجيه ذكي**: إرشاد السائقين عبر أقصر المسارات مع تفادي النقاط الخطرة
- **تحديث تلقائي**: تحديث الخريطة تلقائيًا وفقًا لحركة السيارة عبر GPS
- **توافق مع أنظمة السيارات**: دعم الشاشات الذكية في السيارات (Android Auto/Android Automotive)

### 3. إضافة النقاط والتنبيهات

#### أنواع النقاط المدعومة
- ازدحام مروري
- حفرة
- مطب
- طريق ترابي
- نقطة عسكرية
- حوادث

#### إضافة النقاط من المستخدمين
- تحديد الموقع يدويًا أو تلقائيًا عبر GPS
- إرفاق وصف وصنف للنقطة (مثال: "حفرة كبيرة بجوار مدرسة الأمل")
- رفع طلب إضافة النقطة لإدارة التطبيق للموافقة عليها

#### التنبيهات الصوتية
- تنبيه صوتي مسبق عند الاقتراب من نقطة خطرة (مثال: "انتبه! أمامك حفرة على بعد 500 متر")
- تخصيص صوت التنبيه حسب نوع النقطة (صوت مختلف للازدحام عن المطبات)

### 4. لوحة التحكم الإدارية

#### وظائف المدير
- الموافقة/رفض النقاط المضافة من المستخدمين
- تصنيف النقاط وتعديل تفاصيلها
- حذف نقاط غير دقيقة أو مُبالغ فيها

#### إدارة المستخدمين
- تقسيم المستخدمين إلى: (مدير - مشرف - مستخدم عادي)
- منح صلاحيات محددة (مثال: حظر مستخدم يضيف بيانات خاطئة)
- تتبع أجهزة المستخدمين عبر رقم تسجيل فريد (Device ID)

### 5. الأمان والحماية

- تشفير بيانات المستخدمين وخرائط النظام
- منع الوصول غير المصرح به عبر تسجيل الدخول (بريد إلكتروني/رقم هاتف)
- حظر الأجهزة التي تنتهي شروط الاستخدام

### 6. التكامل مع الأجهزة والأنظمة

- دعم اتصال GPS الخارجي (أجهزة السيارة الذكية)
- توافق التطبيق مع إصدارات أندرويد القديمة (بدءًا من Android 8.0)
- تخصيص واجهة المستخدم لتناسب شاشات السيارات الكبيرة (وضع القيادة)

### 7. وظائف إضافية

- تقارير عن حالة الطرق تُرسل تلقائيًا للإدارة
- تحديثات دورية للخرائط عبر اتصال إنترنت مؤقت
- دليل استخدام صوتي للمكفوفين أو لتجنب تشتيت السائق

## الخصائص الفنية المقترحة

### التقنيات

- **خرائط**: استخدام مكتبة مثل MAPS.ME أو Offline Maps SDK
- **قاعدة بيانات**: SQLite لتخزين البيانات دون إنترنت + Firebase للمزامنة عند الاتصال
- **الصوت**: تكامل مع Text-to-Speech للتنبيهات بالعربية

### التصميم

- واجهة بسيطة مع أزرار كبيرة وواضحة للاستخدام أثناء القيادة
- تباين ألوان عالي لسهولة الرؤية في ظروف الإضاءة المختلفة
- وضع ليلي لتقليل إجهاد العين أثناء القيادة الليلية

### الاختبار

- اختبار ميداني في مناطق يمنية مختلفة لضبط دقة GPS وجودة البيانات
- اختبار أداء على أجهزة مختلفة للتأكد من التوافق مع الأجهزة القديمة
- اختبار استخدام لتحسين تجربة المستخدم أثناء القيادة

## ملاحظات إضافية

- التركيز على دقة بيانات النقاط العسكرية والمناطق الخطرة لتجنب المشاكل الأمنية
- توفير نسخة تجريبية مجانية مع إصدار مدفوع للوظائف المتقدمة (مثال: عدد غير محدود من التنبيهات)
- تطوير آلية لتحفيز المستخدمين على المساهمة في إضافة وتحديث البيانات

## سيناريو الاستخدام النموذجي

1. يحدد السائق وجهته عبر التطبيق (بدون إنترنت)
2. أثناء القيادة، يتلقى تنبيهًا صوتيًا: "انتبه! طريق ترابي بعد 1 كيلومتر"
3. يضيف مستخدم آخر نقطة ازدحام، وتصل إشعار للإدارة للموافقة
4. المدير يوافق على النقطة بعد التحقق، فتصبح مرئية لجميع المستخدمين

## المكونات التقنية المنفذة

1. **هيكل التطبيق الأساسي**
   - تطبيق أندرويد كامل بنمط MVVM
   - قاعدة بيانات Room للتخزين المحلي
   - واجهة برمجة تطبيقات RESTful للاتصال بالخادم
   - خدمة تحديد الموقع في الخلفية

2. **ميزة الخرائط دون اتصال**
   - `OfflineMapManager` لتنزيل وإدارة مناطق الخرائط
   - `OfflineLocationRepository` لمزامنة بيانات الموقع
   - `ConnectivityMonitor` لاكتشاف تغييرات الشبكة
   - واجهة مستخدم لإدارة الخرائط دون اتصال

3. **التوثيق**
   - توثيق واجهة برمجة التطبيقات
   - توثيق مخطط قاعدة البيانات
   - توثيق بنية النظام
   - دليل المستخدم
   - دليل المسؤول
   - دليل إعداد التطوير مع تكوين Red Hat OpenJDK

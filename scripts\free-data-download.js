// سكريبت تحميل البيانات من مصادر مفتوحة (بدون مفاتيح API)
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// قاعدة البيانات
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// مصادر البيانات المفتوحة
const DATA_SOURCES = {
    // OpenStreetMap Nominatim API (مجاني)
    NOMINATIM: 'https://nominatim.openstreetmap.org',

    // Overpass API لبيانات OpenStreetMap
    OVERPASS: 'https://overpass-api.de/api/interpreter',

    // WikiData API
    WIKIDATA: 'https://www.wikidata.org/w/api.php'
};

// أنواع الأماكن في OpenStreetMap
const OSM_AMENITIES = [
    'restaurant', 'cafe', 'hotel', 'hospital', 'school', 'university',
    'bank', 'atm', 'fuel', 'pharmacy', 'police', 'fire_station',
    'post_office', 'library', 'museum', 'theatre', 'cinema',
    'place_of_worship', 'marketplace', 'shopping_mall'
];

const OSM_TOURISM = [
    'attraction', 'museum', 'hotel', 'guest_house', 'hostel',
    'information', 'viewpoint', 'zoo', 'theme_park'
];

class FreeDataDownloader {
    constructor() {
        this.downloadedCount = 0;
        this.errorCount = 0;
        this.startTime = Date.now();
    }

    // البحث في Nominatim
    async searchNominatim(query, countryCode = 'ye') {
        try {
            console.log(`🔍 البحث في Nominatim: ${query}`);

            const response = await axios.get(`${DATA_SOURCES.NOMINATIM}/search`, {
                params: {
                    q: query,
                    format: 'json',
                    countrycodes: countryCode,
                    limit: 50,
                    extratags: 1,
                    namedetails: 1,
                    addressdetails: 1
                },
                headers: {
                    'User-Agent': 'YemenGPS/1.0 (<EMAIL>)'
                }
            });

            console.log(`   ✅ تم العثور على ${response.data.length} نتيجة`);
            return response.data;

        } catch (error) {
            console.error(`   ❌ خطأ في Nominatim: ${error.message}`);
            return [];
        }
    }

    // استعلام Overpass API
    async queryOverpass(query) {
        try {
            console.log(`🔍 استعلام Overpass API...`);

            const response = await axios.post(DATA_SOURCES.OVERPASS, query, {
                headers: {
                    'Content-Type': 'text/plain',
                    'User-Agent': 'YemenGPS/1.0'
                },
                timeout: 30000
            });

            if (response.data && response.data.elements) {
                console.log(`   ✅ تم العثور على ${response.data.elements.length} عنصر`);
                return response.data.elements;
            }
            return [];

        } catch (error) {
            console.error(`   ❌ خطأ في Overpass: ${error.message}`);
            return [];
        }
    }

    // إنشاء استعلام Overpass للأماكن في اليمن
    buildOverpassQuery(amenityType) {
        return `
            [out:json][timeout:25];
            (
                node["amenity"="${amenityType}"]["addr:country"="YE"];
                way["amenity"="${amenityType}"]["addr:country"="YE"];
                relation["amenity"="${amenityType}"]["addr:country"="YE"];
                node["amenity"="${amenityType}"][bbox:12.0,42.0,19.0,54.0];
                way["amenity"="${amenityType}"][bbox:12.0,42.0,19.0,54.0];
                relation["amenity"="${amenityType}"][bbox:12.0,42.0,19.0,54.0];
            );
            out center meta;
        `;
    }

    // تحويل بيانات OSM إلى تنسيق قاعدة البيانات
    convertOsmToPlace(osmElement) {
        const tags = osmElement.tags || {};

        // الحصول على الإحداثيات
        let lat, lon;
        if (osmElement.lat && osmElement.lon) {
            lat = osmElement.lat;
            lon = osmElement.lon;
        } else if (osmElement.center) {
            lat = osmElement.center.lat;
            lon = osmElement.center.lon;
        } else {
            return null; // لا توجد إحداثيات
        }

        return {
            name_ar: tags['name:ar'] || tags.name || 'غير محدد',
            name_en: tags['name:en'] || tags.name || 'Unknown',
            description_ar: tags['description:ar'] || tags.description || '',
            description_en: tags['description:en'] || tags.description || '',
            latitude: parseFloat(lat),
            longitude: parseFloat(lon),
            phone: tags.phone || tags['contact:phone'] || null,
            website: tags.website || tags['contact:website'] || null,
            email: tags.email || tags['contact:email'] || null,
            opening_hours: tags.opening_hours || null,
            amenity: tags.amenity || null,
            tourism: tags.tourism || null,
            osm_id: osmElement.id,
            osm_type: osmElement.type
        };
    }

    // حفظ مكان في قاعدة البيانات
    async savePlace(place, governorateId = 1) {
        try {
            const query = `
                INSERT INTO places (
                    name_ar, name_en, description_ar, description_en,
                    latitude, longitude, governorate_id, category_id,
                    phone, email, website, opening_hours,
                    osm_id, osm_type, created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW()
                )
                RETURNING id
            `;

            // تحديد الفئة
            const categoryId = this.determineCategoryFromOsm(place.amenity, place.tourism);

            const values = [
                place.name_ar,
                place.name_en,
                place.description_ar,
                place.description_en,
                place.latitude,
                place.longitude,
                governorateId,
                categoryId,
                place.phone,
                place.email,
                place.website,
                place.opening_hours,
                place.osm_id,
                place.osm_type
            ];

            await pool.query(query, values);
            this.downloadedCount++;
            console.log(`   💾 تم حفظ: ${place.name_ar}`);

        } catch (error) {
            this.errorCount++;
            console.error(`   ❌ خطأ في الحفظ: ${error.message}`);
        }
    }

    // تحديد الفئة من نوع OSM
    determineCategoryFromOsm(amenity, tourism) {
        const categoryMap = {
            // سياحة
            'attraction': 1, 'museum': 1, 'viewpoint': 1, 'zoo': 1,
            // دينية
            'place_of_worship': 2,
            // مطاعم
            'restaurant': 3, 'cafe': 3, 'fast_food': 3,
            // فنادق
            'hotel': 4, 'guest_house': 4, 'hostel': 4,
            // صحة
            'hospital': 5, 'clinic': 5, 'pharmacy': 5,
            // تعليم
            'school': 6, 'university': 6, 'college': 6, 'library': 6,
            // خدمات
            'bank': 7, 'atm': 7, 'post_office': 7, 'police': 7,
            // تسوق
            'marketplace': 8, 'shopping_mall': 8,
            // مواصلات
            'fuel': 9, 'bus_station': 9
        };

        return categoryMap[amenity] || categoryMap[tourism] || 10; // أخرى
    }

    // تحميل البيانات من جميع المصادر
    async downloadAllData() {
        console.log('🚀 بدء تحميل البيانات من المصادر المفتوحة...\n');

        try {
            // 1. البحث في المدن الرئيسية عبر Nominatim
            const cities = ['صنعاء', 'عدن', 'تعز', 'الحديدة', 'إب', 'ذمار'];

            for (const city of cities) {
                console.log(`\n🏙️ البحث في ${city}:`);

                // البحث عن المطاعم والفنادق والمستشفيات
                const queries = [
                    `restaurant in ${city}, Yemen`,
                    `hotel in ${city}, Yemen`,
                    `hospital in ${city}, Yemen`,
                    `mosque in ${city}, Yemen`,
                    `school in ${city}, Yemen`
                ];

                for (const query of queries) {
                    const results = await this.searchNominatim(query);

                    for (const result of results) {
                        if (result.lat && result.lon) {
                            const place = {
                                name_ar: result.display_name.split(',')[0],
                                name_en: result.display_name.split(',')[0],
                                description_ar: result.display_name,
                                description_en: result.display_name,
                                latitude: parseFloat(result.lat),
                                longitude: parseFloat(result.lon),
                                phone: null,
                                website: null,
                                email: null,
                                opening_hours: null,
                                amenity: result.class,
                                tourism: result.type,
                                osm_id: result.osm_id,
                                osm_type: result.osm_type
                            };

                            await this.savePlace(place);
                        }
                    }

                    // تأخير بين الاستعلامات
                    await this.delay(2000);
                }
            }

            // 2. استخدام Overpass API للحصول على بيانات مفصلة
            console.log('\n🔍 استخدام Overpass API...');

            for (const amenity of OSM_AMENITIES.slice(0, 5)) { // أول 5 أنواع فقط
                const query = this.buildOverpassQuery(amenity);
                const elements = await this.queryOverpass(query);

                for (const element of elements) {
                    const place = this.convertOsmToPlace(element);
                    if (place && place.latitude && place.longitude) {
                        await this.savePlace(place);
                    }
                }

                // تأخير بين الاستعلامات
                await this.delay(5000);
            }

            this.printSummary();

        } catch (error) {
            console.error('❌ خطأ عام:', error);
        } finally {
            await pool.end();
        }
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // طباعة الملخص
    printSummary() {
        const duration = Math.round((Date.now() - this.startTime) / 1000);

        console.log('\n' + '='.repeat(50));
        console.log('📊 ملخص تحميل البيانات المفتوحة');
        console.log('='.repeat(50));
        console.log(`⏱️  الوقت المستغرق: ${duration} ثانية`);
        console.log(`✅ تم تحميلها بنجاح: ${this.downloadedCount}`);
        console.log(`❌ فشل في التحميل: ${this.errorCount}`);
        console.log(`🌐 المصادر: Nominatim + Overpass API`);
        console.log(`💰 التكلفة: مجاني 100%`);
        console.log('='.repeat(50));
    }
}

// تشغيل السكريبت
if (require.main === module) {
    const downloader = new FreeDataDownloader();
    downloader.downloadAllData();
}

module.exports = FreeDataDownloader;

// Helper script to generate PWA icons from SVG source

/**
 * This script helps generate the required PNG icons for the PWA
 * from the base SVG icon that we've created.
 * 
 * To use this script:
 * 1. Make sure you have a modern browser that supports SVG and Canvas
 * 2. Include this script in your HTML
 * 3. Call generateIcons() from the console or trigger it from a button
 */

function generateIcons() {
  const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
  const svgUrl = '/images/icons/icon.svg';
  let completedCount = 0;
  
  console.log('Starting icon generation...');
  document.body.innerHTML += '<div id="icon-generator" style="position:fixed; top:0; left:0; right:0; padding:20px; background:rgba(0,0,0,0.8); color:white; z-index:9999;"><h3>Generating PWA Icons</h3><div id="progress"></div></div>';
  
  const progressDiv = document.getElementById('progress');
  
  // Create a temporary image element to load the SVG
  const img = new Image();
  
  img.onload = function() {
    // Once the image is loaded, generate icons for each size
    sizes.forEach(size => {
      progressDiv.innerHTML += `<p>Processing ${size}x${size} icon...</p>`;
      generateIconForSize(img, size)
        .then(blob => {
          // Create a download link
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = `icon-${size}x${size}.png`;
          link.innerHTML = `Download ${size}x${size} icon`;
          link.style.color = '#4CAF50';
          link.style.display = 'block';
          link.style.margin = '5px 0';
          progressDiv.appendChild(link);
          
          completedCount++;
          if (completedCount === sizes.length) {
            progressDiv.innerHTML += '<p style="font-weight:bold">All icons generated! Click the links above to download each icon, then place them in the public/images/icons/ directory.</p>';
            progressDiv.innerHTML += '<button onclick="document.getElementById(\'icon-generator\').remove()" style="padding:10px; background:#f44336; color:white; border:none; border-radius:4px; cursor:pointer; margin-top:10px;">Close</button>';
          }
        })
        .catch(error => {
          console.error(`Error generating ${size}x${size} icon:`, error);
          progressDiv.innerHTML += `<p style="color:red">Error generating ${size}x${size} icon: ${error.message}</p>`;
        });
    });
  };
  
  img.onerror = function() {
    console.error('Error loading SVG file');
    progressDiv.innerHTML = '<p style="color:red">Error loading SVG file. Make sure the icon.svg file exists in the public/images/icons/ directory.</p>';
  };
  
  // Load the SVG file
  img.src = svgUrl;
}

// Function to generate an icon of a specific size
function generateIconForSize(img, size) {
  return new Promise((resolve, reject) => {
    try {
      // Create a canvas element
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;
      
      // Get the context and draw the image
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0, size, size);
      
      // Convert the canvas to a blob
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/png');
    } catch (error) {
      reject(error);
    }
  });
}

// Add a button to the DOM to trigger icon generation (for user convenience)
function addGenerateIconsButton() {
  const button = document.createElement('button');
  button.textContent = 'Generate PWA Icons';
  button.style.position = 'fixed';
  button.style.bottom = '20px';
  button.style.right = '20px';
  button.style.padding = '10px 15px';
  button.style.backgroundColor = '#4CAF50';
  button.style.color = 'white';
  button.style.border = 'none';
  button.style.borderRadius = '4px';
  button.style.cursor = 'pointer';
  button.style.zIndex = '1000';
  button.onclick = generateIcons;
  
  document.body.appendChild(button);
}

// Add the button when the page loads
document.addEventListener('DOMContentLoaded', () => {
  // Check if we're in an admin or development environment
  if (window.location.pathname.includes('admin') || 
      window.location.pathname.includes('developer') ||
      window.location.hostname === 'localhost') {
    console.log('Icon Generator Tool available - use generateIcons() function or click the button.');
    addGenerateIconsButton();
  }
});

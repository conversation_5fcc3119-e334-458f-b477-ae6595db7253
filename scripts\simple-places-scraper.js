// سكريبت مبسط لسحب بيانات الأماكن من Google Places API
// Simple Google Places Data Scraper

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// مفتاح Google Places API
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';

// جميع المدن اليمنية المستهدفة
const YEMEN_CITIES = [
    { name: 'صنعاء', name_en: 'Sanaa', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', name_en: '<PERSON>', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', name_en: 'Taiz', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', name_en: '<PERSON>', lat: 14.7978, lng: 42.9545 },
    { name: '<PERSON><PERSON>', name_en: 'Ibb', lat: 13.9667, lng: 44.1833 },
    { name: 'ذ<PERSON><PERSON><PERSON>', name_en: '<PERSON><PERSON><PERSON>', lat: 14.5426, lng: 44.4054 },
    { name: 'المكلا', name_en: 'Al Mukalla', lat: 14.5425, lng: 49.1242 },
    { name: 'مأرب', name_en: 'Marib', lat: 15.4694, lng: 45.3222 }
];

// جميع أنواع الأماكن المطلوبة
const PLACE_TYPES = [
    { type: 'lodging', name_ar: 'فنادق', name_en: 'Hotels' },
    { type: 'restaurant', name_ar: 'مطاعم', name_en: 'Restaurants' },
    { type: 'tourist_attraction', name_ar: 'معالم سياحية', name_en: 'Tourist Attractions' },
    { type: 'hospital', name_ar: 'مستشفيات', name_en: 'Hospitals' },
    { type: 'university', name_ar: 'جامعات', name_en: 'Universities' },
    { type: 'school', name_ar: 'مدارس', name_en: 'Schools' },
    { type: 'shopping_mall', name_ar: 'مراكز تسوق', name_en: 'Shopping Malls' },
    { type: 'gas_station', name_ar: 'محطات وقود', name_en: 'Gas Stations' },
    { type: 'bank', name_ar: 'بنوك', name_en: 'Banks' },
    { type: 'mosque', name_ar: 'مساجد', name_en: 'Mosques' },
    { type: 'airport', name_ar: 'مطارات', name_en: 'Airports' },
    { type: 'museum', name_ar: 'متاحف', name_en: 'Museums' },
    { type: 'park', name_ar: 'حدائق', name_en: 'Parks' },
    { type: 'pharmacy', name_ar: 'صيدليات', name_en: 'Pharmacies' },
    { type: 'cafe', name_ar: 'مقاهي', name_en: 'Cafes' }
];

class SimplePlacesScraper {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://maps.googleapis.com/maps/api/place';
        this.results = [];
        this.totalRequests = 0;
        this.successfulRequests = 0;
    }

    // تأخير بين الطلبات
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // البحث عن الأماكن القريبة
    async searchNearbyPlaces(lat, lng, type, radius = 10000) {
        try {
            this.totalRequests++;

            const url = `${this.baseUrl}/nearbysearch/json`;
            const params = {
                location: `${lat},${lng}`,
                radius: radius,
                type: type,
                key: this.apiKey
            };

            console.log(`🔍 البحث عن ${type} في (${lat}, ${lng})`);

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                this.successfulRequests++;
                console.log(`✅ تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            } else {
                console.error(`❌ خطأ: ${response.data.status} - ${response.data.error_message || 'غير محدد'}`);
                return [];
            }
        } catch (error) {
            console.error('خطأ في طلب البحث:', error.message);
            return [];
        }
    }

    // الحصول على تفاصيل مكان
    async getPlaceDetails(placeId) {
        try {
            this.totalRequests++;

            const url = `${this.baseUrl}/details/json`;
            const params = {
                place_id: placeId,
                fields: 'name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,photos,opening_hours,geometry,types,reviews',
                key: this.apiKey
            };

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                this.successfulRequests++;
                return response.data.result;
            } else {
                console.error(`❌ خطأ في التفاصيل: ${response.data.status}`);
                return null;
            }
        } catch (error) {
            console.error('خطأ في طلب التفاصيل:', error.message);
            return null;
        }
    }

    // الحصول على رابط الصورة
    getPhotoUrl(photoReference, maxWidth = 800) {
        return `${this.baseUrl}/photo?maxwidth=${maxWidth}&photoreference=${photoReference}&key=${this.apiKey}`;
    }

    // معالجة بيانات المكان
    processPlaceData(place, details, cityName, categoryName) {
        return {
            // معلومات أساسية
            google_place_id: place.place_id,
            name_ar: this.translateToArabic(place.name),
            name_en: place.name,
            category_ar: categoryName,
            city_ar: cityName,

            // الموقع
            latitude: place.geometry.location.lat,
            longitude: place.geometry.location.lng,

            // معلومات التواصل
            address: details?.formatted_address || '',
            phone: details?.formatted_phone_number || '',
            website: details?.website || '',

            // التقييمات
            rating: details?.rating || 0,
            reviews_count: details?.user_ratings_total || 0,

            // الصور
            photos: details?.photos?.slice(0, 3)?.map(photo => ({
                reference: photo.photo_reference,
                url: this.getPhotoUrl(photo.photo_reference)
            })) || [],

            // أوقات العمل
            opening_hours: details?.opening_hours?.weekday_text || [],

            // التقييمات النصية
            reviews: details?.reviews?.slice(0, 3)?.map(review => ({
                author: review.author_name,
                rating: review.rating,
                text: review.text,
                time: new Date(review.time * 1000).toISOString()
            })) || [],

            // معلومات إضافية
            types: place.types || [],
            price_level: details?.price_level || null,
            scraped_at: new Date().toISOString()
        };
    }

    // ترجمة بسيطة للعربية
    translateToArabic(englishName) {
        const translations = {
            'Hotel': 'فندق',
            'Restaurant': 'مطعم',
            'Cafe': 'مقهى',
            'Hospital': 'مستشفى',
            'University': 'جامعة',
            'Mall': 'مول',
            'Bank': 'بنك',
            'Mosque': 'مسجد',
            'Museum': 'متحف',
            'Park': 'حديقة'
        };

        let arabicName = englishName;
        Object.keys(translations).forEach(en => {
            arabicName = arabicName.replace(new RegExp(en, 'gi'), translations[en]);
        });

        return arabicName;
    }

    // سحب جميع البيانات
    async scrapeAllPlaces() {
        console.log('🚀 بدء سحب بيانات الأماكن اليمنية...\n');

        if (!this.apiKey || this.apiKey === 'YOUR_API_KEY_HERE') {
            console.error('❌ يرجى تعيين مفتاح Google Places API في المتغير GOOGLE_API_KEY');
            console.log('📝 احصل على المفتاح من: https://console.cloud.google.com/');
            return;
        }

        const startTime = Date.now();

        for (const city of YEMEN_CITIES) {
            console.log(`\n🏙️ معالجة مدينة: ${city.name} (${city.name_en})`);

            for (const placeType of PLACE_TYPES) {
                console.log(`\n📍 البحث عن: ${placeType.name_ar} (${placeType.type})`);

                // البحث عن الأماكن
                const places = await this.searchNearbyPlaces(
                    city.lat,
                    city.lng,
                    placeType.type,
                    15000 // نطاق 15 كم
                );

                // معالجة جميع الأماكن (حتى 20 مكان لكل نوع)
                for (let i = 0; i < Math.min(places.length, 20); i++) {
                    const place = places[i];

                    try {
                        console.log(`   📋 معالجة: ${place.name}`);

                        // الحصول على التفاصيل
                        const details = await this.getPlaceDetails(place.place_id);

                        if (details) {
                            // معالجة البيانات
                            const processedPlace = this.processPlaceData(
                                place,
                                details,
                                city.name,
                                placeType.name_ar
                            );

                            this.results.push(processedPlace);
                            console.log(`   ✅ تم حفظ البيانات`);
                        }

                        // تأخير بين الطلبات
                        await this.delay(1000);

                    } catch (error) {
                        console.error(`   ❌ خطأ في معالجة ${place.name}:`, error.message);
                    }
                }

                // تأخير بين أنواع الأماكن
                await this.delay(2000);
            }

            // تأخير بين المدن
            await this.delay(3000);
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log(`\n🎉 تم الانتهاء من السحب!`);
        console.log(`📊 الإحصائيات:`);
        console.log(`   - عدد الأماكن المسحوبة: ${this.results.length}`);
        console.log(`   - إجمالي الطلبات: ${this.totalRequests}`);
        console.log(`   - الطلبات الناجحة: ${this.successfulRequests}`);
        console.log(`   - معدل النجاح: ${Math.round((this.successfulRequests / this.totalRequests) * 100)}%`);
        console.log(`   - الوقت المستغرق: ${duration} ثانية`);

        // حفظ النتائج
        await this.saveResults();

        return this.results;
    }

    // حفظ النتائج
    async saveResults() {
        try {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            const dataDir = path.join(__dirname, '../data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // حفظ البيانات الخام
            const rawDataPath = path.join(dataDir, 'yemen_places_scraped.json');
            fs.writeFileSync(rawDataPath, JSON.stringify(this.results, null, 2), 'utf8');

            // حفظ ملف SQL للاستيراد
            const sqlPath = path.join(__dirname, '../database/scraped_places_import.sql');
            const sqlContent = this.generateSQL();
            fs.writeFileSync(sqlPath, sqlContent, 'utf8');

            // حفظ تقرير مبسط
            const reportPath = path.join(dataDir, 'scraping_report.txt');
            const report = this.generateReport();
            fs.writeFileSync(reportPath, report, 'utf8');

            console.log(`\n💾 تم حفظ النتائج في:`);
            console.log(`   - البيانات الخام: ${rawDataPath}`);
            console.log(`   - ملف SQL: ${sqlPath}`);
            console.log(`   - التقرير: ${reportPath}`);

        } catch (error) {
            console.error('❌ خطأ في حفظ النتائج:', error.message);
        }
    }

    // توليد ملف SQL
    generateSQL() {
        let sql = '-- بيانات الأماكن اليمنية المسحوبة من Google Places API\n';
        sql += `-- تم السحب في: ${new Date().toISOString()}\n`;
        sql += `-- عدد الأماكن: ${this.results.length}\n\n`;

        this.results.forEach((place, index) => {
            sql += `-- ${place.name_ar} (${place.name_en})\n`;
            sql += `INSERT INTO places (name_ar, name_en, description_ar, latitude, longitude, address_ar, phone, website, rating, reviews_count, is_verified) VALUES (\n`;
            sql += `  '${place.name_ar.replace(/'/g, "''")}',\n`;
            sql += `  '${place.name_en.replace(/'/g, "''")}',\n`;
            sql += `  '${place.category_ar} في ${place.city_ar}',\n`;
            sql += `  ${place.latitude},\n`;
            sql += `  ${place.longitude},\n`;
            sql += `  '${place.address.replace(/'/g, "''")}',\n`;
            sql += `  '${place.phone}',\n`;
            sql += `  '${place.website}',\n`;
            sql += `  ${place.rating},\n`;
            sql += `  ${place.reviews_count},\n`;
            sql += `  TRUE\n`;
            sql += `);\n\n`;
        });

        return sql;
    }

    // توليد تقرير
    generateReport() {
        const cityStats = {};
        const categoryStats = {};

        this.results.forEach(place => {
            // إحصائيات المدن
            if (!cityStats[place.city_ar]) {
                cityStats[place.city_ar] = 0;
            }
            cityStats[place.city_ar]++;

            // إحصائيات الفئات
            if (!categoryStats[place.category_ar]) {
                categoryStats[place.category_ar] = 0;
            }
            categoryStats[place.category_ar]++;
        });

        let report = `تقرير سحب بيانات الأماكن اليمنية\n`;
        report += `=====================================\n\n`;
        report += `تاريخ السحب: ${new Date().toLocaleString('ar')}\n`;
        report += `إجمالي الأماكن: ${this.results.length}\n\n`;

        report += `إحصائيات المدن:\n`;
        report += `---------------\n`;
        Object.keys(cityStats).forEach(city => {
            report += `${city}: ${cityStats[city]} مكان\n`;
        });

        report += `\nإحصائيات الفئات:\n`;
        report += `----------------\n`;
        Object.keys(categoryStats).forEach(category => {
            report += `${category}: ${categoryStats[category]} مكان\n`;
        });

        report += `\nأفضل الأماكن تقييماً:\n`;
        report += `--------------------\n`;
        const topRated = this.results
            .filter(place => place.rating > 0)
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 10);

        topRated.forEach((place, index) => {
            report += `${index + 1}. ${place.name_ar} - ${place.rating} نجمة (${place.reviews_count} تقييم)\n`;
        });

        return report;
    }
}

// تشغيل السكريبت
async function main() {
    const scraper = new SimplePlacesScraper(GOOGLE_API_KEY);
    await scraper.scrapeAllPlaces();
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimplePlacesScraper;

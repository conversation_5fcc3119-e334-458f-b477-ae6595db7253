// يمن ناف - ملف لاختبار الاتصال بقاعدة البيانات
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('إعدادات الاتصال بقاعدة البيانات:');
console.log(`- المستخدم: ${dbConfig.user}`);
console.log(`- المضيف: ${dbConfig.host}`);
console.log(`- المنفذ: ${dbConfig.port}`);
console.log(`- قاعدة البيانات: ${dbConfig.database}`);

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// اختبار الاتصال بقاعدة البيانات
async function testConnection() {
    let client;
    try {
        console.log('جاري الاتصال بقاعدة البيانات...');
        client = await pool.connect();
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        
        // التحقق من وجود الجداول
        const tablesQuery = `
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        `;
        
        const tablesResult = await client.query(tablesQuery);
        console.log('الجداول الموجودة في قاعدة البيانات:');
        tablesResult.rows.forEach(row => {
            console.log(`- ${row.table_name}`);
        });
        
        // التحقق من وجود جدول المستخدمين
        const usersQuery = `
            SELECT COUNT(*) as count
            FROM users
        `;
        
        try {
            const usersResult = await client.query(usersQuery);
            console.log(`عدد المستخدمين: ${usersResult.rows[0].count}`);
            
            // التحقق من وجود المستخدم admin
            const adminQuery = `
                SELECT *
                FROM users
                WHERE username = 'admin'
            `;
            
            const adminResult = await client.query(adminQuery);
            if (adminResult.rows.length > 0) {
                console.log('تم العثور على المستخدم admin:');
                console.log(`- المعرف: ${adminResult.rows[0].id}`);
                console.log(`- اسم المستخدم: ${adminResult.rows[0].username}`);
                console.log(`- البريد الإلكتروني: ${adminResult.rows[0].email}`);
                console.log(`- الاسم الكامل: ${adminResult.rows[0].full_name}`);
                console.log(`- معرف الدور: ${adminResult.rows[0].role_id}`);
                console.log(`- نشط: ${adminResult.rows[0].is_active}`);
            } else {
                console.log('لم يتم العثور على المستخدم admin');
            }
        } catch (error) {
            console.error('خطأ في التحقق من جدول المستخدمين:', error.message);
        }
        
        // التحقق من وجود جدول الصلاحيات
        const permissionsQuery = `
            SELECT COUNT(*) as count
            FROM permissions
        `;
        
        try {
            const permissionsResult = await client.query(permissionsQuery);
            console.log(`عدد الصلاحيات: ${permissionsResult.rows[0].count}`);
        } catch (error) {
            console.error('خطأ في التحقق من جدول الصلاحيات:', error.message);
        }
        
        return true;
    } catch (error) {
        console.error('فشل الاتصال بقاعدة البيانات:', error.message);
        return false;
    } finally {
        if (client) {
            client.release();
        }
        
        // إغلاق الاتصال بقاعدة البيانات
        await pool.end();
    }
}

// تنفيذ اختبار الاتصال
testConnection()
    .then(success => {
        if (success) {
            console.log('تم اختبار الاتصال بقاعدة البيانات بنجاح');
        } else {
            console.error('فشل اختبار الاتصال بقاعدة البيانات');
        }
        process.exit(0);
    })
    .catch(error => {
        console.error('حدث خطأ غير متوقع:', error);
        process.exit(1);
    });

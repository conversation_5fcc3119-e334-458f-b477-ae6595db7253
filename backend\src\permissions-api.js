// يمن ناف - واجهة برمجية للتعامل مع الصلاحيات
const express = require('express');
const router = express.Router();
const permissionsManager = require('./postgres-permissions');
const { authenticateToken, isAdmin } = require('./auth-middleware');

// الحصول على جميع الصلاحيات (للمدير فقط)
router.get('/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const permissions = await permissionsManager.getAllPermissions();
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في الحصول على الصلاحيات:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء الحصول على الصلاحيات' });
    }
});

// الحصول على صلاحيات دور معين (للمدير فقط)
router.get('/roles/:roleId/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const roleId = parseInt(req.params.roleId);
        const permissions = await permissionsManager.getRolePermissions(roleId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في الحصول على صلاحيات الدور:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء الحصول على صلاحيات الدور' });
    }
});

// الحصول على صلاحيات المستخدم الحالي
router.get('/my-permissions', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const permissions = await permissionsManager.getUserPermissions(userId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في الحصول على صلاحيات المستخدم:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء الحصول على صلاحيات المستخدم' });
    }
});

// الحصول على صلاحيات مستخدم معين (للمدير فقط)
router.get('/users/:userId/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const permissions = await permissionsManager.getUserPermissions(userId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في الحصول على صلاحيات المستخدم:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء الحصول على صلاحيات المستخدم' });
    }
});

// إنشاء صلاحية جديدة (للمدير فقط)
router.post('/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const { name, code, description } = req.body;
        
        if (!name || !code) {
            return res.status(400).json({ message: 'يجب توفير اسم ورمز الصلاحية' });
        }
        
        const newPermission = await permissionsManager.createPermission({
            name,
            code,
            description
        });
        
        res.status(201).json(newPermission);
    } catch (error) {
        console.error('خطأ في إنشاء صلاحية جديدة:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء إنشاء صلاحية جديدة' });
    }
});

// تحديث صلاحية (للمدير فقط)
router.put('/permissions/:permissionId', authenticateToken, isAdmin, async (req, res) => {
    try {
        const permissionId = parseInt(req.params.permissionId);
        const { name, description } = req.body;
        
        if (!name) {
            return res.status(400).json({ message: 'يجب توفير اسم الصلاحية' });
        }
        
        const updatedPermission = await permissionsManager.updatePermission(permissionId, {
            name,
            description
        });
        
        res.json(updatedPermission);
    } catch (error) {
        console.error('خطأ في تحديث الصلاحية:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء تحديث الصلاحية' });
    }
});

// إضافة صلاحية لدور (للمدير فقط)
router.post('/roles/:roleId/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const roleId = parseInt(req.params.roleId);
        const { permissionId } = req.body;
        
        if (!permissionId) {
            return res.status(400).json({ message: 'يجب توفير معرف الصلاحية' });
        }
        
        await permissionsManager.addPermissionToRole(roleId, permissionId);
        
        const permissions = await permissionsManager.getRolePermissions(roleId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في إضافة صلاحية للدور:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء إضافة صلاحية للدور' });
    }
});

// إزالة صلاحية من دور (للمدير فقط)
router.delete('/roles/:roleId/permissions/:permissionId', authenticateToken, isAdmin, async (req, res) => {
    try {
        const roleId = parseInt(req.params.roleId);
        const permissionId = parseInt(req.params.permissionId);
        
        await permissionsManager.removePermissionFromRole(roleId, permissionId);
        
        const permissions = await permissionsManager.getRolePermissions(roleId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في إزالة صلاحية من الدور:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء إزالة صلاحية من الدور' });
    }
});

// إضافة صلاحية خاصة لمستخدم (للمدير فقط)
router.post('/users/:userId/permissions', authenticateToken, isAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const { permissionId, isGranted } = req.body;
        
        if (!permissionId) {
            return res.status(400).json({ message: 'يجب توفير معرف الصلاحية' });
        }
        
        await permissionsManager.addPermissionToUser(userId, permissionId, isGranted);
        
        const permissions = await permissionsManager.getUserPermissions(userId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في إضافة صلاحية للمستخدم:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء إضافة صلاحية للمستخدم' });
    }
});

// إزالة صلاحية خاصة من مستخدم (للمدير فقط)
router.delete('/users/:userId/permissions/:permissionId', authenticateToken, isAdmin, async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const permissionId = parseInt(req.params.permissionId);
        
        await permissionsManager.removePermissionFromUser(userId, permissionId);
        
        const permissions = await permissionsManager.getUserPermissions(userId);
        res.json(permissions);
    } catch (error) {
        console.error('خطأ في إزالة صلاحية من المستخدم:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء إزالة صلاحية من المستخدم' });
    }
});

module.exports = router;

// map-utils.js - أدوات مساعدة للخريطة

// متغيرات عالمية للاستخدام في جميع الملفات
window.userLocationMarker = null;

// أدوات التحميل
function showLoader() {
  const loader = document.getElementById('map-loader');
  if (loader) {
    loader.style.display = 'flex';
  } else {
    const newLoader = document.createElement('div');
    newLoader.id = 'map-loader';
    newLoader.style = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255,255,255,0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    `;
    newLoader.innerHTML = `
      <div style="text-align: center;">
        <div style="border: 5px solid #f3f3f3; border-top: 5px solid #4285F4; border-radius: 50%; width: 50px; height: 50px; margin: 0 auto; animation: spin 1s linear infinite;"></div>
        <p style="margin-top: 10px; font-weight: bold; color: #333;">جاري تحديد موقعك...</p>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;
    document.body.appendChild(newLoader);
  }
}

function hideLoader() {
  const loader = document.getElementById('map-loader');
  if (loader) {
    loader.style.display = 'none';
  }
}

// عرض رسائل التنبيه للمستخدم
function showAlert(message, type = 'info') {
  const alertBox = document.createElement('div');
  alertBox.style = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 10000;
    direction: rtl;
    max-width: 300px;
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;
  
  // تحديد نوع التنبيه
  if (type === 'error') {
    alertBox.style.background = '#f44336';
    alertBox.style.color = 'white';
  } else if (type === 'success') {
    alertBox.style.background = '#4CAF50';
    alertBox.style.color = 'white';
  } else {
    alertBox.style.background = '#2196F3';
    alertBox.style.color = 'white';
  }
  
  alertBox.textContent = message;
  document.body.appendChild(alertBox);
  
  // إظهار التنبيه بتأثير
  setTimeout(() => { alertBox.style.opacity = '1'; }, 10);
  
  // إخفاء التنبيه بعد 3 ثواني
  setTimeout(() => {
    alertBox.style.opacity = '0';
    setTimeout(() => { alertBox.remove(); }, 300);
  }, 3000);
}

// تصدير الدوال للاستخدام الخارجي
window.showLoader = showLoader;
window.hideLoader = hideLoader;
window.showAlert = showAlert;

// تحديد موقع المستخدم الحالي (نقل من offline-maps.js)
window.locateUser = function() {
  console.log('تحديد الموقع الحالي...');  // إضافة سجل للتشخيص
  if (!navigator.geolocation) {
    showAlert('ميزة تحديد الموقع غير مدعومة في متصفحك', 'error');
    return;
  }

  // إظهار مؤشر التحميل
  showLoader();

  navigator.geolocation.getCurrentPosition(
    position => {
      const userPos = [position.coords.latitude, position.coords.longitude];
      
      console.log('تم العثور على موقعك:', userPos);  // إضافة سجل للتشخيص
      
      // إزالة المحدد القديم إن وجد
      if (window.userLocationMarker) {
        try {
          window.map.removeLayer(window.userLocationMarker);
        } catch (e) {
          console.error('خطأ في إزالة العلامة السابقة:', e);
        }
      }

      // التحقق من وجود الخريطة
      if (!window.map) {
        console.error('الخريطة غير معرفة في النافذة!');
        return;
      }
      
      // استخدام أيقونة دائرية زرقاء بدلاً من الصورة
      const blueIcon = L.divIcon({
        className: 'user-location-marker',
        html: '<div class="marker-inner"></div>',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });
      
      // إنشاء علامة جديدة
      window.userLocationMarker = L.marker(userPos, {
        icon: blueIcon,
        zIndexOffset: 1000
      }).addTo(window.map);
      
      // إضافة نمط للأيقونة مباشرة في الصفحة
      const style = document.createElement('style');
      style.textContent = `
        .user-location-marker {
          background: transparent;
          border: none;
        }
        .marker-inner {
          width: 18px;
          height: 18px;
          background: #2196f3;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 0 10px rgba(33, 150, 243, 0.8);
        }
      `;
      document.head.appendChild(style);

      // ضبط مستوى التكبير الأمثل
      window.map.setView(userPos, 16);

      // إخفاء مؤشر التحميل
      hideLoader();

      // إضافة دائرة دقة الموقع
      L.circle(userPos, {
        color: '#007bff',
        fillColor: '#007bff30',
        radius: position.coords.accuracy
      }).addTo(window.map);
      
      showAlert('تم تحديد موقعك بنجاح', 'success');
    },
    error => {
      hideLoader();
      handleLocationError(error);
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    }
  );
}

// دالة معالجة أخطاء تحديد الموقع
function handleLocationError(error) {
  let errorMsg = 'حدث خطأ أثناء محاولة تحديد الموقع';
  switch(error.code) {
    case error.PERMISSION_DENIED:
      errorMsg = 'تم رفض الإذن بالوصول إلى الموقع';
      break;
    case error.POSITION_UNAVAILABLE:
      errorMsg = 'معلومات الموقع غير متوفرة';
      break;
    case error.TIMEOUT:
      errorMsg = 'انتهى الوقت المخصص لتحديد الموقع';
      break;
  }
  showAlert(errorMsg, 'error');
}

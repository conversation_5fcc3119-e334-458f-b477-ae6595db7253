// يمن ناف - ملف لإضافة مستخدم مدير مع صلاحيات كاملة
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: 'yemen',
    password: 'admin',
    host: 'localhost',
    port: 5432,
    database: 'yemen_gps'
};

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// قراءة ملف SQL
const sqlFilePath = path.join(__dirname, 'add-admin-user.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// تقسيم الملف إلى أوامر SQL منفصلة
const sqlCommands = sqlContent
  .replace(/--.*$/gm, '') // إزالة التعليقات
  .split(';')
  .filter(cmd => cmd.trim() !== '');

// تنفيذ الأوامر SQL
async function executeQueries() {
    const client = await pool.connect();
    
    try {
        await client.query('BEGIN');
        
        console.log(`تنفيذ ${sqlCommands.length} أمر SQL...`);
        
        for (let i = 0; i < sqlCommands.length; i++) {
            const cmd = sqlCommands[i].trim();
            if (cmd) {
                try {
                    await client.query(cmd);
                    console.log(`تم تنفيذ الأمر ${i + 1} بنجاح`);
                } catch (err) {
                    console.error(`خطأ في تنفيذ الأمر ${i + 1}:`, err.message);
                    // استمر في التنفيذ حتى لو فشل أمر واحد
                }
            }
        }
        
        // التحقق من وجود المستخدم admin
        const userResult = await client.query('SELECT * FROM users WHERE username = $1', ['admin']);
        if (userResult.rows.length > 0) {
            console.log('تم التحقق من وجود المستخدم admin بنجاح');
            console.log('معلومات المستخدم:', userResult.rows[0]);
        } else {
            console.error('لم يتم العثور على المستخدم admin بعد تنفيذ الأوامر');
        }
        
        // التحقق من صلاحيات المستخدم admin
        const permissionsResult = await client.query(`
            SELECT p.name, p.code
            FROM permissions p
            JOIN role_permissions rp ON p.id = rp.permission_id
            JOIN roles r ON rp.role_id = r.id
            JOIN users u ON u.role_id = r.id
            WHERE u.username = $1
        `, ['admin']);
        
        if (permissionsResult.rows.length > 0) {
            console.log('صلاحيات المستخدم admin:');
            permissionsResult.rows.forEach(row => {
                console.log(`- ${row.name} (${row.code})`);
            });
        } else {
            console.error('لم يتم العثور على صلاحيات للمستخدم admin');
        }
        
        await client.query('COMMIT');
        console.log('تم تنفيذ جميع الأوامر بنجاح');
    } catch (err) {
        await client.query('ROLLBACK');
        console.error('حدث خطأ أثناء تنفيذ الأوامر:', err);
    } finally {
        client.release();
        // إغلاق الاتصال بقاعدة البيانات
        await pool.end();
    }
}

// تنفيذ الأوامر
executeQueries().catch(err => {
    console.error('خطأ غير متوقع:', err);
    process.exit(1);
});

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.yemengps.app">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:name=".YemenGpsApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />

        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Settings Activity -->
        <activity
            android:name=".ui.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity" />

        <!-- Location Details Activity -->
        <activity
            android:name=".ui.LocationDetailsActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity" />

        <!-- Add Location Activity -->
        <activity
            android:name=".ui.AddLocationActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity" />

        <!-- Login Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false" />

        <!-- Register Activity -->
        <activity
            android:name=".ui.auth.RegisterActivity"
            android:exported="false" />
            
        <!-- Offline Maps Activity -->
        <activity
            android:name=".ui.OfflineMapsActivity"
            android:exported="false"
            android:parentActivityName=".ui.MainActivity" />

        <!-- Location Service -->
        <service
            android:name=".services.LocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

    </application>

</manifest>

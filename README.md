# Yemen GPS Navigation System

A comprehensive GPS navigation system for Yemen with features to guide drivers about road conditions, traffic congestion, and other important points.

## Features

### Mobile Application (Android)
- Interactive map with real-time location tracking
- Display of road conditions (potholes, speed bumps, dirt roads)
- Traffic congestion information
- Military checkpoint locations
- Voice alerts before reaching marked locations
- User contribution system for adding new locations (with admin approval)
- Offline map support
- User authentication and device-specific permissions

### Admin Panel
- Location approval system
- User management
- Analytics and reporting
- Location type management

## Technical Architecture

### Mobile Application
- **Platform**: Android (Java)
- **Minimum SDK**: API 21 (Android 5.0 Lollipop)
- **Target SDK**: API 33 (Android 13)
- **Maps**: Google Maps API
- **Database**: Room (SQLite) for local storage
- **Network**: Retrofit for API communication
- **Location Services**: Google Play Services Location

### Backend (API)
- RESTful API for data synchronization
- User authentication and authorization
- Location data management
- Admin operations

### Database Schema
- **Users**: User information and authentication
- **Locations**: GPS coordinates, type, description, approval status
- **User Contributions**: Track user-added locations

## Location Types
The system supports the following location types:
1. **Traffic Congestion**: Areas with frequent traffic jams
2. **Speed Bumps**: Road speed bumps and humps
3. **Potholes**: Road damage and potholes
4. **Dirt Roads**: Unpaved or dirt roads
5. **Military Checkpoints**: Security checkpoints

## Alert System
- Voice alerts in Arabic/English
- Configurable alert distance (default: 500m)
- Vibration alerts
- Visual notifications

## User Roles
1. **Regular Users**: Can view locations, receive alerts, and submit new locations
2. **Admin Users**: Can approve/reject locations, manage users, and access analytics

## Installation and Setup

### Prerequisites
- Android Studio 4.0+
- JDK 8+
- Google Maps API Key

### Configuration
1. Clone the repository
2. Open the project in Android Studio
3. Add your Google Maps API key in `strings.xml`
4. Configure the backend API URL in `ApiClient.java`
5. Build and run the application

## Development Roadmap

### Phase 1: Core Features
- [x] Basic map integration
- [x] Location tracking
- [x] User authentication
- [x] Location display

### Phase 2: Enhanced Features
- [ ] Offline maps
- [ ] Voice alerts
- [ ] User contributions
- [ ] Admin panel

### Phase 3: Advanced Features
- [ ] Route planning
- [ ] Traffic prediction
- [ ] Integration with other navigation systems
- [ ] iOS application

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments
- Google Maps API
- Android Open Source Community
- Contributors and testers

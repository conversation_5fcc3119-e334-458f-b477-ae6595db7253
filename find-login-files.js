// سكريبت للبحث عن جميع ملفات login.html
const fs = require('fs');
const path = require('path');

function findFiles(dir, fileName, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findFiles(filePath, fileName, results);
    } else if (file === fileName) {
      results.push(filePath);
    }
  }
  
  return results;
}

// البحث عن جميع ملفات login.html
const loginFiles = findFiles('.', 'login.html');
console.log('تم العثور على ملفات login.html في المسارات التالية:');
loginFiles.forEach(file => console.log(file));

/**
 * Yemen GPS - نظام خرائط اليمن
 * أنماط CSS لنافذة تفاصيل المكان بطريقة احترافية
 */

/* نافذة معلومات المكان المفصلة (Google Maps Style) */
.place-details-panel {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    max-width: 90%;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    z-index: 1500;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
    border-radius: 0 0 0 10px;
}

.place-details-panel.active {
    transform: translateX(0);
}

.place-details-header {
    background-color: #f8f8f8;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.place-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.place-header-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.place-header-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.place-header-btn:active {
    background-color: rgba(0, 0, 0, 0.2);
}

.place-search-container {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 4px;
    padding: 6px 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

#place-search-input {
    flex: 1;
    border: none;
    padding: 8px;
    outline: none;
    font-size: 14px;
}

#place-search-clear,
#place-search-btn {
    background: none;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

#place-search-clear:hover,
#place-search-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.place-details-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 20px;
}

/* صورة المكان الرئيسية ومعرض الصور */
.place-image-container {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

#place-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.place-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
}

.place-image-gallery {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 5px;
    scrollbar-width: thin;
}

.place-image-gallery::-webkit-scrollbar {
    height: 4px;
}

.place-image-gallery::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 2px;
}

.gallery-thumbnail {
    width: 60px;
    height: 45px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: border-color 0.2s;
}

.gallery-thumbnail:hover {
    border-color: white;
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* معلومات المكان الأساسية */
.place-basic-info {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

#place-name {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.place-category {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #555;
    font-size: 14px;
    margin-bottom: 8px;
}

.place-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.stars {
    color: #fbbc04;
    letter-spacing: 2px;
}

.stars i {
    margin-right: -2px;
}

.rating-value {
    font-weight: bold;
    color: #333;
}

.review-count {
    color: #777;
    font-size: 13px;
}

.place-address {
    color: #555;
    font-size: 14px;
    margin-top: 10px;
    line-height: 1.4;
}

/* أزرار الإجراءات السريعة */
.place-quick-actions {
    display: flex;
    justify-content: space-around;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
    color: #1a73e8;
}

.quick-action-btn:hover {
    background-color: rgba(26, 115, 232, 0.1);
}

.quick-action-btn:active {
    background-color: rgba(26, 115, 232, 0.2);
}

.quick-action-btn i {
    font-size: 18px;
}

.quick-action-btn span {
    font-size: 12px;
}

.quick-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* معلومات الاتصال وساعات العمل */
.place-info-section {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.place-info-section h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #333;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    color: #555;
}

.contact-item i {
    color: #1a73e8;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.contact-item span {
    font-size: 14px;
    line-height: 1.4;
}

/* ساعات العمل */
.hours-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #555;
}

.hours-item .day {
    font-weight: 500;
}

.hours-item.open-now .hours {
    color: #188038;
    font-weight: 500;
}

.hours-item.closed-now .hours {
    color: #ea4335;
}

/* مربع حوار المشاركة */
.share-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.share-dialog-content {
    background-color: white;
    border-radius: 12px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.share-dialog-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-dialog-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.close-share-dialog {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.close-share-dialog:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.share-dialog-body {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.share-dialog-body p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #555;
}

.share-url-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 12px;
    font-size: 14px;
}

.copy-url-btn {
    padding: 8px 16px;
    background-color: #1a73e8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.copy-url-btn:hover {
    background-color: #1967d2;
}

.share-dialog-footer {
    padding: 16px;
}

.share-options {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.share-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    text-decoration: none;
    color: #333;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
    width: 80px;
}

.share-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.share-option i {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin-bottom: 5px;
}

.share-option span {
    font-size: 12px;
}

.share-option:nth-child(1) i {
    color: #25D366;
}

.share-option:nth-child(2) i {
    color: #0088cc;
}

.share-option:nth-child(3) i {
    color: #1DA1F2;
}

.share-option:nth-child(4) i {
    color: #EA4335;
}

/* إشعارات */
.notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 2000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s, transform 0.3s;
}

.notification.success {
    background-color: #188038;
}

.notification.error {
    background-color: #ea4335;
}

.notification.warning {
    background-color: #fbbc04;
    color: #333;
}

.notification.info {
    background-color: #1a73e8;
}

.notification.fade-out {
    opacity: 0;
    transform: translate(-50%, 20px);
}

/* تعديلات للأجهزة المحمولة */
@media (max-width: 768px) {
    .place-details-panel {
        width: 100%;
        max-width: 100%;
        border-radius: 0;
    }
    
    .share-dialog-content {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .place-image-container {
        height: 180px;
    }
    
    #place-name {
        font-size: 18px;
    }
    
    .share-options {
        gap: 5px;
    }
    
    .share-option {
        width: 70px;
    }
}

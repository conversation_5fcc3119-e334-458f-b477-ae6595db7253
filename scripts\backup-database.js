// سكريبت عمل نسخة احتياطية من قاعدة البيانات
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// إعدادات قاعدة البيانات
const DB_CONFIG = {
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    port: 5432
};

// مجلد النسخ الاحتياطية
const BACKUP_DIR = path.join(__dirname, '..', 'backups');

// إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
if (!fs.existsSync(BACKUP_DIR)) {
    fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

function createBackup() {
    console.log('🔄 بدء عمل نسخة احتياطية من قاعدة البيانات...\n');

    // تاريخ ووقت النسخة الاحتياطية
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const backupFileName = `yemen_gps_backup_${timestamp}.sql`;
    const backupFilePath = path.join(BACKUP_DIR, backupFileName);

    // أمر pg_dump
    const command = `pg_dump -U ${DB_CONFIG.user} -h ${DB_CONFIG.host} -p ${DB_CONFIG.port} -d ${DB_CONFIG.database} > "${backupFilePath}"`;

    console.log(`📁 اسم الملف: ${backupFileName}`);
    console.log(`📂 المسار: ${backupFilePath}`);
    console.log(`⚙️ الأمر: ${command}\n`);

    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error('❌ خطأ في عمل النسخة الاحتياطية:', error);
            return;
        }

        if (stderr) {
            console.log('⚠️ تحذيرات:', stderr);
        }

        // التحقق من وجود الملف وحجمه
        if (fs.existsSync(backupFilePath)) {
            const stats = fs.statSync(backupFilePath);
            const fileSizeKB = Math.round(stats.size / 1024);
            
            console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح!');
            console.log(`📊 حجم الملف: ${fileSizeKB} KB`);
            console.log(`📅 التاريخ: ${new Date().toLocaleString('ar')}`);
            
            // إنشاء ملف معلومات النسخة الاحتياطية
            const infoFile = path.join(BACKUP_DIR, `${backupFileName}.info`);
            const backupInfo = {
                filename: backupFileName,
                created_at: new Date().toISOString(),
                database: DB_CONFIG.database,
                size_kb: fileSizeKB,
                command_used: command
            };
            
            fs.writeFileSync(infoFile, JSON.stringify(backupInfo, null, 2));
            console.log(`📝 ملف المعلومات: ${backupFileName}.info`);
            
        } else {
            console.error('❌ فشل في إنشاء ملف النسخة الاحتياطية');
        }
    });
}

// تشغيل السكريبت
if (require.main === module) {
    createBackup();
}

module.exports = createBackup;

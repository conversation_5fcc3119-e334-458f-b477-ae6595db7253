// سكريبت محسن لسحب جميع الأماكن اليمنية مع الصور
// Enhanced Yemen Places Scraper with Image Download

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// مفتاح Google Places API
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';

// جميع المدن اليمنية
const YEMEN_CITIES = [
    { name: 'صنعاء', name_en: 'Sanaa', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', name_en: '<PERSON>', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', name_en: 'Taiz', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', name_en: '<PERSON>', lat: 14.7978, lng: 42.9545 },
    { name: 'إ<PERSON>', name_en: 'Ibb', lat: 13.9667, lng: 44.1833 },
    { name: 'ذ<PERSON><PERSON><PERSON>', name_en: '<PERSON>hamar', lat: 14.5426, lng: 44.4054 },
    { name: 'المكلا', name_en: 'Al Mukalla', lat: 14.5425, lng: 49.1242 },
    { name: 'مأرب', name_en: 'Marib', lat: 15.4694, lng: 45.3222 }
];

// جميع أنواع الأماكن
const PLACE_TYPES = [
    { type: 'lodging', name_ar: 'فنادق', name_en: 'Hotels' },
    { type: 'restaurant', name_ar: 'مطاعم', name_en: 'Restaurants' },
    { type: 'tourist_attraction', name_ar: 'معالم سياحية', name_en: 'Tourist Attractions' },
    { type: 'hospital', name_ar: 'مستشفيات', name_en: 'Hospitals' },
    { type: 'university', name_ar: 'جامعات', name_en: 'Universities' },
    { type: 'school', name_ar: 'مدارس', name_en: 'Schools' },
    { type: 'shopping_mall', name_ar: 'مراكز تسوق', name_en: 'Shopping Malls' },
    { type: 'bank', name_ar: 'بنوك', name_en: 'Banks' },
    { type: 'mosque', name_ar: 'مساجد', name_en: 'Mosques' },
    { type: 'museum', name_ar: 'متاحف', name_en: 'Museums' },
    { type: 'park', name_ar: 'حدائق', name_en: 'Parks' },
    { type: 'pharmacy', name_ar: 'صيدليات', name_en: 'Pharmacies' },
    { type: 'cafe', name_ar: 'مقاهي', name_en: 'Cafes' }
];

class EnhancedPlacesScraper {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://maps.googleapis.com/maps/api/place';
        this.results = [];
        this.totalRequests = 0;
        this.successfulRequests = 0;
        this.downloadedImages = 0;
        this.delay = 1000; // تأخير بين الطلبات
    }

    // تأخير بين الطلبات
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // إنشاء مجلدات الصور
    createImageDirectories() {
        const baseDir = path.join(__dirname, '../public/images/places');

        if (!fs.existsSync(baseDir)) {
            fs.mkdirSync(baseDir, { recursive: true });
        }

        // إنشاء مجلدات فرعية لكل نوع
        PLACE_TYPES.forEach(type => {
            const typeDir = path.join(baseDir, type.name_en.toLowerCase().replace(/\s+/g, '_'));
            if (!fs.existsSync(typeDir)) {
                fs.mkdirSync(typeDir, { recursive: true });
            }
        });

        console.log('📁 تم إنشاء مجلدات الصور');
    }

    // البحث عن الأماكن القريبة
    async searchNearbyPlaces(lat, lng, type, radius = 15000) {
        try {
            this.totalRequests++;

            const url = `${this.baseUrl}/nearbysearch/json`;
            const params = {
                location: `${lat},${lng}`,
                radius: radius,
                type: type,
                key: this.apiKey
            };

            console.log(`🔍 البحث عن ${type} في (${lat}, ${lng})`);

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                this.successfulRequests++;
                console.log(`✅ تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            } else {
                console.error(`❌ خطأ: ${response.data.status} - ${response.data.error_message || 'غير محدد'}`);
                return [];
            }
        } catch (error) {
            console.error('خطأ في طلب البحث:', error.message);
            return [];
        }
    }

    // الحصول على تفاصيل مكان
    async getPlaceDetails(placeId) {
        try {
            this.totalRequests++;

            const url = `${this.baseUrl}/details/json`;
            const params = {
                place_id: placeId,
                fields: 'name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,photos,opening_hours,geometry,types,reviews,price_level',
                key: this.apiKey
            };

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                this.successfulRequests++;
                return response.data.result;
            } else {
                console.error(`❌ خطأ في التفاصيل: ${response.data.status}`);
                return null;
            }
        } catch (error) {
            console.error('خطأ في طلب التفاصيل:', error.message);
            return null;
        }
    }

    // الحصول على رابط الصورة
    getPhotoUrl(photoReference, maxWidth = 800) {
        return `${this.baseUrl}/photo?maxwidth=${maxWidth}&photoreference=${photoReference}&key=${this.apiKey}`;
    }

    // تحميل وحفظ الصورة محلياً
    async downloadImage(photoReference, placeName, placeType, imageIndex) {
        try {
            const photoUrl = this.getPhotoUrl(photoReference, 800);

            // تنظيف اسم المكان للاستخدام في اسم الملف
            const cleanName = placeName
                .replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '') // إزالة الرموز الخاصة
                .replace(/\s+/g, '_') // استبدال المسافات بـ _
                .substring(0, 50); // تحديد الطول

            const fileName = `${cleanName}_${imageIndex + 1}.jpg`;
            const typeFolder = placeType.toLowerCase().replace(/\s+/g, '_');
            const imagePath = path.join(__dirname, '../public/images/places', typeFolder, fileName);

            console.log(`     📸 تحميل صورة: ${fileName}`);

            const response = await axios.get(photoUrl, {
                responseType: 'stream',
                timeout: 10000 // مهلة زمنية 10 ثواني
            });

            const writer = fs.createWriteStream(imagePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    this.downloadedImages++;
                    console.log(`     ✅ تم حفظ: ${fileName}`);
                    resolve({
                        original_url: photoUrl,
                        local_path: `images/places/${typeFolder}/${fileName}`,
                        filename: fileName
                    });
                });
                writer.on('error', (error) => {
                    console.log(`     ❌ خطأ في حفظ الصورة: ${error.message}`);
                    reject(error);
                });
            });

        } catch (error) {
            console.log(`     ❌ خطأ في تحميل الصورة: ${error.message}`);
            return null;
        }
    }

    // ترجمة بسيطة للعربية
    translateToArabic(englishName) {
        const translations = {
            'Hotel': 'فندق',
            'Restaurant': 'مطعم',
            'Cafe': 'مقهى',
            'Coffee': 'قهوة',
            'Hospital': 'مستشفى',
            'University': 'جامعة',
            'School': 'مدرسة',
            'Mall': 'مول',
            'Bank': 'بنك',
            'Mosque': 'مسجد',
            'Museum': 'متحف',
            'Park': 'حديقة',
            'Pharmacy': 'صيدلية'
        };

        let arabicName = englishName;
        Object.keys(translations).forEach(en => {
            arabicName = arabicName.replace(new RegExp(en, 'gi'), translations[en]);
        });

        return arabicName;
    }

    // معالجة بيانات المكان
    async processPlaceData(place, details, cityName, categoryName, placeType) {
        const processedPlace = {
            // معلومات أساسية
            google_place_id: place.place_id,
            name_ar: this.translateToArabic(place.name),
            name_en: place.name,
            category_ar: categoryName,
            city_ar: cityName,

            // الموقع
            latitude: place.geometry.location.lat,
            longitude: place.geometry.location.lng,

            // معلومات التواصل
            address: details?.formatted_address || '',
            phone: details?.formatted_phone_number || '',
            website: details?.website || '',

            // التقييمات
            rating: details?.rating || 0,
            reviews_count: details?.user_ratings_total || 0,

            // الصور (سيتم ملؤها)
            photos: [],

            // أوقات العمل
            opening_hours: details?.opening_hours?.weekday_text || [],

            // التقييمات النصية
            reviews: details?.reviews?.slice(0, 3)?.map(review => ({
                author: review.author_name,
                rating: review.rating,
                text: review.text,
                time: new Date(review.time * 1000).toISOString()
            })) || [],

            // معلومات إضافية
            types: place.types || [],
            price_level: details?.price_level || null,
            scraped_at: new Date().toISOString()
        };

        // تحميل الصور
        if (details?.photos && details.photos.length > 0) {
            console.log(`     📸 تحميل ${Math.min(details.photos.length, 5)} صور...`);

            for (let i = 0; i < Math.min(details.photos.length, 5); i++) {
                const photo = details.photos[i];

                try {
                    const downloadedImage = await this.downloadImage(
                        photo.photo_reference,
                        place.name,
                        placeType.name_en,
                        i
                    );

                    if (downloadedImage) {
                        processedPlace.photos.push({
                            reference: photo.photo_reference,
                            url: this.getPhotoUrl(photo.photo_reference),
                            local_path: downloadedImage.local_path,
                            filename: downloadedImage.filename,
                            width: photo.width,
                            height: photo.height
                        });
                    }

                    // تأخير بين تحميل الصور
                    await new Promise(resolve => setTimeout(resolve, 500));

                } catch (error) {
                    console.log(`     ⚠️ تخطي الصورة ${i + 1}: ${error.message}`);
                }
            }
        }

        return processedPlace;
    }

    // سحب جميع البيانات
    async scrapeAllPlaces() {
        console.log('🚀 بدء سحب جميع الأماكن اليمنية مع الصور...\n');

        if (!this.apiKey || this.apiKey === 'YOUR_API_KEY_HERE') {
            console.error('❌ يرجى تعيين مفتاح Google Places API');
            return;
        }

        const startTime = Date.now();

        // إنشاء مجلدات الصور
        this.createImageDirectories();

        for (const city of YEMEN_CITIES) {
            console.log(`\n🏙️ معالجة مدينة: ${city.name} (${city.name_en})`);

            for (const placeType of PLACE_TYPES) {
                console.log(`\n📍 البحث عن: ${placeType.name_ar} (${placeType.type})`);

                // البحث عن الأماكن
                const places = await this.searchNearbyPlaces(
                    city.lat,
                    city.lng,
                    placeType.type,
                    20000 // نطاق 20 كم
                );

                // معالجة كل مكان
                for (let i = 0; i < Math.min(places.length, 15); i++) {
                    const place = places[i];

                    try {
                        console.log(`   📋 معالجة: ${place.name}`);

                        // الحصول على التفاصيل
                        const details = await this.getPlaceDetails(place.place_id);

                        if (details) {
                            // معالجة البيانات مع تحميل الصور
                            const processedPlace = await this.processPlaceData(
                                place,
                                details,
                                city.name,
                                placeType.name_ar,
                                placeType
                            );

                            this.results.push(processedPlace);
                            console.log(`   ✅ تم حفظ البيانات (${processedPlace.photos.length} صور)`);
                        }

                        // تأخير بين الطلبات
                        await new Promise(resolve => setTimeout(resolve, 1000));

                    } catch (error) {
                        console.error(`   ❌ خطأ في معالجة ${place.name}:`, error.message);
                    }
                }

                // تأخير بين أنواع الأماكن
                await this.delay(2000);
            }

            // تأخير بين المدن
            await this.delay(3000);
        }

        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log(`\n🎉 تم الانتهاء من السحب!`);
        console.log(`📊 الإحصائيات:`);
        console.log(`   - عدد الأماكن المسحوبة: ${this.results.length}`);
        console.log(`   - عدد الصور المحملة: ${this.downloadedImages}`);
        console.log(`   - إجمالي الطلبات: ${this.totalRequests}`);
        console.log(`   - الطلبات الناجحة: ${this.successfulRequests}`);
        console.log(`   - معدل النجاح: ${Math.round((this.successfulRequests / this.totalRequests) * 100)}%`);
        console.log(`   - الوقت المستغرق: ${Math.round(duration / 60)} دقيقة`);

        // حفظ النتائج
        await this.saveResults();

        return this.results;
    }

    // حفظ النتائج
    async saveResults() {
        try {
            // إنشاء مجلد البيانات
            const dataDir = path.join(__dirname, '../data');
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // حفظ البيانات الخام
            const rawDataPath = path.join(dataDir, 'all_yemen_places_with_images.json');
            fs.writeFileSync(rawDataPath, JSON.stringify(this.results, null, 2), 'utf8');

            // حفظ تقرير مفصل
            const reportPath = path.join(dataDir, 'complete_scraping_report.txt');
            const report = this.generateDetailedReport();
            fs.writeFileSync(reportPath, report, 'utf8');

            console.log(`\n💾 تم حفظ النتائج في:`);
            console.log(`   - البيانات الكاملة: ${rawDataPath}`);
            console.log(`   - التقرير المفصل: ${reportPath}`);
            console.log(`   - الصور: public/images/places/`);

        } catch (error) {
            console.error('❌ خطأ في حفظ النتائج:', error.message);
        }
    }

    // توليد تقرير مفصل
    generateDetailedReport() {
        const cityStats = {};
        const categoryStats = {};
        let totalImages = 0;

        this.results.forEach(place => {
            // إحصائيات المدن
            if (!cityStats[place.city_ar]) {
                cityStats[place.city_ar] = { count: 0, images: 0 };
            }
            cityStats[place.city_ar].count++;
            cityStats[place.city_ar].images += place.photos.length;

            // إحصائيات الفئات
            if (!categoryStats[place.category_ar]) {
                categoryStats[place.category_ar] = { count: 0, images: 0 };
            }
            categoryStats[place.category_ar].count++;
            categoryStats[place.category_ar].images += place.photos.length;

            totalImages += place.photos.length;
        });

        let report = `تقرير شامل لسحب الأماكن اليمنية مع الصور\n`;
        report += `=============================================\n\n`;
        report += `تاريخ السحب: ${new Date().toLocaleString('ar')}\n`;
        report += `إجمالي الأماكن: ${this.results.length}\n`;
        report += `إجمالي الصور: ${totalImages}\n`;
        report += `متوسط الصور لكل مكان: ${(totalImages / this.results.length).toFixed(1)}\n\n`;

        report += `إحصائيات المدن:\n`;
        report += `===============\n`;
        Object.keys(cityStats).forEach(city => {
            const stats = cityStats[city];
            report += `${city}: ${stats.count} مكان، ${stats.images} صورة\n`;
        });

        report += `\nإحصائيات الفئات:\n`;
        report += `================\n`;
        Object.keys(categoryStats).forEach(category => {
            const stats = categoryStats[category];
            report += `${category}: ${stats.count} مكان، ${stats.images} صورة\n`;
        });

        report += `\nأفضل الأماكن تقييماً (مع صور):\n`;
        report += `===============================\n`;
        const topRatedWithImages = this.results
            .filter(place => place.rating > 0 && place.photos.length > 0)
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 20);

        topRatedWithImages.forEach((place, index) => {
            report += `${index + 1}. ${place.name_ar} - ${place.rating} نجمة (${place.reviews_count} تقييم، ${place.photos.length} صور)\n`;
        });

        return report;
    }
}

// تشغيل السكريبت
async function main() {
    const scraper = new EnhancedPlacesScraper(GOOGLE_API_KEY);
    await scraper.scrapeAllPlaces();
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
    main().catch(console.error);
}

module.exports = EnhancedPlacesScraper;

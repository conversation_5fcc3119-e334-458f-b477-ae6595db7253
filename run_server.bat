@echo off
echo Starting Yemen Nav Server...
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: Node.js is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    goto :error
)

REM Check if required packages are installed
if not exist node_modules (
    echo Installing required packages...
    call npm init -y
    call npm install express cors body-parser dotenv pg
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install required packages.
        goto :error
    )
)

REM Check if .env file exists
if not exist .env (
    echo Warning: .env file not found. Creating default .env file...
    echo DB_HOST=localhost > .env
    echo DB_PORT=5432 >> .env
    echo DB_NAME=yemen_nav >> .env
    echo DB_USER=yemen >> .env
    echo DB_PASSWORD=admin >> .env
    echo PORT=3000 >> .env
    echo NODE_ENV=development >> .env
    echo JWT_SECRET=your_jwt_secret_key >> .env
    echo STORAGE_PATH=C:/yemen-nav/storage >> .env
    echo Created default .env file. Please edit it with your actual database credentials.
)

REM Check if server.js file exists
if not exist server.js (
    echo Error: server.js file not found.
    goto :error
)

REM Setup admin user and permissions
echo Setting up admin user and permissions...
node backend/src/add-permissions-columns.js
if %ERRORLEVEL% NEQ 0 (
    echo Warning: Failed to setup admin user and permissions.
    echo The server will still start, but admin access might not work properly.
)

echo Starting server...
echo.
echo Server is now running at http://localhost:3000
echo Press Ctrl+C to stop the server
echo.

node server.js

goto :end

:error
echo.
echo An error occurred while starting the server.
pause
exit /b 1

:end
pause

// public/js/routing/road-network.js
class RoadNetwork {
  constructor() {
    this.nodes = new Map();
    this.edges = new Map();
  }
  
  // إضافة عقدة (تقاطع أو نقطة على الطريق)
  addNode(id, lat, lng) {
    this.nodes.set(id, { id, lat, lng, x: lng, y: lat });
    this.edges.set(id, []);
    return this.nodes.get(id);
  }
  
  // إضافة حافة (طريق بين عقدتين)
  addEdge(fromId, toId, properties = {}) {
    const fromNode = this.nodes.get(fromId);
    const toNode = this.nodes.get(toId);
    
    if (!fromNode || !toNode) {
      throw new Error('Node not found');
    }
    
    const distance = this.calculateDistance(fromNode, toNode);
    const edge = {
      from: fromId,
      to: toId,
      distance,
      ...properties
    };
    
    this.edges.get(fromId).push(edge);
    
    // إذا كان الطريق ثنائي الاتجاه
    if (!properties.oneWay) {
      this.edges.get(toId).push({
        from: toId,
        to: fromId,
        distance,
        ...properties
      });
    }
  }
  
  // الحصول على العقد المجاورة
  getNeighbors(node) {
    const edges = this.edges.get(node.id) || [];
    return edges.map(edge => this.nodes.get(edge.to));
  }
  
  // حساب المسافة بين عقدتين
  getDistance(nodeA, nodeB) {
    const edges = this.edges.get(nodeA.id) || [];
    const edge = edges.find(e => e.to === nodeB.id);
    
    if (edge) {
      return edge.distance;
    }
    
    return this.calculateDistance(nodeA, nodeB);
  }
  
  // حساب المسافة الإقليدية بين عقدتين
  calculateDistance(nodeA, nodeB) {
    const R = 6371; // نصف قطر الأرض بالكيلومتر
    const dLat = (nodeB.lat - nodeA.lat) * Math.PI / 180;
    const dLon = (nodeB.lng - nodeA.lng) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(nodeA.lat * Math.PI / 180) * Math.cos(nodeB.lat * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
  
  // البحث عن أقرب عقدة إلى إحداثيات معينة
  findNearestNode(lat, lng) {
    let nearestNode = null;
    let minDistance = Infinity;
    
    for (const node of this.nodes.values()) {
      const distance = this.calculateDistance(
        { lat, lng },
        { lat: node.lat, lng: node.lng }
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        nearestNode = node;
      }
    }
    
    return nearestNode;
  }
  
  // تحميل بيانات الطرق من GeoJSON
  loadFromGeoJSON(geojson) {
    // إنشاء العقد
    for (const feature of geojson.features) {
      if (feature.geometry.type === 'Point') {
        const [lng, lat] = feature.geometry.coordinates;
        this.addNode(feature.properties.id, lat, lng);
      }
    }
    
    // إنشاء الحواف (الطرق)
    for (const feature of geojson.features) {
      if (feature.geometry.type === 'LineString') {
        const coordinates = feature.geometry.coordinates;
        
        for (let i = 0; i < coordinates.length - 1; i++) {
          const [lng1, lat1] = coordinates[i];
          const [lng2, lat2] = coordinates[i + 1];
          
          const fromNode = this.findNearestNode(lat1, lng1);
          const toNode = this.findNearestNode(lat2, lng2);
          
          if (fromNode && toNode) {
            this.addEdge(fromNode.id, toNode.id, {
              roadType: feature.properties.type,
              name: feature.properties.name,
              oneWay: feature.properties.oneway === 'yes'
            });
          }
        }
      }
    }
  }

  // تحميل بيانات الطرق من مصفوفة
  loadFromArray(roads) {
    // إنشاء العقد
    const nodeIds = new Map();
    let nodeIdCounter = 1;

    // أولاً، إنشاء جميع العقد الفريدة
    roads.forEach(road => {
      road.points.forEach(point => {
        const pointKey = `${point[0]},${point[1]}`;
        if (!nodeIds.has(pointKey)) {
          const nodeId = `n${nodeIdCounter++}`;
          nodeIds.set(pointKey, nodeId);
          this.addNode(nodeId, point[0], point[1]);
        }
      });
    });

    // ثانياً، إنشاء الحواف (الطرق)
    roads.forEach(road => {
      for (let i = 0; i < road.points.length - 1; i++) {
        const point1 = road.points[i];
        const point2 = road.points[i + 1];
        
        const pointKey1 = `${point1[0]},${point1[1]}`;
        const pointKey2 = `${point2[0]},${point2[1]}`;
        
        const nodeId1 = nodeIds.get(pointKey1);
        const nodeId2 = nodeIds.get(pointKey2);
        
        this.addEdge(nodeId1, nodeId2, {
          roadType: road.type || 'primary',
          name: road.name,
          oneWay: road.oneWay || false,
          speedLimit: road.speedLimit || 50 // كم/ساعة
        });
      }
    });
  }
}

// إضافة الوحدة إلى النافذة العالمية
window.RoadNetwork = RoadNetwork;

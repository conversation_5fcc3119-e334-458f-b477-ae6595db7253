# شرح ملف الدفعة (Batch File) لإنشاء قاعدة البيانات

## ما هو ملف create_db.bat؟

ملف `create_db.bat` هو ملف دفعة (Batch File) يستخدم لأتمتة عملية إنشاء قاعدة بيانات PostgreSQL لنظام "يمن ناف". يقوم هذا الملف بتنفيذ سلسلة من الأوامر تلقائيًا لإنشاء قاعدة البيانات وإعدادها بشكل صحيح.

## كيفية استخدام الملف

1. تأكد من تثبيت PostgreSQL على جهازك
2. افتح الملف `create_db.bat` في محرر نصوص وقم بتعديل المتغيرات التالية حسب إعدادات PostgreSQL الخاصة بك:
   - `PSQL_PATH`: مسار برنامج psql.exe
   - `PGPASSWORD`: كلمة مرور مستخدم postgres
3. احفظ الملف بعد التعديل
4. انقر نقرًا مزدوجًا على الملف `create_db.bat` لتشغيله
5. انتظر حتى تظهر رسالة "Yemen Nav database is ready to use"

## ما الذي يفعله هذا الملف؟

يقوم الملف بتنفيذ الخطوات التالية:

1. **إعداد المتغيرات البيئية**:
   ```batch
   set PSQL_PATH="C:\Program Files\PostgreSQL\14\bin\psql.exe"
   set PGHOST=localhost
   set PGPORT=5432
   set PGUSER=postgres
   set PGPASSWORD=your_secure_password
   ```
   هذه المتغيرات تحدد معلومات الاتصال بخادم PostgreSQL.

2. **إنشاء قاعدة البيانات**:
   ```batch
   %PSQL_PATH% -c "CREATE DATABASE yemen_nav;"
   ```
   هذا الأمر ينشئ قاعدة بيانات جديدة باسم `yemen_nav`.

3. **إنشاء امتدادات PostGIS**:
   ```batch
   %PSQL_PATH% -d yemen_nav -c "CREATE EXTENSION postgis; CREATE EXTENSION postgis_topology;"
   ```
   هذا الأمر يضيف امتدادات PostGIS إلى قاعدة البيانات، وهي ضرورية للتعامل مع البيانات الجغرافية.

4. **إنشاء مستخدم قاعدة البيانات**:
   ```batch
   %PSQL_PATH% -c "CREATE USER yemen_nav_user WITH ENCRYPTED PASSWORD 'your_secure_password';"
   ```
   هذا الأمر ينشئ مستخدمًا جديدًا باسم `yemen_nav_user` وكلمة مرور محددة.

5. **منح الصلاحيات**:
   ```batch
   %PSQL_PATH% -c "GRANT ALL PRIVILEGES ON DATABASE yemen_nav TO yemen_nav_user;"
   ```
   هذا الأمر يمنح المستخدم `yemen_nav_user` جميع الصلاحيات على قاعدة البيانات `yemen_nav`.

6. **منح صلاحيات إضافية**:
   ```batch
   %PSQL_PATH% -d yemen_nav -c "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yemen_nav_user; GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yemen_nav_user;"
   ```
   هذا الأمر يمنح المستخدم صلاحيات على جميع الجداول والتسلسلات في قاعدة البيانات.

## ميزات الملف

1. **التحقق من الأخطاء**: يتحقق الملف من نجاح كل خطوة قبل الانتقال إلى الخطوة التالية:
   ```batch
   if %ERRORLEVEL% NEQ 0 (
       echo Failed to create database.
       goto :error
   )
   ```
   إذا فشلت أي خطوة، سيتم عرض رسالة خطأ والخروج من البرنامج.

2. **الرسائل التوضيحية**: يعرض الملف رسائل توضح ما يحدث في كل خطوة:
   ```batch
   echo Creating database...
   ```
   هذا يساعد المستخدم على فهم ما يحدث أثناء تنفيذ الملف.

3. **الإيقاف المؤقت في النهاية**: يتوقف الملف في النهاية حتى يتمكن المستخدم من قراءة الرسائل:
   ```batch
   pause
   ```
   هذا يمنع إغلاق نافذة الأوامر فورًا بعد انتهاء التنفيذ.

## تخصيص الملف

يمكنك تخصيص الملف حسب احتياجاتك:

1. **تغيير مسار PostgreSQL**: إذا كان PostgreSQL مثبتًا في مسار مختلف، قم بتعديل المتغير `PSQL_PATH`.

2. **تغيير كلمة المرور**: قم بتغيير `your_secure_password` إلى كلمة مرور قوية وآمنة.

3. **تغيير اسم قاعدة البيانات أو المستخدم**: يمكنك تغيير `yemen_nav` و `yemen_nav_user` إلى أسماء أخرى إذا كنت ترغب في ذلك.

4. **إضافة أوامر SQL إضافية**: يمكنك إضافة أوامر SQL إضافية لإنشاء جداول أو إدخال بيانات أولية.

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء تنفيذ الملف، تحقق من الأمور التالية:

1. **تأكد من تثبيت PostgreSQL**: تأكد من تثبيت PostgreSQL بشكل صحيح وأن المسار في المتغير `PSQL_PATH` صحيح.

2. **تحقق من كلمة المرور**: تأكد من أن كلمة مرور مستخدم postgres صحيحة.

3. **تحقق من الصلاحيات**: تأكد من أن لديك صلاحيات كافية لإنشاء قواعد بيانات ومستخدمين.

4. **تحقق من تثبيت PostGIS**: تأكد من تثبيت امتداد PostGIS مع PostgreSQL.

5. **تشغيل الأوامر يدويًا**: إذا استمرت المشاكل، حاول تنفيذ الأوامر يدويًا واحدًا تلو الآخر لتحديد المشكلة بدقة.

## ملاحظة أمنية

هذا الملف يحتوي على كلمة مرور في نص واضح. في بيئة الإنتاج، يُفضل استخدام طرق أكثر أمانًا لتخزين كلمات المرور، مثل ملف pgpass أو متغيرات بيئية آمنة.

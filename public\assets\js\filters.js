/**
 * Yemen GPS - نظام خرائط اليمن
 * نظام تصفية وتصنيف متقدم للمواقع
 */

class LocationFilters {
    constructor(mapInstance) {
        this.map = mapInstance;
        this.filters = {
            categories: [],
            rating: 0,
            openNow: false,
            distance: 0  // 0 يعني عدم وجود حد للمسافة
        };
        this.allLocations = [];
        this.filteredLocations = [];
        
        this.initFilterPanel();
        this.bindEvents();
    }
    
    /**
     * تهيئة لوحة التصفية
     */
    initFilterPanel() {
        // إنشاء لوحة التصفية
        const filterPanel = document.createElement('div');
        filterPanel.id = 'filter-panel';
        filterPanel.className = 'filter-panel';
        
        filterPanel.innerHTML = `
            <div class="filter-header">
                <h3>تصفية المواقع</h3>
                <button id="close-filter-btn" class="filter-header-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <h4>التصنيفات</h4>
                    <div class="category-filters">
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="restaurant"> مطاعم
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="cafe"> مقاهي
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="hospital"> مستشفيات
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="hotel"> فنادق
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="mosque"> مساجد
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="shopping"> تسوق
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="park"> حدائق
                        </label>
                        <label class="category-checkbox">
                            <input type="checkbox" data-category="school"> مدارس
                        </label>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h4>التقييم</h4>
                    <div class="rating-filter">
                        <input type="range" id="rating-slider" min="0" max="5" step="0.5" value="0">
                        <div class="rating-display">
                            <span id="rating-value">الكل</span>
                            <div class="stars-display" id="stars-display"></div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h4>خيارات إضافية</h4>
                    <div class="additional-filters">
                        <label class="switch-checkbox">
                            <input type="checkbox" id="open-now-checkbox">
                            <span class="switch-slider"></span>
                            <span class="switch-label">مفتوح الآن</span>
                        </label>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h4>المسافة القصوى</h4>
                    <div class="distance-filter">
                        <input type="range" id="distance-slider" min="0" max="50" step="1" value="0">
                        <div class="distance-display">
                            <span id="distance-value">غير محدود</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="filter-actions">
                <button id="apply-filters-btn" class="primary-btn">تطبيق</button>
                <button id="reset-filters-btn" class="secondary-btn">إعادة ضبط</button>
            </div>
        `;
        
        document.body.appendChild(filterPanel);
        
        // إضافة زر لفتح لوحة التصفية
        const filterBtn = document.createElement('button');
        filterBtn.id = 'filter-btn';
        filterBtn.className = 'map-control-btn filter-btn';
        filterBtn.innerHTML = '<i class="fas fa-filter"></i>';
        
        const controlsContainer = document.querySelector('.map-controls') || document.body;
        controlsContainer.appendChild(filterBtn);
    }
    
    /**
     * ربط أحداث لوحة التصفية
     */
    bindEvents() {
        // زر فتح لوحة التصفية
        const filterBtn = document.getElementById('filter-btn');
        if (filterBtn) {
            filterBtn.addEventListener('click', () => this.toggleFilterPanel(true));
        }
        
        // زر إغلاق لوحة التصفية
        const closeFilterBtn = document.getElementById('close-filter-btn');
        if (closeFilterBtn) {
            closeFilterBtn.addEventListener('click', () => this.toggleFilterPanel(false));
        }
        
        // شريط تمرير التقييم
        const ratingSlider = document.getElementById('rating-slider');
        const ratingValue = document.getElementById('rating-value');
        const starsDisplay = document.getElementById('stars-display');
        
        if (ratingSlider && ratingValue && starsDisplay) {
            ratingSlider.addEventListener('input', () => {
                const value = parseFloat(ratingSlider.value);
                this.filters.rating = value;
                
                if (value === 0) {
                    ratingValue.textContent = 'الكل';
                    starsDisplay.innerHTML = '';
                } else {
                    ratingValue.textContent = value;
                    starsDisplay.innerHTML = this.generateStars(value);
                }
            });
        }
        
        // مربع اختيار "مفتوح الآن"
        const openNowCheckbox = document.getElementById('open-now-checkbox');
        if (openNowCheckbox) {
            openNowCheckbox.addEventListener('change', () => {
                this.filters.openNow = openNowCheckbox.checked;
            });
        }
        
        // شريط تمرير المسافة
        const distanceSlider = document.getElementById('distance-slider');
        const distanceValue = document.getElementById('distance-value');
        
        if (distanceSlider && distanceValue) {
            distanceSlider.addEventListener('input', () => {
                const value = parseInt(distanceSlider.value);
                this.filters.distance = value;
                
                if (value === 0) {
                    distanceValue.textContent = 'غير محدود';
                } else {
                    distanceValue.textContent = `${value} كم`;
                }
            });
        }
        
        // مربعات اختيار التصنيفات
        const categoryCheckboxes = document.querySelectorAll('input[data-category]');
        categoryCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const category = checkbox.getAttribute('data-category');
                
                if (checkbox.checked) {
                    if (!this.filters.categories.includes(category)) {
                        this.filters.categories.push(category);
                    }
                } else {
                    const index = this.filters.categories.indexOf(category);
                    if (index !== -1) {
                        this.filters.categories.splice(index, 1);
                    }
                }
            });
        });
        
        // زر تطبيق التصفية
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
                this.toggleFilterPanel(false);
            });
        }
        
        // زر إعادة ضبط التصفية
        const resetFiltersBtn = document.getElementById('reset-filters-btn');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', () => this.resetFilters());
        }
    }
    
    /**
     * تبديل عرض لوحة التصفية
     * @param {boolean} show - إظهار أو إخفاء اللوحة
     */
    toggleFilterPanel(show) {
        const filterPanel = document.getElementById('filter-panel');
        if (filterPanel) {
            if (show) {
                filterPanel.classList.add('active');
            } else {
                filterPanel.classList.remove('active');
            }
        }
    }
    
    /**
     * تعيين قائمة المواقع الكاملة
     * @param {Array} locations - قائمة المواقع
     */
    setLocations(locations) {
        this.allLocations = locations;
        this.filteredLocations = [...locations];
    }
    
    /**
     * تطبيق التصفية على المواقع
     * @returns {Array} قائمة المواقع المصفاة
     */
    applyFilters() {
        this.filteredLocations = this.allLocations.filter(location => {
            // تصفية حسب التصنيف
            if (this.filters.categories.length > 0) {
                const locationTypes = location.types || [];
                if (!this.filters.categories.some(category => locationTypes.includes(category))) {
                    return false;
                }
            }
            
            // تصفية حسب التقييم
            if (this.filters.rating > 0) {
                const rating = location.rating || 0;
                if (rating < this.filters.rating) {
                    return false;
                }
            }
            
            // تصفية حسب "مفتوح الآن"
            if (this.filters.openNow && location.opening_hours) {
                if (!location.opening_hours.open_now) {
                    return false;
                }
            }
            
            // تصفية حسب المسافة
            if (this.filters.distance > 0 && this.map.currentLocation) {
                const distance = this.calculateDistance(
                    this.map.currentLocation.lat,
                    this.map.currentLocation.lng,
                    location.geometry.location.lat(),
                    location.geometry.location.lng()
                );
                
                if (distance > this.filters.distance) {
                    return false;
                }
            }
            
            return true;
        });
        
        // تحديث عرض المواقع على الخريطة
        if (this.map && typeof this.map.displayFilteredLocations === 'function') {
            this.map.displayFilteredLocations(this.filteredLocations);
        }
        
        return this.filteredLocations;
    }
    
    /**
     * إعادة ضبط التصفية
     */
    resetFilters() {
        // إعادة ضبط القيم في النموذج
        const ratingSlider = document.getElementById('rating-slider');
        const ratingValue = document.getElementById('rating-value');
        const starsDisplay = document.getElementById('stars-display');
        const openNowCheckbox = document.getElementById('open-now-checkbox');
        const distanceSlider = document.getElementById('distance-slider');
        const distanceValue = document.getElementById('distance-value');
        const categoryCheckboxes = document.querySelectorAll('input[data-category]');
        
        if (ratingSlider) ratingSlider.value = 0;
        if (ratingValue) ratingValue.textContent = 'الكل';
        if (starsDisplay) starsDisplay.innerHTML = '';
        if (openNowCheckbox) openNowCheckbox.checked = false;
        if (distanceSlider) distanceSlider.value = 0;
        if (distanceValue) distanceValue.textContent = 'غير محدود';
        
        categoryCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // إعادة ضبط قيم التصفية
        this.filters = {
            categories: [],
            rating: 0,
            openNow: false,
            distance: 0
        };
        
        // إعادة عرض جميع المواقع
        this.filteredLocations = [...this.allLocations];
        
        if (this.map && typeof this.map.displayFilteredLocations === 'function') {
            this.map.displayFilteredLocations(this.filteredLocations);
        }
    }
    
    /**
     * توليد نجوم التقييم
     * @param {number} rating - قيمة التقييم
     * @returns {string} HTML للنجوم
     */
    generateStars(rating) {
        if (!rating) return '';
        
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        let starsHtml = '';
        
        // إضافة النجوم الممتلئة
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // إضافة نصف نجمة إذا لزم الأمر
        if (halfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // إضافة النجوم الفارغة
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }
        
        return starsHtml;
    }
    
    /**
     * حساب المسافة بين نقطتين (بالكيلومتر)
     * @param {number} lat1 - خط العرض للنقطة الأولى
     * @param {number} lon1 - خط الطول للنقطة الأولى
     * @param {number} lat2 - خط العرض للنقطة الثانية
     * @param {number} lon2 - خط الطول للنقطة الثانية
     * @returns {number} المسافة بالكيلومتر
     */
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // نصف قطر الأرض بالكيلومتر
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        
        const a = 
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    
    /**
     * تحويل الدرجات إلى راديان
     * @param {number} degrees - الزاوية بالدرجات
     * @returns {number} الزاوية بالراديان
     */
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
}

// تصدير الكلاس
window.LocationFilters = LocationFilters;

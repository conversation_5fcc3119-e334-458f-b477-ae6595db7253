package com.yemengps.app.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.model.LocationType;

import java.util.List;

/**
 * Data Access Object for the LocationPoint entity
 */
@Dao
public interface LocationDao {

    /**
     * Insert a new location
     * @param location The location to insert
     * @return The row ID of the inserted location
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(LocationPoint location);

    /**
     * Insert multiple locations
     * @param locations The locations to insert
     * @return The row IDs of the inserted locations
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAll(List<LocationPoint> locations);

    /**
     * Update a location
     * @param location The location to update
     */
    @Update
    void update(LocationPoint location);

    /**
     * Delete a location
     * @param location The location to delete
     */
    @Delete
    void delete(LocationPoint location);

    /**
     * Get a location by ID
     * @param id The ID of the location
     * @return The location
     */
    @Query("SELECT * FROM locations WHERE id = :id")
    LiveData<LocationPoint> getLocationById(String id);

    /**
     * Get all locations
     * @return All locations
     */
    @Query("SELECT * FROM locations WHERE active = 1 AND approved = 1")
    LiveData<List<LocationPoint>> getAllLocations();

    /**
     * Get all locations by type
     * @param type The type of locations to get
     * @return All locations of the specified type
     */
    @Query("SELECT * FROM locations WHERE type = :type AND active = 1 AND approved = 1")
    LiveData<List<LocationPoint>> getLocationsByType(LocationType type);

    /**
     * Get all locations created by a user
     * @param userId The ID of the user
     * @return All locations created by the user
     */
    @Query("SELECT * FROM locations WHERE userId = :userId AND active = 1")
    LiveData<List<LocationPoint>> getLocationsByUser(String userId);

    /**
     * Get all locations within a radius of a point
     * @param latitude The latitude of the center point
     * @param longitude The longitude of the center point
     * @param radiusKm The radius in kilometers
     * @return All locations within the radius
     */
    @Query("SELECT * FROM locations WHERE " +
            "(6371 * acos(cos(radians(:latitude)) * cos(radians(latitude)) * " +
            "cos(radians(longitude) - radians(:longitude)) + " +
            "sin(radians(:latitude)) * sin(radians(latitude)))) < :radiusKm " +
            "AND active = 1 AND approved = 1")
    LiveData<List<LocationPoint>> getLocationsWithinRadius(double latitude, double longitude, double radiusKm);

    /**
     * Get all locations pending approval
     * @return All locations pending approval
     */
    @Query("SELECT * FROM locations WHERE approved = 0 AND active = 1")
    LiveData<List<LocationPoint>> getPendingLocations();

    /**
     * Approve a location
     * @param id The ID of the location to approve
     */
    @Query("UPDATE locations SET approved = 1 WHERE id = :id")
    void approveLocation(String id);

    /**
     * Reject a location
     * @param id The ID of the location to reject
     */
    @Query("UPDATE locations SET active = 0 WHERE id = :id")
    void rejectLocation(String id);

    /**
     * Get the count of locations by type
     * @return The count of locations by type
     */
    @Query("SELECT type, COUNT(*) as count FROM locations WHERE active = 1 AND approved = 1 GROUP BY type")
    LiveData<List<LocationTypeCount>> getLocationCountByType();

    /**
     * Get the count of locations by user
     * @return The count of locations by user
     */
    @Query("SELECT userId, COUNT(*) as count FROM locations WHERE active = 1 GROUP BY userId")
    LiveData<List<UserLocationCount>> getLocationCountByUser();

    /**
     * Class to hold the count of locations by type
     */
    class LocationTypeCount {
        public LocationType type;
        public int count;
    }

    /**
     * Class to hold the count of locations by user
     */
    class UserLocationCount {
        public String userId;
        public int count;
    }
}

/**
 * وحدة الخرائط دون اتصال باستخدام Google Maps
 * تتيح هذه الوحدة تحميل وتخزين خرائط Google للاستخدام دون اتصال
 */

const GoogleOfflineMaps = (function() {
    // المتغيرات الخاصة
    let map = null;
    let offlineMapData = {};
    let currentOfflineRegions = [];
    let dbPromise = null;
    
    // تهيئة قاعدة بيانات IndexedDB
    function initDatabase() {
        dbPromise = idb.openDB('yemen-gps-maps', 1, {
            upgrade(db) {
                // مخزن بيانات الخرائط
                if (!db.objectStoreNames.contains('map-tiles')) {
                    const tileStore = db.createObjectStore('map-tiles', { keyPath: 'url' });
                    tileStore.createIndex('timestamp', 'timestamp');
                }
                
                // مخزن بيانات المناطق المحفوظة
                if (!db.objectStoreNames.contains('offline-regions')) {
                    db.createObjectStore('offline-regions', { keyPath: 'id' });
                }
                
                // مخزن بيانات النقاط المهمة
                if (!db.objectStoreNames.contains('pois')) {
                    const poiStore = db.createObjectStore('pois', { keyPath: 'id' });
                    poiStore.createIndex('region', 'region');
                    poiStore.createIndex('category', 'category');
                }
            }
        });
        return dbPromise;
    }
    
    // تهيئة الخريطة
    function initMap(mapInstance) {
        map = mapInstance;
        
        // تحميل المناطق المحفوظة
        loadSavedRegions();
        
        return {
            downloadRegion,
            deleteRegion,
            getSavedRegions,
            isRegionSaved
        };
    }
    
    // تحميل المناطق المحفوظة
    async function loadSavedRegions() {
        const db = await dbPromise;
        currentOfflineRegions = await db.getAll('offline-regions');
        return currentOfflineRegions;
    }
    
    // الحصول على المناطق المحفوظة
    async function getSavedRegions() {
        return await loadSavedRegions();
    }
    
    // التحقق مما إذا كانت المنطقة محفوظة
    async function isRegionSaved(regionId) {
        const db = await dbPromise;
        const region = await db.get('offline-regions', regionId);
        return !!region;
    }
    
    // تحميل منطقة للاستخدام دون اتصال
    async function downloadRegion(bounds, zoomLevels, name) {
        if (!map) {
            throw new Error('يجب تهيئة الخريطة أولاً');
        }
        
        const regionId = `${bounds.getSouth()}_${bounds.getWest()}_${bounds.getNorth()}_${bounds.getEast()}`;
        const region = {
            id: regionId,
            name: name || `منطقة ${new Date().toLocaleString('ar-YE')}`,
            bounds: {
                south: bounds.getSouth(),
                west: bounds.getWest(),
                north: bounds.getNorth(),
                east: bounds.getEast()
            },
            zoomLevels: zoomLevels || [8, 9, 10, 11, 12, 13],
            timestamp: Date.now(),
            tilesCount: 0,
            size: 0
        };
        
        // عرض مؤشر التحميل
        showLoadingIndicator(`جاري تحميل خرائط منطقة ${region.name}...`);
        
        try {
            // حساب عدد البلاطات المطلوبة
            let totalTiles = 0;
            for (const zoom of region.zoomLevels) {
                const tilesCount = calculateTilesCount(bounds, zoom);
                totalTiles += tilesCount;
            }
            
            // تحديث معلومات المنطقة
            region.tilesCount = totalTiles;
            
            // تحميل البلاطات
            let downloadedTiles = 0;
            const db = await dbPromise;
            
            // حفظ معلومات المنطقة
            await db.put('offline-regions', region);
            
            // تحميل البلاطات لكل مستوى تكبير
            for (const zoom of region.zoomLevels) {
                await downloadTilesForZoom(bounds, zoom, (progress) => {
                    const totalProgress = (downloadedTiles + progress) / totalTiles;
                    updateLoadingProgress(Math.round(totalProgress * 100));
                });
                downloadedTiles += calculateTilesCount(bounds, zoom);
            }
            
            // تحميل نقاط الاهتمام في المنطقة
            await downloadPOIs(bounds);
            
            // تحديث حجم المنطقة
            const size = await calculateRegionSize(regionId);
            region.size = size;
            await db.put('offline-regions', region);
            
            // تحديث قائمة المناطق المحفوظة
            currentOfflineRegions = await loadSavedRegions();
            
            // إخفاء مؤشر التحميل
            hideLoadingIndicator();
            
            // عرض إشعار نجاح
            showNotification(`تم تحميل منطقة ${region.name} بنجاح (${formatSize(size)})`, 'success');
            
            return region;
        } catch (error) {
            console.error('خطأ في تحميل المنطقة:', error);
            hideLoadingIndicator();
            showNotification('حدث خطأ أثناء تحميل المنطقة', 'error');
            
            // حذف المنطقة في حالة الفشل
            try {
                const db = await dbPromise;
                await db.delete('offline-regions', regionId);
            } catch (e) {
                console.error('خطأ في حذف المنطقة بعد الفشل:', e);
            }
            
            throw error;
        }
    }
    
    // حساب عدد البلاطات المطلوبة لمنطقة ومستوى تكبير محددين
    function calculateTilesCount(bounds, zoom) {
        const northEast = bounds.getNorthEast();
        const southWest = bounds.getSouthWest();
        
        const neTile = latLngToTile(northEast.lat, northEast.lng, zoom);
        const swTile = latLngToTile(southWest.lat, southWest.lng, zoom);
        
        const xCount = Math.abs(neTile.x - swTile.x) + 1;
        const yCount = Math.abs(neTile.y - swTile.y) + 1;
        
        return xCount * yCount;
    }
    
    // تحويل الإحداثيات إلى رقم البلاطة
    function latLngToTile(lat, lng, zoom) {
        const n = Math.pow(2, zoom);
        const x = Math.floor((lng + 180) / 360 * n);
        const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
        return { x, y };
    }
    
    // تحميل البلاطات لمستوى تكبير محدد
    async function downloadTilesForZoom(bounds, zoom, progressCallback) {
        const northEast = bounds.getNorthEast();
        const southWest = bounds.getSouthWest();
        
        const neTile = latLngToTile(northEast.lat, northEast.lng, zoom);
        const swTile = latLngToTile(southWest.lat, southWest.lng, zoom);
        
        const minX = Math.min(neTile.x, swTile.x);
        const maxX = Math.max(neTile.x, swTile.x);
        const minY = Math.min(neTile.y, swTile.y);
        const maxY = Math.max(neTile.y, swTile.y);
        
        const totalTiles = (maxX - minX + 1) * (maxY - minY + 1);
        let downloadedTiles = 0;
        
        const db = await dbPromise;
        const tx = db.transaction('map-tiles', 'readwrite');
        const tileStore = tx.objectStore('map-tiles');
        
        const promises = [];
        
        for (let x = minX; x <= maxX; x++) {
            for (let y = minY; y <= maxY; y++) {
                const url = getTileUrl(x, y, zoom);
                promises.push(
                    downloadAndStoreTile(url, tileStore)
                        .then(() => {
                            downloadedTiles++;
                            if (progressCallback) {
                                progressCallback(downloadedTiles / totalTiles);
                            }
                        })
                );
            }
        }
        
        await Promise.all(promises);
        await tx.done;
    }
    
    // الحصول على عنوان URL للبلاطة
    function getTileUrl(x, y, zoom) {
        // استخدام خرائط Google
        return `https://mt1.google.com/vt/lyrs=m&x=${x}&y=${y}&z=${zoom}`;
    }
    
    // تحميل وتخزين بلاطة
    async function downloadAndStoreTile(url, tileStore) {
        // التحقق من وجود البلاطة في التخزين
        const existingTile = await tileStore.get(url);
        if (existingTile) {
            // تحديث الطابع الزمني
            existingTile.timestamp = Date.now();
            return tileStore.put(existingTile);
        }
        
        // تحميل البلاطة
        const response = await fetch(url, { mode: 'cors' });
        if (!response.ok) {
            throw new Error(`فشل تحميل البلاطة: ${response.status} ${response.statusText}`);
        }
        
        const blob = await response.blob();
        
        // تخزين البلاطة
        return tileStore.put({
            url,
            data: blob,
            timestamp: Date.now()
        });
    }
    
    // تحميل نقاط الاهتمام في المنطقة
    async function downloadPOIs(bounds) {
        try {
            // الحصول على نقاط الاهتمام من الخادم
            const response = await fetch(`/api/pois?south=${bounds.getSouth()}&west=${bounds.getWest()}&north=${bounds.getNorth()}&east=${bounds.getEast()}`);
            if (!response.ok) {
                throw new Error(`فشل تحميل نقاط الاهتمام: ${response.status} ${response.statusText}`);
            }
            
            const pois = await response.json();
            
            // تخزين نقاط الاهتمام
            const db = await dbPromise;
            const tx = db.transaction('pois', 'readwrite');
            const poiStore = tx.objectStore('pois');
            
            for (const poi of pois) {
                await poiStore.put(poi);
            }
            
            await tx.done;
            
            return pois;
        } catch (error) {
            console.error('خطأ في تحميل نقاط الاهتمام:', error);
            // نستمر حتى لو فشل تحميل نقاط الاهتمام
            return [];
        }
    }
    
    // حساب حجم المنطقة
    async function calculateRegionSize(regionId) {
        const db = await dbPromise;
        const region = await db.get('offline-regions', regionId);
        if (!region) {
            return 0;
        }
        
        // الحصول على جميع البلاطات
        const tiles = await db.getAll('map-tiles');
        
        // حساب الحجم الإجمالي
        let totalSize = 0;
        for (const tile of tiles) {
            if (tile.data) {
                totalSize += tile.data.size;
            }
        }
        
        return totalSize;
    }
    
    // تنسيق حجم الملف
    function formatSize(bytes) {
        if (bytes < 1024) {
            return `${bytes} بايت`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} كيلوبايت`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(1)} ميجابايت`;
        }
    }
    
    // حذف منطقة محفوظة
    async function deleteRegion(regionId) {
        try {
            const db = await dbPromise;
            
            // الحصول على معلومات المنطقة
            const region = await db.get('offline-regions', regionId);
            if (!region) {
                throw new Error('المنطقة غير موجودة');
            }
            
            // حذف المنطقة من قاعدة البيانات
            await db.delete('offline-regions', regionId);
            
            // حذف نقاط الاهتمام المرتبطة بالمنطقة
            const poiTx = db.transaction('pois', 'readwrite');
            const poiStore = poiTx.objectStore('pois');
            const poiIndex = poiStore.index('region');
            const poisToDelete = await poiIndex.getAllKeys(regionId);
            
            for (const poiId of poisToDelete) {
                await poiStore.delete(poiId);
            }
            
            await poiTx.done;
            
            // تحديث قائمة المناطق المحفوظة
            currentOfflineRegions = await loadSavedRegions();
            
            // عرض إشعار نجاح
            showNotification(`تم حذف منطقة ${region.name} بنجاح`, 'success');
            
            return true;
        } catch (error) {
            console.error('خطأ في حذف المنطقة:', error);
            showNotification('حدث خطأ أثناء حذف المنطقة', 'error');
            throw error;
        }
    }
    
    // تصدير الواجهة العامة
    return {
        init: function(mapInstance) {
            initDatabase();
            return initMap(mapInstance);
        }
    };
})();

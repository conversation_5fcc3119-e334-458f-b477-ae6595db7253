/**
 * Yemen GPS - نظام خرائط اليمن
 * وظائف الوضع دون اتصال بالإنترنت
 */

class OfflineManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.offlineDB = null;
        this.dbName = 'yemenGpsOfflineDB';
        this.dbVersion = 1;
        this.offlineMapTiles = new Map();
        
        // تهيئة قاعدة البيانات
        this.initDatabase();
        
        // تسجيل مستمع لحالة الاتصال
        window.addEventListener('online', () => this.handleConnectionChange(true));
        window.addEventListener('offline', () => this.handleConnectionChange(false));
        
        // تسجيل مستمع للـ Service Worker
        this.registerServiceWorker();
    }
    
    /**
     * تهيئة قاعدة البيانات المحلية
     */
    initDatabase() {
        const request = indexedDB.open(this.dbName, this.dbVersion);
        
        request.onerror = (event) => {
            console.error('فشل في فتح قاعدة البيانات المحلية:', event.target.error);
        };
        
        request.onsuccess = (event) => {
            this.offlineDB = event.target.result;
            console.log('تم فتح قاعدة البيانات المحلية بنجاح');
        };
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            
            // إنشاء مخزن للمواقع
            if (!db.objectStoreNames.contains('locations')) {
                const locationsStore = db.createObjectStore('locations', { keyPath: 'id' });
                locationsStore.createIndex('name', 'name', { unique: false });
                locationsStore.createIndex('lat', 'lat', { unique: false });
                locationsStore.createIndex('lng', 'lng', { unique: false });
            }
            
            // إنشاء مخزن للصور
            if (!db.objectStoreNames.contains('images')) {
                const imagesStore = db.createObjectStore('images', { keyPath: 'id' });
                imagesStore.createIndex('locationId', 'locationId', { unique: false });
            }
            
            // إنشاء مخزن لقطع الخريطة
            if (!db.objectStoreNames.contains('mapTiles')) {
                const tilesStore = db.createObjectStore('mapTiles', { keyPath: 'url' });
                tilesStore.createIndex('timestamp', 'timestamp', { unique: false });
            }
            
            // إنشاء مخزن للبحث
            if (!db.objectStoreNames.contains('searchHistory')) {
                const searchStore = db.createObjectStore('searchHistory', { keyPath: 'id', autoIncrement: true });
                searchStore.createIndex('query', 'query', { unique: false });
                searchStore.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };
    }
    
    /**
     * تسجيل Service Worker للعمل دون اتصال
     */
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/service-worker.js')
                .then(registration => {
                    console.log('تم تسجيل Service Worker بنجاح:', registration.scope);
                })
                .catch(error => {
                    console.error('فشل تسجيل Service Worker:', error);
                });
        }
    }
    
    /**
     * معالجة تغيير حالة الاتصال
     * @param {boolean} isOnline - حالة الاتصال
     */
    handleConnectionChange(isOnline) {
        this.isOnline = isOnline;
        
        // إظهار إشعار للمستخدم
        const message = isOnline ? 
            'تم استعادة الاتصال بالإنترنت. يمكنك الآن استخدام جميع الميزات.' : 
            'أنت الآن في وضع عدم الاتصال. بعض الميزات قد لا تعمل.';
        
        const type = isOnline ? 'success' : 'warning';
        
        // إذا كان يمنMaps موجودًا، استخدم دالة الإشعار الخاصة به
        if (window.yemenMaps && typeof window.yemenMaps.showNotification === 'function') {
            window.yemenMaps.showNotification(message, type);
        } else {
            alert(message);
        }
        
        // تحديث واجهة المستخدم
        this.updateUI(isOnline);
        
        // إذا كان متصلاً، قم بمزامنة البيانات المحلية
        if (isOnline) {
            this.syncOfflineData();
        }
    }
    
    /**
     * تحديث واجهة المستخدم بناءً على حالة الاتصال
     * @param {boolean} isOnline - حالة الاتصال
     */
    updateUI(isOnline) {
        const offlineIndicator = document.getElementById('offline-indicator');
        
        if (!offlineIndicator) {
            // إنشاء مؤشر وضع عدم الاتصال إذا لم يكن موجودًا
            const indicator = document.createElement('div');
            indicator.id = 'offline-indicator';
            indicator.className = 'offline-indicator';
            indicator.innerHTML = '<i class="fas fa-wifi-slash"></i> وضع عدم الاتصال';
            
            document.body.appendChild(indicator);
        } else {
            // تحديث حالة المؤشر
            offlineIndicator.style.display = isOnline ? 'none' : 'flex';
        }
        
        // تعطيل/تفعيل العناصر التي تتطلب اتصالاً بالإنترنت
        const onlineElements = document.querySelectorAll('.requires-online');
        onlineElements.forEach(element => {
            if (isOnline) {
                element.classList.remove('disabled');
                element.removeAttribute('disabled');
            } else {
                element.classList.add('disabled');
                element.setAttribute('disabled', 'disabled');
            }
        });
    }
    
    /**
     * حفظ موقع في قاعدة البيانات المحلية
     * @param {Object} location - بيانات الموقع
     * @returns {Promise} وعد بنتيجة العملية
     */
    saveLocation(location) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['locations'], 'readwrite');
            const locationsStore = transaction.objectStore('locations');
            
            // إضافة طابع زمني للمزامنة
            location.timestamp = Date.now();
            location.synced = this.isOnline;
            
            const request = locationsStore.put(location);
            
            request.onsuccess = () => {
                console.log('تم حفظ الموقع محليًا');
                resolve(true);
                
                // مزامنة مع الخادم إذا كان متصلاً
                if (this.isOnline && !location.synced) {
                    this.syncLocationWithServer(location);
                }
            };
            
            request.onerror = (event) => {
                console.error('فشل في حفظ الموقع محليًا:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * حفظ صورة موقع في قاعدة البيانات المحلية
     * @param {string} locationId - معرف الموقع
     * @param {string} imageId - معرف الصورة
     * @param {Blob} imageBlob - بيانات الصورة
     * @returns {Promise} وعد بنتيجة العملية
     */
    saveImage(locationId, imageId, imageBlob) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['images'], 'readwrite');
            const imagesStore = transaction.objectStore('images');
            
            const imageData = {
                id: imageId,
                locationId: locationId,
                data: imageBlob,
                timestamp: Date.now()
            };
            
            const request = imagesStore.put(imageData);
            
            request.onsuccess = () => {
                console.log('تم حفظ الصورة محليًا');
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('فشل في حفظ الصورة محليًا:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * الحصول على موقع من قاعدة البيانات المحلية
     * @param {string} locationId - معرف الموقع
     * @returns {Promise} وعد ببيانات الموقع
     */
    getLocation(locationId) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['locations'], 'readonly');
            const locationsStore = transaction.objectStore('locations');
            const request = locationsStore.get(locationId);
            
            request.onsuccess = (event) => {
                const location = event.target.result;
                resolve(location || null);
            };
            
            request.onerror = (event) => {
                console.error('فشل في الحصول على الموقع:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * الحصول على جميع المواقع المحفوظة محليًا
     * @returns {Promise} وعد بقائمة المواقع
     */
    getAllLocations() {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['locations'], 'readonly');
            const locationsStore = transaction.objectStore('locations');
            const request = locationsStore.getAll();
            
            request.onsuccess = (event) => {
                const locations = event.target.result;
                resolve(locations || []);
            };
            
            request.onerror = (event) => {
                console.error('فشل في الحصول على المواقع:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * حفظ قطعة من الخريطة للاستخدام دون اتصال
     * @param {string} url - رابط قطعة الخريطة
     * @param {Blob} tileData - بيانات قطعة الخريطة
     * @returns {Promise} وعد بنتيجة العملية
     */
    saveMapTile(url, tileData) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['mapTiles'], 'readwrite');
            const tilesStore = transaction.objectStore('mapTiles');
            
            const tileInfo = {
                url: url,
                data: tileData,
                timestamp: Date.now()
            };
            
            const request = tilesStore.put(tileInfo);
            
            request.onsuccess = () => {
                // تخزين في الذاكرة المؤقتة للوصول السريع
                this.offlineMapTiles.set(url, tileData);
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('فشل في حفظ قطعة الخريطة:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * الحصول على قطعة خريطة محفوظة محليًا
     * @param {string} url - رابط قطعة الخريطة
     * @returns {Promise} وعد ببيانات قطعة الخريطة
     */
    getMapTile(url) {
        // التحقق أولاً من الذاكرة المؤقتة
        if (this.offlineMapTiles.has(url)) {
            return Promise.resolve(this.offlineMapTiles.get(url));
        }
        
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['mapTiles'], 'readonly');
            const tilesStore = transaction.objectStore('mapTiles');
            const request = tilesStore.get(url);
            
            request.onsuccess = (event) => {
                const tile = event.target.result;
                if (tile) {
                    // تخزين في الذاكرة المؤقتة للوصول السريع في المستقبل
                    this.offlineMapTiles.set(url, tile.data);
                    resolve(tile.data);
                } else {
                    resolve(null);
                }
            };
            
            request.onerror = (event) => {
                console.error('فشل في الحصول على قطعة الخريطة:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * حفظ سجل البحث
     * @param {string} query - استعلام البحث
     * @returns {Promise} وعد بنتيجة العملية
     */
    saveSearchQuery(query) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['searchHistory'], 'readwrite');
            const searchStore = transaction.objectStore('searchHistory');
            
            const searchData = {
                query: query,
                timestamp: Date.now()
            };
            
            const request = searchStore.add(searchData);
            
            request.onsuccess = () => {
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('فشل في حفظ استعلام البحث:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * الحصول على سجل البحث
     * @param {number} limit - عدد العناصر المطلوبة
     * @returns {Promise} وعد بقائمة استعلامات البحث
     */
    getSearchHistory(limit = 10) {
        return new Promise((resolve, reject) => {
            if (!this.offlineDB) {
                reject(new Error('قاعدة البيانات المحلية غير جاهزة'));
                return;
            }
            
            const transaction = this.offlineDB.transaction(['searchHistory'], 'readonly');
            const searchStore = transaction.objectStore('searchHistory');
            const index = searchStore.index('timestamp');
            
            // الحصول على آخر عمليات بحث بترتيب تنازلي
            const request = index.openCursor(null, 'prev');
            const results = [];
            
            request.onsuccess = (event) => {
                const cursor = event.target.result;
                
                if (cursor && results.length < limit) {
                    results.push(cursor.value);
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };
            
            request.onerror = (event) => {
                console.error('فشل في الحصول على سجل البحث:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * مزامنة البيانات غير المتزامنة مع الخادم
     */
    syncOfflineData() {
        if (!this.isOnline) return;
        
        console.log('جاري مزامنة البيانات المحلية مع الخادم...');
        
        // مزامنة المواقع غير المتزامنة
        this.syncLocations();
    }
    
    /**
     * مزامنة المواقع المحفوظة محليًا مع الخادم
     */
    syncLocations() {
        if (!this.offlineDB) return;
        
        const transaction = this.offlineDB.transaction(['locations'], 'readonly');
        const locationsStore = transaction.objectStore('locations');
        const request = locationsStore.getAll();
        
        request.onsuccess = (event) => {
            const locations = event.target.result;
            const unsyncedLocations = locations.filter(location => !location.synced);
            
            if (unsyncedLocations.length === 0) {
                console.log('لا توجد مواقع للمزامنة');
                return;
            }
            
            console.log(`مزامنة ${unsyncedLocations.length} موقع مع الخادم...`);
            
            // مزامنة كل موقع غير متزامن
            unsyncedLocations.forEach(location => {
                this.syncLocationWithServer(location);
            });
        };
        
        request.onerror = (event) => {
            console.error('فشل في الحصول على المواقع للمزامنة:', event.target.error);
        };
    }
    
    /**
     * مزامنة موقع محدد مع الخادم
     * @param {Object} location - بيانات الموقع
     */
    syncLocationWithServer(location) {
        // إرسال الموقع إلى الخادم
        fetch('/api/add-google-place', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(location)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث حالة المزامنة في قاعدة البيانات المحلية
                this.updateLocationSyncStatus(location.id, true);
                console.log(`تمت مزامنة الموقع: ${location.name || location.id}`);
            } else {
                console.error('فشل في مزامنة الموقع مع الخادم:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ أثناء مزامنة الموقع مع الخادم:', error);
        });
    }
    
    /**
     * تحديث حالة مزامنة موقع في قاعدة البيانات المحلية
     * @param {string} locationId - معرف الموقع
     * @param {boolean} synced - حالة المزامنة
     */
    updateLocationSyncStatus(locationId, synced) {
        if (!this.offlineDB) return;
        
        const transaction = this.offlineDB.transaction(['locations'], 'readwrite');
        const locationsStore = transaction.objectStore('locations');
        const request = locationsStore.get(locationId);
        
        request.onsuccess = (event) => {
            const location = event.target.result;
            if (location) {
                location.synced = synced;
                locationsStore.put(location);
            }
        };
        
        request.onerror = (event) => {
            console.error('فشل في تحديث حالة مزامنة الموقع:', event.target.error);
        };
    }
    
    /**
     * حفظ منطقة من الخريطة للاستخدام دون اتصال
     * @param {Object} bounds - حدود المنطقة المراد حفظها
     * @param {number} minZoom - أدنى مستوى تكبير
     * @param {number} maxZoom - أعلى مستوى تكبير
     * @returns {Promise} وعد بتقدم العملية
     */
    saveMapArea(bounds, minZoom = 13, maxZoom = 17) {
        if (!this.isOnline) {
            return Promise.reject(new Error('يجب أن تكون متصلاً بالإنترنت لحفظ منطقة من الخريطة'));
        }
        
        return new Promise((resolve, reject) => {
            // التحقق من وجود Leaflet
            if (!window.L) {
                reject(new Error('مكتبة Leaflet غير متوفرة'));
                return;
            }
            
            // تحديد عدد القطع التي سيتم تنزيلها
            const sw = bounds.getSouthWest();
            const ne = bounds.getNorthEast();
            
            // تقدير عدد قطع الخريطة
            let tileCount = 0;
            let downloadedTiles = 0;
            
            // إنشاء مؤشر التقدم
            const progressContainer = document.createElement('div');
            progressContainer.className = 'download-progress-container';
            progressContainer.innerHTML = `
                <div class="download-progress-header">
                    <h4>جاري تنزيل خرائط للعمل دون اتصال</h4>
                    <button class="close-progress-btn"><i class="fas fa-times"></i></button>
                </div>
                <div class="download-progress-bar-container">
                    <div class="download-progress-bar" style="width: 0%"></div>
                </div>
                <div class="download-progress-info">
                    <span class="download-status">جاري التحضير...</span>
                    <span class="download-percentage">0%</span>
                </div>
            `;
            
            document.body.appendChild(progressContainer);
            
            const progressBar = progressContainer.querySelector('.download-progress-bar');
            const progressStatus = progressContainer.querySelector('.download-status');
            const progressPercentage = progressContainer.querySelector('.download-percentage');
            const closeButton = progressContainer.querySelector('.close-progress-btn');
            
            let isCancelled = false;
            
            closeButton.addEventListener('click', () => {
                isCancelled = true;
                progressContainer.remove();
                reject(new Error('تم إلغاء التنزيل'));
            });
            
            // حساب عدد القطع
            for (let zoom = minZoom; zoom <= maxZoom; zoom++) {
                const nwTile = window.L.point(window.L.CRS.EPSG3857.latLngToPoint(ne, zoom).divideBy(256).floor());
                const seTile = window.L.point(window.L.CRS.EPSG3857.latLngToPoint(sw, zoom).divideBy(256).ceil());
                const tileCountX = seTile.x - nwTile.x + 1;
                const tileCountY = seTile.y - nwTile.y + 1;
                tileCount += tileCountX * tileCountY;
            }
            
            progressStatus.textContent = `جاري تنزيل ${tileCount} قطعة من الخريطة...`;
            
            // تنزيل القطع
            const downloadTiles = async () => {
                for (let zoom = minZoom; zoom <= maxZoom; zoom++) {
                    if (isCancelled) break;
                    
                    const nwTile = window.L.point(window.L.CRS.EPSG3857.latLngToPoint(ne, zoom).divideBy(256).floor());
                    const seTile = window.L.point(window.L.CRS.EPSG3857.latLngToPoint(sw, zoom).divideBy(256).ceil());
                    
                    for (let x = nwTile.x; x <= seTile.x; x++) {
                        for (let y = nwTile.y; y <= seTile.y; y++) {
                            if (isCancelled) break;
                            
                            // إنشاء عنوان URL للقطعة
                            const tileUrl = `https://{s}.tile.openstreetmap.org/${zoom}/${x}/${y}.png`;
                            const url = tileUrl.replace('{s}', 'a');
                            
                            try {
                                // تنزيل القطعة
                                const response = await fetch(url);
                                const blob = await response.blob();
                                
                                // حفظ القطعة في قاعدة البيانات المحلية
                                await this.saveMapTile(url, blob);
                                
                                // تحديث التقدم
                                downloadedTiles++;
                                const progress = Math.round((downloadedTiles / tileCount) * 100);
                                
                                progressBar.style.width = `${progress}%`;
                                progressPercentage.textContent = `${progress}%`;
                                progressStatus.textContent = `تم تنزيل ${downloadedTiles} من ${tileCount} قطعة...`;
                            } catch (error) {
                                console.error(`فشل في تنزيل قطعة الخريطة: ${url}`, error);
                            }
                        }
                    }
                }
                
                if (!isCancelled) {
                    progressBar.style.width = '100%';
                    progressPercentage.textContent = '100%';
                    progressStatus.textContent = 'تم تنزيل الخريطة بنجاح!';
                    
                    // إخفاء المؤشر بعد 3 ثوانٍ
                    setTimeout(() => {
                        progressContainer.remove();
                    }, 3000);
                    
                    resolve({
                        totalTiles: tileCount,
                        downloadedTiles: downloadedTiles,
                        success: true
                    });
                }
            };
            
            // بدء التنزيل
            downloadTiles();
        });
    }
}

// تصدير الكلاس
window.OfflineManager = OfflineManager;

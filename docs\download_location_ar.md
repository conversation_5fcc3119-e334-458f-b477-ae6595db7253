# مكان تنزيل الملفات أثناء تثبيت نظام "يمن ناف"

## أين يتم تخزين الملفات التي يتم تنزيلها؟

أثناء عملية التثبيت، يتم تنزيل الملفات إلى مجلد مؤقت في نظام التشغيل. المسار الدقيق هو:

```
%TEMP%\yemen-nav-temp
```

وهذا يترجم عادة إلى:

```
C:\Users\<USER>\AppData\Local\Temp\yemen-nav-temp
```

حيث `[اسم المستخدم]` هو اسم المستخدم الخاص بك في نظام ويندوز.

## كيفية التحقق من وجود الملفات التي يتم تنزيلها

### الطريقة 1: من خلال نافذة PowerShell

يمكنك التحقق من وجود الملفات التي يتم تنزيلها من خلال فتح نافذة PowerShell جديدة (دون إغلاق النافذة الحالية) وكتابة الأمر التالي:

```powershell
dir $env:TEMP\yemen-nav-temp
```

إذا كان التنزيل قد بدأ، فسترى قائمة بالملفات التي تم تنزيلها أو التي يتم تنزيلها حاليًا، مثل:

```
    Directory: C:\Users\<USER>\AppData\Local\Temp\yemen-nav-temp

Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a----         5/2/2025  12:58 AM       20123456 node-v16.20.0-x64.msi
-a----         5/2/2025  12:59 AM        5678901 Git-2.36.1-64-bit.exe
```

### الطريقة 2: من خلال مستكشف الملفات (File Explorer)

1. افتح مستكشف الملفات (اضغط على `Win + E`)
2. انسخ المسار التالي وألصقه في شريط العنوان:
   ```
   %TEMP%\yemen-nav-temp
   ```
3. اضغط على Enter

ستفتح نافذة تعرض محتويات المجلد المؤقت حيث يتم تنزيل الملفات.

## الملفات التي يتم تنزيلها

الملفات الرئيسية التي يتم تنزيلها هي:

1. **Node.js**: `node-v16.20.0-x64.msi` (حوالي 20-30 ميجابايت)
2. **PostgreSQL**: `postgresql-13.7-1-windows-x64.exe` (حوالي 200-300 ميجابايت)
3. **Git**: `Git-2.36.1-64-bit.exe` (حوالي 50-60 ميجابايت)
4. **URL Rewrite Module**: `rewrite_amd64_en-US.msi` (حوالي 10 ميجابايت)

## كيفية معرفة ما إذا كان التنزيل جاريًا

### 1. حجم الملفات

إذا كان حجم الملف أقل من الحجم المتوقع، فهذا يعني أن التنزيل لا يزال جاريًا. على سبيل المثال، إذا كان حجم ملف PostgreSQL 150 ميجابايت بينما الحجم المتوقع هو 300 ميجابايت، فهذا يعني أن التنزيل لا يزال جاريًا.

### 2. تاريخ التعديل

إذا كان تاريخ آخر تعديل للملف هو التاريخ والوقت الحاليين، فهذا يعني أن الملف لا يزال قيد التنزيل أو تم تنزيله للتو.

### 3. نشاط الشبكة

يمكنك أيضًا التحقق من نشاط الشبكة لمعرفة ما إذا كان هناك تنزيل جارٍ:

1. افتح مدير المهام (اضغط على `Ctrl + Shift + Esc`)
2. انتقل إلى علامة التبويب "Performance"
3. انقر على "Ethernet" أو "Wi-Fi" (حسب نوع اتصالك)
4. راقب الرسم البياني لنشاط الشبكة. إذا كان هناك نشاط كبير، فهذا يعني أن التنزيل جارٍ

## ماذا يحدث بعد تنزيل الملفات؟

بعد تنزيل الملفات، سيقوم ملف التثبيت بتثبيت البرامج واحدًا تلو الآخر. ستظهر نوافذ منبثقة أثناء تثبيت البرامج، وقد تحتاج إلى التفاعل معها (النقر على "Next" أو "Yes").

بعد تثبيت البرامج، سيقوم ملف التثبيت بإعداد النظام، وإنشاء قاعدة البيانات، وإعداد الواجهة الخلفية والأمامية، وتكوين IIS وPM2.

## هل يتم حذف الملفات المؤقتة بعد التثبيت؟

نعم، في نهاية عملية التثبيت، سيقوم ملف التثبيت بتنظيف الملفات المؤقتة وحذف المجلد `%TEMP%\yemen-nav-temp`.

## ملاحظات مهمة

- **لا تحذف الملفات المؤقتة** أثناء عملية التثبيت، حتى لو كانت تبدو كبيرة
- **لا تغلق نافذة PowerShell** التي تعرض تقدم التثبيت
- **تفاعل مع أي نوافذ منبثقة** قد تظهر أثناء تثبيت البرامج

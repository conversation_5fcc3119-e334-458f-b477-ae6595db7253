/* Google Maps Enhanced Styles */
/* استفادة من ملفات Google المحفوظة */

/* Google Fonts Integration */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Google Material Icons */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* Enhanced Google Maps Style Variables */
:root {
    --google-blue: #1a73e8;
    --google-blue-dark: #1557b0;
    --google-red: #ea4335;
    --google-yellow: #fbbc04;
    --google-green: #34a853;
    --google-grey-50: #f8f9fa;
    --google-grey-100: #f1f3f4;
    --google-grey-200: #e8eaed;
    --google-grey-300: #dadce0;
    --google-grey-400: #bdc1c6;
    --google-grey-500: #9aa0a6;
    --google-grey-600: #80868b;
    --google-grey-700: #5f6368;
    --google-grey-800: #3c4043;
    --google-grey-900: #202124;
    
    --shadow-1: 0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15);
    --shadow-2: 0 1px 2px 0 rgba(60,64,67,.3), 0 2px 6px 2px rgba(60,64,67,.15);
    --shadow-3: 0 4px 8px 3px rgba(60,64,67,.15), 0 1px 3px rgba(60,64,67,.3);
    --shadow-4: 0 2px 16px 4px rgba(60,64,67,.15), 0 8px 16px 8px rgba(60,64,67,.15);
}

/* Enhanced Map Tiles with Google Style */
.leaflet-tile {
    filter: contrast(1.1) saturate(1.2) brightness(1.05);
    transition: filter 0.3s ease;
}

/* Google Street View Style Pegman */
.streetview-control {
    position: absolute;
    bottom: 80px;
    right: 16px;
    z-index: 1000;
}

.pegman {
    width: 40px;
    height: 40px;
    background: var(--google-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: grab;
    box-shadow: var(--shadow-2);
    transition: all 0.2s ease;
}

.pegman:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-3);
}

.pegman:active {
    cursor: grabbing;
    transform: scale(0.95);
}

/* Enhanced Place Markers */
.place-marker {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.place-marker:hover {
    transform: scale(1.1);
    z-index: 1000;
}

/* Google Maps Style Info Windows */
.leaflet-popup-content-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-3);
    border: none;
    padding: 0;
    overflow: hidden;
}

.leaflet-popup-content {
    margin: 0;
    padding: 16px;
    font-family: 'Roboto', sans-serif;
    line-height: 1.4;
}

.leaflet-popup-tip {
    background: white;
    border: none;
    box-shadow: var(--shadow-2);
}

/* Enhanced Search Results */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow-3);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.search-results.show {
    display: block;
}

.search-result-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--google-grey-200);
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-result-item:hover {
    background: var(--google-grey-50);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-icon {
    width: 32px;
    height: 32px;
    background: var(--google-grey-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.search-result-info h4 {
    font-size: 14px;
    font-weight: 500;
    color: var(--google-grey-800);
    margin: 0 0 4px 0;
}

.search-result-info p {
    font-size: 12px;
    color: var(--google-grey-600);
    margin: 0;
}

/* Enhanced Traffic Layer */
.traffic-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 500;
}

.traffic-line {
    stroke-width: 4;
    stroke-linecap: round;
    opacity: 0.8;
}

.traffic-heavy { stroke: var(--google-red); }
.traffic-medium { stroke: var(--google-yellow); }
.traffic-light { stroke: var(--google-green); }

/* Enhanced Route Planning */
.route-panel {
    position: absolute;
    top: 80px;
    left: 16px;
    width: 320px;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-3);
    z-index: 1000;
    display: none;
}

.route-panel.show {
    display: block;
}

.route-header {
    padding: 16px;
    border-bottom: 1px solid var(--google-grey-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.route-inputs {
    padding: 16px;
}

.route-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--google-grey-300);
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.route-input:focus {
    border-color: var(--google-blue);
}

.route-options {
    padding: 0 16px 16px;
    display: flex;
    gap: 8px;
}

.route-option {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--google-grey-300);
    border-radius: 20px;
    background: white;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.route-option.active {
    background: var(--google-blue);
    color: white;
    border-color: var(--google-blue);
}

.route-option:hover:not(.active) {
    background: var(--google-grey-50);
}

/* Enhanced Place Details */
.place-details {
    max-width: 100%;
}

.place-header {
    position: relative;
    margin-bottom: 16px;
}

.place-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.place-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0,0,0,0.6));
}

.place-title {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    color: white;
    z-index: 1;
}

.place-title h3 {
    font-size: 20px;
    font-weight: 500;
    margin: 0 0 4px 0;
    text-shadow: 0 1px 3px rgba(0,0,0,0.5);
}

.place-title p {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.place-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: var(--google-yellow);
    font-size: 16px;
}

.rating-text {
    font-size: 14px;
    color: var(--google-grey-600);
}

.place-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.info-item {
    text-align: center;
    padding: 12px;
    background: var(--google-grey-50);
    border-radius: 8px;
}

.info-item-icon {
    font-size: 20px;
    color: var(--google-blue);
    margin-bottom: 4px;
}

.info-item-label {
    font-size: 12px;
    color: var(--google-grey-600);
    margin-bottom: 2px;
}

.info-item-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--google-grey-800);
}

/* Enhanced Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
    margin-top: 16px;
}

.action-button {
    padding: 12px 16px;
    border: 1px solid var(--google-grey-300);
    border-radius: 20px;
    background: white;
    color: var(--google-blue);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-button:hover {
    background: var(--google-grey-50);
    box-shadow: var(--shadow-1);
}

.action-button.primary {
    background: var(--google-blue);
    color: white;
    border-color: var(--google-blue);
}

.action-button.primary:hover {
    background: var(--google-blue-dark);
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--google-grey-100) 25%, var(--google-grey-50) 50%, var(--google-grey-100) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-text.large {
    height: 20px;
}

.skeleton-text.small {
    height: 12px;
    width: 60%;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .route-panel {
        left: 8px;
        right: 8px;
        width: auto;
    }
    
    .place-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
}

/* Enhanced Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --google-grey-50: #202124;
        --google-grey-100: #303134;
        --google-grey-200: #3c4043;
        --google-grey-300: #5f6368;
        --google-grey-400: #80868b;
        --google-grey-500: #9aa0a6;
        --google-grey-600: #bdc1c6;
        --google-grey-700: #dadce0;
        --google-grey-800: #e8eaed;
        --google-grey-900: #f8f9fa;
    }
    
    .leaflet-tile {
        filter: contrast(1.1) saturate(0.8) brightness(0.7) hue-rotate(180deg);
    }
}

/* Enhanced Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--google-blue);
    outline-offset: 2px;
}

/* Enhanced Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease;
}

.animate-slide-in {
    animation: slideIn 0.3s ease;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

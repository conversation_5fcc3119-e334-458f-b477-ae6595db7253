/**
 * ملف جافا سكريبت للوحة تحكم يمن GPS
 * يتضمن جميع الوظائف اللازمة للتفاعل مع قاعدة البيانات وعرض البيانات
 */

// متغيرات عامة
let currentUser = null;
let allUsers = [];
let allLocations = [];
let allCategories = [];
let allClients = [];
let allEvents = [];
let allAdvertisements = [];

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من حالة تسجيل الدخول
    checkLoginStatus();
    
    // إعداد أحداث التنقل
    setupNavigation();
    
    // إعداد أحداث النماذج
    setupFormEvents();
    
    // تحميل البيانات الأولية
    loadInitialData();
});

/**
 * التحقق من حالة تسجيل الدخول
 */
function checkLoginStatus() {
    // التحقق من وجود رمز الجلسة في تخزين الجلسة أو التخزين المحلي
    const sessionToken = sessionStorage.getItem('yemenGpsToken') || sessionStorage.getItem('yemenNavToken');
    const localToken = localStorage.getItem('yemenGpsToken') || localStorage.getItem('yemenNavToken');
    const sessionUser = sessionStorage.getItem('yemenGpsUser') || sessionStorage.getItem('yemenNavCurrentUser');
    const localUser = localStorage.getItem('yemenGpsUser') || localStorage.getItem('yemenNavCurrentUser');
    
    // استخدام التوكن وبيانات المستخدم من أي مصدر متاح
    const token = sessionToken || localToken;
    const storedUser = sessionUser || localUser;
    
    console.log('التحقق من حالة تسجيل الدخول:', { 
        sessionToken: sessionToken ? 'موجود' : 'غير موجود', 
        localToken: localToken ? 'موجود' : 'غير موجود', 
        sessionUser: sessionUser ? 'موجود' : 'غير موجود', 
        localUser: localUser ? 'موجود' : 'غير موجود'
    });
    
    if (!token || !storedUser) {
        console.log('لم يتم العثور على بيانات المستخدم');
        // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
        window.location.href = 'admin-login.html';
        return;
    }
    
    try {
        // تحليل بيانات المستخدم
        currentUser = JSON.parse(storedUser);
        console.log('بيانات المستخدم:', currentUser);
        
        // التحقق من صلاحيات المستخدم (مع التحقق من جميع الحقول المحتملة)
        const userRoleId = currentUser.role_id || currentUser.roleId || 0;
        const userRole = currentUser.role || '';
        
        console.log('صلاحيات المستخدم:', { userRoleId, userRole });
        
        // السماح بالوصول في وضع التطوير
        const DEV_MODE = true;
        
        if (DEV_MODE) {
            console.log('وضع التطوير مفعل: تم تجاوز التحقق من الصلاحيات');
            // تعيين صلاحيات المستخدم إلى مسؤول في وضع التطوير
            currentUser.role = 'admin';
            currentUser.roleId = 1;
            currentUser.role_id = 1;
            
            // حفظ التغييرات في التخزين
            sessionStorage.setItem('yemenNavCurrentUser', JSON.stringify(currentUser));
            localStorage.setItem('yemenNavCurrentUser', JSON.stringify(currentUser));
            
            // التأكد من وجود توكن صالح
            if (!token) {
                // إنشاء توكن وهمي لوضع التطوير
                const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIn0.8tat9EiVFNHcHt6Kt4CfP-Svo3sJOqbUYHrS-nQM7Yw';
                sessionStorage.setItem('yemenNavToken', mockToken);
                localStorage.setItem('yemenNavToken', mockToken);
            }
            
            // تحديث معلومات المستخدم في الواجهة
            updateUserInfo();
            return;
        }
        
        if (userRoleId !== 1 && userRoleId !== 3 && userRole !== 'admin' && userRole !== 'developer') {
            // المستخدم ليس مديرًا أو مطورًا
            alert('ليس لديك صلاحية الوصول إلى لوحة التحكم');
            window.location.href = 'index.html';
            return;
        }
        
        // تحديث معلومات المستخدم في الواجهة
        updateUserInfo();
    } catch (error) {
        console.error('خطأ في تحليل بيانات المستخدم:', error);
        localStorage.removeItem('yemenNavToken');
        sessionStorage.removeItem('yemenNavToken');
        localStorage.removeItem('yemenNavCurrentUser');
        sessionStorage.removeItem('yemenNavCurrentUser');
        window.location.href = 'admin-login.html';
    }
}

/**
 * تحديث معلومات المستخدم في الواجهة
 */
function updateUserInfo() {
    const userNameElement = document.getElementById('userName');
    if (userNameElement && currentUser) {
        userNameElement.textContent = currentUser.full_name || currentUser.username;
    }
}

/**
 * إعداد أحداث التنقل بين الأقسام
 */
function setupNavigation() {
    // أحداث النقر على روابط القائمة الجانبية
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(l => l.classList.remove('active'));
            
            // إضافة الفئة النشطة للرابط المنقور
            this.classList.add('active');
            
            // إظهار القسم المناسب
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
        });
    });
    
    // زر تسجيل الخروج
    document.getElementById('logoutBtn').addEventListener('click', logout);
    document.getElementById('logoutBtnDropdown').addEventListener('click', logout);
    
    // زر تبديل القائمة الجانبية (للشاشات الصغيرة)
    document.getElementById('toggleSidebar').addEventListener('click', function() {
        document.getElementById('sidebar').classList.toggle('show');
    });
}

/**
 * إظهار قسم محدد وإخفاء الأقسام الأخرى
 */
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
    });
    
    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionId + 'Section');
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.style.display = 'block';
        
        // تحديث البيانات في القسم المعروض
        updateSectionData(sectionId);
    }
}

/**
 * تحديث بيانات القسم المعروض
 */
function updateSectionData(sectionId) {
    switch(sectionId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'users':
            loadUsers();
            break;
        case 'locations':
            loadLocations();
            break;
        case 'categories':
            loadCategories();
            break;
        case 'clients':
            loadClients();
            break;
        case 'events':
            loadEvents();
            break;
        case 'advertisements':
            loadAdvertisements();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

/**
 * إعداد أحداث النماذج
 */
function setupFormEvents() {
    // نموذج إضافة مستخدم
    document.getElementById('saveUserBtn').addEventListener('click', addUser);
    
    // نموذج تعديل مستخدم
    document.getElementById('updateUserBtn').addEventListener('click', updateUser);
    
    // أزرار إعادة تعيين التصفية
    document.getElementById('resetUserFilters').addEventListener('click', resetUserFilters);
    
    // أحداث البحث
    document.getElementById('userSearchInput').addEventListener('input', filterUsers);
    document.getElementById('userRoleFilter').addEventListener('change', filterUsers);
    document.getElementById('userStatusFilter').addEventListener('change', filterUsers);
}

/**
 * تحميل البيانات الأولية
 */
function loadInitialData() {
    // إظهار مؤشر التحميل
    showLoading();
    
    // تحميل بيانات لوحة المعلومات
    loadDashboardData();
    
    // إخفاء مؤشر التحميل بعد الانتهاء
    setTimeout(hideLoading, 1000);
}

/**
 * تحميل بيانات لوحة المعلومات
 */
function loadDashboardData() {
    // تحميل الإحصائيات
    loadStatistics();
    
    // تحميل آخر المواقع المضافة
    loadRecentLocations();
    
    // تحميل آخر المستخدمين المسجلين
    loadRecentUsers();
}

/**
 * تحميل الإحصائيات
 */
function loadStatistics() {
    // إحصائيات المستخدمين
    apiRequest('/admin/users/count')
        .then(data => {
            document.getElementById('usersCount').textContent = data.count || 0;
        })
        .catch(error => {
            console.error('خطأ في تحميل إحصائيات المستخدمين:', error);
            document.getElementById('usersCount').textContent = 0;
        });
    
    // إحصائيات المواقع
    apiRequest('/admin/locations/count')
        .then(data => {
            document.getElementById('locationsCount').textContent = data.count || 0;
        })
        .catch(error => {
            console.error('خطأ في تحميل إحصائيات المواقع:', error);
            document.getElementById('locationsCount').textContent = 0;
        });
    
    // إحصائيات التصنيفات
    apiRequest('/admin/categories/count')
        .then(data => {
            document.getElementById('categoriesCount').textContent = data.count || 0;
        })
        .catch(error => {
            console.error('خطأ في تحميل إحصائيات التصنيفات:', error);
            document.getElementById('categoriesCount').textContent = 0;
        });
    
    // إحصائيات العملاء
    apiRequest('/admin/clients/count')
        .then(data => {
            document.getElementById('clientsCount').textContent = data.count || 0;
        })
        .catch(error => {
            console.error('خطأ في تحميل إحصائيات العملاء:', error);
            document.getElementById('clientsCount').textContent = 0;
        });
}

/**
 * تحميل آخر المواقع المضافة
 */
function loadRecentLocations() {
    apiRequest('/admin/locations/recent')
        .then(data => {
            const tableBody = document.getElementById('recentLocationsTable');
            tableBody.innerHTML = '';
            
            if (data.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">لا توجد مواقع مضافة حديثًا</td>';
                tableBody.appendChild(row);
                return;
            }
            
            data.forEach(location => {
                const row = document.createElement('tr');
                
                // تحديد فئة حالة الموقع
                let statusClass = '';
                let statusText = '';
                
                switch(location.status) {
                    case 'active':
                        statusClass = 'status-active';
                        statusText = 'نشط';
                        break;
                    case 'inactive':
                        statusClass = 'status-inactive';
                        statusText = 'غير نشط';
                        break;
                    case 'pending':
                        statusClass = 'status-pending';
                        statusText = 'قيد المراجعة';
                        break;
                    default:
                        statusClass = '';
                        statusText = location.status;
                }
                
                // تنسيق التاريخ
                const createdDate = new Date(location.created_at);
                const formattedDate = createdDate.toLocaleDateString('ar-SA');
                
                row.innerHTML = `
                    <td>${location.name}</td>
                    <td>${location.category_name || 'غير مصنف'}</td>
                    <td>${location.address || '-'}</td>
                    <td>${formattedDate}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                `;
                
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل آخر المواقع المضافة:', error);
            const tableBody = document.getElementById('recentLocationsTable');
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">خطأ في تحميل البيانات</td></tr>';
        });
}

/**
 * تحميل آخر المستخدمين المسجلين
 */
function loadRecentUsers() {
    apiRequest('/admin/users/recent')
        .then(data => {
            const tableBody = document.getElementById('recentUsersTable');
            tableBody.innerHTML = '';
            
            if (data.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="5" class="text-center">لا يوجد مستخدمين مسجلين حديثًا</td>';
                tableBody.appendChild(row);
                return;
            }
            
            data.forEach(user => {
                const row = document.createElement('tr');
                
                // تحديد فئة حالة المستخدم
                const statusClass = user.is_active ? 'status-active' : 'status-inactive';
                const statusText = user.is_active ? 'نشط' : 'غير نشط';
                
                // تنسيق التاريخ
                const registrationDate = new Date(user.registration_date);
                const formattedDate = registrationDate.toLocaleDateString('ar-SA');
                
                row.innerHTML = `
                    <td>${user.full_name}</td>
                    <td>${user.username}</td>
                    <td>${user.email || '-'}</td>
                    <td>${formattedDate}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                `;
                
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل آخر المستخدمين المسجلين:', error);
            const tableBody = document.getElementById('recentUsersTable');
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">خطأ في تحميل البيانات</td></tr>';
        });
}

/**
 * تحميل المستخدمين
 */
function loadUsers() {
    showLoading();
    
    apiRequest('/api/admin/users')
        .then(data => {
            allUsers = data;
            displayUsers(data);
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل المستخدمين:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء تحميل بيانات المستخدمين', 'error');
        });
}

/**
 * عرض المستخدمين في الجدول
 */
function displayUsers(users) {
    const tableBody = document.getElementById('usersTable');
    tableBody.innerHTML = '';
    
    if (users.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="8" class="text-center">لا يوجد مستخدمين</td>';
        tableBody.appendChild(row);
        return;
    }
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        // تحديد فئة حالة المستخدم
        const statusClass = user.is_active ? 'status-active' : 'status-inactive';
        const statusText = user.is_active ? 'نشط' : 'غير نشط';
        
        // تنسيق التاريخ
        const registrationDate = new Date(user.registration_date);
        const formattedDate = registrationDate.toLocaleDateString('ar-SA');
        
        // تحديد اسم الدور
        let roleName = 'مستخدم عادي';
        switch(user.role_id) {
            case 1:
                roleName = 'مدير';
                break;
            case 3:
                roleName = 'مطور';
                break;
        }
        
        row.innerHTML = `
            <td>${user.full_name}</td>
            <td>${user.username}</td>
            <td>${user.email || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>${roleName}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>${formattedDate}</td>
            <td>
                <button class="btn btn-sm btn-primary btn-action edit-user-btn" data-id="${user.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-action delete-user-btn" data-id="${user.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    // إضافة أحداث للأزرار
    addUserButtonsEvents();
}

/**
 * إضافة أحداث للأزرار في جدول المستخدمين
 */
function addUserButtonsEvents() {
    // أزرار تعديل المستخدم
    const editButtons = document.querySelectorAll('.edit-user-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            editUser(userId);
        });
    });
    
    // أزرار حذف المستخدم
    const deleteButtons = document.querySelectorAll('.delete-user-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            deleteUser(userId);
        });
    });
}

/**
 * تصفية المستخدمين
 */
function filterUsers() {
    const searchText = document.getElementById('userSearchInput').value.toLowerCase();
    const roleFilter = document.getElementById('userRoleFilter').value;
    const statusFilter = document.getElementById('userStatusFilter').value;
    
    const filteredUsers = allUsers.filter(user => {
        // تصفية حسب النص
        const matchesSearch = 
            user.full_name.toLowerCase().includes(searchText) ||
            user.username.toLowerCase().includes(searchText) ||
            (user.email && user.email.toLowerCase().includes(searchText)) ||
            (user.phone && user.phone.includes(searchText));
        
        // تصفية حسب الدور
        const matchesRole = roleFilter === 'all' || user.role_id.toString() === roleFilter;
        
        // تصفية حسب الحالة
        const matchesStatus = statusFilter === 'all' || 
            (statusFilter === '1' && user.is_active) || 
            (statusFilter === '0' && !user.is_active);
        
        return matchesSearch && matchesRole && matchesStatus;
    });
    
    displayUsers(filteredUsers);
}

/**
 * إعادة تعيين تصفية المستخدمين
 */
function resetUserFilters() {
    document.getElementById('userSearchInput').value = '';
    document.getElementById('userRoleFilter').value = 'all';
    document.getElementById('userStatusFilter').value = 'all';
    
    displayUsers(allUsers);
}

/**
 * إضافة مستخدم جديد
 */
function addUser() {
    // جمع بيانات النموذج
    const fullName = document.getElementById('fullName').value;
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    const password = document.getElementById('password').value;
    const roleId = document.getElementById('roleId').value;
    const isActive = document.getElementById('isActive').checked;
    
    // التحقق من البيانات المطلوبة
    if (!fullName || !username || !password) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال البيانات إلى الخادم
    apiRequest('/admin/users', {
        method: 'POST',
        body: JSON.stringify({
            full_name: fullName,
            username: username,
            email: email,
            phone: phone,
            password: password,
            role_id: roleId,
            is_active: isActive
        })
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
        modal.hide();
        
        // مسح النموذج
        document.getElementById('addUserForm').reset();
        
        // إظهار رسالة نجاح
        showNotification('تمت إضافة المستخدم بنجاح');
        
        // إعادة تحميل المستخدمين
        loadUsers();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        showNotification('حدث خطأ أثناء إضافة المستخدم: ' + error.message, 'error');
    });
}

/**
 * تعديل مستخدم
 */
function editUser(userId) {
    // البحث عن المستخدم في المصفوفة
    const user = allUsers.find(u => u.id == userId);
    
    if (!user) {
        showNotification('لم يتم العثور على المستخدم', 'error');
        return;
    }
    
    // ملء النموذج ببيانات المستخدم
    document.getElementById('editUserId').value = user.id;
    document.getElementById('editFullName').value = user.full_name;
    document.getElementById('editUsername').value = user.username;
    document.getElementById('editEmail').value = user.email || '';
    document.getElementById('editPhone').value = user.phone || '';
    document.getElementById('editPassword').value = '';
    document.getElementById('editRoleId').value = user.role_id;
    document.getElementById('editIsActive').checked = user.is_active;
    
    // فتح النموذج
    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
    modal.show();
}

/**
 * تحديث بيانات المستخدم
 */
function updateUser() {
    // جمع بيانات النموذج
    const userId = document.getElementById('editUserId').value;
    const fullName = document.getElementById('editFullName').value;
    const username = document.getElementById('editUsername').value;
    const email = document.getElementById('editEmail').value;
    const phone = document.getElementById('editPhone').value;
    const password = document.getElementById('editPassword').value;
    const roleId = document.getElementById('editRoleId').value;
    const isActive = document.getElementById('editIsActive').checked;
    
    // التحقق من البيانات المطلوبة
    if (!fullName || !username) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إعداد البيانات للإرسال
    const userData = {
        full_name: fullName,
        username: username,
        email: email,
        phone: phone,
        role_id: roleId,
        is_active: isActive
    };
    
    // إضافة كلمة المرور فقط إذا تم تغييرها
    if (password) {
        userData.password = password;
    }
    
    // إرسال البيانات إلى الخادم
    apiRequest(`/admin/users/${userId}`, {
        method: 'PUT',
        body: JSON.stringify(userData)
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
        modal.hide();
        
        // إظهار رسالة نجاح
        showNotification('تم تحديث بيانات المستخدم بنجاح');
        
        // إعادة تحميل المستخدمين
        loadUsers();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        showNotification('حدث خطأ أثناء تحديث بيانات المستخدم: ' + error.message, 'error');
    });
}

/**
 * حذف مستخدم
 */
function deleteUser(userId) {
    // التأكيد قبل الحذف
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟')) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال طلب الحذف إلى الخادم
    apiRequest(`/admin/users/${userId}`, {
        method: 'DELETE'
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة نجاح
        showNotification('تم حذف المستخدم بنجاح');
        
        // إعادة تحميل المستخدمين
        loadUsers();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        showNotification('حدث خطأ أثناء حذف المستخدم: ' + error.message, 'error');
    });
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات الجلسة من تخزين الجلسة
    sessionStorage.removeItem('yemenNavToken');
    sessionStorage.removeItem('yemenNavCurrentUser');
    sessionStorage.removeItem('yemenNavLoggedIn');
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // الحصول على التوكن من sessionStorage أو localStorage
    const token = sessionStorage.getItem('yemenGpsToken') || localStorage.getItem('yemenGpsToken') || 
              sessionStorage.getItem('yemenNavToken') || localStorage.getItem('yemenNavToken');
    
    console.log('التوكن المستخدم في الطلب:', token ? 'موجود' : 'غير موجود');
    
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http') && !endpoint.startsWith('/api')) {
        // استخدام مسار نسبي للـ API
        endpoint = '/api' + endpoint;
    }
    
    // طباعة معلومات الطلب للتشخيص
    console.group(`\nطلب API: ${endpoint}`);
    console.log(`نوع الطلب: ${options.method || 'GET'}`);
    console.log(`التوكن المستخدم: ${token ? token.substring(0, 15) + '...' : 'غير موجود'}`);
    if (options.body) {
        console.log(`بيانات الطلب:`, JSON.parse(options.body));
    }
    console.groupEnd();
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            // طباعة معلومات الاستجابة
            console.group(`استجابة للطلب: ${endpoint}`);
            console.log(`حالة الاستجابة: ${response.status} ${response.statusText}`);
            console.log(`رؤوس الاستجابة:`, Object.fromEntries([...response.headers.entries()]));
            
            if (!response.ok) {
                console.error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
                console.groupEnd();
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            
            return response.json().then(data => {
                console.log(`بيانات الاستجابة:`, data);
                console.groupEnd();
                return data;
            });
        })
        .catch(error => {
            console.error(`خطأ في طلب API: ${endpoint}`, error);
            throw error;
        });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('show');
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.remove('show');
}

/**
 * إظهار إشعار
 */
function showNotification(message, type = 'success') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.innerHTML = message;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // إخفاء الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// إضافة CSS للإشعارات
const notificationStyle = document.createElement('style');
notificationStyle.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-100px);
        opacity: 0;
        transition: all 0.3s;
    }
    
    .notification.show {
        transform: translateY(0);
        opacity: 1;
    }
`;
document.head.appendChild(notificationStyle);

<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/AppTheme.PopupOverlay" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <!-- Map Fragment Container -->
            <fragment
                android:id="@+id/map"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Search Bar -->
            <androidx.cardview.widget.CardView
                android:id="@+id/search_card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/search_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:iconifiedByDefault="false"
                    android:queryHint="@string/search_hint" />

            </androidx.cardview.widget.CardView>

            <!-- Location Type Filter Chips -->
            <HorizontalScrollView
                android:id="@+id/filter_scroll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:scrollbars="none"
                app:layout_constraintTop_toBottomOf="@id/search_card">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/filter_chip_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    app:singleLine="true"
                    app:singleSelection="false">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_traffic"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/type_traffic"
                        app:chipBackgroundColor="@color/colorTraffic"
                        app:chipIconTint="@android:color/white"
                        app:chipStrokeWidth="0dp"
                        app:textEndPadding="8dp"
                        app:textStartPadding="8dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_speedbump"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/type_speedbump"
                        app:chipBackgroundColor="@color/colorSpeedbump"
                        app:chipIconTint="@android:color/white"
                        app:chipStrokeWidth="0dp"
                        app:textEndPadding="8dp"
                        app:textStartPadding="8dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_pothole"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/type_pothole"
                        app:chipBackgroundColor="@color/colorPothole"
                        app:chipIconTint="@android:color/white"
                        app:chipStrokeWidth="0dp"
                        app:textEndPadding="8dp"
                        app:textStartPadding="8dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_dirt_road"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/type_dirt_road"
                        app:chipBackgroundColor="@color/colorDirtRoad"
                        app:chipIconTint="@android:color/white"
                        app:chipStrokeWidth="0dp"
                        app:textEndPadding="8dp"
                        app:textStartPadding="8dp" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_checkpoint"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/type_checkpoint"
                        app:chipBackgroundColor="@color/colorCheckpoint"
                        app:chipIconTint="@android:color/white"
                        app:chipStrokeWidth="0dp"
                        app:textEndPadding="8dp"
                        app:textStartPadding="8dp" />

                </com.google.android.material.chip.ChipGroup>
            </HorizontalScrollView>

            <!-- Action Buttons -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_my_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:contentDescription="@string/my_location"
                app:layout_constraintBottom_toTopOf="@id/fab_add_location"
                app:layout_constraintEnd_toEndOf="parent"
                app:srcCompat="@android:drawable/ic_menu_mylocation"
                app:tint="@android:color/white" />

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/fab_add_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:contentDescription="@string/add_location"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:srcCompat="@android:drawable/ic_menu_add"
                app:tint="@android:color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>

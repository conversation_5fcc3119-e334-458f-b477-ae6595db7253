# خطة تنفيذ تحسينات نظام "يمن ناف"

## المرحلة الأولى: تحسين الأداء والبنية الأساسية (1-3 أشهر)

### 1. تحويل الموقع إلى تطبيق ويب تقدمي (PWA)

#### الخطوات التنفيذية:
1. **إنشاء ملف Service Worker**:
   ```javascript
   // public/service-worker.js
   const CACHE_NAME = 'yemen-nav-cache-v1';
   const urlsToCache = [
     '/',
     '/index.html',
     '/admin.html',
     '/css/styles.css',
     '/js/app.js',
     '/js/map.js',
     '/images/icons/'
   ];

   self.addEventListener('install', event => {
     event.waitUntil(
       caches.open(CACHE_NAME)
         .then(cache => cache.addAll(urlsToCache))
     );
   });

   self.addEventListener('fetch', event => {
     event.respondWith(
       caches.match(event.request)
         .then(response => response || fetch(event.request))
     );
   });
   ```

2. **إنشاء ملف Web App Manifest**:
   ```json
   // public/manifest.json
   {
     "name": "يمن ناف - نظام الملاحة اليمني",
     "short_name": "يمن ناف",
     "start_url": "/index.html",
     "display": "standalone",
     "background_color": "#ffffff",
     "theme_color": "#4CAF50",
     "icons": [
       {
         "src": "/images/icons/icon-72x72.png",
         "sizes": "72x72",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-96x96.png",
         "sizes": "96x96",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-128x128.png",
         "sizes": "128x128",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-144x144.png",
         "sizes": "144x144",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-152x152.png",
         "sizes": "152x152",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-192x192.png",
         "sizes": "192x192",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-384x384.png",
         "sizes": "384x384",
         "type": "image/png"
       },
       {
         "src": "/images/icons/icon-512x512.png",
         "sizes": "512x512",
         "type": "image/png"
       }
     ]
   }
   ```

3. **تعديل ملف index.html لتسجيل Service Worker**:
   ```html
   <script>
     if ('serviceWorker' in navigator) {
       window.addEventListener('load', () => {
         navigator.serviceWorker.register('/service-worker.js')
           .then(registration => {
             console.log('ServiceWorker registered: ', registration);
           })
           .catch(error => {
             console.log('ServiceWorker registration failed: ', error);
           });
       });
     }
   </script>
   ```

4. **إنشاء أيقونات التطبيق بأحجام مختلفة**

### 2. تحسين تخزين الخرائط المؤقت

#### الخطوات التنفيذية:
1. **تعديل Service Worker لتخزين بلاطات الخرائط**:
   ```javascript
   // تعديل service-worker.js
   const TILE_CACHE_NAME = 'yemen-nav-map-tiles-v1';
   
   // استراتيجية التخزين المؤقت للخرائط
   self.addEventListener('fetch', event => {
     const url = new URL(event.request.url);
     
     // التحقق مما إذا كان الطلب لبلاطات الخرائط
     if (url.pathname.includes('tile.openstreetmap.org') || 
         url.pathname.includes('server.arcgisonline.com') || 
         url.pathname.includes('stamen-tiles')) {
       
       event.respondWith(
         caches.open(TILE_CACHE_NAME).then(cache => {
           return cache.match(event.request).then(response => {
             return response || fetch(event.request).then(networkResponse => {
               cache.put(event.request, networkResponse.clone());
               return networkResponse;
             });
           });
         })
       );
     } else {
       // استراتيجية التخزين المؤقت العادية للموارد الأخرى
       event.respondWith(
         caches.match(event.request)
           .then(response => response || fetch(event.request))
       );
     }
   });
   
   // تنظيف التخزين المؤقت القديم
   self.addEventListener('activate', event => {
     event.waitUntil(
       caches.keys().then(cacheNames => {
         return Promise.all(
           cacheNames.filter(cacheName => {
             return cacheName.startsWith('yemen-nav-') && 
                   cacheName !== CACHE_NAME && 
                   cacheName !== TILE_CACHE_NAME;
           }).map(cacheName => {
             return caches.delete(cacheName);
           })
         );
       })
     );
   });
   ```

2. **إضافة وظيفة لتنزيل الخرائط للاستخدام دون اتصال**:
   ```javascript
   // public/js/offline-maps.js
   class OfflineMapManager {
     constructor(map) {
       this.map = map;
     }
     
     // تنزيل منطقة من الخريطة للاستخدام دون اتصال
     downloadRegion(bounds, zoomLevels) {
       const minZoom = zoomLevels.min || 10;
       const maxZoom = zoomLevels.max || 16;
       
       // الحصول على قائمة عناوين URL لبلاطات الخرائط ضمن الحدود المحددة
       const urls = this.getTileUrls(bounds, minZoom, maxZoom);
       
       // تنزيل البلاطات وتخزينها في التخزين المؤقت
       return this.cacheTiles(urls);
     }
     
     // الحصول على عناوين URL لبلاطات الخرائط
     getTileUrls(bounds, minZoom, maxZoom) {
       const urls = [];
       
       for (let zoom = minZoom; zoom <= maxZoom; zoom++) {
         const northEast = bounds.getNorthEast();
         const southWest = bounds.getSouthWest();
         
         const neTile = this.latLngToTile(northEast.lat, northEast.lng, zoom);
         const swTile = this.latLngToTile(southWest.lat, southWest.lng, zoom);
         
         for (let x = swTile.x; x <= neTile.x; x++) {
           for (let y = neTile.y; y <= swTile.y; y++) {
             urls.push(`https://a.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
             urls.push(`https://b.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
             urls.push(`https://c.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
           }
         }
       }
       
       return urls;
     }
     
     // تحويل الإحداثيات إلى رقم البلاطة
     latLngToTile(lat, lng, zoom) {
       const n = Math.pow(2, zoom);
       const x = Math.floor((lng + 180) / 360 * n);
       const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
       return { x, y };
     }
     
     // تخزين البلاطات في التخزين المؤقت
     cacheTiles(urls) {
       return caches.open('yemen-nav-map-tiles-v1').then(cache => {
         const fetchPromises = urls.map(url => {
           return fetch(url)
             .then(response => {
               if (response.ok) {
                 return cache.put(url, response);
               }
             })
             .catch(error => console.error('Error caching tile:', url, error));
         });
         
         return Promise.all(fetchPromises);
       });
     }
   }
   ```

### 3. تحسين خوارزميات حساب المسارات

#### الخطوات التنفيذية:
1. **تنفيذ خوارزمية A* لحساب المسارات**:
   ```javascript
   // public/js/routing/astar.js
   class AStarRouter {
     constructor(roadNetwork) {
       this.roadNetwork = roadNetwork;
     }
     
     // حساب المسار باستخدام خوارزمية A*
     findPath(startNode, endNode) {
       const openSet = new PriorityQueue();
       const closedSet = new Set();
       const cameFrom = new Map();
       
       const gScore = new Map();
       const fScore = new Map();
       
       gScore.set(startNode.id, 0);
       fScore.set(startNode.id, this.heuristic(startNode, endNode));
       
       openSet.enqueue(startNode, fScore.get(startNode.id));
       
       while (!openSet.isEmpty()) {
         const current = openSet.dequeue();
         
         if (current.id === endNode.id) {
           return this.reconstructPath(cameFrom, current);
         }
         
         closedSet.add(current.id);
         
         for (const neighbor of this.roadNetwork.getNeighbors(current)) {
           if (closedSet.has(neighbor.id)) {
             continue;
           }
           
           const tentativeGScore = gScore.get(current.id) + this.roadNetwork.getDistance(current, neighbor);
           
           if (!gScore.has(neighbor.id) || tentativeGScore < gScore.get(neighbor.id)) {
             cameFrom.set(neighbor.id, current);
             gScore.set(neighbor.id, tentativeGScore);
             fScore.set(neighbor.id, gScore.get(neighbor.id) + this.heuristic(neighbor, endNode));
             
             if (!openSet.contains(neighbor)) {
               openSet.enqueue(neighbor, fScore.get(neighbor.id));
             }
           }
         }
       }
       
       return null; // لم يتم العثور على مسار
     }
     
     // دالة التقدير (المسافة الإقليدية)
     heuristic(nodeA, nodeB) {
       const dx = nodeA.x - nodeB.x;
       const dy = nodeA.y - nodeB.y;
       return Math.sqrt(dx * dx + dy * dy);
     }
     
     // إعادة بناء المسار
     reconstructPath(cameFrom, current) {
       const path = [current];
       
       while (cameFrom.has(current.id)) {
         current = cameFrom.get(current.id);
         path.unshift(current);
       }
       
       return path;
     }
   }
   
   // صف الأولوية لخوارزمية A*
   class PriorityQueue {
     constructor() {
       this.elements = [];
     }
     
     isEmpty() {
       return this.elements.length === 0;
     }
     
     enqueue(element, priority) {
       this.elements.push({ element, priority });
       this.elements.sort((a, b) => a.priority - b.priority);
     }
     
     dequeue() {
       return this.elements.shift().element;
     }
     
     contains(element) {
       return this.elements.some(item => item.element.id === element.id);
     }
   }
   ```

2. **تنفيذ نموذج شبكة الطرق**:
   ```javascript
   // public/js/routing/road-network.js
   class RoadNetwork {
     constructor() {
       this.nodes = new Map();
       this.edges = new Map();
     }
     
     // إضافة عقدة (تقاطع أو نقطة على الطريق)
     addNode(id, lat, lng) {
       this.nodes.set(id, { id, lat, lng, x: lng, y: lat });
       this.edges.set(id, []);
       return this.nodes.get(id);
     }
     
     // إضافة حافة (طريق بين عقدتين)
     addEdge(fromId, toId, properties = {}) {
       const fromNode = this.nodes.get(fromId);
       const toNode = this.nodes.get(toId);
       
       if (!fromNode || !toNode) {
         throw new Error('Node not found');
       }
       
       const distance = this.calculateDistance(fromNode, toNode);
       const edge = {
         from: fromId,
         to: toId,
         distance,
         ...properties
       };
       
       this.edges.get(fromId).push(edge);
       
       // إذا كان الطريق ثنائي الاتجاه
       if (!properties.oneWay) {
         this.edges.get(toId).push({
           from: toId,
           to: fromId,
           distance,
           ...properties
         });
       }
     }
     
     // الحصول على العقد المجاورة
     getNeighbors(node) {
       const edges = this.edges.get(node.id) || [];
       return edges.map(edge => this.nodes.get(edge.to));
     }
     
     // حساب المسافة بين عقدتين
     getDistance(nodeA, nodeB) {
       const edges = this.edges.get(nodeA.id) || [];
       const edge = edges.find(e => e.to === nodeB.id);
       
       if (edge) {
         return edge.distance;
       }
       
       return this.calculateDistance(nodeA, nodeB);
     }
     
     // حساب المسافة الإقليدية بين عقدتين
     calculateDistance(nodeA, nodeB) {
       const R = 6371; // نصف قطر الأرض بالكيلومتر
       const dLat = (nodeB.lat - nodeA.lat) * Math.PI / 180;
       const dLon = (nodeB.lng - nodeA.lng) * Math.PI / 180;
       
       const a = 
         Math.sin(dLat/2) * Math.sin(dLat/2) +
         Math.cos(nodeA.lat * Math.PI / 180) * Math.cos(nodeB.lat * Math.PI / 180) * 
         Math.sin(dLon/2) * Math.sin(dLon/2);
       
       const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
       return R * c;
     }
     
     // البحث عن أقرب عقدة إلى إحداثيات معينة
     findNearestNode(lat, lng) {
       let nearestNode = null;
       let minDistance = Infinity;
       
       for (const node of this.nodes.values()) {
         const distance = this.calculateDistance(
           { lat, lng },
           { lat: node.lat, lng: node.lng }
         );
         
         if (distance < minDistance) {
           minDistance = distance;
           nearestNode = node;
         }
       }
       
       return nearestNode;
     }
     
     // تحميل بيانات الطرق من GeoJSON
     loadFromGeoJSON(geojson) {
       // إنشاء العقد
       for (const feature of geojson.features) {
         if (feature.geometry.type === 'Point') {
           const [lng, lat] = feature.geometry.coordinates;
           this.addNode(feature.properties.id, lat, lng);
         }
       }
       
       // إنشاء الحواف (الطرق)
       for (const feature of geojson.features) {
         if (feature.geometry.type === 'LineString') {
           const coordinates = feature.geometry.coordinates;
           
           for (let i = 0; i < coordinates.length - 1; i++) {
             const [lng1, lat1] = coordinates[i];
             const [lng2, lat2] = coordinates[i + 1];
             
             const fromNode = this.findNearestNode(lat1, lng1);
             const toNode = this.findNearestNode(lat2, lng2);
             
             if (fromNode && toNode) {
               this.addEdge(fromNode.id, toNode.id, {
                 roadType: feature.properties.type,
                 name: feature.properties.name,
                 oneWay: feature.properties.oneway === 'yes'
               });
             }
           }
         }
       }
     }
   }
   ```

3. **تنفيذ واجهة لحساب المسارات المتعددة**:
   ```javascript
   // public/js/routing/router.js
   class Router {
     constructor(roadNetwork) {
       this.roadNetwork = roadNetwork;
       this.astar = new AStarRouter(roadNetwork);
     }
     
     // حساب المسار بين نقطتين
     calculateRoute(startLat, startLng, endLat, endLng) {
       // البحث عن أقرب عقد إلى نقاط البداية والنهاية
       const startNode = this.roadNetwork.findNearestNode(startLat, startLng);
       const endNode = this.roadNetwork.findNearestNode(endLat, endLng);
       
       if (!startNode || !endNode) {
         throw new Error('Could not find nearest nodes');
       }
       
       // حساب المسار باستخدام خوارزمية A*
       const path = this.astar.findPath(startNode, endNode);
       
       if (!path) {
         throw new Error('Could not find a path');
       }
       
       // تحويل المسار إلى تنسيق GeoJSON
       return this.pathToGeoJSON(path);
     }
     
     // حساب مسارات بديلة
     calculateAlternativeRoutes(startLat, startLng, endLat, endLng, numAlternatives = 2) {
       const routes = [];
       
       // حساب المسار الرئيسي
       const mainRoute = this.calculateRoute(startLat, startLng, endLat, endLng);
       routes.push(mainRoute);
       
       // حساب المسارات البديلة
       for (let i = 0; i < numAlternatives; i++) {
         try {
           // تعديل شبكة الطرق مؤقتًا لتجنب بعض الطرق في المسار الرئيسي
           const mo

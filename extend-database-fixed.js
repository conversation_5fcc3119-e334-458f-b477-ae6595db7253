/**
 * سكربت توسيع قاعدة بيانات Yemen GPS
 * 
 * هذا السكربت يقوم بإضافة جداول وبيانات إضافية لقاعدة بيانات Yemen GPS
 * يعتمد على وجود قاعدة البيانات الأساسية التي تم إنشاؤها بواسطة setup-database.js
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

/**
 * دالة لإنشاء جداول إضافية في قاعدة البيانات
 */
async function createAdditionalTables() {
    const client = new Client(dbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء جداول إضافية...');
        await client.connect();
        
        console.log('جاري إنشاء الجداول الإضافية...');
        
        // إنشاء جدول إعدادات النظام
        await client.query(`
            CREATE TABLE IF NOT EXISTS system_settings (
                id SERIAL PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول إعدادات النظام.');
        
        // إنشاء جدول الإشعارات
        await client.query(`
            CREATE TABLE IF NOT EXISTS notifications (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                title VARCHAR(100) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(20) DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                related_entity VARCHAR(50),
                related_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الإشعارات.');
        
        // إنشاء جدول تاريخ المواقع (للتتبع)
        await client.query(`
            CREATE TABLE IF NOT EXISTS location_history (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                action VARCHAR(20) NOT NULL,
                old_data JSONB,
                new_data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول تاريخ المواقع.');
        
        // إنشاء جدول صور المواقع
        await client.query(`
            CREATE TABLE IF NOT EXISTS location_images (
                id SERIAL PRIMARY KEY,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                image_url VARCHAR(255) NOT NULL,
                caption TEXT,
                is_primary BOOLEAN DEFAULT FALSE,
                uploaded_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول صور المواقع.');
        
        // إنشاء جدول الأجهزة
        await client.query(`
            CREATE TABLE IF NOT EXISTS devices (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                device_id VARCHAR(100) NOT NULL,
                device_type VARCHAR(50),
                device_name VARCHAR(100),
                device_model VARCHAR(100),
                push_token VARCHAR(255),
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, device_id)
            );
        `);
        console.log('تم إنشاء جدول الأجهزة.');
        
        // إنشاء جدول الجلسات
        await client.query(`
            CREATE TABLE IF NOT EXISTS sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                token VARCHAR(255) UNIQUE NOT NULL,
                device_id VARCHAR(100),
                ip_address VARCHAR(45),
                user_agent TEXT,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الجلسات.');
        
        // إنشاء جدول المناطق الجغرافية
        await client.query(`
            CREATE TABLE IF NOT EXISTS geo_areas (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(20) NOT NULL, -- city, district, neighborhood
                parent_id INTEGER REFERENCES geo_areas(id) ON DELETE SET NULL,
                boundaries JSONB,
                center_lat DECIMAL(10, 8),
                center_lng DECIMAL(11, 8),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول المناطق الجغرافية.');
        
        // إنشاء جدول الإعلانات
        await client.query(`
            CREATE TABLE IF NOT EXISTS advertisements (
                id SERIAL PRIMARY KEY,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                image_url VARCHAR(255),
                link_url VARCHAR(255),
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP NOT NULL,
                status VARCHAR(20) DEFAULT 'active',
                position VARCHAR(50),
                client_id INTEGER REFERENCES clients(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الإعلانات.');
        
        // إنشاء جدول الأحداث
        await client.query(`
            CREATE TABLE IF NOT EXISTS events (
                id SERIAL PRIMARY KEY,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                location_id INTEGER REFERENCES locations(id) ON DELETE SET NULL,
                custom_location TEXT,
                start_date TIMESTAMP NOT NULL,
                end_date TIMESTAMP,
                organizer VARCHAR(100),
                contact_info TEXT,
                image_url VARCHAR(255),
                status VARCHAR(20) DEFAULT 'upcoming',
                created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الأحداث.');
        
        console.log('تم إنشاء جميع الجداول الإضافية بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الجداول الإضافية:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء الفهارس (Indexes) لتحسين أداء قاعدة البيانات
 */
async function createIndexes() {
    const client = new Client(dbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء الفهارس...');
        await client.connect();
        
        console.log('جاري إنشاء الفهارس...');
        
        // إنشاء فهارس للبحث السريع
        await client.query(`
            -- فهارس لجدول المستخدمين
            CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
            CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
            
            -- فهارس لجدول المواقع
            CREATE INDEX IF NOT EXISTS idx_locations_name ON locations(name);
            CREATE INDEX IF NOT EXISTS idx_locations_category_id ON locations(category_id);
            CREATE INDEX IF NOT EXISTS idx_locations_status ON locations(status);
            CREATE INDEX IF NOT EXISTS idx_locations_coords ON locations(lat, lng);
            
            -- فهارس لجدول التصنيفات
            CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
            
            -- فهارس لجدول الإشعارات
            CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
            CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
            
            -- فهارس لجدول الجلسات
            CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
            CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token);
            CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
            
            -- فهارس لجدول المناطق الجغرافية
            CREATE INDEX IF NOT EXISTS idx_geo_areas_parent_id ON geo_areas(parent_id);
            CREATE INDEX IF NOT EXISTS idx_geo_areas_type ON geo_areas(type);
            
            -- فهارس لجدول الأحداث
            CREATE INDEX IF NOT EXISTS idx_events_start_date ON events(start_date);
            CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
        `);
        
        console.log('تم إنشاء جميع الفهارس بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الفهارس:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء الدوال والإجراءات المخزنة في قاعدة البيانات
 */
async function createStoredProcedures() {
    const client = new Client(dbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإنشاء الإجراءات المخزنة...');
        await client.connect();
        
        console.log('جاري إنشاء الدوال والإجراءات المخزنة...');
        
        // دالة لتحديث وقت التعديل تلقائياً
        await client.query(`
            -- دالة لتحديث وقت التعديل تلقائياً
            CREATE OR REPLACE FUNCTION update_modified_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);
        
        // إنشاء المشغلات (Triggers) لتحديث وقت التعديل تلقائياً
        await client.query(`
            -- مشغل لتحديث وقت التعديل في جدول المستخدمين
            DROP TRIGGER IF EXISTS update_users_modtime ON users;
            CREATE TRIGGER update_users_modtime
            BEFORE UPDATE ON users
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
            
            -- مشغل لتحديث وقت التعديل في جدول المواقع
            DROP TRIGGER IF EXISTS update_locations_modtime ON locations;
            CREATE TRIGGER update_locations_modtime
            BEFORE UPDATE ON locations
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
            
            -- مشغل لتحديث وقت التعديل في جدول العملاء
            DROP TRIGGER IF EXISTS update_clients_modtime ON clients;
            CREATE TRIGGER update_clients_modtime
            BEFORE UPDATE ON clients
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
            
            -- مشغل لتحديث وقت التعديل في جدول إعدادات النظام
            DROP TRIGGER IF EXISTS update_system_settings_modtime ON system_settings;
            CREATE TRIGGER update_system_settings_modtime
            BEFORE UPDATE ON system_settings
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
            
            -- مشغل لتحديث وقت التعديل في جدول الإعلانات
            DROP TRIGGER IF EXISTS update_advertisements_modtime ON advertisements;
            CREATE TRIGGER update_advertisements_modtime
            BEFORE UPDATE ON advertisements
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
            
            -- مشغل لتحديث وقت التعديل في جدول الأحداث
            DROP TRIGGER IF EXISTS update_events_modtime ON events;
            CREATE TRIGGER update_events_modtime
            BEFORE UPDATE ON events
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();
        `);
        
        // دالة لتسجيل تغييرات المواقع
        await client.query(`
            -- دالة لتسجيل تغييرات المواقع
            CREATE OR REPLACE FUNCTION log_location_changes()
            RETURNS TRIGGER AS $$
            BEGIN
                IF (TG_OP = 'UPDATE') THEN
                    INSERT INTO location_history (location_id, action, old_data, new_data)
                    VALUES (NEW.id, 'update', row_to_json(OLD), row_to_json(NEW));
                    RETURN NEW;
                ELSIF (TG_OP = 'DELETE') THEN
                    INSERT INTO location_history (location_id, action, old_data)
                    VALUES (OLD.id, 'delete', row_to_json(OLD));
                    RETURN OLD;
                ELSIF (TG_OP = 'INSERT') THEN
                    INSERT INTO location_history (location_id, action, new_data)
                    VALUES (NEW.id, 'insert', row_to_json(NEW));
                    RETURN NEW;
                END IF;
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;
            
            -- مشغل لتسجيل تغييرات المواقع
            DROP TRIGGER IF EXISTS log_location_changes_trigger ON locations;
            CREATE TRIGGER log_location_changes_trigger
            AFTER INSERT OR UPDATE OR DELETE ON locations
            FOR EACH ROW
            EXECUTE FUNCTION log_location_changes();
        `);
        
        console.log('تم إنشاء جميع الدوال والإجراءات المخزنة بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الإجراءات المخزنة:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإدخال بيانات إضافية في الجداول
 */
async function insertAdditionalData() {
    const client = new Client(dbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإدخال بيانات إضافية...');
        await client.connect();
        
        console.log('جاري إدخال البيانات الإضافية...');
        
        // إدخال بيانات إعدادات النظام
        await client.query(`
            INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public)
            VALUES 
                ('site_name', 'Yemen GPS', 'string', 'اسم الموقع', true),
                ('site_description', 'نظام تحديد المواقع اليمني', 'string', 'وصف الموقع', true),
                ('contact_email', '<EMAIL>', 'string', 'البريد الإلكتروني للتواصل', true),
                ('contact_phone', '+967 123456789', 'string', 'رقم الهاتف للتواصل', true),
                ('max_locations_per_user', '10', 'integer', 'الحد الأقصى لعدد المواقع لكل مستخدم', false),
                ('enable_registration', 'true', 'boolean', 'تفعيل التسجيل للمستخدمين الجدد', false),
                ('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', false),
                ('api_rate_limit', '100', 'integer', 'الحد الأقصى لعدد طلبات API لكل ساعة', false),
                ('default_location_lat', '15.3694', 'float', 'خط العرض الافتراضي للخريطة', true),
                ('default_location_lng', '44.1910', 'float', 'خط الطول الافتراضي للخريطة', true),
                ('default_zoom', '12', 'integer', 'مستوى التكبير الافتراضي للخريطة', true)
            ON CONFLICT (setting_key) DO UPDATE 
            SET setting_value = EXCLUDED.setting_value,
                description = EXCLUDED.description,
                is_public = EXCLUDED.is_public;
        `);
        console.log('تم إدخال بيانات إعدادات النظام.');
        
        // إدخال بيانات المناطق الجغرافية
        await client.query(`
            INSERT INTO geo_areas (name, type, center_lat, center_lng)
            VALUES 
                ('صنعاء', 'city', 15.3694, 44.1910),
                ('عدن', 'city', 12.7797, 45.0095),
                ('تعز', 'city', 13.5789, 44.0178),
                ('الحديدة', 'city', 14.7970, 42.9532),
                ('المكلا', 'city', 14.5393, 49.1275)
            ON CONFLICT DO NOTHING;
        `);
        console.log('تم إدخال بيانات المناطق الجغرافية.');
        
        console.log('تم إدخال جميع البيانات الإضافية بنجاح!');
    } catch (err) {
        console.error('خطأ في إدخال البيانات الإضافية:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء نسخة احتياطية من قاعدة البيانات
 */
async function backupDatabase() {
    const client = new Client(dbConfig);
    
    try {
        console.log('جاري إنشاء نسخة احتياطية من قاعدة البيانات...');
        
        // إنشاء مجلد للنسخ الاحتياطية إذا لم يكن موجوداً
        const backupDir = path.join(__dirname, 'backups');
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir);
        }
        
        // إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت الحالي
        const now = new Date();
        const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}-${now.getMinutes().toString().padStart(2, '0')}`;
        const backupFileName = `yemen_gps_backup_${timestamp}.sql`;
        const backupFilePath = path.join(backupDir, backupFileName);
        
        // لا يمكن إنشاء نسخة احتياطية مباشرة من خلال Node.js
        // نحتاج استخدام أمر pg_dump من خلال تنفيذ أمر في نظام التشغيل
        // هذا مثال فقط، ولا يمكن تنفيذه مباشرة من هنا
        console.log(`لإنشاء نسخة احتياطية، يرجى تنفيذ الأمر التالي في سطر الأوامر:`);
        console.log(`pg_dump -U ${dbConfig.user} -h ${dbConfig.host} -p ${dbConfig.port} -d ${dbConfig.database} -f "${backupFilePath}"`);
        
        console.log(`سيتم حفظ النسخة الاحتياطية في: ${backupFilePath}`);
    } catch (err) {
        console.error('خطأ في إنشاء نسخة احتياطية:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * الدالة الرئيسية لتنفيذ جميع الخطوات
 */
async function extendDatabase() {
    try {
        console.log('بدء توسيع قاعدة البيانات Yemen GPS...');
        
        // إنشاء الجداول الإضافية
        await createAdditionalTables();
        
        // إنشاء الفهارس
        await createIndexes();
        
        // إنشاء الدوال والإجراءات المخزنة
        await createStoredProcedures();
        
        // إدخال البيانات الإضافية
        await insertAdditionalData();
        
        // إنشاء نسخة احتياطية
        await backupDatabase();
        
        console.log('تم توسيع قاعدة البيانات Yemen GPS بنجاح!');
    } catch (err) {
        console.error('حدث خطأ أثناء توسيع قاعدة البيانات:', err);
    }
}

// تنفيذ الدالة الرئيسية
extendDatabase();

// service-worker.js
const CACHE_NAME = 'yemen-gps-cache-v1';
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/css/styles.css',
  '/css/enhanced-maps.css',
  '/js/enhanced-offline-maps.js',
  '/js/enhanced-navigation.js',
  '/js/location-details.js',
  '/images/icons/default.png',
  '/images/icons/restaurant.png',
  '/images/icons/hotel.png',
  '/images/icons/hospital.png',
  '/images/icons/school.png',
  '/images/icons/mosque.png',
  '/images/icons/shop.png',
  '/images/icons/cafe.png',
  '/images/icons/bank.png',
  '/images/icons/gas_station.png',
  '/images/icons/pharmacy.png',
  '/images/icons/destination-marker.png',
  '/images/icons/user-marker.png',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
  'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
  'https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js',
  'https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.min.js'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// تنشيط Service Worker
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName !== CACHE_NAME;
        }).map(cacheName => {
          return caches.delete(cacheName);
        })
      );
    }).then(() => self.clients.claim())
  );
});

// استراتيجية الشبكة أولاً ثم التخزين المؤقت
self.addEventListener('fetch', event => {
  // تجاهل طلبات POST
  if (event.request.method !== 'GET') return;
  
  // تجاهل طلبات API
  if (event.request.url.includes('/api/')) {
    // استراتيجية الشبكة مع التخزين المؤقت للنسخة الاحتياطية
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // نسخة من الاستجابة للتخزين المؤقت
          const responseToCache = response.clone();
          
          caches.open(CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseToCache);
            });
          
          return response;
        })
        .catch(() => {
          return caches.match(event.request);
        })
    );
    return;
  }
  
  // استراتيجية التخزين المؤقت أولاً ثم الشبكة للأصول الثابتة
  if (
    event.request.url.match(/\.(js|css|png|jpg|jpeg|svg|gif|ico|woff|woff2|ttf|eot)$/) ||
    STATIC_ASSETS.includes(new URL(event.request.url).pathname)
  ) {
    event.respondWith(
      caches.match(event.request)
        .then(response => {
          return response || fetch(event.request)
            .then(fetchResponse => {
              // نسخة من الاستجابة للتخزين المؤقت
              const responseToCache = fetchResponse.clone();
              
              caches.open(CACHE_NAME)
                .then(cache => {
                  cache.put(event.request, responseToCache);
                });
              
              return fetchResponse;
            });
        })
    );
    return;
  }
  
  // استراتيجية الشبكة أولاً ثم التخزين المؤقت لباقي الطلبات
  event.respondWith(
    fetch(event.request)
      .then(response => {
        // التحقق من أن الاستجابة صالحة للتخزين المؤقت
        // تجنب تخزين الاستجابات الجزئية (status code 206)
        if (response.ok && response.status !== 206) {
          // نسخة من الاستجابة للتخزين المؤقت
          const responseToCache = response.clone();
          
          caches.open(CACHE_NAME)
            .then(cache => {
              try {
                cache.put(event.request, responseToCache);
              } catch (error) {
                console.error('خطأ في تخزين الاستجابة في ذاكرة التخزين المؤقت:', error);
              }
            });
        }
        
        return response;
      })
      .catch(() => {
        return caches.match(event.request);
      })
  );
});

// معالجة رسائل من الصفحة
self.addEventListener('message', event => {
  if (event.data.action === 'skipWaiting') {
    self.skipWaiting();
  }
});

// تحديث التخزين المؤقت للبلاطات
self.addEventListener('sync', event => {
  if (event.tag === 'sync-map-tiles') {
    event.waitUntil(syncMapTiles());
  }
});

// وظيفة مزامنة بلاطات الخريطة
async function syncMapTiles() {
  try {
    const cache = await caches.open(CACHE_NAME);
    const requests = await cache.keys();
    
    // تحديث البلاطات المخزنة مؤقتاً
    const tileRequests = requests.filter(request => 
      request.url.includes('tile.openstreetmap.org') || 
      request.url.includes('mt1.google.com/vt/lyrs=m')
    );
    
    return Promise.all(
      tileRequests.map(async request => {
        try {
          const response = await fetch(request);
          return cache.put(request, response);
        } catch (error) {
          console.error('خطأ في تحديث بلاطة الخريطة:', error);
          return Promise.resolve();
        }
      })
    );
  } catch (error) {
    console.error('خطأ في مزامنة بلاطات الخريطة:', error);
    return Promise.resolve();
  }
}

/**
 * Yemen GPS - إدارة الأحداث
 * ملف JavaScript لإدارة الأحداث في لوحة التحكم
 */

// المتغيرات العامة
const API_BASE_URL = 'http://localhost:3000/api';
let allEvents = [];
let filteredEvents = [];
let allLocations = [];

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات
    loadEvents();
    loadLocations();
    
    // إضافة مستمعي الأحداث
    document.getElementById('event-search').addEventListener('input', filterEvents);
    document.getElementById('event-form').addEventListener('submit', saveEvent);
    
    // تهيئة الوضع المتجاوب للشاشات الصغيرة
    initResponsiveLayout();
});

/**
 * تهيئة الوضع المتجاوب للشاشات الصغيرة
 */
function initResponsiveLayout() {
    const toggleButton = document.querySelector('.toggle-sidebar');
    
    if (window.innerWidth <= 768) {
        document.querySelector('.sidebar').classList.remove('active');
        document.querySelector('.main-content').style.marginRight = '0';
        toggleButton.style.display = 'block';
    } else {
        document.querySelector('.sidebar').classList.add('active');
        document.querySelector('.main-content').style.marginRight = '250px';
        toggleButton.style.display = 'none';
    }
    
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar').classList.remove('active');
            document.querySelector('.main-content').style.marginRight = '0';
            toggleButton.style.display = 'block';
        } else {
            document.querySelector('.sidebar').classList.add('active');
            document.querySelector('.main-content').style.marginRight = '250px';
            toggleButton.style.display = 'none';
        }
    });
}

/**
 * تبديل حالة القائمة الجانبية
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('active');
    
    if (sidebar.classList.contains('active')) {
        mainContent.style.marginRight = '250px';
    } else {
        mainContent.style.marginRight = '0';
    }
}

/**
 * تحميل الأحداث
 */
function loadEvents() {
    showLoading();
    
    apiRequest('/admin/events')
        .then(data => {
            allEvents = data;
            filteredEvents = [...allEvents];
            displayEvents(filteredEvents);
            updateEventStats();
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل الأحداث:', error);
            document.getElementById('events-error').textContent = 'حدث خطأ أثناء تحميل بيانات الأحداث: ' + error.message;
            document.getElementById('events-error').style.display = 'block';
            hideLoading();
        });
}

/**
 * تحميل المواقع
 */
function loadLocations() {
    apiRequest('/admin/locations')
        .then(data => {
            allLocations = data;
            
            // ملء قائمة المواقع في نموذج إضافة/تعديل الحدث
            const locationSelect = document.getElementById('event-location');
            locationSelect.innerHTML = '<option value="">-- اختر الموقع --</option>';
            
            allLocations.forEach(location => {
                const option = document.createElement('option');
                option.value = location.id;
                option.textContent = location.name;
                locationSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل المواقع:', error);
        });
}

/**
 * عرض الأحداث في الجدول
 */
function displayEvents(events) {
    const tableBody = document.getElementById('events-table-body');
    tableBody.innerHTML = '';
    
    if (events.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد أحداث للعرض</td></tr>';
        return;
    }
    
    events.forEach(event => {
        const row = document.createElement('tr');
        
        // تحديد حالة الحدث
        let statusText = 'قادم';
        let statusClass = 'status-upcoming';
        
        if (event.status === 'ongoing') {
            statusText = 'جاري';
            statusClass = 'status-ongoing';
        } else if (event.status === 'completed') {
            statusText = 'منتهي';
            statusClass = 'status-completed';
        } else if (event.status === 'cancelled') {
            statusText = 'ملغي';
            statusClass = 'status-cancelled';
        }
        
        // العثور على اسم الموقع
        let locationName = event.custom_location || '-';
        if (event.location_id) {
            const location = allLocations.find(loc => loc.id === event.location_id);
            if (location) {
                locationName = location.name;
            }
        }
        
        // تنسيق تاريخ البدء والانتهاء
        const startDate = new Date(event.start_date);
        const formattedStartDate = startDate.toLocaleString('ar-SA');
        
        let formattedEndDate = '-';
        if (event.end_date) {
            const endDate = new Date(event.end_date);
            formattedEndDate = endDate.toLocaleString('ar-SA');
        }
        
        row.innerHTML = `
            <td>${event.id}</td>
            <td>${event.title}</td>
            <td>${locationName}</td>
            <td>${formattedStartDate}</td>
            <td>${formattedEndDate}</td>
            <td>${event.organizer || '-'}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="editEvent(${event.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteEvent(${event.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    updateEventStats();
}

/**
 * تحديث إحصائيات الأحداث
 */
function updateEventStats() {
    document.getElementById('total-events').textContent = allEvents.length;
    document.getElementById('displayed-events').textContent = filteredEvents.length;
}

/**
 * تصفية الأحداث حسب المعايير المحددة
 */
function filterEvents() {
    const searchTerm = document.getElementById('event-search').value.trim().toLowerCase();
    const statusFilter = document.getElementById('event-status-filter').value;
    
    // إظهار/إخفاء زر مسح البحث
    document.getElementById('clear-event-search').style.display = searchTerm ? 'block' : 'none';
    
    filteredEvents = allEvents.filter(event => {
        // تصفية حسب نص البحث
        const matchesSearch = 
            event.title.toLowerCase().includes(searchTerm) ||
            (event.description && event.description.toLowerCase().includes(searchTerm)) ||
            (event.organizer && event.organizer.toLowerCase().includes(searchTerm)) ||
            (event.custom_location && event.custom_location.toLowerCase().includes(searchTerm));
        
        // تصفية حسب الحالة
        const matchesStatus = statusFilter === 'all' || event.status === statusFilter;
        
        return matchesSearch && matchesStatus;
    });
    
    displayEvents(filteredEvents);
}

/**
 * مسح البحث
 */
function clearEventSearch() {
    document.getElementById('event-search').value = '';
    filterEvents();
}

/**
 * عرض نافذة إضافة حدث جديد
 */
function showAddEventModal() {
    // تغيير عنوان النافذة
    document.getElementById('event-modal-title').textContent = 'إضافة حدث جديد';
    
    // إعادة تعيين النموذج
    document.getElementById('event-form').reset();
    document.getElementById('event-id').value = '';
    
    // تعيين الحالة الافتراضية
    document.getElementById('event-status').value = 'upcoming';
    
    // عرض النافذة
    document.getElementById('event-modal').style.display = 'block';
}

/**
 * عرض نافذة تعديل حدث
 */
function editEvent(eventId) {
    // البحث عن الحدث
    const event = allEvents.find(evt => evt.id === eventId);
    
    if (!event) {
        alert('لم يتم العثور على الحدث');
        return;
    }
    
    // تغيير عنوان النافذة
    document.getElementById('event-modal-title').textContent = 'تعديل الحدث';
    
    // تنسيق التواريخ لحقول التاريخ والوقت
    const startDate = new Date(event.start_date);
    const formattedStartDate = startDate.toISOString().slice(0, 16);
    
    let formattedEndDate = '';
    if (event.end_date) {
        const endDate = new Date(event.end_date);
        formattedEndDate = endDate.toISOString().slice(0, 16);
    }
    
    // ملء النموذج ببيانات الحدث
    document.getElementById('event-id').value = event.id;
    document.getElementById('event-title').value = event.title;
    document.getElementById('event-description').value = event.description || '';
    document.getElementById('event-location').value = event.location_id || '';
    document.getElementById('event-custom-location').value = event.custom_location || '';
    document.getElementById('event-start-date').value = formattedStartDate;
    document.getElementById('event-end-date').value = formattedEndDate;
    document.getElementById('event-organizer').value = event.organizer || '';
    document.getElementById('event-contact').value = event.contact_info || '';
    document.getElementById('event-image').value = event.image_url || '';
    document.getElementById('event-status').value = event.status || 'upcoming';
    
    // عرض النافذة
    document.getElementById('event-modal').style.display = 'block';
}

/**
 * إغلاق نافذة إضافة/تعديل الحدث
 */
function closeEventModal() {
    document.getElementById('event-modal').style.display = 'none';
}

/**
 * حفظ بيانات الحدث (إضافة/تعديل)
 */
function saveEvent(event) {
    event.preventDefault();
    
    // جمع بيانات النموذج
    const eventId = document.getElementById('event-id').value;
    const eventData = {
        title: document.getElementById('event-title').value,
        description: document.getElementById('event-description').value || null,
        location_id: document.getElementById('event-location').value || null,
        custom_location: document.getElementById('event-custom-location').value || null,
        start_date: document.getElementById('event-start-date').value,
        end_date: document.getElementById('event-end-date').value || null,
        organizer: document.getElementById('event-organizer').value || null,
        contact_info: document.getElementById('event-contact').value || null,
        image_url: document.getElementById('event-image').value || null,
        status: document.getElementById('event-status').value
    };
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // تحديد ما إذا كانت عملية إضافة أو تعديل
    const isUpdate = eventId !== '';
    const url = isUpdate ? `/admin/events/${eventId}` : '/admin/events';
    const method = isUpdate ? 'PUT' : 'POST';
    
    // إرسال البيانات إلى الخادم
    apiRequest(url, {
        method: method,
        body: JSON.stringify(eventData)
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النافذة
        closeEventModal();
        
        // إظهار رسالة نجاح
        alert(isUpdate ? 'تم تحديث الحدث بنجاح' : 'تم إضافة الحدث بنجاح');
        
        // إعادة تحميل الأحداث
        loadEvents();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حفظ الحدث: ' + error.message);
    });
}

/**
 * حذف حدث
 */
function deleteEvent(eventId) {
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الحدث؟')) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال طلب الحذف إلى الخادم
    apiRequest(`/admin/events/${eventId}`, {
        method: 'DELETE'
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة نجاح
        alert('تم حذف الحدث بنجاح');
        
        // إعادة تحميل الأحداث
        loadEvents();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حذف الحدث: ' + error.message);
    });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    document.getElementById('events-loading').style.display = 'flex';
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    document.getElementById('events-loading').style.display = 'none';
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات الجلسة من تخزين الجلسة
    sessionStorage.removeItem('yemenNavToken');
    sessionStorage.removeItem('yemenNavCurrentUser');
    sessionStorage.removeItem('yemenNavLoggedIn');
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + sessionStorage.getItem('yemenNavToken')
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = `${API_BASE_URL}${endpoint}`;
    }
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        });
}

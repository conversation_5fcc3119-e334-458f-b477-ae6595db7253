// تثبيت المكتبات المطلوبة لسحب البيانات
// Install Required Dependencies for Data Scraping

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📦 تثبيت المكتبات المطلوبة لسحب البيانات من Google Maps...\n');

// قائمة المكتبات المطلوبة
const dependencies = [
    'puppeteer',           // للتحكم في المتصفح
    'axios',               // لطلبات HTTP
    'cheerio',             // لتحليل HTML
    'playwright',          // بديل لـ Puppeteer
    'selenium-webdriver',  // بديل آخر للتحكم في المتصفح
    'node-html-parser',    // لتحليل HTML
    'sharp',               // لمعالجة الصور
    'jimp',                // لمعالجة الصور
    'request',             // لتحميل الملفات
    'fs-extra'             // لعمليات الملفات المتقدمة
];

// تثبيت المكتبات
function installDependencies() {
    try {
        console.log('🔄 تثبيت المكتبات...');
        
        const installCommand = `npm install ${dependencies.join(' ')}`;
        console.log(`تنفيذ: ${installCommand}\n`);
        
        execSync(installCommand, { stdio: 'inherit' });
        
        console.log('\n✅ تم تثبيت جميع المكتبات بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في تثبيت المكتبات:', error.message);
        console.log('\n💡 جرب تثبيت المكتبات يدوياً:');
        dependencies.forEach(dep => {
            console.log(`   npm install ${dep}`);
        });
    }
}

// إنشاء مجلدات البيانات
function createDirectories() {
    const directories = [
        '../data',
        '../public/images/places',
        '../scripts/output'
    ];
    
    directories.forEach(dir => {
        const fullPath = path.join(__dirname, dir);
        if (!fs.existsSync(fullPath)) {
            fs.mkdirSync(fullPath, { recursive: true });
            console.log(`📁 تم إنشاء مجلد: ${fullPath}`);
        }
    });
}

// إنشاء ملف التكوين
function createConfigFile() {
    const configPath = path.join(__dirname, 'scraper-config.json');
    
    const config = {
        "google_api_key": "YOUR_GOOGLE_PLACES_API_KEY",
        "scraping_settings": {
            "delay_between_requests": 2000,
            "max_places_per_search": 20,
            "max_images_per_place": 5,
            "timeout": 30000
        },
        "yemen_cities": [
            {
                "name_ar": "صنعاء",
                "name_en": "Sanaa",
                "lat": 15.3547,
                "lng": 44.2066
            },
            {
                "name_ar": "عدن", 
                "name_en": "Aden",
                "lat": 12.7797,
                "lng": 45.0365
            },
            {
                "name_ar": "تعز",
                "name_en": "Taiz", 
                "lat": 13.5795,
                "lng": 44.0205
            },
            {
                "name_ar": "الحديدة",
                "name_en": "Al Hudaydah",
                "lat": 14.7978,
                "lng": 42.9545
            },
            {
                "name_ar": "إب",
                "name_en": "Ibb",
                "lat": 13.9667,
                "lng": 44.1833
            }
        ],
        "place_categories": [
            {
                "name_ar": "فنادق",
                "name_en": "Hotels",
                "google_type": "lodging"
            },
            {
                "name_ar": "مطاعم",
                "name_en": "Restaurants", 
                "google_type": "restaurant"
            },
            {
                "name_ar": "معالم سياحية",
                "name_en": "Tourist Attractions",
                "google_type": "tourist_attraction"
            },
            {
                "name_ar": "مستشفيات",
                "name_en": "Hospitals",
                "google_type": "hospital"
            },
            {
                "name_ar": "جامعات",
                "name_en": "Universities",
                "google_type": "university"
            },
            {
                "name_ar": "مراكز تسوق",
                "name_en": "Shopping Malls",
                "google_type": "shopping_mall"
            },
            {
                "name_ar": "بنوك",
                "name_en": "Banks",
                "google_type": "bank"
            },
            {
                "name_ar": "مساجد",
                "name_en": "Mosques",
                "google_type": "mosque"
            },
            {
                "name_ar": "متاحف",
                "name_en": "Museums",
                "google_type": "museum"
            },
            {
                "name_ar": "حدائق",
                "name_en": "Parks",
                "google_type": "park"
            }
        ]
    };
    
    if (!fs.existsSync(configPath)) {
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8');
        console.log(`⚙️ تم إنشاء ملف التكوين: ${configPath}`);
    }
}

// إنشاء ملف README للتعليمات
function createReadme() {
    const readmePath = path.join(__dirname, 'README.md');
    
    const readmeContent = `# سحب بيانات الأماكن من Google Maps

## الطرق المتاحة:

### 1. استخدام Google Places API (الطريقة الرسمية)
\`\`\`bash
node google-places-scraper.js
\`\`\`

**المتطلبات:**
- مفتاح Google Places API
- حساب Google Cloud Platform
- تفعيل Places API

**المميزات:**
- بيانات دقيقة ومحدثة
- صور عالية الجودة
- تفاصيل شاملة
- قانوني ومدعوم رسمياً

### 2. Web Scraping (بدون API)
\`\`\`bash
node google-maps-web-scraper.js
\`\`\`

**المتطلبات:**
- مكتبة Puppeteer
- متصفح Chrome/Chromium

**المميزات:**
- مجاني تماماً
- لا يحتاج مفاتيح API
- يمكن تخصيصه بسهولة

**تحذيرات:**
- قد يكون بطيئاً
- عرضة للتغييرات في واجهة Google Maps
- يجب احترام شروط الاستخدام

## خطوات الإعداد:

### 1. الحصول على مفتاح Google Places API:
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Places API
4. أنشئ مفتاح API
5. ضع المفتاح في ملف \`scraper-config.json\`

### 2. تشغيل السكريبت:
\`\`\`bash
# تثبيت المكتبات
npm install

# تشغيل سحب البيانات بـ API
node google-places-scraper.js

# أو تشغيل Web Scraping
node google-maps-web-scraper.js
\`\`\`

### 3. النتائج:
- البيانات الخام: \`../data/\`
- الصور: \`../public/images/places/\`
- ملفات SQL: \`../database/\`

## نصائح مهمة:

1. **احترم حدود الاستخدام** لتجنب الحظر
2. **استخدم تأخير مناسب** بين الطلبات
3. **احفظ البيانات بانتظام** لتجنب فقدانها
4. **تحقق من جودة البيانات** قبل الاستيراد
5. **احترم شروط الخدمة** لـ Google Maps

## استكشاف الأخطاء:

### خطأ في API:
- تحقق من صحة المفتاح
- تأكد من تفعيل Places API
- تحقق من الحصة المتاحة

### خطأ في Web Scraping:
- تحقق من تثبيت Puppeteer
- جرب تشغيل المتصفح يدوياً
- تحقق من اتصال الإنترنت

## الدعم:
للمساعدة أو الاستفسارات، راجع الوثائق أو اتصل بفريق التطوير.
`;

    if (!fs.existsSync(readmePath)) {
        fs.writeFileSync(readmePath, readmeContent, 'utf8');
        console.log(`📖 تم إنشاء ملف التعليمات: ${readmePath}`);
    }
}

// تشغيل الإعداد
function main() {
    console.log('🚀 بدء إعداد أدوات سحب البيانات...\n');
    
    try {
        createDirectories();
        createConfigFile();
        createReadme();
        installDependencies();
        
        console.log('\n🎉 تم الإعداد بنجاح!');
        console.log('\n📋 الخطوات التالية:');
        console.log('1. احصل على مفتاح Google Places API');
        console.log('2. ضع المفتاح في ملف scraper-config.json');
        console.log('3. شغّل السكريبت: node google-places-scraper.js');
        console.log('\n📖 راجع ملف README.md للتعليمات التفصيلية');
        
    } catch (error) {
        console.error('❌ خطأ في الإعداد:', error.message);
    }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    main();
}

module.exports = {
    installDependencies,
    createDirectories,
    createConfigFile,
    createReadme
};

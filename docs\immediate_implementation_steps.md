# الخطوات الفورية لبدء تنفيذ تحسينات نظام "يمن ناف"

## الخطوات العملية للبدء بالتنفيذ الفوري

### 1. تحويل الموقع إلى تطبيق ويب تقدمي (PWA)

#### الخطوة 1: إنشاء ملف Service Worker
1. قم بإنشاء ملف `service-worker.js` في مجلد `public`:

```bash
# إنشاء ملف service-worker.js
touch public/service-worker.js
```

2. افتح الملف وأضف الكود التالي:

```javascript
// public/service-worker.js
const CACHE_NAME = 'yemen-nav-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/admin.html',
  '/css/styles.css',
  '/js/app.js',
  '/js/map.js',
  '/images/icons/'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => response || fetch(event.request))
  );
});
```

#### الخطوة 2: إنشاء ملف Web App Manifest
1. قم بإنشاء ملف `manifest.json` في مجلد `public`:

```bash
# إنشاء ملف manifest.json
touch public/manifest.json
```

2. افتح الملف وأضف الكود التالي:

```json
{
  "name": "يمن ناف - نظام الملاحة اليمني",
  "short_name": "يمن ناف",
  "start_url": "/index.html",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#4CAF50",
  "icons": [
    {
      "src": "/images/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png"
    },
    {
      "src": "/images/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

#### الخطوة 3: تعديل ملف index.html لتسجيل Service Worker
1. افتح ملف `public/index.html` وأضف الأكواد التالية قبل نهاية وسم `</head>`:

```html
<link rel="manifest" href="/manifest.json">
<meta name="theme-color" content="#4CAF50">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="apple-mobile-web-app-title" content="يمن ناف">
<link rel="apple-touch-icon" href="/images/icons/icon-152x152.png">
```

2. أضف الكود التالي قبل نهاية وسم `</body>`:

```html
<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
          console.log('ServiceWorker registered: ', registration);
        })
        .catch(error => {
          console.log('ServiceWorker registration failed: ', error);
        });
    });
  }
</script>
```

#### الخطوة 4: إنشاء أيقونات التطبيق
1. قم بإنشاء مجلد للأيقونات:

```bash
# إنشاء مجلد للأيقونات
mkdir -p public/images/icons
```

2. قم بإنشاء أيقونات بالأحجام المطلوبة (يمكن استخدام أي برنامج لتحرير الصور مثل GIMP أو Photoshop):
   - icon-72x72.png
   - icon-96x96.png
   - icon-128x128.png
   - icon-144x144.png
   - icon-152x152.png
   - icon-192x192.png
   - icon-384x384.png
   - icon-512x512.png

### 2. تحسين تخزين الخرائط المؤقت

#### الخطوة 1: تعديل Service Worker لتخزين بلاطات الخرائط
1. افتح ملف `public/service-worker.js` وقم بتحديثه بالكود التالي:

```javascript
// public/service-worker.js
const CACHE_NAME = 'yemen-nav-cache-v1';
const TILE_CACHE_NAME = 'yemen-nav-map-tiles-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/admin.html',
  '/css/styles.css',
  '/js/app.js',
  '/js/map.js',
  '/images/icons/'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

// استراتيجية التخزين المؤقت للخرائط
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // التحقق مما إذا كان الطلب لبلاطات الخرائط
  if (url.pathname.includes('tile.openstreetmap.org') || 
      url.pathname.includes('server.arcgisonline.com') || 
      url.pathname.includes('stamen-tiles')) {
    
    event.respondWith(
      caches.open(TILE_CACHE_NAME).then(cache => {
        return cache.match(event.request).then(response => {
          return response || fetch(event.request).then(networkResponse => {
            cache.put(event.request, networkResponse.clone());
            return networkResponse;
          });
        });
      })
    );
  } else {
    // استراتيجية التخزين المؤقت العادية للموارد الأخرى
    event.respondWith(
      caches.match(event.request)
        .then(response => response || fetch(event.request))
    );
  }
});

// تنظيف التخزين المؤقت القديم
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName.startsWith('yemen-nav-') && 
                cacheName !== CACHE_NAME && 
                cacheName !== TILE_CACHE_NAME;
        }).map(cacheName => {
          return caches.delete(cacheName);
        })
      );
    })
  );
});
```

#### الخطوة 2: إنشاء وحدة لإدارة الخرائط دون اتصال
1. قم بإنشاء ملف `offline-maps.js` في مجلد `public/js`:

```bash
# إنشاء ملف offline-maps.js
mkdir -p public/js
touch public/js/offline-maps.js
```

2. افتح الملف وأضف الكود التالي:

```javascript
// public/js/offline-maps.js
class OfflineMapManager {
  constructor(map) {
    this.map = map;
  }
  
  // تنزيل منطقة من الخريطة للاستخدام دون اتصال
  downloadRegion(bounds, zoomLevels) {
    const minZoom = zoomLevels.min || 10;
    const maxZoom = zoomLevels.max || 16;
    
    // الحصول على قائمة عناوين URL لبلاطات الخرائط ضمن الحدود المحددة
    const urls = this.getTileUrls(bounds, minZoom, maxZoom);
    
    // تنزيل البلاطات وتخزينها في التخزين المؤقت
    return this.cacheTiles(urls);
  }
  
  // الحصول على عناوين URL لبلاطات الخرائط
  getTileUrls(bounds, minZoom, maxZoom) {
    const urls = [];
    
    for (let zoom = minZoom; zoom <= maxZoom; zoom++) {
      const northEast = bounds.getNorthEast();
      const southWest = bounds.getSouthWest();
      
      const neTile = this.latLngToTile(northEast.lat, northEast.lng, zoom);
      const swTile = this.latLngToTile(southWest.lat, southWest.lng, zoom);
      
      for (let x = swTile.x; x <= neTile.x; x++) {
        for (let y = neTile.y; y <= swTile.y; y++) {
          urls.push(`https://a.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
          urls.push(`https://b.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
          urls.push(`https://c.tile.openstreetmap.org/${zoom}/${x}/${y}.png`);
        }
      }
    }
    
    return urls;
  }
  
  // تحويل الإحداثيات إلى رقم البلاطة
  latLngToTile(lat, lng, zoom) {
    const n = Math.pow(2, zoom);
    const x = Math.floor((lng + 180) / 360 * n);
    const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
    return { x, y };
  }
  
  // تخزين البلاطات في التخزين المؤقت
  cacheTiles(urls) {
    return caches.open('yemen-nav-map-tiles-v1').then(cache => {
      const fetchPromises = urls.map(url => {
        return fetch(url)
          .then(response => {
            if (response.ok) {
              return cache.put(url, response);
            }
          })
          .catch(error => console.error('Error caching tile:', url, error));
      });
      
      return Promise.all(fetchPromises);
    });
  }
}

// إضافة الوحدة إلى النافذة العالمية
window.OfflineMapManager = OfflineMapManager;
```

3. قم بتضمين الملف في `index.html` قبل نهاية وسم `</body>`:

```html
<script src="/js/offline-maps.js"></script>
```

#### الخطوة 3: إضافة واجهة مستخدم لإدارة الخرائط دون اتصال
1. قم بإضافة الكود التالي إلى ملف `public/index.html` قبل نهاية وسم `</body>`:

```html
<!-- واجهة مستخدم لإدارة الخرائط دون اتصال -->
<div class="offline-maps-panel" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 2000; background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); width: 80%; max-width: 400px;">
  <h3 style="margin-top: 0; text-align: center;">تنزيل الخرائط للاستخدام دون اتصال</h3>
  <p>حدد المنطقة التي تريد تنزيلها للاستخدام دون اتصال بالإنترنت.</p>
  
  <div style="margin-bottom: 15px;">
    <label for="min-zoom">مستوى التكبير الأدنى:</label>
    <input type="range" id="min-zoom" min="5" max="15" value="10" style="width: 100%;">
    <span id="min-zoom-value">10</span>
  </div>
  
  <div style="margin-bottom: 15px;">
    <label for="max-zoom">مستوى التكبير الأقصى:</label>
    <input type="range" id="max-zoom" min="10" max="18" value="16" style="width: 100%;">
    <span id="max-zoom-value">16</span>
  </div>
  
  <div style="margin-bottom: 15px;">
    <label for="region-name">اسم المنطقة:</label>
    <input type="text" id="region-name" placeholder="أدخل اسم المنطقة" style="width: 100%; padding: 5px;">
  </div>
  
  <div style="display: flex; justify-content: space-between;">
    <button id="download-region" style="background-color: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">تنزيل المنطقة</button>
    <button id="close-offline-maps-panel" style="background-color: #f44336; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer;">إغلاق</button>
  </div>
  
  <div id="download-progress" style="margin-top: 15px; display: none;">
    <div style="height: 20px; background-color: #f1f1f1; border-radius: 5px; overflow: hidden;">
      <div id="progress-bar" style="height: 100%; width: 0%; background-color: #4CAF50; transition: width 0.3s;"></div>
    </div>
    <div id="progress-text" style="text-align: center; margin-top: 5px;">0%</div>
  </div>
</div>
```

2. قم بإضافة زر لفتح لوحة إدارة الخرائط دون اتصال في قائمة أزرار التحكم:

```html
<button class="control-button" id="offline-maps-btn" title="الخرائط دون اتصال">&#128190;</button>
```

3. قم بإضافة الكود التالي إلى ملف `public/js/app.js` (قم بإنشاء الملف إذا لم يكن موجودًا):

```javascript
// public/js/app.js

// تهيئة مدير الخرائط دون اتصال
let offlineMapManager;

// عند تحميل الخريطة
document.addEventListener('DOMContentLoaded', () => {
  // تهيئة مدير الخرائط دون اتصال بعد تحميل الخريطة
  if (window.map) {
    offlineMapManager = new OfflineMapManager(window.map);
  }
  
  // إضافة حدث النقر على زر الخرائط دون اتصال
  document.getElementById('offline-maps-btn').addEventListener('click', () => {
    document.querySelector('.offline-maps-panel').style.display = 'block';
  });
  
  // إضافة حدث النقر على زر إغلاق لوحة الخرائط دون اتصال
  document.getElementById('close-offline-maps-panel').addEventListener('click', () => {
    document.querySelector('.offline-maps-panel').style.display = 'none';
  });
  
  // إضافة حدث تغيير مستوى التكبير الأدنى
  document.getElementById('min-zoom').addEventListener('input', function() {
    document.getElementById('min-zoom-value').textContent = this.value;
  });
  
  // إضافة حدث تغيير مستوى التكبير الأقصى
  document.getElementById('max-zoom').addEventListener('input', function() {
    document.getElementById('max-zoom-value').textContent = this.value;
  });
  
  // إضافة حدث النقر على زر تنزيل المنطقة
  document.getElementById('download-region').addEventListener('click', () => {
    if (!offlineMapManager) {
      alert('لم يتم تهيئة مدير الخرائط دون اتصال بعد.');
      return;
    }
    
    const minZoom = parseInt(document.getElementById('min-zoom').value);
    const maxZoom = parseInt(document.getElementById('max-zoom').value);
    const regionName = document.getElementById('region-name').value.trim();
    
    if (!regionName) {
      alert('يرجى إدخال اسم المنطقة.');
      return;
    }
    
    // الحصول على حدود الخريطة الحالية
    const bounds = window.map.getBounds();
    
    // عرض شريط التقدم
    document.getElementById('download-progress').style.display = 'block';
    document.getElementById('progress-bar').style.width = '0%';
    document.getElementById('progress-text').textContent = '0%';
    
    // تنزيل المنطقة
    offlineMapManager.downloadRegion(bounds, { min: minZoom, max: maxZoom })
      .then(() => {
        // تحديث شريط التقدم
        document.getElementById('progress-bar').style.width = '100%';
        document.getElementById('progress-text').textContent = '100%';
        
        // حفظ معلومات المنطقة في التخزين المحلي
        const offlineRegions = JSON.parse(localStorage.getItem('offlineRegions') || '[]');
        offlineRegions.push({
          name: regionName,
          bounds: {
            north: bounds.getNorth(),
            south: bounds.getSouth(),
            east: bounds.getEast(),
            west: bounds.getWest()
          },
          minZoom,
          maxZoom,
          date: new Date().toISOString()
        });
        localStorage.setItem('offlineRegions', JSON.stringify(offlineRegions));
        
        // إظهار رسالة نجاح
        alert(`تم تنزيل المنطقة "${regionName}" بنجاح للاستخدام دون اتصال.`);
        
        // إخفاء لوحة الخرائط دون اتصال
        document.querySelector('.offline-maps-panel').style.display = 'none';
      })
      .catch(error => {
        console.error('Error downloading region:', error);
        alert('حدث خطأ أثناء تنزيل المنطقة. يرجى المحاولة مرة أخرى.');
      });
  });
});
```

4. قم بتضمين ملف `app.js` في `index.html` قبل نهاية وسم `</body>`:

```html
<script src="/js/app.js"></script>
```

### 3. إضافة وضع القيادة الليلية

#### الخطوة 1: إنشاء ملف CSS للوضع الليلي
1. قم بإنشاء ملف `night-mode.css` في مجلد `public/css`:

```bash
# إنشاء مجلد CSS إذا لم يكن موجودًا
mkdir -p public/css
touch public/css/night-mode.css
```

2. افتح الملف وأضف الكود التالي:

```css
/* public/css/night-mode.css */
body.night-mode {
  background-color: #121212;
  color: #ffffff;
}

/* الخريطة في الوضع الليلي */
body.night-mode .leaflet-container {
  background-color: #242424;
}

/* العناصر في الوضع الليلي */
body.night-mode .header,
body.night-mode .controls .control-button,
body.night-mode .device-info,
body.night-mode .location-info,
body.night-mode .search-popup,
body.night-mode .status-bar,
body.night-mode .layers-control,
body.night-mode #route-info-box {
  background-color: #242424;
  color: #ffffff;
  border-color: #444444;
}

/* الأزرار في الوضع الليلي */
body.night-mode button:not(.control-button) {
  background-color: #444444;
  color: #ffffff;
  border-color: #666666;
}

/* حقول الإدخال في الوضع الليلي */
body.night-mode input,
body.night-mode select {
  background-color: #333333;
  color: #ffffff;
  border-color: #666666;
}

/* تنبيهات الصوت في الوضع الليلي */
body.night-mode #audio-notification {
  background-color: rgba(36, 36, 36, 0.9);
}

/* تعديل ألوان المسارات في الوضع الليلي */
body.night-mode .main-route {
  stroke: #4CAF50;
  stroke-opacity: 0.9;
}

body.night-mode .alternative-route {
  stroke: #ff9800;
  stroke-opacity: 0.7;
}
```

3. قم بتضمين ملف CSS في `index.html` داخل وسم `<head>`:

```html
<link rel="stylesheet" href="/css/night-mode.css">
```

#### الخطوة 2: إضافة زر للتبديل بين الوضع العادي والوضع الليلي
1. قم بإضافة زر إلى قائمة أزرار التحكم في `index.html`:

```html
<button class="control-button" id="night-mode-toggle" title="وضع القيادة الليلية">&#127769;</button>
```

#### الخطوة 3: إنشاء ملف JavaScript لإدارة الوضع الليلي
1. قم بإنشاء ملف `night-mode.js` في مجلد `public/js`:

```bash
touch public/js/night-mode.js
```

2. افتح الملف وأضف الكود التالي:

```javascript
// public/js/night-mode.js
class NightModeManager {
  constructor() {
    this.isNightMode = false;
    this.autoNightMode = false;
    this.nightModeStartHour = 18; // 6 مساءً
    this.nightModeEndHour = 6; // 6 صباحًا
    
    // تحميل الإعدادات من التخزين المحلي
    this.loadSettings();
    
    // تهيئة الوضع الليلي بناءً على الوقت إذا كان التبديل التلقائي مفعلاً
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
  }
  
  // تحميل الإعدادات
  loadSettings() {
    const settings = JSON.parse(localStorage.getItem('nightModeSettings')) || {};
    this.isNightMode = settings.isNightMode || false;
    this.autoNightMode = settings.autoNightMode || false;
    this.nightModeStartHour = settings.nightModeStartHour || 18;
    this.nightModeEndHour = settings.nightModeEndHour || 6;
    
    // تطبيق الوضع الليلي إذا كان مفعلاً
    if (this.isNightMode) {
      this.enableNightMode();
    }
  }
  
  // حفظ الإعدادات
  saveSettings() {
    const settings = {
      isNightMode: this.isNightMode,
      autoNightMode: this.autoNightMode,
      nightModeStartHour: this.nightModeStartHour,
      nightModeEndHour: this.nightModeEndHour
    };
    
    localStorage.setItem('nightModeSettings', JSON.stringify(settings));
  }
  
  // تفعيل الوضع الليلي
  enableNightMode() {
    document.body.classList.add('night-mode');
    this.isNightMode = true;
    this.saveSettings();
    
    // تغيير نمط الخريطة إلى الوضع الليلي
    if (window.map && window.baseMaps) {
      // إزالة جميع الطبقات
      Object.values(window.baseMaps).forEach(layer => {
        if (window.map.hasLayer(layer)) {
          window.map.removeLayer(layer);
        }
      });
      
      // إضافة طبقة الخريطة الليلية
      if (window.baseMaps['خريطة ليلية']) {
        window.baseMaps['خريطة ليلية'].addTo(window.map);
      } else if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    }
  }
  
  // تعطيل الوضع الليلي
  disableNightMode() {
    document.body.classList.remove('night-mode');
    this.isNightMode = false;
    this.saveSettings();
    
    // تغيير نمط الخريطة إلى الوضع العادي
    if (window.map && window.baseMaps) {
      // إزالة جميع الطبقات
      Object.values(window.baseMaps).forEach(layer => {
        if (window.map.hasLayer(layer)) {
          window.map.removeLayer(layer);
        }
      });
      
      // إضافة طبقة الخريطة العادية
      if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    }
  }
  
  // تبديل الوضع الليلي
  toggleNightMode() {
    if (this.isNightMode) {
      this.disableNightMode();
    } else {
      this.enableNightMode();
    }
  }
  
  // تفعيل/تعطيل التبديل التلقائي
  toggleAutoNightMode() {
    this.autoNightMode = !this.autoNightMode;
    this.saveSettings();
    
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
  }
  
  // التحقق من الوقت وتعيين الوضع المناسب
  checkTimeAndSetMode() {
    const currentHour = new Date().getHours();
    
    // التحقق مما إذا كان الوقت الحالي ضمن ساعات الليل
    const isNightTime = (currentHour >= this.nightModeStartHour || currentHour < this.nightModeEndHour);
    
    if (isNightTime && !this.isNightMode) {
      this.enableNightMode();
    } else if (!isNightTime && this.isNightMode) {
      this.disableNightMode();
    }
  }
  
  // تعيين ساعات الوضع الليلي
  setNightModeHours(startHour, endHour) {
    this.nightModeStartHour = startHour;
    this.nightModeEndHour = endHour;
    this.saveSettings();
    
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
  }
}

// تهيئة مدير الوضع الليلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  window.nightModeManager = new NightModeManager();
  
  // إضافة حدث النقر على زر الوضع الليلي
  document.getElementById('night-mode-toggle').addEventListener('click', () => {
    window.nightModeManager.toggleNightMode();
  });
  
  // التحقق من الوقت كل ساعة إذا كان التبديل التلقائي مفعلاً
  if (window.nightModeManager.autoNightMode) {
    setInterval(() => {
      window.nightModeManager.checkTimeAndSetMode();
    }, 60 * 60 * 1000); // كل ساعة
  }
});
```

3. قم بتضمين ملف `night-mode.js` في `index.html` قبل نهاية وسم `</body>`:

```html
<script src="/js/night-mode.js"></script>

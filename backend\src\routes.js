// وحدة خدمات التوجيه لتطبيق Yemen GPS
const axios = require('axios');
const db = require('./postgres-db');
const dotenv = require('dotenv');

// تحميل المتغيرات البيئية
dotenv.config();

// تكوين محركات التوجيه المختلفة
const ROUTING_ENGINE = process.env.ROUTING_ENGINE || 'osrm'; // يمكن أن يكون 'osrm', 'graphhopper', 'ors'

// عناوين URL لمحركات التوجيه
const ROUTING_URLS = {
  osrm: 'https://router.project-osrm.org/route/v1', // خدمة OSRM العامة
  graphhopper: process.env.GRAPHHOPPER_URL || 'http://localhost:8989/route', // GraphHopper المحلي
  ors: process.env.ORS_URL || 'http://localhost:8080/ors/v2/directions' // OpenRouteService المحلي
};

// مفاتيح API للخدمات
const API_KEYS = {
  graphhopper: process.env.GRAPHHOPPER_API_KEY || '',
  ors: process.env.ORS_API_KEY || ''
};

// الحصول على مسار بين نقطتين
async function getRoute(startPoint, endPoint, waypoints = []) {
  try {
    // تنسيق النقاط للاستخدام مع OSRM
    const start = `${startPoint.longitude},${startPoint.latitude}`;
    const end = `${endPoint.longitude},${endPoint.latitude}`;

    // إعداد نقاط العبور إذا وجدت
    let waypointsString = '';
    if (waypoints && waypoints.length > 0) {
      waypointsString = waypoints.map(wp => `${wp.longitude},${wp.latitude}`).join(';');
      waypointsString = `;${waypointsString}`;
    }

    // بناء عنوان URL للطلب
    const url = `${ROUTING_URLS[ROUTING_ENGINE]}/driving/${start}${waypointsString};${end}?overview=full&steps=true&geometries=geojson&annotations=true`;

    console.log('طلب التوجيه:', url);

    // إرسال الطلب إلى خدمة OSRM
    const response = await axios.get(url);

    // التحقق من وجود مسار
    if (!response.data || !response.data.routes || response.data.routes.length === 0) {
      throw new Error('لم يتم العثور على مسار');
    }

    const route = response.data.routes[0];

    // تحويل الاستجابة إلى التنسيق المطلوب
    const result = {
      distance: route.distance, // المسافة بالمتر
      duration: route.duration, // المدة بالثانية
      geometry: route.geometry,
      steps: processSteps(route.legs)
    };

    // حفظ المسار في قاعدة البيانات إذا كان المستخدم مسجل الدخول
    // (هذه الوظيفة ستكون في واجهة API وليس هنا)

    return result;
  } catch (error) {
    console.error('خطأ في الحصول على المسار:', error);

    // إذا فشل الطلب، استخدم مسار وهمي للاختبار
    if (process.env.NODE_ENV !== 'production') {
      console.log('استخدام مسار وهمي للاختبار');
      return getMockRoute(startPoint, endPoint);
    }

    throw error;
  }
}

// معالجة خطوات المسار وترجمتها إلى العربية
function processSteps(legs) {
  const steps = [];

  legs.forEach(leg => {
    leg.steps.forEach(step => {
      // تحويل التعليمات إلى العربية
      const instruction = translateInstruction(step.maneuver.type, step.maneuver.modifier);

      steps.push({
        distance: step.distance,
        duration: step.duration,
        instruction,
        name: step.name || '',
        type: step.maneuver.type,
        modifier: step.maneuver.modifier,
        location: step.maneuver.location
      });
    });
  });

  return steps;
}

// ترجمة تعليمات التوجيه إلى العربية
function translateInstruction(type, modifier) {
  // القاموس للترجمة
  const typeTranslations = {
    'turn': 'انعطف',
    'new name': 'استمر على',
    'depart': 'ابدأ من',
    'arrive': 'وصلت إلى',
    'merge': 'اندمج مع',
    'on ramp': 'اسلك المنحدر',
    'off ramp': 'اخرج من المنحدر',
    'fork': 'خذ التفرع',
    'end of road': 'نهاية الطريق',
    'continue': 'استمر',
    'roundabout': 'دوار',
    'rotary': 'دوار',
    'roundabout turn': 'انعطف في الدوار',
    'exit roundabout': 'اخرج من الدوار',
    'exit rotary': 'اخرج من الدوار'
  };

  const modifierTranslations = {
    'left': 'يسارًا',
    'slight left': 'يسارًا قليلًا',
    'sharp left': 'يسارًا بحدة',
    'right': 'يمينًا',
    'slight right': 'يمينًا قليلًا',
    'sharp right': 'يمينًا بحدة',
    'straight': 'مباشرة',
    'uturn': 'استدر للخلف'
  };

  // الترجمة حسب النوع والاتجاه
  let instruction = typeTranslations[type] || type;

  if (modifier && modifierTranslations[modifier]) {
    if (type === 'arrive') {
      instruction = 'لقد ' + instruction + ' وجهتك';
    } else if (type === 'depart') {
      instruction = instruction + ' نقطة البداية';
    } else if (type === 'roundabout' || type === 'rotary') {
      instruction = 'ادخل الدوار واخرج ' + modifierTranslations[modifier];
    } else {
      instruction = instruction + ' ' + modifierTranslations[modifier];
    }
  }

  return instruction;
}

// إنشاء مسار وهمي للاختبار
function getMockRoute(startPoint, endPoint) {
  // إنشاء نقاط وسيطة بين نقطة البداية والنهاية
  const numPoints = 10;
  const coordinates = [];

  for (let i = 0; i <= numPoints; i++) {
    const fraction = i / numPoints;
    const lat = startPoint.latitude + fraction * (endPoint.latitude - startPoint.latitude);
    const lng = startPoint.longitude + fraction * (endPoint.longitude - startPoint.longitude);
    coordinates.push([lng, lat]);
  }

  // حساب المسافة التقريبية
  const distance = calculateDistance(
    startPoint.latitude, startPoint.longitude,
    endPoint.latitude, endPoint.longitude
  );

  // إنشاء خطوات وهمية
  const steps = [
    {
      distance: 0,
      duration: 0,
      instruction: 'ابدأ من نقطة البداية',
      name: 'نقطة البداية',
      type: 'depart',
      modifier: null,
      location: [startPoint.longitude, startPoint.latitude]
    },
    {
      distance: distance * 0.5 * 1000,
      duration: distance * 0.5 * 60,
      instruction: 'استمر مباشرة',
      name: 'الطريق الرئيسي',
      type: 'continue',
      modifier: 'straight',
      location: [
        startPoint.longitude + (endPoint.longitude - startPoint.longitude) * 0.5,
        startPoint.latitude + (endPoint.latitude - startPoint.latitude) * 0.5
      ]
    },
    {
      distance: distance * 0.5 * 1000,
      duration: distance * 0.5 * 60,
      instruction: 'لقد وصلت إلى وجهتك',
      name: 'نقطة النهاية',
      type: 'arrive',
      modifier: null,
      location: [endPoint.longitude, endPoint.latitude]
    }
  ];

  return {
    distance: distance * 1000, // تحويل إلى متر
    duration: distance * 60, // تحويل إلى ثانية (تقريبًا 60 ثانية لكل كيلومتر)
    geometry: {
      type: 'LineString',
      coordinates: coordinates
    },
    steps: steps
  };
}

// حساب المسافة بين نقطتين بالكيلومتر
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // نصف قطر الأرض بالكيلومتر
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // المسافة بالكيلومتر
  return distance;
}

// تحويل الدرجات إلى راديان
function deg2rad(deg) {
  return deg * (Math.PI/180);
}

// حفظ مسار للمستخدم
async function saveRoute(userId, routeData) {
  try {
    const {
      name, startPoint, startName, endPoint, endName,
      distance, duration, geometry, waypoints
    } = routeData;

    const query = `
      INSERT INTO saved_routes (
        user_id, name, start_point, start_name, end_point, end_name,
        distance_km, duration_minutes, route_geometry, waypoints
      ) VALUES (
        $1, $2,
        ST_SetSRID(ST_MakePoint($4, $3), 4326), $5,
        ST_SetSRID(ST_MakePoint($7, $6), 4326), $8,
        $9, $10, ST_GeomFromGeoJSON($11), $12
      )
      RETURNING *
    `;

    const result = await db.query(query, [
      userId, name,
      startPoint.latitude, startPoint.longitude, startName,
      endPoint.latitude, endPoint.longitude, endName,
      distance / 1000, // تحويل إلى كيلومتر
      duration / 60, // تحويل إلى دقائق
      JSON.stringify(geometry),
      JSON.stringify(waypoints || [])
    ]);

    return result.rows[0];
  } catch (error) {
    console.error('خطأ في حفظ المسار:', error);
    throw error;
  }
}

// الحصول على المسارات المحفوظة للمستخدم
async function getSavedRoutes(userId) {
  try {
    const query = `
      SELECT
        route_id, name, start_name, end_name,
        distance_km, duration_minutes,
        ST_AsGeoJSON(start_point) as start_point,
        ST_AsGeoJSON(end_point) as end_point,
        ST_AsGeoJSON(route_geometry) as route_geometry,
        waypoints, created_at, last_used, is_favorite
      FROM saved_routes
      WHERE user_id = $1
      ORDER BY last_used DESC NULLS LAST, created_at DESC
    `;

    const result = await db.query(query, [userId]);

    // تحويل النقاط الجغرافية إلى كائنات JSON
    return result.rows.map(route => {
      return {
        ...route,
        start_point: JSON.parse(route.start_point),
        end_point: JSON.parse(route.end_point),
        route_geometry: JSON.parse(route.route_geometry)
      };
    });
  } catch (error) {
    console.error('خطأ في الحصول على المسارات المحفوظة:', error);
    throw error;
  }
}

// تصدير الوظائف
module.exports = {
  getRoute,
  saveRoute,
  getSavedRoutes
};

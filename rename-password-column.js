// سكريبت لتعديل اسم عمود كلمة المرور من password_hash إلى password
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('معلومات الاتصال بقاعدة البيانات:', dbConfig);

// إنشاء مجمع اتصال بقاعدة البيانات
const pool = new Pool(dbConfig);

// تعديل اسم العمود
async function renamePasswordColumn() {
    let client;
    try {
        console.log('محاولة الاتصال بقاعدة البيانات...');
        client = await pool.connect();
        console.log('تم الاتصال بقاعدة البيانات بنجاح');

        // التحقق من وجود عمود password_hash
        const checkColumnQuery = `
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'password_hash'
            );
        `;
        
        const checkResult = await client.query(checkColumnQuery);
        const columnExists = checkResult.rows[0].exists;
        
        if (!columnExists) {
            console.log('عمود password_hash غير موجود في جدول المستخدمين');
            return false;
        }
        
        // التحقق من وجود عمود password
        const checkPasswordColumnQuery = `
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'password'
            );
        `;
        
        const checkPasswordResult = await client.query(checkPasswordColumnQuery);
        const passwordColumnExists = checkPasswordResult.rows[0].exists;
        
        if (passwordColumnExists) {
            console.log('عمود password موجود بالفعل في جدول المستخدمين');
            console.log('سيتم حذف عمود password_hash ونقل البيانات إلى عمود password');
            
            // نقل البيانات من password_hash إلى password
            await client.query(`
                UPDATE users 
                SET password = password_hash 
                WHERE password IS NULL OR password = '';
            `);
            
            // حذف عمود password_hash
            await client.query(`ALTER TABLE users DROP COLUMN password_hash;`);
            
            console.log('تم حذف عمود password_hash بنجاح');
        } else {
            // تغيير اسم العمود من password_hash إلى password
            console.log('جاري تغيير اسم العمود من password_hash إلى password...');
            await client.query(`ALTER TABLE users RENAME COLUMN password_hash TO password;`);
            console.log('تم تغيير اسم العمود بنجاح');
        }
        
        // التحقق من نجاح العملية
        const verifyQuery = `
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'password'
            );
        `;
        
        const verifyResult = await client.query(verifyQuery);
        const success = verifyResult.rows[0].exists;
        
        if (success) {
            console.log('تم التحقق: عمود password موجود الآن في جدول المستخدمين');
        } else {
            console.log('فشل: عمود password غير موجود في جدول المستخدمين');
        }
        
        return success;
    } catch (error) {
        console.error('خطأ في تعديل اسم العمود:', error);
        return false;
    } finally {
        if (client) {
            client.release();
        }
        await pool.end();
    }
}

// تنفيذ عملية تعديل اسم العمود
renamePasswordColumn().then(success => {
    console.log('اكتملت العملية بنجاح:', success);
    process.exit(0);
}).catch(err => {
    console.error('خطأ غير متوقع:', err);
    process.exit(1);
});

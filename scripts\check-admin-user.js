const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function checkAdminUser() {
  try {
    const res = await pool.query("SELECT * FROM users WHERE username = 'admin'");
    console.log('نتيجة الاستعلام:', res.rows);
  } catch (error) {
    console.error('خطأ في الاستعلام:', error);
  } finally {
    await pool.end();
  }
}

checkAdminUser();

-- يمن ناف - <PERSON>ل<PERSON> SQL لإضافة أعمدة الصلاحيات في جدول المستخدمين

-- إضافة عمود permissions_json لتخزين الصلاحيات كـ JSON
ALTER TABLE users
ADD COLUMN IF NOT EXISTS permissions_json JSONB DEFAULT '[]';

-- إضافة عمود can_access_admin لتحديد ما إذا كان المستخدم يمكنه الوصول إلى صفحة الإدارة
ALTER TABLE users
ADD COLUMN IF NOT EXISTS can_access_admin BOOLEAN DEFAULT FALSE;

-- تحديث المستخدم admin لمنحه صلاحية الوصول إلى صفحة الإدارة
UPDATE users
SET can_access_admin = TRUE
WHERE username = 'admin';

-- إضا<PERSON>ة صلاحيات افتراضية للمستخدم admin
UPDATE users
SET permissions_json = '[
    {"code": "view_dashboard", "name": "عرض لوحة التحكم"},
    {"code": "manage_users", "name": "إدارة المستخدمين"},
    {"code": "manage_clients", "name": "إدارة العملاء"},
    {"code": "manage_locations", "name": "إدارة المواقع"},
    {"code": "manage_settings", "name": "إدارة الإعدادات"},
    {"code": "manage_backups", "name": "إدارة النسخ الاحتياطي"},
    {"code": "manage_categories", "name": "إدارة التصنيفات"},
    {"code": "manage_routes", "name": "إدارة المسارات"},
    {"code": "manage_reports", "name": "إدارة التقارير"},
    {"code": "manage_roles", "name": "إدارة الأدوار"}
]'::jsonb
WHERE username = 'admin';

-- إنشاء مستخدم admin إذا لم يكن موجودًا
INSERT INTO users (username, email, password, full_name, role_id, is_active, is_verified, can_access_admin, permissions_json)
SELECT 
    'admin', 
    '<EMAIL>', 
    '$2b$10$X7tPj4cjLsVUoA/2o8yw3.Qi.Ym5OJbR8vQzPYxbniQTgPOxRVSJi', -- كلمة المرور: yemen123
    'مدير النظام', 
    1, 
    TRUE, 
    TRUE, 
    TRUE, 
    '[
        {"code": "view_dashboard", "name": "عرض لوحة التحكم"},
        {"code": "manage_users", "name": "إدارة المستخدمين"},
        {"code": "manage_clients", "name": "إدارة العملاء"},
        {"code": "manage_locations", "name": "إدارة المواقع"},
        {"code": "manage_settings", "name": "إدارة الإعدادات"},
        {"code": "manage_backups", "name": "إدارة النسخ الاحتياطي"},
        {"code": "manage_categories", "name": "إدارة التصنيفات"},
        {"code": "manage_routes", "name": "إدارة المسارات"},
        {"code": "manage_reports", "name": "إدارة التقارير"},
        {"code": "manage_roles", "name": "إدارة الأدوار"}
    ]'::jsonb
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE username = 'admin'
);

-- إضافة دالة للتحقق من صلاحيات المستخدم
CREATE OR REPLACE FUNCTION user_has_permission(user_id INTEGER, permission_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM users
        WHERE id = user_id
        AND (
            -- التحقق من وجود الصلاحية في permissions_json
            permissions_json @> format('[{"code": "%s"}]', permission_code)::jsonb
            OR
            -- التحقق من أن المستخدم مدير (role_id = 1)
            role_id = 1
        )
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;

/* متغيرات CSS للألوان والقيم */
:root {
    --primary-color: #1a73e8;
    --primary-hover: #1557b0;
    --secondary-color: #34a853;
    --secondary-hover: #2d8f47;
    --danger-color: #ea4335;
    --warning-color: #fbbc04;
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-disabled: #9aa0a6;
    --border-color: #dadce0;
    --border-hover: #bdc1c6;
    --shadow-light: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-medium: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-heavy: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --border-radius: 8px;
    --border-radius-small: 4px;
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين الأنماط الأساسية */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Tajawal', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    direction: rtl;
    overflow: hidden;
    background-color: var(--surface-color);
    color: var(--text-primary);
    line-height: 1.5;
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h2 {
    font-size: 24px;
    margin-bottom: 8px;
    font-weight: 500;
}

.loading-content p {
    font-size: 16px;
    opacity: 0.9;
}

/* حاوي الخريطة */
.map-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    background-color: var(--surface-color);
}

/* شريط البحث */
.search-container {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    width: 400px;
    max-width: calc(100vw - 40px);
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid transparent;
}

.search-box:focus-within {
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-color);
}

.search-icon {
    padding: 0 16px;
    color: var(--text-secondary);
    font-size: 16px;
}

.search-box input {
    flex: 1;
    border: none;
    padding: 14px 8px;
    font-size: 16px;
    outline: none;
    background: transparent;
    color: var(--text-primary);
    font-family: inherit;
}

.search-box input::placeholder {
    color: var(--text-disabled);
}

.search-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 14px 16px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
}

.search-btn:hover {
    background: var(--primary-hover);
}

/* نتائج البحث */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    margin-top: 8px;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.search-suggestions {
    padding: 8px 0;
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 12px;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--surface-color);
}

.suggestion-icon {
    color: var(--text-secondary);
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.suggestion-text {
    flex: 1;
}

.suggestion-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.suggestion-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
}

/* أزرار التحكم */
.map-controls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.control-btn {
    width: 48px;
    height: 48px;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    box-shadow: var(--shadow-light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-primary);
    transition: var(--transition);
    position: relative;
}

.control-btn:hover {
    background: var(--surface-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-light);
}

/* أزرار التكبير والتصغير */
.zoom-controls {
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.zoom-controls .control-btn {
    box-shadow: none;
    border: none;
    border-radius: 0;
    border-bottom: 1px solid var(--border-color);
}

.zoom-controls .control-btn:last-child {
    border-bottom: none;
}

.zoom-controls .control-btn:hover {
    background: var(--surface-color);
    box-shadow: none;
    transform: none;
}

/* أزرار خاصة */
.location-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.location-btn:hover {
    background: var(--primary-hover) !important;
}

.directions-btn {
    background: var(--secondary-color) !important;
    color: white !important;
    border-color: var(--secondary-color) !important;
}

.directions-btn:hover {
    background: var(--secondary-hover) !important;
}

/* لوحات جانبية */
.layers-panel,
.directions-panel {
    position: absolute;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    z-index: 1001;
    border: 1px solid var(--border-color);
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.layers-panel {
    top: 80px;
    right: 20px;
    width: 280px;
    max-width: calc(100vw - 40px);
}

.directions-panel {
    top: 20px;
    left: 20px;
    width: 350px;
    max-width: calc(100vw - 40px);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--surface-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.panel-header h3 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
    border-radius: var(--border-radius-small);
    transition: var(--transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.panel-content {
    padding: 20px;
}

/* خيارات طبقات الخريطة */
.layer-options {
    margin-bottom: 24px;
}

.layer-option {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    transition: var(--transition);
    border-radius: var(--border-radius-small);
    margin-bottom: 8px;
}

.layer-option:hover {
    background: var(--surface-color);
    padding-left: 8px;
    padding-right: 8px;
}

.layer-option input[type="radio"] {
    margin-left: 12px;
    accent-color: var(--primary-color);
}

.layer-icon {
    width: 24px;
    text-align: center;
    margin-left: 12px;
    color: var(--text-secondary);
    font-size: 16px;
}

.layer-text {
    font-weight: 400;
    color: var(--text-primary);
}

.layer-option input[type="radio"]:checked + .layer-icon {
    color: var(--primary-color);
}

.layer-option input[type="radio"]:checked ~ .layer-text {
    color: var(--primary-color);
    font-weight: 500;
}

/* طبقات إضافية */
.layer-overlays {
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
}

.layer-overlays h4 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.overlay-option {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;
    transition: var(--transition);
}

.overlay-option input[type="checkbox"] {
    margin-left: 12px;
    accent-color: var(--primary-color);
}

.overlay-option:hover {
    color: var(--primary-color);
}

/* نافذة معلومات النقطة */
.point-info-popup {
    position: absolute;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    max-width: 350px;
    z-index: 1002;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--surface-color);
}

.popup-header h3 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    margin-left: 12px;
}

.popup-content {
    max-height: 400px;
    overflow-y: auto;
}

/* صور المكان */
.point-images {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.image-slider {
    position: relative;
    width: 100%;
    height: 100%;
}

.slider-container {
    display: flex;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.slider-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    flex-shrink: 0;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    z-index: 10;
}

.slider-btn:hover {
    background: rgba(0,0,0,0.7);
}

.prev-btn {
    right: 8px;
}

.next-btn {
    left: 8px;
}

/* تفاصيل المكان */
.point-details {
    padding: 20px;
}

.point-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 16px;
}

.point-address {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--text-secondary);
}

.point-address i {
    color: var(--primary-color);
    margin-top: 2px;
}

.point-contact {
    margin-bottom: 16px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}

.contact-item i {
    color: var(--text-secondary);
    width: 16px;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-link:hover {
    text-decoration: underline;
}

/* تقييم المكان */
.point-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    color: var(--warning-color);
    font-size: 14px;
}

.rating-text {
    font-size: 14px;
    color: var(--text-secondary);
}

/* أزرار الإجراءات */
.point-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--surface-color);
}

.action-btn {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    color: var(--text-primary);
}

.action-btn:hover {
    background: var(--surface-color);
    border-color: var(--border-hover);
}

.route-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.save-btn:hover {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.share-btn:hover {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.streetview-btn:hover {
    background: var(--text-primary);
    color: white;
    border-color: var(--text-primary);
}

/* فئة مخفية */
.hidden {
    display: none !important;
}

/* نموذج الاتجاهات */
.directions-form {
    margin-bottom: 20px;
}

.route-inputs {
    margin-bottom: 16px;
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    background: var(--surface-color);
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.input-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.input-icon {
    padding: 0 12px;
    display: flex;
    align-items: center;
}

.start-icon {
    color: var(--secondary-color);
}

.end-icon {
    color: var(--danger-color);
}

.route-input {
    flex: 1;
    border: none;
    padding: 12px 8px;
    background: transparent;
    font-size: 14px;
    outline: none;
    color: var(--text-primary);
    font-family: inherit;
}

.route-input::placeholder {
    color: var(--text-disabled);
}

.input-action {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: var(--border-radius-small);
    transition: var(--transition);
    margin-left: 4px;
}

.input-action:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* خيارات وسائل النقل */
.transport-options {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px;
    background: var(--surface-color);
    border-radius: var(--border-radius);
}

.transport-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: var(--transition);
    color: var(--text-secondary);
}

.transport-btn:hover {
    background: var(--surface-color);
    border-color: var(--border-hover);
}

.transport-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.transport-btn i {
    font-size: 16px;
}

/* خيارات المسار */
.route-options {
    margin-bottom: 16px;
}

.route-option {
    display: flex;
    align-items: center;
    padding: 8px 0;
    cursor: pointer;
    font-size: 14px;
}

.route-option input {
    margin-left: 8px;
    accent-color: var(--primary-color);
}

/* زر حساب المسار */
.calculate-btn {
    width: 100%;
    padding: 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.calculate-btn:hover {
    background: var(--primary-hover);
}

.calculate-btn:disabled {
    background: var(--text-disabled);
    cursor: not-allowed;
}

/* نتائج المسار */
.route-results {
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
}

.route-summary {
    margin-bottom: 16px;
}

.route-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.route-stat {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: var(--text-secondary);
}

.route-stat i {
    color: var(--primary-color);
}

.route-actions {
    display: flex;
    gap: 8px;
}

.start-nav-btn {
    flex: 2;
    padding: 10px 16px;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.start-nav-btn:hover {
    background: var(--secondary-hover);
}

.preview-btn {
    flex: 1;
    padding: 10px 16px;
    background: var(--background-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.preview-btn:hover {
    background: var(--surface-color);
}

/* تعليمات المسار */
.route-instructions h4 {
    font-size: 16px;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.instructions-list {
    max-height: 200px;
    overflow-y: auto;
}

.instruction-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

.instruction-item:last-child {
    border-bottom: none;
}

.instruction-icon {
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
    margin-top: 2px;
}

.instruction-text {
    flex: 1;
    line-height: 1.4;
}

.instruction-distance {
    color: var(--text-secondary);
    font-size: 12px;
    margin-top: 2px;
}

/* شريط الملاحة */
.navigation-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 1003;
}

.nav-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    gap: 16px;
}

.nav-instruction {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
}

.instruction-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.instruction-text {
    flex: 1;
}

.instruction-main {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.instruction-distance {
    font-size: 14px;
    color: var(--text-secondary);
}

.nav-controls {
    display: flex;
    gap: 8px;
}

.nav-btn {
    width: 40px;
    height: 40px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    color: var(--text-primary);
}

.nav-btn:hover {
    background: var(--border-color);
}

.stop-btn {
    background: var(--danger-color) !important;
    color: white !important;
    border-color: var(--danger-color) !important;
}

.stop-btn:hover {
    background: #d33b2c !important;
}

.nav-progress {
    padding: 0 20px 16px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: var(--secondary-color);
    transition: width 0.3s ease;
}

.nav-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-secondary);
}

/* رسائل التنبيه */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.toast {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 12px 16px;
    max-width: 300px;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-color: var(--secondary-color);
    background: #f0f9f0;
}

.toast.error {
    border-color: var(--danger-color);
    background: #fef7f7;
}

.toast.warning {
    border-color: var(--warning-color);
    background: #fffbf0;
}

.toast-icon {
    font-size: 16px;
}

.toast.success .toast-icon {
    color: var(--secondary-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast-text {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .search-container {
        width: calc(100vw - 20px);
        top: 10px;
    }

    .map-controls {
        bottom: 10px;
        right: 10px;
        gap: 8px;
    }

    .control-btn {
        width: 44px;
        height: 44px;
        font-size: 16px;
    }

    .layers-panel {
        right: 10px;
        top: 70px;
        width: calc(100vw - 20px);
        max-width: 320px;
    }

    .directions-panel {
        left: 10px;
        top: 10px;
        width: calc(100vw - 20px);
        max-width: 100%;
    }

    .point-info-popup {
        bottom: 20px;
        left: 10px;
        right: 10px;
        max-width: none;
        position: fixed;
    }

    .point-actions {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }

    .action-btn {
        flex-direction: column;
        gap: 4px;
        padding: 8px 4px;
        font-size: 12px;
    }

    .action-btn i {
        font-size: 16px;
    }

    .transport-options {
        flex-wrap: wrap;
    }

    .transport-btn {
        min-width: 70px;
    }

    .route-info {
        flex-direction: column;
        gap: 8px;
    }

    .route-actions {
        flex-direction: column;
    }

    .nav-content {
        padding: 12px 16px;
    }

    .nav-instruction {
        gap: 8px;
    }

    .instruction-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .toast-container {
        right: 10px;
        left: 10px;
    }

    .toast {
        max-width: none;
    }
}

@media (max-width: 480px) {
    .search-container {
        width: calc(100vw - 10px);
        top: 5px;
    }

    .map-controls {
        bottom: 5px;
        right: 5px;
    }

    .layers-panel,
    .directions-panel {
        left: 5px;
        right: 5px;
        width: auto;
    }

    .point-info-popup {
        left: 5px;
        right: 5px;
        bottom: 10px;
    }

    .panel-content {
        padding: 16px;
    }

    .nav-content {
        padding: 10px 12px;
    }

    .nav-progress {
        padding: 0 12px 12px;
    }
}

/**
 * Yemen GPS - نظام خرائط اليمن
 * ملف وظائف عرض تفاصيل المكان بطريقة احترافية
 */

class PlaceDetailsManager {
    constructor() {
        this.placeDetailsPanel = document.getElementById('place-details-panel');
        this.currentPlaceId = null;
        this.bindEvents();
    }

    /**
     * ربط أحداث عناصر نافذة تفاصيل المكان
     */
    bindEvents() {
        // زر إغلاق نافذة التفاصيل
        const closeBtn = document.getElementById('close-place-details-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closePlaceDetails());
        }

        // زر مشاركة المكان
        const shareBtn = document.getElementById('share-place-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.sharePlace());
        }

        // زر حفظ المكان
        const saveBtn = document.getElementById('save-place-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.savePlace());
        }

        // زر الحصول على المسار
        const directionsBtn = document.getElementById('directions-btn');
        if (directionsBtn) {
            directionsBtn.addEventListener('click', () => this.getDirections());
        }

        // زر الاتصال
        const callBtn = document.getElementById('call-btn');
        if (callBtn) {
            callBtn.addEventListener('click', () => this.callPlace());
        }

        // زر الموقع الإلكتروني
        const websiteBtn = document.getElementById('website-btn');
        if (websiteBtn) {
            websiteBtn.addEventListener('click', () => this.openWebsite());
        }
    }

    /**
     * عرض تفاصيل المكان
     * @param {Object} place - بيانات المكان
     */
    showPlaceDetails(place) {
        if (!place) return;
        
        this.currentPlaceId = place.place_id || place.id;
        
        // تعيين اسم المكان
        const placeName = document.getElementById('place-name');
        if (placeName) placeName.textContent = place.name || 'مكان غير معروف';
        
        // تعيين تصنيف المكان
        const placeCategory = document.getElementById('place-category');
        if (placeCategory) {
            const categorySpan = placeCategory.querySelector('span');
            if (categorySpan) {
                categorySpan.textContent = place.types ? this.formatPlaceType(place.types[0]) : 'موقع';
            }
        }
        
        // تعيين تقييم المكان
        this.setPlaceRating(place);
        
        // تعيين عنوان المكان
        const placeAddress = document.getElementById('place-address');
        if (placeAddress) placeAddress.textContent = place.formatted_address || place.vicinity || 'عنوان غير متوفر';
        
        // تعيين صورة المكان الرئيسية
        this.setPlaceImages(place);
        
        // تعيين معلومات الاتصال
        this.setContactInfo(place);
        
        // تعيين ساعات العمل
        this.setOpeningHours(place);
        
        // إظهار نافذة التفاصيل
        this.placeDetailsPanel.classList.add('active');
        
        // تحديث أزرار الإجراءات
        this.updateActionButtons(place);
    }
    
    /**
     * تنسيق نوع المكان بشكل مقروء
     * @param {string} type - نوع المكان
     * @returns {string} النوع المنسق
     */
    formatPlaceType(type) {
        if (!type) return 'موقع';
        
        const typeMap = {
            'restaurant': 'مطعم',
            'cafe': 'مقهى',
            'hospital': 'مستشفى',
            'school': 'مدرسة',
            'mosque': 'مسجد',
            'store': 'متجر',
            'supermarket': 'سوبرماركت',
            'shopping_mall': 'مركز تسوق',
            'pharmacy': 'صيدلية',
            'hotel': 'فندق',
            'bank': 'بنك',
            'gas_station': 'محطة وقود',
            'park': 'حديقة',
            'market': 'سوق',
            // إضافة المزيد من الأنواع حسب الحاجة
        };
        
        return typeMap[type] || type.replace(/_/g, ' ');
    }
    
    /**
     * تعيين تقييم المكان
     * @param {Object} place - بيانات المكان
     */
    setPlaceRating(place) {
        const placeRating = document.getElementById('place-rating');
        if (!placeRating) return;
        
        const stars = placeRating.querySelector('.stars');
        const ratingValue = placeRating.querySelector('.rating-value');
        const reviewCount = placeRating.querySelector('.review-count');
        
        const rating = place.rating || 0;
        const reviews = place.user_ratings_total || place.reviews_count || 0;
        
        if (stars) {
            stars.innerHTML = this.generateStars(rating);
        }
        
        if (ratingValue) {
            ratingValue.textContent = rating ? rating.toFixed(1) : 'غير متوفر';
        }
        
        if (reviewCount) {
            reviewCount.textContent = reviews ? `(${reviews} تقييم)` : '';
        }
    }
    
    /**
     * توليد نجوم التقييم
     * @param {number} rating - قيمة التقييم
     * @returns {string} HTML للنجوم
     */
    generateStars(rating) {
        if (!rating) return '☆☆☆☆☆';
        
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        let starsHtml = '';
        
        // إضافة النجوم الممتلئة
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // إضافة نصف نجمة إذا لزم الأمر
        if (halfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // إضافة النجوم الفارغة
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }
        
        return starsHtml;
    }
    
    /**
     * تعيين صور المكان
     * @param {Object} place - بيانات المكان
     */
    async setPlaceImages(place) {
        const mainImage = document.getElementById('place-main-image');
        const imageGallery = document.getElementById('place-image-gallery');
        
        if (!mainImage || !imageGallery) return;
        
        // تعيين الصورة الرئيسية
        let imageUrl = 'assets/images/placeholder.jpg';
        
        if (place.photos && place.photos.length > 0) {
            // إذا كان لدينا صور من Google Places API
            if (place.photos[0].getUrl) {
                imageUrl = place.photos[0].getUrl({ maxWidth: 800, maxHeight: 600 });
            } else if (place.photos[0].url) {
                imageUrl = place.photos[0].url;
            }
        } else if (place.image_url || place.icon) {
            // إذا كان لدينا URL للصورة في البيانات
            imageUrl = place.image_url || place.icon;
        }
        
        mainImage.src = imageUrl;
        
        // تعيين معرض الصور
        imageGallery.innerHTML = '';
        
        if (place.photos && place.photos.length > 1) {
            // إضافة الصور الإضافية إلى المعرض
            for (let i = 1; i < Math.min(place.photos.length, 6); i++) {
                const photoUrl = place.photos[i].getUrl ? 
                    place.photos[i].getUrl({ maxWidth: 200, maxHeight: 150 }) : 
                    place.photos[i].url || 'assets/images/placeholder.jpg';
                
                const thumbnailDiv = document.createElement('div');
                thumbnailDiv.className = 'gallery-thumbnail';
                thumbnailDiv.innerHTML = `<img src="${photoUrl}" alt="صورة ${i + 1}" onerror="this.onerror=null;this.src='assets/images/placeholder.jpg';">`;
                
                thumbnailDiv.addEventListener('click', () => {
                    mainImage.src = photoUrl;
                });
                
                imageGallery.appendChild(thumbnailDiv);
            }
        } else {
            // إذا لم تكن هناك صور إضافية، أخفِ المعرض
            imageGallery.style.display = 'none';
        }
    }
    
    /**
     * تعيين معلومات الاتصال
     * @param {Object} place - بيانات المكان
     */
    setContactInfo(place) {
        // رقم الهاتف
        const contactPhone = document.getElementById('contact-phone');
        if (contactPhone) {
            const phoneSpan = contactPhone.querySelector('span');
            if (phoneSpan) {
                const phone = place.formatted_phone_number || place.phone_number || place.international_phone_number;
                if (phone) {
                    phoneSpan.textContent = phone;
                    contactPhone.style.display = 'flex';
                    
                    // تحديث زر الاتصال
                    const callBtn = document.getElementById('call-btn');
                    if (callBtn) {
                        callBtn.querySelector('span').textContent = 'اتصال';
                        callBtn.disabled = false;
                    }
                } else {
                    contactPhone.style.display = 'none';
                    
                    // تعطيل زر الاتصال
                    const callBtn = document.getElementById('call-btn');
                    if (callBtn) {
                        callBtn.querySelector('span').textContent = 'لا يوجد رقم';
                        callBtn.disabled = true;
                    }
                }
            }
        }
        
        // الموقع الإلكتروني
        const contactWebsite = document.getElementById('contact-website');
        if (contactWebsite) {
            const websiteSpan = contactWebsite.querySelector('span');
            if (websiteSpan) {
                const website = place.website;
                if (website) {
                    websiteSpan.textContent = this.formatWebsite(website);
                    contactWebsite.style.display = 'flex';
                    
                    // تحديث زر الموقع
                    const websiteBtn = document.getElementById('website-btn');
                    if (websiteBtn) {
                        websiteBtn.querySelector('span').textContent = 'الموقع';
                        websiteBtn.disabled = false;
                    }
                } else {
                    contactWebsite.style.display = 'none';
                    
                    // تعطيل زر الموقع
                    const websiteBtn = document.getElementById('website-btn');
                    if (websiteBtn) {
                        websiteBtn.querySelector('span').textContent = 'لا يوجد موقع';
                        websiteBtn.disabled = true;
                    }
                }
            }
        }
        
        // العنوان
        const contactAddress = document.getElementById('contact-address');
        if (contactAddress) {
            const addressSpan = contactAddress.querySelector('span');
            if (addressSpan) {
                const address = place.formatted_address || place.vicinity;
                if (address) {
                    addressSpan.textContent = address;
                    contactAddress.style.display = 'flex';
                } else {
                    contactAddress.style.display = 'none';
                }
            }
        }
    }
    
    /**
     * تنسيق عنوان الموقع الإلكتروني
     * @param {string} website - عنوان الموقع الإلكتروني
     * @returns {string} عنوان منسق
     */
    formatWebsite(website) {
        if (!website) return '';
        
        try {
            const url = new URL(website);
            return url.hostname;
        } catch (e) {
            return website;
        }
    }
    
    /**
     * تعيين ساعات العمل
     * @param {Object} place - بيانات المكان
     */
    setOpeningHours(place) {
        const openingHoursSection = document.getElementById('place-opening-hours');
        const hoursList = document.getElementById('hours-list');
        
        if (!openingHoursSection || !hoursList) return;
        
        if (place.opening_hours) {
            hoursList.innerHTML = '';
            
            const weekdayTexts = place.opening_hours.weekday_text || [];
            
            if (weekdayTexts.length > 0) {
                weekdayTexts.forEach(day => {
                    const dayItem = document.createElement('div');
                    dayItem.className = 'hours-item';
                    
                    // تقسيم النص إلى يوم وساعات
                    const parts = day.split(': ');
                    if (parts.length === 2) {
                        const dayName = this.translateDay(parts[0]);
                        const hours = parts[1];
                        
                        dayItem.innerHTML = `
                            <div class="day">${dayName}</div>
                            <div class="hours">${hours}</div>
                        `;
                    } else {
                        dayItem.textContent = day;
                    }
                    
                    hoursList.appendChild(dayItem);
                });
                
                openingHoursSection.style.display = 'block';
            } else {
                openingHoursSection.style.display = 'none';
            }
        } else {
            openingHoursSection.style.display = 'none';
        }
    }
    
    /**
     * ترجمة أيام الأسبوع إلى العربية
     * @param {string} day - اسم اليوم بالإنجليزية
     * @returns {string} اسم اليوم بالعربية
     */
    translateDay(day) {
        const days = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        };
        
        return days[day] || day;
    }
    
    /**
     * تحديث أزرار الإجراءات
     * @param {Object} place - بيانات المكان
     */
    updateActionButtons(place) {
        // زر المسار
        const directionsBtn = document.getElementById('directions-btn');
        if (directionsBtn) {
            directionsBtn.disabled = !(place.geometry && place.geometry.location);
        }
        
        // زر الحفظ - تحديث الأيقونة بناءً على ما إذا كان المكان محفوظًا أم لا
        this.updateSaveButton();
    }
    
    /**
     * تحديث زر الحفظ
     */
    updateSaveButton() {
        const saveBtn = document.getElementById('save-place-btn');
        if (!saveBtn) return;
        
        // التحقق مما إذا كان المكان محفوظًا
        const isSaved = this.isPlaceSaved(this.currentPlaceId);
        
        // تحديث الأيقونة
        const icon = saveBtn.querySelector('i');
        if (icon) {
            if (isSaved) {
                icon.className = 'fas fa-bookmark';  // ممتلئ
            } else {
                icon.className = 'far fa-bookmark';  // فارغ
            }
        }
    }
    
    /**
     * التحقق مما إذا كان المكان محفوظًا
     * @param {string} placeId - معرف المكان
     * @returns {boolean} محفوظ أم لا
     */
    isPlaceSaved(placeId) {
        if (!placeId) return false;
        
        const savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
        return savedPlaces.some(place => place.id === placeId);
    }
    
    /**
     * إغلاق نافذة تفاصيل المكان
     */
    closePlaceDetails() {
        this.placeDetailsPanel.classList.remove('active');
        this.currentPlaceId = null;
    }
    
    /**
     * مشاركة المكان
     */
    sharePlace() {
        if (!this.currentPlaceId) return;
        
        // الحصول على بيانات المكان الحالي
        const placeName = document.getElementById('place-name').textContent;
        const placeAddress = document.getElementById('place-address').textContent;
        
        const shareText = `${placeName} - ${placeAddress}`;
        const shareUrl = `${window.location.origin}?place_id=${this.currentPlaceId}`;
        
        // استخدام Web Share API إذا كانت متوفرة
        if (navigator.share) {
            navigator.share({
                title: placeName,
                text: shareText,
                url: shareUrl
            })
            .catch(error => {
                console.error('خطأ في مشاركة المكان:', error);
                this.fallbackShare(shareText, shareUrl);
            });
        } else {
            this.fallbackShare(shareText, shareUrl);
        }
    }
    
    /**
     * مشاركة بديلة إذا كانت Web Share API غير متوفرة
     * @param {string} text - نص المشاركة
     * @param {string} url - رابط المشاركة
     */
    fallbackShare(text, url) {
        // إنشاء مربع حوار مشاركة مخصص
        const shareDialog = document.createElement('div');
        shareDialog.className = 'share-dialog';
        shareDialog.innerHTML = `
            <div class="share-dialog-content">
                <div class="share-dialog-header">
                    <h3>مشاركة المكان</h3>
                    <button class="close-share-dialog"><i class="fas fa-times"></i></button>
                </div>
                <div class="share-dialog-body">
                    <p>${text}</p>
                    <input type="text" readonly value="${url}" class="share-url-input">
                    <button class="copy-url-btn">نسخ الرابط</button>
                </div>
                <div class="share-dialog-footer">
                    <div class="share-options">
                        <a href="https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}" target="_blank" class="share-option">
                            <i class="fab fa-whatsapp"></i>
                            <span>واتساب</span>
                        </a>
                        <a href="https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}" target="_blank" class="share-option">
                            <i class="fab fa-telegram"></i>
                            <span>تلجرام</span>
                        </a>
                        <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(text + ' ' + url)}" target="_blank" class="share-option">
                            <i class="fab fa-twitter"></i>
                            <span>تويتر</span>
                        </a>
                        <a href="mailto:?subject=${encodeURIComponent(text)}&body=${encodeURIComponent(text + ' ' + url)}" class="share-option">
                            <i class="fas fa-envelope"></i>
                            <span>بريد إلكتروني</span>
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(shareDialog);
        
        // زر إغلاق مربع الحوار
        const closeBtn = shareDialog.querySelector('.close-share-dialog');
        closeBtn.addEventListener('click', () => {
            shareDialog.remove();
        });
        
        // زر نسخ الرابط
        const copyBtn = shareDialog.querySelector('.copy-url-btn');
        const urlInput = shareDialog.querySelector('.share-url-input');
        
        copyBtn.addEventListener('click', () => {
            urlInput.select();
            document.execCommand('copy');
            copyBtn.textContent = 'تم النسخ!';
            setTimeout(() => {
                copyBtn.textContent = 'نسخ الرابط';
            }, 2000);
        });
    }
    
    /**
     * حفظ المكان
     */
    savePlace() {
        if (!this.currentPlaceId) return;
        
        // الحصول على بيانات المكان الحالي
        const placeName = document.getElementById('place-name').textContent;
        const placeAddress = document.getElementById('place-address').textContent;
        
        // الحصول على الإحداثيات إذا كانت متوفرة
        let lat = null;
        let lng = null;
        if (window.yemenMaps && window.yemenMaps.map) {
            const center = window.yemenMaps.map.getCenter();
            if (center) {
                lat = center.lat;
                lng = center.lng;
            }
        }
        
        // الحصول على الأماكن المحفوظة الحالية
        const savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
        
        // التحقق مما إذا كان المكان محفوظًا بالفعل
        const placeIndex = savedPlaces.findIndex(place => place.id === this.currentPlaceId);
        
        if (placeIndex !== -1) {
            // إذا كان المكان محفوظًا بالفعل، قم بإزالته
            savedPlaces.splice(placeIndex, 1);
            localStorage.setItem('savedPlaces', JSON.stringify(savedPlaces));
            
            // تحديث زر الحفظ
            this.updateSaveButton();
            
            // إظهار إشعار
            this.showNotification('تم إزالة المكان من المحفوظات', 'info');
        } else {
            // إذا لم يكن المكان محفوظًا، قم بإضافته
            const newPlace = {
                id: this.currentPlaceId,
                name: placeName,
                address: placeAddress,
                lat: lat,
                lng: lng,
                savedAt: new Date().toISOString()
            };
            
            savedPlaces.push(newPlace);
            localStorage.setItem('savedPlaces', JSON.stringify(savedPlaces));
            
            // تحديث زر الحفظ
            this.updateSaveButton();
            
            // إظهار إشعار
            this.showNotification('تم حفظ المكان بنجاح', 'success');
        }
    }
    
    /**
     * الحصول على المسار إلى المكان
     */
    getDirections() {
        if (!this.currentPlaceId) return;
        
        // الحصول على إحداثيات المكان
        const place = this.getCurrentPlace();
        if (!place || !place.geometry || !place.geometry.location) {
            this.showNotification('لا يمكن الحصول على إحداثيات المكان', 'error');
            return;
        }
        
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();
        const placeName = document.getElementById('place-name').textContent;
        
        // استدعاء دالة الحصول على المسار من الكائن الرئيسي
        if (window.yemenMaps && window.yemenMaps.getDirections) {
            window.yemenMaps.getDirections(lat, lng, placeName);
            this.closePlaceDetails();
        } else {
            // فتح خرائط جوجل كبديل
            const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${this.currentPlaceId}`;
            window.open(googleMapsUrl, '_blank');
        }
    }
    
    /**
     * الاتصال بالمكان
     */
    callPlace() {
        const contactPhone = document.getElementById('contact-phone');
        if (!contactPhone) return;
        
        const phoneSpan = contactPhone.querySelector('span');
        if (!phoneSpan) return;
        
        const phone = phoneSpan.textContent.trim();
        if (!phone) return;
        
        // إنشاء رابط الاتصال
        window.location.href = `tel:${phone}`;
    }
    
    /**
     * فتح الموقع الإلكتروني للمكان
     */
    openWebsite() {
        const contactWebsite = document.getElementById('contact-website');
        if (!contactWebsite) return;
        
        const websiteSpan = contactWebsite.querySelector('span');
        if (!websiteSpan) return;
        
        // الحصول على الموقع الإلكتروني من بيانات المكان الحالي
        const place = this.getCurrentPlace();
        if (place && place.website) {
            window.open(place.website, '_blank');
        } else {
            this.showNotification('لا يوجد موقع إلكتروني متاح', 'error');
        }
    }
    
    /**
     * الحصول على بيانات المكان الحالي
     * @returns {Object} بيانات المكان
     */
    getCurrentPlace() {
        // هذه الدالة تعتمد على كيفية تخزين بيانات المكان في التطبيق
        // يمكن تعديلها لتتناسب مع بنية التطبيق
        
        if (window.yemenMaps && window.yemenMaps.places) {
            return window.yemenMaps.places.find(place => place.place_id === this.currentPlaceId);
        }
        
        return null;
    }
    
    /**
     * إظهار إشعار للمستخدم
     * @param {string} message - نص الإشعار
     * @param {string} type - نوع الإشعار (success, error, info, warning)
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }
}

// إنشاء مدير تفاصيل المكان عند تحميل الصفحة
let placeDetailsManager;
document.addEventListener('DOMContentLoaded', () => {
    placeDetailsManager = new PlaceDetailsManager();
    
    // إتاحة الكائن للاستخدام العام
    window.placeDetailsManager = placeDetailsManager;
});

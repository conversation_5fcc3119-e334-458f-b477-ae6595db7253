const { Pool } = require('pg');
const logger = require('../utils/logger');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

pool.on('error', (err) => {
  logger.error('Unexpected error on idle client', err);
});

module.exports = {
  query: (text, params) => {
    logger.debug('Executing query:', { text, params });
    return pool.query(text, params);
  },
};

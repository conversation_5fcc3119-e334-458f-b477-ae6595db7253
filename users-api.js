// يمن ناف - واجهة برمجة تطبيقات المستخدمين
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const dotenv = require('dotenv');
const path = require('path');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'yemen123',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps',
};

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// إعداد التطبيق
const app = express();
const PORT = process.env.PORT || 3000;

// middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// تمكين CORS لجميع الطلبات
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    return res.status(200).json({});
  }
  console.log(`طلب ${req.method} إلى ${req.path}`);
  next();
});

// واجهة API للمستخدمين
app.get('/api/users', async (req, res) => {
  try {
    console.log('جاري تنفيذ استعلام المستخدمين...');
    
    const query = `
      SELECT
        u.id as user_id,
        u.username,
        u.email,
        u.full_name,
        u.phone,
        u.profile_image,
        u.account_type,
        r.name as role_name,
        r.id as role_id,
        u.created_at as registration_date,
        u.last_login,
        u.is_active,
        u.is_verified
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      ORDER BY u.id
    `;
    
    console.log('استعلام SQL:', query);
    
    const result = await pool.query(query);
    console.log(`تم الحصول على ${result.rows.length} مستخدم`);
    
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين', error: err.message });
  }
});

// نقطة نهاية لاختبار الاتصال بقاعدة البيانات
app.get('/api/db-test', async (req, res) => {
  try {
    const result = await pool.query('SELECT version()');
    res.json({ 
      success: true, 
      message: 'تم الاتصال بقاعدة البيانات بنجاح',
      version: result.rows[0].version 
    });
  } catch (err) {
    console.error('خطأ في اختبار قاعدة البيانات:', err);
    res.status(500).json({ 
      success: false, 
      message: 'خطأ في الاتصال بقاعدة البيانات', 
      error: err.message 
    });
  }
});

// بدء تشغيل الخادم
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ: ${PORT}`);
  console.log(`يمكنك الوصول إلى API المستخدمين على: http://localhost:${PORT}/api/users`);
  console.log(`يمكنك اختبار الاتصال بقاعدة البيانات على: http://localhost:${PORT}/api/db-test`);
  console.log(`يمكنك الوصول إلى صفحة المستخدمين على: http://localhost:${PORT}/admin-users-direct.html`);
});

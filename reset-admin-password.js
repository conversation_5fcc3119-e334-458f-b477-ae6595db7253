// سكريبت لإعادة تعيين كلمة مرور المستخدم المسؤول
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  host: 'localhost',
  port: 5433, // استخدام المنفذ الصحيح 5433
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function resetAdminPassword() {
  try {
    console.log('بدء عملية إعادة تعيين كلمة مرور المستخدم المسؤول...');
    
    // كلمة المرور الجديدة للمستخدم المسؤول (admin)
    const newPassword = 'admin';
    
    // تشفير كلمة المرور باستخدام bcrypt
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // تحديث كلمة المرور في قاعدة البيانات
    await pool.query(
      'UPDATE users SET password = $1 WHERE username = $2',
      [hashedPassword, 'admin']
    );
    
    console.log('تم إعادة تعيين كلمة مرور المستخدم المسؤول بنجاح!');
    console.log('\nيمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: admin');
    console.log('- كلمة المرور: admin');
    
  } catch (error) {
    console.error('حدث خطأ أثناء إعادة تعيين كلمة المرور:', error);
  } finally {
    await pool.end();
  }
}

resetAdminPassword();

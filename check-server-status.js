// فحص حالة خادم يمن GPS
const http = require('http');

console.log('🔍 فحص حالة خادم يمن GPS...\n');

const checkEndpoint = (url, name) => {
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        const req = http.get(url, (res) => {
            const responseTime = Date.now() - startTime;
            const status = res.statusCode;
            
            if (status === 200) {
                console.log(`✅ ${name}: يعمل بشكل طبيعي (${responseTime}ms)`);
                resolve(true);
            } else {
                console.log(`⚠️ ${name}: رمز الحالة ${status} (${responseTime}ms)`);
                resolve(false);
            }
        });
        
        req.on('error', (error) => {
            console.log(`❌ ${name}: غير متاح - ${error.message}`);
            resolve(false);
        });
        
        req.setTimeout(5000, () => {
            console.log(`⏰ ${name}: انتهت مهلة الاتصال`);
            req.destroy();
            resolve(false);
        });
    });
};

async function checkServerStatus() {
    const baseUrl = 'http://localhost:5000';
    const externalUrl = 'http://***********:5000';
    
    console.log('📡 فحص الخادم المحلي...');
    const localResults = await Promise.all([
        checkEndpoint(`${baseUrl}/`, 'الصفحة الرئيسية'),
        checkEndpoint(`${baseUrl}/api/places`, 'API الأماكن'),
        checkEndpoint(`${baseUrl}/places`, 'صفحة الأماكن'),
        checkEndpoint(`${baseUrl}/admin`, 'لوحة التحكم')
    ]);
    
    console.log('\n🌐 فحص الخادم الخارجي...');
    const externalResults = await Promise.all([
        checkEndpoint(`${externalUrl}/`, 'الصفحة الرئيسية'),
        checkEndpoint(`${externalUrl}/api/places`, 'API الأماكن'),
        checkEndpoint(`${externalUrl}/places`, 'صفحة الأماكن'),
        checkEndpoint(`${externalUrl}/admin`, 'لوحة التحكم')
    ]);
    
    console.log('\n📊 ملخص النتائج:');
    console.log('================');
    
    const localWorking = localResults.filter(r => r).length;
    const externalWorking = externalResults.filter(r => r).length;
    
    console.log(`🏠 الخادم المحلي: ${localWorking}/4 خدمات تعمل`);
    console.log(`🌐 الخادم الخارجي: ${externalWorking}/4 خدمات تعمل`);
    
    if (localWorking === 4) {
        console.log('\n✅ الخادم المحلي يعمل بشكل مثالي!');
        console.log('🔗 الروابط المحلية:');
        console.log(`   🗺️ الخريطة: ${baseUrl}/`);
        console.log(`   📍 الأماكن: ${baseUrl}/places`);
        console.log(`   ⚙️ الإدارة: ${baseUrl}/admin`);
        console.log(`   📡 API: ${baseUrl}/api/places`);
    } else {
        console.log('\n⚠️ بعض خدمات الخادم المحلي لا تعمل');
    }
    
    if (externalWorking === 4) {
        console.log('\n🌟 الخادم الخارجي يعمل بشكل مثالي!');
        console.log('🔗 الروابط الخارجية:');
        console.log(`   🗺️ الخريطة: ${externalUrl}/`);
        console.log(`   📍 الأماكن: ${externalUrl}/places`);
        console.log(`   ⚙️ الإدارة: ${externalUrl}/admin`);
        console.log(`   📡 API: ${externalUrl}/api/places`);
    } else if (externalWorking > 0) {
        console.log('\n⚠️ بعض خدمات الخادم الخارجي لا تعمل');
    } else {
        console.log('\n❌ الخادم الخارجي غير متاح');
        console.log('💡 تأكد من:');
        console.log('   - تشغيل الخادم على البورت 5000');
        console.log('   - فتح البورت 5000 في الفايروول');
        console.log('   - صحة عنوان IP الخارجي');
    }
    
    console.log('\n🔄 لإعادة الفحص، شغل هذا السكريبت مرة أخرى');
}

// تشغيل الفحص
checkServerStatus().catch(console.error);

// فحص بنية قاعدة البيانات
const { Pool } = require('pg');

// إعداد اتصال قاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'admin',
  port: 5432,
});

async function checkDatabaseStructure() {
  const client = await pool.connect();
  
  try {
    console.log('فحص بنية قاعدة البيانات...\n');
    
    // فحص جدول المستخدمين
    console.log('=== بنية جدول المستخدمين ===');
    const usersColumns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `);
    console.log('أعمدة جدول users:');
    usersColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // فحص جدول التصنيفات
    console.log('\n=== بنية جدول التصنيفات ===');
    const categoriesColumns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'categories' 
      ORDER BY ordinal_position
    `);
    console.log('أعمدة جدول categories:');
    categoriesColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // فحص جدول المواقع
    console.log('\n=== بنية جدول المواقع ===');
    const locationsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'locations' 
      ORDER BY ordinal_position
    `);
    console.log('أعمدة جدول locations:');
    locationsColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // فحص جدول العملاء
    console.log('\n=== بنية جدول العملاء ===');
    const clientsColumns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'clients' 
      ORDER BY ordinal_position
    `);
    console.log('أعمدة جدول clients:');
    clientsColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // عرض عينة من البيانات الموجودة
    console.log('\n=== عينة من البيانات الموجودة ===');
    
    const usersCount = await client.query('SELECT COUNT(*) FROM users');
    console.log(`عدد المستخدمين: ${usersCount.rows[0].count}`);
    
    const categoriesCount = await client.query('SELECT COUNT(*) FROM categories');
    console.log(`عدد التصنيفات: ${categoriesCount.rows[0].count}`);
    
    const locationsCount = await client.query('SELECT COUNT(*) FROM locations');
    console.log(`عدد المواقع: ${locationsCount.rows[0].count}`);
    
    const clientsCount = await client.query('SELECT COUNT(*) FROM clients');
    console.log(`عدد العملاء: ${clientsCount.rows[0].count}`);
    
  } catch (error) {
    console.error('خطأ في فحص قاعدة البيانات:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل الدالة
checkDatabaseStructure();

<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Customize your theme here -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:fontFamily">@font/roboto</item>
    </style>

    <!-- No Action Bar theme -->
    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash screen theme -->
    <style name="SplashTheme" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
    </style>

    <!-- Button styles -->
    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="SecondaryButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!-- Text styles -->
    <style name="HeaderText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="SubheaderText">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/textColorPrimary</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/textColorSecondary</item>
    </style>

    <style name="CaptionText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/textColorTertiary</item>
    </style>

    <!-- Input field styles -->
    <style name="InputField" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <!-- Card styles -->
    <style name="LocationCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>
</resources>

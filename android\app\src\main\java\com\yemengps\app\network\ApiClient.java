package com.yemengps.app.network;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.model.User;

import java.util.List;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Client for communicating with the API
 */
public class ApiClient {

    private static final String BASE_URL = "https://api.yemengps.com/"; // Replace with your actual API URL
    private final ApiService apiService;
    private final PreferenceManager preferenceManager;

    /**
     * Constructor
     * @param context The application context
     */
    public ApiClient(Context context) {
        preferenceManager = new PreferenceManager(context);
        
        // Create a logging interceptor
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        // Create an OkHttpClient with the interceptor
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)
                .addInterceptor(chain -> {
                    Request original = chain.request();
                    
                    // Add authorization header if user is logged in
                    if (preferenceManager.isLoggedIn() && preferenceManager.getAuthToken() != null) {
                        Request request = original.newBuilder()
                                .header("Authorization", "Bearer " + preferenceManager.getAuthToken())
                                .method(original.method(), original.body())
                                .build();
                        return chain.proceed(request);
                    }
                    
                    return chain.proceed(original);
                })
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        
        // Create a Gson instance that handles dates properly
        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                .create();
        
        // Create a Retrofit instance
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        
        // Create the API service
        apiService = retrofit.create(ApiService.class);
    }

    /**
     * Register a new user
     * @param user The user to register
     * @param callback The callback to handle the response
     */
    public void registerUser(User user, Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.registerUser(user);
        call.enqueue(callback);
    }

    /**
     * Login a user
     * @param email The user's email
     * @param deviceId The user's device ID
     * @param callback The callback to handle the response
     */
    public void loginUser(String email, String deviceId, Callback<ApiResponse<User>> callback) {
        LoginRequest request = new LoginRequest(email, deviceId);
        Call<ApiResponse<User>> call = apiService.loginUser(request);
        call.enqueue(callback);
    }

    /**
     * Get all locations
     * @param callback The callback to handle the response
     */
    public void getAllLocations(Callback<ApiResponse<List<LocationPoint>>> callback) {
        Call<ApiResponse<List<LocationPoint>>> call = apiService.getAllLocations();
        call.enqueue(callback);
    }

    /**
     * Get locations by type
     * @param type The type of locations to get
     * @param callback The callback to handle the response
     */
    public void getLocationsByType(String type, Callback<ApiResponse<List<LocationPoint>>> callback) {
        Call<ApiResponse<List<LocationPoint>>> call = apiService.getLocationsByType(type);
        call.enqueue(callback);
    }

    /**
     * Get locations within a radius
     * @param latitude The latitude of the center point
     * @param longitude The longitude of the center point
     * @param radiusKm The radius in kilometers
     * @param callback The callback to handle the response
     */
    public void getLocationsWithinRadius(double latitude, double longitude, double radiusKm, Callback<ApiResponse<List<LocationPoint>>> callback) {
        Call<ApiResponse<List<LocationPoint>>> call = apiService.getLocationsWithinRadius(latitude, longitude, radiusKm);
        call.enqueue(callback);
    }

    /**
     * Add a new location
     * @param location The location to add
     * @param callback The callback to handle the response
     */
    public void addLocation(LocationPoint location, Callback<ApiResponse<LocationPoint>> callback) {
        Call<ApiResponse<LocationPoint>> call = apiService.addLocation(location);
        call.enqueue(callback);
    }

    /**
     * Update a location
     * @param id The ID of the location to update
     * @param location The updated location
     * @param callback The callback to handle the response
     */
    public void updateLocation(String id, LocationPoint location, Callback<ApiResponse<LocationPoint>> callback) {
        Call<ApiResponse<LocationPoint>> call = apiService.updateLocation(id, location);
        call.enqueue(callback);
    }

    /**
     * Delete a location
     * @param id The ID of the location to delete
     * @param callback The callback to handle the response
     */
    public void deleteLocation(String id, Callback<ApiResponse<Void>> callback) {
        Call<ApiResponse<Void>> call = apiService.deleteLocation(id);
        call.enqueue(callback);
    }

    /**
     * Get pending locations
     * @param callback The callback to handle the response
     */
    public void getPendingLocations(Callback<ApiResponse<List<LocationPoint>>> callback) {
        Call<ApiResponse<List<LocationPoint>>> call = apiService.getPendingLocations();
        call.enqueue(callback);
    }

    /**
     * Approve a location
     * @param id The ID of the location to approve
     * @param callback The callback to handle the response
     */
    public void approveLocation(String id, Callback<ApiResponse<LocationPoint>> callback) {
        Call<ApiResponse<LocationPoint>> call = apiService.approveLocation(id);
        call.enqueue(callback);
    }

    /**
     * Reject a location
     * @param id The ID of the location to reject
     * @param callback The callback to handle the response
     */
    public void rejectLocation(String id, Callback<ApiResponse<Void>> callback) {
        Call<ApiResponse<Void>> call = apiService.rejectLocation(id);
        call.enqueue(callback);
    }

    /**
     * Get user profile
     * @param callback The callback to handle the response
     */
    public void getUserProfile(Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.getUserProfile();
        call.enqueue(callback);
    }

    /**
     * Update user profile
     * @param user The updated user
     * @param callback The callback to handle the response
     */
    public void updateUserProfile(User user, Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.updateUserProfile(user);
        call.enqueue(callback);
    }

    /**
     * Get all users (admin only)
     * @param callback The callback to handle the response
     */
    public void getAllUsers(Callback<ApiResponse<List<User>>> callback) {
        Call<ApiResponse<List<User>>> call = apiService.getAllUsers();
        call.enqueue(callback);
    }

    /**
     * Make a user an admin (admin only)
     * @param id The ID of the user to make an admin
     * @param callback The callback to handle the response
     */
    public void makeUserAdmin(String id, Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.makeUserAdmin(id);
        call.enqueue(callback);
    }

    /**
     * Remove admin privileges from a user (admin only)
     * @param id The ID of the user to remove admin privileges from
     * @param callback The callback to handle the response
     */
    public void removeUserAdmin(String id, Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.removeUserAdmin(id);
        call.enqueue(callback);
    }

    /**
     * Deactivate a user (admin only)
     * @param id The ID of the user to deactivate
     * @param callback The callback to handle the response
     */
    public void deactivateUser(String id, Callback<ApiResponse<Void>> callback) {
        Call<ApiResponse<Void>> call = apiService.deactivateUser(id);
        call.enqueue(callback);
    }

    /**
     * Activate a user (admin only)
     * @param id The ID of the user to activate
     * @param callback The callback to handle the response
     */
    public void activateUser(String id, Callback<ApiResponse<User>> callback) {
        Call<ApiResponse<User>> call = apiService.activateUser(id);
        call.enqueue(callback);
    }

    /**
     * Login request class
     */
    private static class LoginRequest {
        private final String email;
        private final String deviceId;

        public LoginRequest(String email, String deviceId) {
            this.email = email;
            this.deviceId = deviceId;
        }
    }
}

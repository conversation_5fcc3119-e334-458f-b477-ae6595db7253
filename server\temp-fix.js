// Archivo temporal para arreglar el admin-db.js
const fs = require('fs');
const path = require('path');

// Ruta al archivo admin-db.js
const adminDbPath = path.join(__dirname, '..', 'public', 'js', 'admin-db.js');

// Leer el contenido actual del archivo
fs.readFile(adminDbPath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error al leer el archivo:', err);
        return;
    }
    
    // Añadir la llave de cierre para el evento DOMContentLoaded
    const updatedContent = data + '\n// Cierre del evento DOMContentLoaded\n});';
    
    // Escribir el contenido actualizado de vuelta al archivo
    fs.writeFile(adminDbPath, updatedContent, 'utf8', (err) => {
        if (err) {
            console.error('Error al escribir el archivo:', err);
            return;
        }
        console.log('Archivo admin-db.js actualizado correctamente');
    });
});

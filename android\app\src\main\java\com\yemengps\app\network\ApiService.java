package com.yemengps.app.network;

import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.model.User;

import java.util.List;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

/**
 * Retrofit service interface for API calls
 */
public interface ApiService {

    // User endpoints
    
    /**
     * Register a new user
     * @param user The user to register
     * @return The registered user
     */
    @POST("users/register")
    Call<ApiResponse<User>> registerUser(@Body User user);
    
    /**
     * Login a user
     * @param loginRequest The login request containing email and device ID
     * @return The logged in user
     */
    @POST("users/login")
    Call<ApiResponse<User>> loginUser(@Body Object loginRequest);
    
    /**
     * Get the current user's profile
     * @return The user profile
     */
    @GET("users/profile")
    Call<ApiResponse<User>> getUserProfile();
    
    /**
     * Update the current user's profile
     * @param user The updated user
     * @return The updated user
     */
    @PUT("users/profile")
    Call<ApiResponse<User>> updateUserProfile(@Body User user);
    
    /**
     * Get all users (admin only)
     * @return All users
     */
    @GET("admin/users")
    Call<ApiResponse<List<User>>> getAllUsers();
    
    /**
     * Make a user an admin (admin only)
     * @param id The ID of the user to make an admin
     * @return The updated user
     */
    @PUT("admin/users/{id}/make-admin")
    Call<ApiResponse<User>> makeUserAdmin(@Path("id") String id);
    
    /**
     * Remove admin privileges from a user (admin only)
     * @param id The ID of the user to remove admin privileges from
     * @return The updated user
     */
    @PUT("admin/users/{id}/remove-admin")
    Call<ApiResponse<User>> removeUserAdmin(@Path("id") String id);
    
    /**
     * Deactivate a user (admin only)
     * @param id The ID of the user to deactivate
     * @return Void
     */
    @PUT("admin/users/{id}/deactivate")
    Call<ApiResponse<Void>> deactivateUser(@Path("id") String id);
    
    /**
     * Activate a user (admin only)
     * @param id The ID of the user to activate
     * @return The updated user
     */
    @PUT("admin/users/{id}/activate")
    Call<ApiResponse<User>> activateUser(@Path("id") String id);
    
    // Location endpoints
    
    /**
     * Get all locations
     * @return All locations
     */
    @GET("locations")
    Call<ApiResponse<List<LocationPoint>>> getAllLocations();
    
    /**
     * Get locations by type
     * @param type The type of locations to get
     * @return All locations of the specified type
     */
    @GET("locations/type/{type}")
    Call<ApiResponse<List<LocationPoint>>> getLocationsByType(@Path("type") String type);
    
    /**
     * Get locations within a radius
     * @param latitude The latitude of the center point
     * @param longitude The longitude of the center point
     * @param radiusKm The radius in kilometers
     * @return All locations within the radius
     */
    @GET("locations/nearby")
    Call<ApiResponse<List<LocationPoint>>> getLocationsWithinRadius(
            @Query("lat") double latitude,
            @Query("lng") double longitude,
            @Query("radius") double radiusKm);
    
    /**
     * Add a new location
     * @param location The location to add
     * @return The added location
     */
    @POST("locations")
    Call<ApiResponse<LocationPoint>> addLocation(@Body LocationPoint location);
    
    /**
     * Update a location
     * @param id The ID of the location to update
     * @param location The updated location
     * @return The updated location
     */
    @PUT("locations/{id}")
    Call<ApiResponse<LocationPoint>> updateLocation(
            @Path("id") String id,
            @Body LocationPoint location);
    
    /**
     * Delete a location
     * @param id The ID of the location to delete
     * @return Void
     */
    @DELETE("locations/{id}")
    Call<ApiResponse<Void>> deleteLocation(@Path("id") String id);
    
    /**
     * Get pending locations (admin only)
     * @return All pending locations
     */
    @GET("admin/locations/pending")
    Call<ApiResponse<List<LocationPoint>>> getPendingLocations();
    
    /**
     * Approve a location (admin only)
     * @param id The ID of the location to approve
     * @return The approved location
     */
    @PUT("admin/locations/{id}/approve")
    Call<ApiResponse<LocationPoint>> approveLocation(@Path("id") String id);
    
    /**
     * Reject a location (admin only)
     * @param id The ID of the location to reject
     * @return Void
     */
    @PUT("admin/locations/{id}/reject")
    Call<ApiResponse<Void>> rejectLocation(@Path("id") String id);
}

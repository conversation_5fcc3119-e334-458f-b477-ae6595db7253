// خدمة محاكاة الموقع لنظام "يمن ناف"
const express = require('express');
const cors = require('cors');
const app = express();
const port = 3000;

// تمكين CORS لجميع الطلبات
app.use(cors());
app.use(express.json());

// الموقع الافتراضي (صنعاء، اليمن)
const DEFAULT_LOCATION = {
  latitude: 15.3694,
  longitude: 44.191,
  accuracy: 10,
  timestamp: Date.now()
};

// تخزين الموقع الحالي
let currentLocation = { ...DEFAULT_LOCATION };

// الحصول على الموقع الحالي
app.get('/api/location', (req, res) => {
  // تحديث الطابع الزمني في كل مرة يتم فيها طلب الموقع
  currentLocation.timestamp = Date.now();
  res.json(currentLocation);
});

// تعيين موقع مخصص
app.post('/api/location', (req, res) => {
  const { latitude, longitude, accuracy } = req.body;
  
  if (latitude !== undefined && longitude !== undefined) {
    currentLocation = {
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      accuracy: accuracy !== undefined ? parseFloat(accuracy) : 10,
      timestamp: Date.now()
    };
    res.json({ success: true, message: 'تم تحديث الموقع بنجاح', location: currentLocation });
  } else {
    res.status(400).json({ success: false, message: 'يجب توفير خط العرض وخط الطول' });
  }
});

// إعادة تعيين الموقع إلى الموقع الافتراضي
app.post('/api/location/reset', (req, res) => {
  currentLocation = { ...DEFAULT_LOCATION, timestamp: Date.now() };
  res.json({ success: true, message: 'تم إعادة تعيين الموقع إلى الموقع الافتراضي', location: currentLocation });
});

// صفحة الترحيب البسيطة
app.get('/', (req, res) => {
  res.send(`
    <html dir="rtl">
      <head>
        <title>خدمة محاكاة الموقع - يمن ناف</title>
        <meta charset="UTF-8">
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          h1 { color: #2c3e50; }
          .container { max-width: 800px; margin: 0 auto; }
          .endpoint { background: #f8f9fa; padding: 10px; margin-bottom: 10px; border-radius: 5px; }
          code { background: #eee; padding: 2px 5px; border-radius: 3px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>خدمة محاكاة الموقع - يمن ناف</h1>
          <p>هذه خدمة بسيطة توفر إحداثيات ثابتة لصنعاء لاستخدامها في تطبيق "يمن ناف".</p>
          
          <h2>نقاط النهاية المتاحة:</h2>
          
          <div class="endpoint">
            <h3>الحصول على الموقع الحالي</h3>
            <p><code>GET /api/location</code></p>
            <p>مثال: <a href="/api/location" target="_blank">/api/location</a></p>
          </div>
          
          <div class="endpoint">
            <h3>تعيين موقع مخصص</h3>
            <p><code>POST /api/location</code></p>
            <p>مع بيانات JSON تحتوي على:</p>
            <pre><code>{
  "latitude": 15.4,
  "longitude": 44.2,
  "accuracy": 5
}</code></pre>
          </div>
          
          <div class="endpoint">
            <h3>إعادة تعيين الموقع إلى الموقع الافتراضي</h3>
            <p><code>POST /api/location/reset</code></p>
          </div>
          
          <p>الموقع الحالي: ${JSON.stringify(currentLocation)}</p>
        </div>
      </body>
    </html>
  `);
});

// بدء الخادم
app.listen(port, () => {
  console.log(`خدمة محاكاة الموقع تعمل على المنفذ ${port}`);
  console.log(`الموقع الافتراضي: ${DEFAULT_LOCATION.latitude}, ${DEFAULT_LOCATION.longitude}`);
});

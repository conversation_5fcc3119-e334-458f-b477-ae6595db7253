@echo off
echo ========================================
echo    Yemen Maps Complete - Project Setup
echo ========================================
echo.

REM إنشاء هيكل المجلدات
echo Creating project structure...
mkdir "E:\yemen-maps-complete"
mkdir "E:\yemen-maps-complete\data"
mkdir "E:\yemen-maps-complete\data\maptiler"
mkdir "E:\yemen-maps-complete\data\tiles"
mkdir "E:\yemen-maps-complete\data\backups"
mkdir "E:\yemen-maps-complete\database"
mkdir "E:\yemen-maps-complete\database\migrations"
mkdir "E:\yemen-maps-complete\database\seeds"
mkdir "E:\yemen-maps-complete\images"
mkdir "E:\yemen-maps-complete\images\places"
mkdir "E:\yemen-maps-complete\images\imported"
mkdir "E:\yemen-maps-complete\images\temp"
mkdir "E:\yemen-maps-complete\server"
mkdir "E:\yemen-maps-complete\server\api"
mkdir "E:\yemen-maps-complete\server\tiles"
mkdir "E:\yemen-maps-complete\server\admin"
mkdir "E:\yemen-maps-complete\public"
mkdir "E:\yemen-maps-complete\public\css"
mkdir "E:\yemen-maps-complete\public\js"
mkdir "E:\yemen-maps-complete\public\assets"
mkdir "E:\yemen-maps-complete\tools"
mkdir "E:\yemen-maps-complete\tools\import"
mkdir "E:\yemen-maps-complete\tools\download"
mkdir "E:\yemen-maps-complete\tools\backup"

echo Project structure created successfully!
echo.

REM نسخ الصور الموجودة
echo Copying existing images...
if exist "E:\yemen gps\public\images\places" (
    xcopy "E:\yemen gps\public\images\places\*.*" "E:\yemen-maps-complete\images\imported\" /E /I /Y
    echo Images copied successfully!
) else (
    echo Warning: Source images directory not found!
)

echo.
echo ========================================
echo Project setup completed!
echo Location: E:\yemen-maps-complete
echo ========================================
pause

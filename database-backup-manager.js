/**
 * مدير النسخ الاحتياطية لقاعدة بيانات Yemen GPS
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const { pool } = require('./server/db-config');

class DatabaseBackupManager {
    constructor() {
        this.backupDir = './backups';
        this.dbConfig = {
            user: 'yemen',
            host: 'localhost',
            database: 'yemen_gps',
            password: 'admin',
            port: 5432
        };
        
        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
        }
    }

    /**
     * إنشاء نسخة احتياطية من قاعدة البيانات
     */
    async createBackup(customName = null) {
        try {
            const timestamp = new Date().toISOString()
                .replace(/[:.]/g, '-')
                .replace('T', '_')
                .split('.')[0];
            
            const fileName = customName || `yemen_gps_backup_${timestamp}.sql`;
            const filePath = path.join(this.backupDir, fileName);
            
            console.log('🔄 جاري إنشاء نسخة احتياطية...');
            console.log(`📁 اسم الملف: ${fileName}`);
            
            // تحديد متغيرات البيئة لـ PostgreSQL
            const env = {
                ...process.env,
                PGPASSWORD: this.dbConfig.password
            };
            
            const command = `pg_dump -h ${this.dbConfig.host} -p ${this.dbConfig.port} -U ${this.dbConfig.user} -d ${this.dbConfig.database} -f "${filePath}" --verbose --clean --if-exists --create`;
            
            return new Promise((resolve, reject) => {
                exec(command, { env }, (error, stdout, stderr) => {
                    if (error) {
                        console.error('❌ فشل في إنشاء النسخة الاحتياطية:', error.message);
                        reject(error);
                        return;
                    }
                    
                    // التحقق من وجود الملف وحجمه
                    if (fs.existsSync(filePath)) {
                        const stats = fs.statSync(filePath);
                        console.log('✅ تم إنشاء النسخة الاحتياطية بنجاح');
                        console.log(`📊 حجم الملف: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
                        console.log(`📍 المسار: ${filePath}`);
                        
                        resolve({
                            success: true,
                            fileName,
                            filePath,
                            size: stats.size,
                            timestamp: new Date()
                        });
                    } else {
                        reject(new Error('فشل في إنشاء ملف النسخة الاحتياطية'));
                    }
                });
            });
            
        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error.message);
            throw error;
        }
    }

    /**
     * استعادة قاعدة البيانات من نسخة احتياطية
     */
    async restoreBackup(fileName) {
        try {
            const filePath = path.join(this.backupDir, fileName);
            
            // التحقق من وجود الملف
            if (!fs.existsSync(filePath)) {
                throw new Error(`ملف النسخة الاحتياطية غير موجود: ${fileName}`);
            }
            
            console.log('🔄 جاري استعادة قاعدة البيانات...');
            console.log(`📁 من الملف: ${fileName}`);
            
            // تحديد متغيرات البيئة لـ PostgreSQL
            const env = {
                ...process.env,
                PGPASSWORD: this.dbConfig.password
            };
            
            const command = `psql -h ${this.dbConfig.host} -p ${this.dbConfig.port} -U ${this.dbConfig.user} -d postgres -f "${filePath}" --verbose`;
            
            return new Promise((resolve, reject) => {
                exec(command, { env }, (error, stdout, stderr) => {
                    if (error) {
                        console.error('❌ فشل في استعادة قاعدة البيانات:', error.message);
                        reject(error);
                        return;
                    }
                    
                    console.log('✅ تم استعادة قاعدة البيانات بنجاح');
                    resolve({
                        success: true,
                        fileName,
                        timestamp: new Date()
                    });
                });
            });
            
        } catch (error) {
            console.error('❌ خطأ في استعادة قاعدة البيانات:', error.message);
            throw error;
        }
    }

    /**
     * عرض قائمة بالنسخ الاحتياطية المتاحة
     */
    listBackups() {
        try {
            const files = fs.readdirSync(this.backupDir)
                .filter(file => file.endsWith('.sql'))
                .map(file => {
                    const filePath = path.join(this.backupDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                })
                .sort((a, b) => b.created - a.created);
            
            console.log('📋 النسخ الاحتياطية المتاحة:');
            files.forEach((file, index) => {
                console.log(`${index + 1}. ${file.name}`);
                console.log(`   📊 الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
                console.log(`   📅 تاريخ الإنشاء: ${file.created.toLocaleString('ar-SA')}`);
                console.log('');
            });
            
            return files;
            
        } catch (error) {
            console.error('❌ خطأ في قراءة النسخ الاحتياطية:', error.message);
            throw error;
        }
    }

    /**
     * حذف نسخة احتياطية
     */
    deleteBackup(fileName) {
        try {
            const filePath = path.join(this.backupDir, fileName);
            
            if (!fs.existsSync(filePath)) {
                throw new Error(`الملف غير موجود: ${fileName}`);
            }
            
            fs.unlinkSync(filePath);
            console.log(`✅ تم حذف النسخة الاحتياطية: ${fileName}`);
            
            return { success: true, fileName };
            
        } catch (error) {
            console.error('❌ خطأ في حذف النسخة الاحتياطية:', error.message);
            throw error;
        }
    }

    /**
     * التحقق من حالة قاعدة البيانات
     */
    async checkDatabaseStatus() {
        try {
            const client = await pool.connect();
            
            // الحصول على معلومات قاعدة البيانات
            const dbInfo = await client.query(`
                SELECT 
                    current_database() as database_name,
                    current_user as current_user,
                    version() as version
            `);
            
            // الحصول على قائمة الجداول
            const tables = await client.query(`
                SELECT 
                    table_name,
                    table_type
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            `);
            
            // الحصول على إحصائيات الجداول
            const stats = await client.query(`
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes
                FROM pg_stat_user_tables
                ORDER BY tablename
            `);
            
            client.release();
            
            console.log('📊 حالة قاعدة البيانات:');
            console.log(`🗄️  اسم قاعدة البيانات: ${dbInfo.rows[0].database_name}`);
            console.log(`👤 المستخدم الحالي: ${dbInfo.rows[0].current_user}`);
            console.log(`📋 عدد الجداول: ${tables.rows.length}`);
            console.log('');
            
            return {
                database: dbInfo.rows[0],
                tables: tables.rows,
                statistics: stats.rows
            };
            
        } catch (error) {
            console.error('❌ خطأ في التحقق من حالة قاعدة البيانات:', error.message);
            throw error;
        }
    }
}

// تصدير الكلاس
module.exports = DatabaseBackupManager;

// إذا تم تشغيل الملف مباشرة
if (require.main === module) {
    const backupManager = new DatabaseBackupManager();
    
    // معالجة الأوامر من سطر الأوامر
    const command = process.argv[2];
    const fileName = process.argv[3];
    
    switch (command) {
        case 'backup':
            backupManager.createBackup(fileName)
                .then(result => {
                    console.log('✅ تمت العملية بنجاح');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('❌ فشلت العملية:', error.message);
                    process.exit(1);
                });
            break;
            
        case 'restore':
            if (!fileName) {
                console.error('❌ يجب تحديد اسم ملف النسخة الاحتياطية');
                process.exit(1);
            }
            backupManager.restoreBackup(fileName)
                .then(result => {
                    console.log('✅ تمت العملية بنجاح');
                    process.exit(0);
                })
                .catch(error => {
                    console.error('❌ فشلت العملية:', error.message);
                    process.exit(1);
                });
            break;
            
        case 'list':
            backupManager.listBackups();
            break;
            
        case 'status':
            backupManager.checkDatabaseStatus()
                .then(() => process.exit(0))
                .catch(error => {
                    console.error('❌ فشلت العملية:', error.message);
                    process.exit(1);
                });
            break;
            
        default:
            console.log('📖 استخدام مدير النسخ الاحتياطية:');
            console.log('');
            console.log('node database-backup-manager.js backup [اسم_الملف]');
            console.log('node database-backup-manager.js restore <اسم_الملف>');
            console.log('node database-backup-manager.js list');
            console.log('node database-backup-manager.js status');
            console.log('');
            console.log('أمثلة:');
            console.log('node database-backup-manager.js backup');
            console.log('node database-backup-manager.js backup my_backup.sql');
            console.log('node database-backup-manager.js restore yemen_gps_backup_2025-05-24.sql');
            console.log('node database-backup-manager.js list');
            console.log('node database-backup-manager.js status');
    }
}

// سكريبت تحميل المعلومات والصور للأماكن اليمنية
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// إعدادات قاعدة البيانات
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// إعدادات Google Places API
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0'; // مفتاح Google Places API
const PLACES_API_BASE = 'https://maps.googleapis.com/maps/api/place';

// مجلد حفظ الصور
const IMAGES_DIR = path.join(__dirname, '..', 'public', 'images', 'places');

// إنشاء مجلد الصور إذا لم يكن موجوداً
if (!fs.existsSync(IMAGES_DIR)) {
    fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

// قائمة أنواع الأماكن المهمة في اليمن
const PLACE_TYPES = [
    'tourist_attraction',
    'museum',
    'mosque',
    'restaurant',
    'lodging',
    'hospital',
    'school',
    'university',
    'bank',
    'gas_station',
    'shopping_mall',
    'airport',
    'bus_station',
    'park',
    'cemetery',
    'government_office',
    'police',
    'fire_station',
    'embassy'
];

// المحافظات اليمنية مع إحداثياتها
const YEMEN_GOVERNORATES = [
    { name: 'صنعاء', name_en: 'Sanaa', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', name_en: 'Aden', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', name_en: 'Taiz', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', name_en: 'Hodeidah', lat: 14.7978, lng: 42.9545 },
    { name: 'إب', name_en: 'Ibb', lat: 13.9667, lng: 44.1833 },
    { name: 'ذمار', name_en: 'Dhamar', lat: 14.5426, lng: 44.4054 },
    { name: 'المكلا', name_en: 'Mukalla', lat: 14.5425, lng: 49.1242 },
    { name: 'مأرب', name_en: 'Marib', lat: 15.4694, lng: 45.3222 },
    { name: 'حضرموت', name_en: 'Hadramout', lat: 14.5425, lng: 49.1242 },
    { name: 'لحج', name_en: 'Lahij', lat: 13.0582, lng: 44.8819 },
    { name: 'أبين', name_en: 'Abyan', lat: 13.9667, lng: 45.3667 },
    { name: 'شبوة', name_en: 'Shabwah', lat: 14.5333, lng: 46.8333 },
    { name: 'المهرة', name_en: 'Al Mahrah', lat: 16.7167, lng: 52.1667 },
    { name: 'الجوف', name_en: 'Al Jawf', lat: 16.6333, lng: 45.6333 },
    { name: 'صعدة', name_en: 'Saada', lat: 16.9333, lng: 43.7667 },
    { name: 'حجة', name_en: 'Hajjah', lat: 15.6833, lng: 43.6000 },
    { name: 'عمران', name_en: 'Amran', lat: 15.6667, lng: 44.0000 },
    { name: 'البيضاء', name_en: 'Al Bayda', lat: 14.1667, lng: 45.5667 },
    { name: 'ريمة', name_en: 'Raymah', lat: 14.3833, lng: 43.5167 },
    { name: 'الضالع', name_en: 'Ad Dali', lat: 13.7000, lng: 44.7333 }
];

class YemenPlacesDownloader {
    constructor() {
        this.downloadedCount = 0;
        this.errorCount = 0;
        this.totalPlaces = 0;
        this.startTime = Date.now();
    }

    // البحث عن الأماكن في منطقة معينة
    async searchPlacesInArea(governorate, placeType, radius = 50000) {
        try {
            console.log(`🔍 البحث عن ${placeType} في ${governorate.name}...`);

            const url = `${PLACES_API_BASE}/nearbysearch/json`;
            const params = {
                key: GOOGLE_API_KEY,
                location: `${governorate.lat},${governorate.lng}`,
                radius: radius,
                type: placeType,
                language: 'ar'
            };

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                console.log(`   ✅ تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            } else {
                console.log(`   ❌ خطأ في البحث: ${response.data.status}`);
                return [];
            }
        } catch (error) {
            console.error(`   ❌ خطأ في البحث: ${error.message}`);
            return [];
        }
    }

    // الحصول على تفاصيل مكان معين
    async getPlaceDetails(placeId) {
        try {
            const url = `${PLACES_API_BASE}/details/json`;
            const params = {
                key: GOOGLE_API_KEY,
                place_id: placeId,
                fields: 'name,formatted_address,geometry,photos,rating,reviews,formatted_phone_number,website,opening_hours,types,price_level',
                language: 'ar'
            };

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                return response.data.result;
            } else {
                console.log(`   ❌ خطأ في تفاصيل المكان: ${response.data.status}`);
                return null;
            }
        } catch (error) {
            console.error(`   ❌ خطأ في تفاصيل المكان: ${error.message}`);
            return null;
        }
    }

    // تحميل صورة من Google Places
    async downloadPlacePhoto(photoReference, placeId, photoIndex = 0) {
        try {
            const url = `${PLACES_API_BASE}/photo`;
            const params = {
                key: GOOGLE_API_KEY,
                photoreference: photoReference,
                maxwidth: 800
            };

            const response = await axios.get(url, {
                params,
                responseType: 'stream'
            });

            const fileName = `${placeId}_${photoIndex}.jpg`;
            const filePath = path.join(IMAGES_DIR, fileName);

            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => {
                    console.log(`   📷 تم تحميل الصورة: ${fileName}`);
                    resolve(`/images/places/${fileName}`);
                });
                writer.on('error', reject);
            });
        } catch (error) {
            console.error(`   ❌ خطأ في تحميل الصورة: ${error.message}`);
            return null;
        }
    }

    // حفظ بيانات المكان في قاعدة البيانات
    async savePlaceToDatabase(place, governorateId, photos = []) {
        try {
            const query = `
                INSERT INTO places (
                    name_ar, name_en, description_ar, description_en,
                    latitude, longitude, governorate_id, category_id,
                    phone, email, website, whatsapp,
                    rating, google_place_id, photos,
                    opening_hours, price_level, place_types,
                    created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
                )
                ON CONFLICT (google_place_id)
                DO UPDATE SET
                    name_ar = EXCLUDED.name_ar,
                    name_en = EXCLUDED.name_en,
                    description_ar = EXCLUDED.description_ar,
                    latitude = EXCLUDED.latitude,
                    longitude = EXCLUDED.longitude,
                    phone = EXCLUDED.phone,
                    website = EXCLUDED.website,
                    rating = EXCLUDED.rating,
                    photos = EXCLUDED.photos,
                    opening_hours = EXCLUDED.opening_hours,
                    price_level = EXCLUDED.price_level,
                    place_types = EXCLUDED.place_types,
                    updated_at = NOW()
                RETURNING id
            `;

            // تحديد الفئة بناءً على نوع المكان
            const categoryId = this.determineCategoryId(place.types);

            // إعداد البيانات
            const values = [
                place.name || 'غير محدد', // name_ar
                place.name || 'Unknown', // name_en
                place.formatted_address || '', // description_ar
                place.formatted_address || '', // description_en
                place.geometry?.location?.lat || 0, // latitude
                place.geometry?.location?.lng || 0, // longitude
                governorateId, // governorate_id
                categoryId, // category_id
                place.formatted_phone_number || null, // phone
                null, // email
                place.website || null, // website
                null, // whatsapp
                place.rating || null, // rating
                place.place_id, // google_place_id
                JSON.stringify(photos), // photos
                JSON.stringify(place.opening_hours?.weekday_text || []), // opening_hours
                place.price_level || null, // price_level
                JSON.stringify(place.types || []), // place_types
            ];

            const result = await pool.query(query, values);
            console.log(`   💾 تم حفظ المكان: ${place.name}`);
            return result.rows[0]?.id;
        } catch (error) {
            console.error(`   ❌ خطأ في حفظ المكان: ${error.message}`);
            this.errorCount++;
            return null;
        }
    }

    // تحديد فئة المكان
    determineCategoryId(types) {
        const categoryMap = {
            'tourist_attraction': 1, // سياحة
            'museum': 1,
            'mosque': 2, // دينية
            'restaurant': 3, // مطاعم
            'lodging': 4, // فنادق
            'hospital': 5, // صحة
            'school': 6, // تعليم
            'university': 6,
            'bank': 7, // خدمات
            'gas_station': 7,
            'shopping_mall': 8, // تسوق
            'airport': 9, // مواصلات
            'bus_station': 9
        };

        for (const type of types || []) {
            if (categoryMap[type]) {
                return categoryMap[type];
            }
        }
        return 10; // أخرى
    }

    // الحصول على معرف المحافظة
    async getGovernorateId(governorateName) {
        try {
            const result = await pool.query(
                'SELECT id FROM governorates WHERE name_ar = $1 OR name_en = $1',
                [governorateName]
            );
            return result.rows[0]?.id || 1; // افتراضي صنعاء
        } catch (error) {
            console.error(`خطأ في الحصول على معرف المحافظة: ${error.message}`);
            return 1;
        }
    }

    // معالجة مكان واحد
    async processPlace(place, governorateId) {
        try {
            console.log(`\n📍 معالجة: ${place.name}`);

            // الحصول على تفاصيل إضافية
            const details = await this.getPlaceDetails(place.place_id);
            if (!details) {
                console.log(`   ⚠️ لا توجد تفاصيل إضافية`);
                return;
            }

            // تحميل الصور
            const photos = [];
            if (details.photos && details.photos.length > 0) {
                console.log(`   📷 تحميل ${Math.min(details.photos.length, 3)} صور...`);

                for (let i = 0; i < Math.min(details.photos.length, 3); i++) {
                    const photoPath = await this.downloadPlacePhoto(
                        details.photos[i].photo_reference,
                        place.place_id,
                        i
                    );
                    if (photoPath) {
                        photos.push(photoPath);
                    }

                    // تأخير بين تحميل الصور
                    await this.delay(500);
                }
            }

            // حفظ في قاعدة البيانات
            await this.savePlaceToDatabase(details, governorateId, photos);
            this.downloadedCount++;

        } catch (error) {
            console.error(`   ❌ خطأ في معالجة المكان: ${error.message}`);
            this.errorCount++;
        }
    }

    // تأخير لتجنب تجاوز حدود API
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // تشغيل عملية التحميل الكاملة
    async downloadAllPlaces() {
        console.log('🚀 بدء تحميل بيانات الأماكن اليمنية...\n');

        try {
            for (const governorate of YEMEN_GOVERNORATES) {
                console.log(`\n🏛️ معالجة محافظة: ${governorate.name}`);

                const governorateId = await this.getGovernorateId(governorate.name);

                for (const placeType of PLACE_TYPES) {
                    // البحث عن الأماكن
                    const places = await this.searchPlacesInArea(governorate, placeType);
                    this.totalPlaces += places.length;

                    // معالجة كل مكان
                    for (const place of places) {
                        await this.processPlace(place, governorateId);

                        // تأخير بين المعالجات
                        await this.delay(1000);
                    }

                    // تأخير بين أنواع الأماكن
                    await this.delay(2000);
                }

                // تأخير بين المحافظات
                await this.delay(5000);
            }

            this.printSummary();

        } catch (error) {
            console.error('❌ خطأ عام في التحميل:', error);
        } finally {
            await pool.end();
        }
    }

    // طباعة ملخص العملية
    printSummary() {
        const duration = Math.round((Date.now() - this.startTime) / 1000);

        console.log('\n' + '='.repeat(50));
        console.log('📊 ملخص عملية التحميل');
        console.log('='.repeat(50));
        console.log(`⏱️  الوقت المستغرق: ${duration} ثانية`);
        console.log(`📍 إجمالي الأماكن المكتشفة: ${this.totalPlaces}`);
        console.log(`✅ تم تحميلها بنجاح: ${this.downloadedCount}`);
        console.log(`❌ فشل في التحميل: ${this.errorCount}`);
        console.log(`📷 مجلد الصور: ${IMAGES_DIR}`);
        console.log(`💾 قاعدة البيانات: yemen_gps`);
        console.log('='.repeat(50));
    }
}

// تشغيل السكريبت
if (require.main === module) {
    const downloader = new YemenPlacesDownloader();
    downloader.downloadAllPlaces();
}

module.exports = YemenPlacesDownloader;

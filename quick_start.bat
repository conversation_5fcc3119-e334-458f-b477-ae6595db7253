@echo off
chcp 65001 >nul
echo ========================================
echo    Yemen Maps Complete - Quick Start
echo ========================================
echo.

echo 🚀 بدء إعداد مشروع خرائط اليمن الشامل...
echo.

REM التحقق من Python
echo 🔍 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python 3.8+ أولاً
    echo 📥 تحميل من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM التحقق من PostgreSQL
echo 🔍 التحقق من PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL غير متوفر في PATH
    echo ⚠️ تأكد من تثبيت PostgreSQL وإضافته للـ PATH
    echo 📍 عادة يكون في: C:\Program Files\PostgreSQL\15\bin
    echo.
    echo هل تريد المتابعة؟ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)
echo ✅ PostgreSQL متوفر

echo.
echo 📁 إنشاء هيكل المشروع...
call setup_project.bat

echo.
echo 📦 تثبيت المكتبات المطلوبة...
cd /d "E:\yemen-maps-complete"

REM إنشاء بيئة افتراضية
echo 🔧 إنشاء بيئة افتراضية...
python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM تحديث pip
echo 🔧 تحديث pip...
python -m pip install --upgrade pip

REM تثبيت المكتبات
echo 📦 تثبيت المكتبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت بعض المكتبات
    echo ⚠️ سيتم المتابعة مع المكتبات الأساسية...
    pip install Flask Flask-CORS psycopg2-binary requests Pillow
)

echo.
echo 🗄️ إعداد قاعدة البيانات...
echo ⚠️ سيتم طلب كلمة مرور PostgreSQL...
echo.

REM إنشاء قاعدة البيانات
psql -U postgres -f database\create_database.sql
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo 💡 يرجى تشغيل الأمر التالي يدوياً:
    echo    psql -U postgres -f database\create_database.sql
    echo.
    echo هل تريد المتابعة؟ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)

echo.
echo 📊 استيراد البيانات الموجودة...
python tools\import\import_existing_data.py
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في استيراد البيانات الموجودة
    echo 💡 سيتم إنشاء بيانات تجريبية بدلاً من ذلك
)

echo.
echo 🌐 اختبار الخادم...
echo 🚀 بدء تشغيل الخادم للاختبار...
echo.
echo ⏱️ سيتم إيقاف الخادم تلقائياً بعد 30 ثانية للاختبار...
echo 🌐 يمكنك زيارة: http://localhost:5000
echo.

REM تشغيل الخادم لفترة قصيرة للاختبار
timeout /t 3 /nobreak >nul
start /min python server\app.py
timeout /t 30 /nobreak

REM إيقاف الخادم
taskkill /f /im python.exe >nul 2>&1

echo.
echo ========================================
echo ✅ تم الانتهاء من الإعداد الأولي!
echo ========================================
echo.
echo 📋 الخطوات التالية:
echo.
echo 1️⃣ تشغيل الخادم:
echo    cd E:\yemen-maps-complete
echo    venv\Scripts\activate
echo    python server\app.py
echo.
echo 2️⃣ زيارة الموقع:
echo    http://localhost:5000
echo.
echo 3️⃣ لوحة الإدارة:
echo    http://localhost:5000/admin
echo.
echo 4️⃣ API للأماكن:
echo    http://localhost:5000/api/places
echo.
echo ========================================
echo 📞 للدعم الفني:
echo    - تحقق من ملف README.md
echo    - راجع مجلد docs للتوثيق
echo ========================================
echo.
echo 🎉 مرحباً بك في نظام خرائط اليمن الشامل!
echo.
pause

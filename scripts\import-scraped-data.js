// استيراد البيانات المسحوبة من Google Places API
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function importScrapedData() {
    try {
        console.log('📥 بدء استيراد البيانات المسحوبة من Google Places API...\n');
        
        // قراءة ملف SQL
        const sqlPath = path.join(__dirname, '../database/scraped_places_import.sql');
        
        if (!fs.existsSync(sqlPath)) {
            console.error('❌ ملف SQL غير موجود:', sqlPath);
            return;
        }
        
        const sqlContent = fs.readFileSync(sqlPath, 'utf8');
        
        // تقسيم الملف إلى استعلامات منفصلة
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📊 عدد الاستعلامات: ${statements.length}\n`);
        
        let successCount = 0;
        let errorCount = 0;
        
        // تنفيذ كل استعلام
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            try {
                // استخراج اسم المكان من الاستعلام للعرض
                const nameMatch = statement.match(/name_ar,.*?VALUES\s*\(\s*'([^']+)'/);
                const placeName = nameMatch ? nameMatch[1] : `الاستعلام ${i + 1}`;
                
                console.log(`📝 استيراد: ${placeName}`);
                
                await pool.query(statement);
                successCount++;
                
            } catch (error) {
                console.error(`❌ خطأ في الاستعلام ${i + 1}:`, error.message);
                errorCount++;
                
                // إذا كان الخطأ بسبب تكرار البيانات، تجاهله
                if (error.message.includes('duplicate key')) {
                    console.log('   ℹ️ المكان موجود مسبقاً - تم التجاهل');
                }
            }
        }
        
        console.log(`\n🎉 انتهى الاستيراد!`);
        console.log(`✅ نجح: ${successCount} استعلام`);
        console.log(`❌ فشل: ${errorCount} استعلام`);
        
        // التحقق من النتائج
        const result = await pool.query(`
            SELECT COUNT(*) as total_places 
            FROM places 
            WHERE is_verified = TRUE
        `);
        
        console.log(`\n📈 إجمالي الأماكن المؤكدة في قاعدة البيانات: ${result.rows[0].total_places}`);
        
        // عرض إحصائيات سريعة
        const stats = await pool.query(`
            SELECT 
                c.name_ar as category,
                COUNT(p.id) as count
            FROM places p
            LEFT JOIN place_categories c ON p.category_id = c.id
            WHERE p.is_verified = TRUE
            GROUP BY c.name_ar
            ORDER BY count DESC
            LIMIT 10
        `);
        
        console.log('\n📊 إحصائيات الفئات:');
        stats.rows.forEach(row => {
            console.log(`   ${row.category || 'غير محدد'}: ${row.count} مكان`);
        });
        
    } catch (error) {
        console.error('❌ خطأ في الاستيراد:', error);
    } finally {
        await pool.end();
    }
}

importScrapedData();

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدير بلاطات الخرائط - نظام يمن ناف</title>
    <link rel="icon" href="/images/icons/icon.svg" type="image/svg+xml">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css">
    
    <style>
        :root {
            --primary-color: #4285F4;
            --secondary-color: #EA4335;
            --accent-color: #FBBC05;
            --success-color: #34A853;
            --dark-gray: #5F6368;
            --light-gray: #DADCE0;
            --background-white: #FFFFFF;
            --shadow-color: rgba(0, 0, 0, 0.2);
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #3c4043;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-color);
            overflow: hidden;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h1 {
            font-size: 24px;
            margin: 0;
        }
        
        .content {
            padding: 24px;
        }
        
        .tile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
            padding: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-card h3 {
            color: var(--dark-gray);
            margin-bottom: 8px;
        }
        
        .stat-card .value {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-card .sub-text {
            color: var(--dark-gray);
            font-size: 14px;
            margin-top: 4px;
        }
        
        .actions-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .action-card h3 {
            margin-bottom: 16px;
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-gray);
        }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--light-gray);
            border-radius: 4px;
            font-size: 16px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 16px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2a75f3;
        }
        
        .map-container {
            height: 400px;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }
        
        .progress-container {
            margin-top: 20px;
        }
        
        .progress-bar {
            background-color: var(--light-gray);
            border-radius: 4px;
            height: 20px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s;
        }
        
        .progress-text {
            color: var(--dark-gray);
            font-size: 14px;
        }
        
        .log-container {
            margin-top: 30px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-title {
            color: var(--dark-gray);
            margin-bottom: 10px;
        }
        
        .log-entry {
            font-family: monospace;
            margin-bottom: 4px;
            white-space: pre-wrap;
            font-size: 14px;
        }
        
        .success-log {
            color: var(--success-color);
        }
        
        .error-log {
            color: var(--secondary-color);
        }
        
        .checkbox-group {
            margin-top: 10px;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>مدير بلاطات الخرائط المحلية - يمن ناف</h1>
            <a href="/index.html" style="color: white;"><i class="fas fa-home"></i> العودة للرئيسية</a>
        </header>
        
        <div class="content">
            <div class="tile-stats">
                <div class="stat-card">
                    <h3>إجمالي البلاطات</h3>
                    <div class="value" id="total-tiles">0</div>
                    <div class="sub-text">بلاطة مخزنة محليًا</div>
                </div>
                
                <div class="stat-card">
                    <h3>المساحة المستخدمة</h3>
                    <div class="value" id="total-size">0 MB</div>
                    <div class="sub-text">من مساحة التخزين</div>
                </div>
                
                <div class="stat-card">
                    <h3>نسبة التغطية</h3>
                    <div class="value" id="coverage-percent">0%</div>
                    <div class="sub-text">من مساحة اليمن</div>
                </div>
            </div>
            
            <div class="map-container" id="preview-map"></div>
            
            <div class="actions-panel">
                <div class="action-card">
                    <h3>تنزيل بلاطات جديدة</h3>
                    
                    <div class="form-group">
                        <label for="map-type">نوع الخريطة:</label>
                        <select id="map-type">
                            <option value="streets">خريطة الشوارع</option>
                            <option value="satellite">صور الأقمار الصناعية</option>
                            <option value="terrain">خريطة التضاريس</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="min-zoom">أدنى مستوى تكبير:</label>
                        <select id="min-zoom">
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="max-zoom">أعلى مستوى تكبير:</label>
                        <select id="max-zoom">
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                            <option value="13">13</option>
                            <option value="14">14</option>
                            <option value="15">15</option>
                        </select>
                    </div>
                    
                    <div class="checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="use-dummy" checked>
                            استخدام بلاطات وهمية (لتوفير الذاكرة)
                        </label>
                    </div>
                    
                    <button id="download-tiles-btn">بدء التنزيل</button>
                    
                    <div class="progress-container" id="progress-container" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text">0%</div>
                    </div>
                </div>
                
                <div class="action-card">
                    <h3>إدارة البلاطات المحلية</h3>
                    
                    <div class="form-group">
                        <label for="manage-action">الإجراء:</label>
                        <select id="manage-action">
                            <option value="count">حساب البلاطات</option>
                            <option value="clear">حذف البلاطات</option>
                            <option value="export">تصدير البلاطات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="manage-type">نوع الخريطة:</label>
                        <select id="manage-type">
                            <option value="all">جميع الأنواع</option>
                            <option value="streets">خريطة الشوارع</option>
                            <option value="satellite">صور الأقمار الصناعية</option>
                            <option value="terrain">خريطة التضاريس</option>
                        </select>
                    </div>
                    
                    <button id="manage-tiles-btn">تنفيذ الإجراء</button>
                </div>
            </div>
            
            <div class="log-container">
                <h3 class="log-title">سجل العمليات:</h3>
                <div id="log-content"></div>
            </div>
        </div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // حدود اليمن للخريطة
        const yemenBounds = L.latLngBounds(
            L.latLng(12.1, 41.6),  // الزاوية الجنوبية الغربية
            L.latLng(19.0, 54.5)   // الزاوية الشمالية الشرقية
        );
        
        // تهيئة الخريطة
        const map = L.map('preview-map', {
            center: [15.3694, 44.1910], // إحداثيات صنعاء
            zoom: 7,
            minZoom: 5,
            maxZoom: 15,
        });
        
        // إضافة طبقة الخريطة الأساسية
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Yemen Nav'
        }).addTo(map);
        
        // مستطيل يوضح حدود اليمن
        L.rectangle(yemenBounds, {
            color: "#4285F4",
            weight: 2,
            fill: true,
            fillOpacity: 0.1
        }).addTo(map);
        
        // إضافة نقاط المدن الرئيسية
        const cities = [
            { name: 'صنعاء', lat: 15.3694, lon: 44.1910 },
            { name: 'عدن', lat: 12.7792, lon: 45.0189 },
            { name: 'تعز', lat: 13.5766, lon: 44.0178 },
            { name: 'الحديدة', lat: 14.7979, lon: 42.9530 }
        ];
        
        cities.forEach(city => {
            L.marker([city.lat, city.lon])
                .bindPopup(city.name)
                .addTo(map);
        });
        
        // ضبط حجم الخريطة ليشمل حدود اليمن
        map.fitBounds(yemenBounds);
        
        // وظيفة لإضافة سجل
        function addLog(message, type = 'normal') {
            const logContent = document.getElementById('log-content');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type === 'success' ? 'success-log' : type === 'error' ? 'error-log' : ''}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        // وظيفة لتحويل الإحداثيات الجغرافية إلى إحداثيات البلاطة
        function latLonToTile(lat, lon, zoom) {
            const x = Math.floor((lon + 180) / 360 * Math.pow(2, zoom));
            const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));
            return { x, y };
        }
        
        // عداد البلاطات الوهمي (سيتم استبداله بقراءة حقيقية)
        let tileCounts = {
            streets: 125,
            satellite: 87,
            terrain: 103
        };
        
        // تحديث إحصائيات البلاطات
        function updateTileStats() {
            const totalTiles = Object.values(tileCounts).reduce((a, b) => a + b, 0);
            document.getElementById('total-tiles').textContent = totalTiles.toLocaleString();
            
            // حساب المساحة (بشكل تقريبي - افتراض أن كل بلاطة حوالي 10 كيلوبايت)
            const estimatedSize = totalTiles * 10 / 1024; // تحويل إلى ميجابايت
            document.getElementById('total-size').textContent = estimatedSize.toFixed(2) + ' MB';
            
            // نسبة التغطية (تقريبية)
            const coveragePercent = Math.min(100, totalTiles / 500 * 10);
            document.getElementById('coverage-percent').textContent = coveragePercent.toFixed(1) + '%';
        }
        
        // تنزيل البلاطات
        document.getElementById('download-tiles-btn').addEventListener('click', function() {
            const mapType = document.getElementById('map-type').value;
            const minZoom = parseInt(document.getElementById('min-zoom').value);
            const maxZoom = parseInt(document.getElementById('max-zoom').value);
            const useDummy = document.getElementById('use-dummy').checked;
            
            // التحقق من صحة الإدخال
            if (minZoom > maxZoom) {
                addLog('خطأ: أدنى مستوى تكبير يجب أن يكون أقل من أو يساوي أعلى مستوى', 'error');
                return;
            }
            
            const progressContainer = document.getElementById('progress-container');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            
            progressContainer.style.display = 'block';
            
            addLog(`بدء تنزيل بلاطات ${mapType} من مستوى ${minZoom} إلى ${maxZoom}`, 'success');
            
            // محاكاة عملية التنزيل
            let progress = 0;
            const totalSteps = 100;
            const interval = setInterval(() => {
                progress += 1;
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${progress}%`;
                
                if (progress % 10 === 0) {
                    addLog(`تم تنزيل ${progress}% من البلاطات`);
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    addLog(`اكتمل تنزيل بلاطات ${mapType} بنجاح!`, 'success');
                    
                    // تحديث عدد البلاطات بشكل وهمي
                    const newTiles = Math.floor(Math.random() * 50) + 20;
                    tileCounts[mapType] += newTiles;
                    updateTileStats();
                    
                    progressContainer.style.display = 'none';
                }
            }, 100);
        });
        
        // إدارة البلاطات
        document.getElementById('manage-tiles-btn').addEventListener('click', function() {
            const action = document.getElementById('manage-action').value;
            const tileType = document.getElementById('manage-type').value;
            
            if (action === 'count') {
                addLog(`عدد بلاطات ${tileType === 'all' ? 'جميع الأنواع' : tileType}:`, 'success');
                
                if (tileType === 'all') {
                    Object.entries(tileCounts).forEach(([type, count]) => {
                        addLog(`  - ${type}: ${count} بلاطة`);
                    });
                    addLog(`  - المجموع: ${Object.values(tileCounts).reduce((a, b) => a + b, 0)} بلاطة`);
                } else {
                    addLog(`  - ${tileType}: ${tileCounts[tileType] || 0} بلاطة`);
                }
            } else if (action === 'clear') {
                if (confirm(`هل أنت متأكد من حذف بلاطات ${tileType === 'all' ? 'جميع الأنواع' : tileType}؟`)) {
                    addLog(`تم حذف بلاطات ${tileType === 'all' ? 'جميع الأنواع' : tileType}`, 'success');
                    
                    if (tileType === 'all') {
                        Object.keys(tileCounts).forEach(key => {
                            tileCounts[key] = 0;
                        });
                    } else {
                        tileCounts[tileType] = 0;
                    }
                    
                    updateTileStats();
                }
            } else if (action === 'export') {
                addLog(`بدء تصدير بلاطات ${tileType === 'all' ? 'جميع الأنواع' : tileType}`, 'success');
                setTimeout(() => {
                    addLog('ميزة التصدير غير متاحة في هذا الإصدار التجريبي', 'error');
                }, 1500);
            }
        });
        
        // تحديث الإحصائيات عند التحميل
        updateTileStats();
        
        // إضافة سجل بدء التشغيل
        addLog('تم تحميل مدير بلاطات الخرائط المحلية', 'success');
        addLog('الإصدار التجريبي 1.0.0');
        addLog('استخدم الخيارات أعلاه لإدارة بلاطات الخرائط المحلية');
    </script>
</body>
</html>

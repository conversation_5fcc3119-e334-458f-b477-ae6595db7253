// يمن ناف - ملف إدارة العملاء باستخدام قاعدة بيانات PostgreSQL
const db = require('./postgres-db');

// دالة للحصول على جميع العملاء
async function getAllClients() {
    try {
        const query = `
            SELECT
                id,
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            FROM clients
            ORDER BY id
        `;

        const result = await db.query(query);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على العملاء:', error);
        throw error;
    }
}

// دالة للحصول على عميل بواسطة المعرف
async function getClientById(clientId) {
    try {
        const query = `
            SELECT
                id,
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            FROM clients
            WHERE id = $1
        `;

        const result = await db.query(query, [clientId]);
        return result.rows[0] || null;
    } catch (error) {
        console.error('خطأ في الحصول على العميل:', error);
        throw error;
    }
}

// دالة لإنشاء عميل جديد
async function createClient(clientData) {
    const client = await db.getClient();

    try {
        await client.query('BEGIN');

        // إدراج العميل الجديد
        const insertQuery = `
            INSERT INTO clients (
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING id
        `;

        const values = [
            clientData.name,
            clientData.email || null,
            clientData.deviceSN || null,
            clientData.licenseN || null,
            clientData.status || 'active'
        ];

        const result = await client.query(insertQuery, values);
        const clientId = result.rows[0].id;

        // الحصول على بيانات العميل المدرج
        const clientQuery = `
            SELECT
                id,
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            FROM clients
            WHERE id = $1
        `;

        const clientResult = await client.query(clientQuery, [clientId]);

        await client.query('COMMIT');

        return clientResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في إنشاء العميل:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لتحديث بيانات عميل
async function updateClient(clientId, clientData) {
    const client = await db.getClient();

    try {
        await client.query('BEGIN');

        // بناء استعلام التحديث
        let updateQuery = 'UPDATE clients SET ';
        const values = [];
        const updateFields = [];
        let paramIndex = 1;

        if (clientData.name !== undefined) {
            updateFields.push(`name = $${paramIndex++}`);
            values.push(clientData.name);
        }

        if (clientData.email !== undefined) {
            updateFields.push(`email = $${paramIndex++}`);
            values.push(clientData.email);
        }

        if (clientData.deviceSN !== undefined) {
            updateFields.push(`"deviceSN" = $${paramIndex++}`);
            values.push(clientData.deviceSN);
        }

        if (clientData.licenseN !== undefined) {
            updateFields.push(`"licenseN" = $${paramIndex++}`);
            values.push(clientData.licenseN);
        }

        if (clientData.status !== undefined) {
            updateFields.push(`status = $${paramIndex++}`);
            values.push(clientData.status);
        }

        // إضافة تاريخ التحديث
        updateFields.push(`updated_at = $${paramIndex++}`);
        values.push(new Date());

        // إذا لم تكن هناك حقول للتحديث
        if (updateFields.length === 0) {
            return await getClientById(clientId);
        }

        // إكمال استعلام التحديث
        updateQuery += updateFields.join(', ');
        updateQuery += ` WHERE id = $${paramIndex}`;
        values.push(clientId);

        // تنفيذ استعلام التحديث
        await client.query(updateQuery, values);

        // الحصول على بيانات العميل المحدثة
        const clientQuery = `
            SELECT
                id,
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            FROM clients
            WHERE id = $1
        `;

        const clientResult = await client.query(clientQuery, [clientId]);

        await client.query('COMMIT');

        return clientResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في تحديث العميل:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لحذف عميل
async function deleteClient(clientId) {
    try {
        const query = 'DELETE FROM clients WHERE id = $1';
        await db.query(query, [clientId]);

        return true;
    } catch (error) {
        console.error('خطأ في حذف العميل:', error);
        throw error;
    }
}

// دالة للبحث عن العملاء
async function searchClients(searchTerm) {
    try {
        const query = `
            SELECT
                id,
                name,
                email,
                "deviceSN",
                "licenseN",
                status,
                created_at,
                updated_at
            FROM clients
            WHERE
                name ILIKE $1 OR
                email ILIKE $1 OR
                "deviceSN" ILIKE $1 OR
                "licenseN" ILIKE $1
            ORDER BY id
        `;

        const result = await db.query(query, [`%${searchTerm}%`]);
        return result.rows;
    } catch (error) {
        console.error('خطأ في البحث عن العملاء:', error);
        throw error;
    }
}

// دالة للحصول على عدد العملاء
async function getClientsCount() {
    try {
        const query = 'SELECT COUNT(*) as count FROM clients';
        const result = await db.query(query);
        return parseInt(result.rows[0].count);
    } catch (error) {
        console.error('خطأ في الحصول على عدد العملاء:', error);
        throw error;
    }
}

// دالة للتحقق من وجود عميل بواسطة الاسم
async function checkClientExists(name) {
    try {
        const query = 'SELECT COUNT(*) as count FROM clients WHERE name = $1';
        const result = await db.query(query, [name]);
        return parseInt(result.rows[0].count) > 0;
    } catch (error) {
        console.error('خطأ في التحقق من وجود العميل:', error);
        throw error;
    }
}

// دالة للتحقق من وجود عميل بواسطة رقم الجهاز
async function checkDeviceSNExists(deviceSN) {
    try {
        const query = 'SELECT COUNT(*) as count FROM clients WHERE "deviceSN" = $1';
        const result = await db.query(query, [deviceSN]);
        return parseInt(result.rows[0].count) > 0;
    } catch (error) {
        console.error('خطأ في التحقق من وجود رقم الجهاز:', error);
        throw error;
    }
}

// دالة للتحقق من وجود عميل بواسطة رقم الترخيص
async function checkLicenseNExists(licenseN) {
    try {
        const query = 'SELECT COUNT(*) as count FROM clients WHERE "licenseN" = $1';
        const result = await db.query(query, [licenseN]);
        return parseInt(result.rows[0].count) > 0;
    } catch (error) {
        console.error('خطأ في التحقق من وجود رقم الترخيص:', error);
        throw error;
    }
}

// تصدير الدوال
module.exports = {
    getAllClients,
    getClientById,
    createClient,
    updateClient,
    deleteClient,
    searchClients,
    getClientsCount,
    checkClientExists,
    checkDeviceSNExists,
    checkLicenseNExists
};

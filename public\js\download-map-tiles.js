/**
 * أداة تنزيل بلاطات الخرائط - Yemen Nav
 * تستخدم هذه الأداة لتنزيل بلاطات الخرائط من مصادر مختلفة وحفظها محليًا
 * يجب تشغيل هذا السكريبت من جانب الخادم باستخدام Node.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { createCanvas } = require('canvas');

// تكوين المصادر
const sources = {
    streets: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
    satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
    terrain: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}'
};

// مسارات الحفظ
const basePath = path.join(__dirname, '..', 'map-tiles');

// حدود اليمن (تقريبية)
const yemenBounds = {
    south: 12.1,
    west: 41.6,
    north: 19.0,
    east: 54.5
};

// وظيفة لتحويل الإحداثيات الجغرافية إلى إحداثيات البلاطة
function latLonToTile(lat, lon, zoom) {
    const x = Math.floor((lon + 180) / 360 * Math.pow(2, zoom));
    const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));
    return { x, y };
}

// وظيفة لإنشاء بلاطة خريطة وهمية للاختبار
function createDummyTile(type, z, x, y) {
    // إنشاء لوحة رسم
    const canvas = createCanvas(256, 256);
    const ctx = canvas.getContext('2d');
    
    // لون الخلفية حسب نوع الخريطة
    let bgColor;
    switch (type) {
        case 'streets':
            bgColor = '#f2f2f2';
            break;
        case 'satellite':
            bgColor = '#3d3d3d';
            break;
        case 'terrain':
            bgColor = '#e8e8e8';
            break;
        default:
            bgColor = '#ffffff';
    }
    
    // رسم الخلفية
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, 256, 256);
    
    // رسم خطوط الشبكة
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 1;
    
    // خطوط أفقية
    for (let i = 0; i <= 256; i += 32) {
        ctx.beginPath();
        ctx.moveTo(0, i);
        ctx.lineTo(256, i);
        ctx.stroke();
    }
    
    // خطوط عمودية
    for (let i = 0; i <= 256; i += 32) {
        ctx.beginPath();
        ctx.moveTo(i, 0);
        ctx.lineTo(i, 256);
        ctx.stroke();
    }
    
    // إضافة نص المعلومات
    ctx.fillStyle = '#333333';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`Yemen Nav ${type}`, 128, 110);
    ctx.fillText(`Z:${z} X:${x} Y:${y}`, 128, 130);
    
    // إضافة رمز للخريطة
    if (type === 'streets') {
        drawRoads(ctx);
    } else if (type === 'satellite') {
        drawSatellite(ctx);
    } else if (type === 'terrain') {
        drawTerrain(ctx);
    }
    
    // تحويل اللوحة إلى صورة
    return canvas.toBuffer('image/png');
}

// رسم طرق بسيطة للخريطة
function drawRoads(ctx) {
    // رسم طريق رئيسي
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 6;
    ctx.beginPath();
    ctx.moveTo(20, 128);
    ctx.lineTo(236, 128);
    ctx.stroke();
    
    ctx.strokeStyle = '#ffcc00';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(20, 128);
    ctx.lineTo(236, 128);
    ctx.stroke();
    
    // رسم طرق فرعية
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 4;
    
    ctx.beginPath();
    ctx.moveTo(128, 20);
    ctx.lineTo(128, 236);
    ctx.stroke();
}

// رسم عناصر الأقمار الصناعية
function drawSatellite(ctx) {
    // إضافة تأثير القمر الصناعي
    ctx.fillStyle = 'rgba(0, 128, 0, 0.1)';
    for (let i = 0; i < 10; i++) {
        const x = Math.random() * 256;
        const y = Math.random() * 256;
        const size = 20 + Math.random() * 30;
        
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
    }
}

// رسم عناصر التضاريس
function drawTerrain(ctx) {
    // رسم خطوط كنتورية
    ctx.strokeStyle = '#a8a8a8';
    ctx.lineWidth = 1;
    
    for (let i = 1; i <= 5; i++) {
        const y = 50 + i * 30;
        
        ctx.beginPath();
        ctx.moveTo(20, y);
        ctx.bezierCurveTo(70, y - 10, 140, y + 15, 236, y - 5);
        ctx.stroke();
    }
}

// وظيفة لإنشاء المجلدات اللازمة
function createDirectories(type, zoom, x) {
    const typeDir = path.join(basePath, type);
    const zoomDir = path.join(typeDir, zoom.toString());
    const xDir = path.join(zoomDir, x.toString());
    
    if (!fs.existsSync(typeDir)) {
        fs.mkdirSync(typeDir, { recursive: true });
    }
    
    if (!fs.existsSync(zoomDir)) {
        fs.mkdirSync(zoomDir, { recursive: true });
    }
    
    if (!fs.existsSync(xDir)) {
        fs.mkdirSync(xDir, { recursive: true });
    }
    
    return xDir;
}

// وظيفة لتنزيل بلاطة واحدة
async function downloadTile(type, z, x, y, useDummy = true) {
    try {
        const xDir = createDirectories(type, z, x);
        const filePath = path.join(xDir, `${y}.png`);
        
        if (fs.existsSync(filePath)) {
            console.log(`البلاطة موجودة بالفعل: ${type}/${z}/${x}/${y}.png`);
            return;
        }
        
        if (useDummy) {
            // إنشاء بلاطة وهمية
            const tileBuffer = createDummyTile(type, z, x, y);
            fs.writeFileSync(filePath, tileBuffer);
            console.log(`تم إنشاء بلاطة وهمية: ${type}/${z}/${x}/${y}.png`);
        } else {
            // تنزيل البلاطة من المصدر
            let url = sources[type];
            url = url.replace('{z}', z).replace('{x}', x).replace('{y}', y).replace('{s}', 'a');
            
            const response = await axios.get(url, { responseType: 'arraybuffer' });
            fs.writeFileSync(filePath, response.data);
            console.log(`تم تنزيل البلاطة: ${type}/${z}/${x}/${y}.png`);
        }
    } catch (error) {
        console.error(`خطأ في تنزيل البلاطة ${type}/${z}/${x}/${y}.png:`, error.message);
    }
}

// وظيفة لإنشاء بلاطات عينة للاختبار
async function createSampleTiles() {
    // إنشاء بلاطات لمستويات تكبير مختلفة
    const mapTypes = ['streets', 'satellite', 'terrain'];
    const zoomLevels = [5, 6, 7, 8, 9, 10, 11, 12];
    
    // المناطق المهمة في اليمن
    const importantLocations = [
        { name: 'صنعاء', lat: 15.3694, lon: 44.1910 },
        { name: 'عدن', lat: 12.7792, lon: 45.0189 },
        { name: 'تعز', lat: 13.5766, lon: 44.0178 },
        { name: 'الحديدة', lat: 14.7979, lon: 42.9530 }
    ];
    
    // عداد البلاطات
    let tileCount = 0;
    
    // إنشاء بلاطات لكل موقع ومستوى تكبير
    for (const location of importantLocations) {
        for (const zoom of zoomLevels) {
            // الحصول على إحداثيات البلاطة
            const tile = latLonToTile(location.lat, location.lon, zoom);
            
            // توسيع المنطقة قليلاً
            const offsets = [-1, 0, 1];
            
            for (const type of mapTypes) {
                for (const xOffset of offsets) {
                    for (const yOffset of offsets) {
                        const x = tile.x + xOffset;
                        const y = tile.y + yOffset;
                        
                        await downloadTile(type, zoom, x, y, true);
                        tileCount++;
                    }
                }
            }
        }
    }
    
    console.log(`تم إنشاء ${tileCount} بلاطة عينة للاختبار`);
}

// استدعاء الوظيفة الرئيسية
createSampleTiles().then(() => {
    console.log('اكتمل إنشاء بلاطات الخرائط العينة');
}).catch(error => {
    console.error('حدث خطأ أثناء إنشاء بلاطات الخرائط:', error);
});

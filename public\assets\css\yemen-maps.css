/* Yemen Maps CSS - نظام خرائط اليمن */

/* Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    overflow: hidden;
    direction: rtl;
}

/* Map Container */
#map-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

#map {
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Search Bar */
.search-container {
    position: absolute;
    top: 16px;
    right: 16px;
    left: 16px;
    max-width: 480px;
    margin: 0 auto;
    z-index: 1000;
}

.search-box {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    padding: 8px 16px;
    width: 100%;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 8px;
}

.search-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #555;
}

/* Map Controls */
.map-controls {
    position: absolute;
    bottom: 24px;
    right: 16px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.map-control-btn {
    width: 40px;
    height: 40px;
    background-color: white;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #555;
    font-size: 16px;
}

.map-control-btn:hover {
    background-color: #f5f5f5;
}

/* Layer Controls */
.layer-controls {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 1000;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.layer-btn {
    display: block;
    padding: 8px 16px;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
    cursor: pointer;
    font-size: 14px;
    border-bottom: 1px solid #eee;
}

.layer-btn:last-child {
    border-bottom: none;
}

.layer-btn.active {
    background-color: #f0f7ff;
    color: #1a73e8;
    font-weight: bold;
}

.layer-btn:hover:not(.active) {
    background-color: #f5f5f5;
}

/* Popup Styles - تنسيقات نافذة المعلومات */
.custom-popup {
    min-width: 280px;
    max-width: 350px;
    direction: rtl;
    padding: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content-wrapper {
    padding: 0;
    overflow: hidden;
    border-radius: 8px;
}

.leaflet-popup-content {
    margin: 0;
    width: 100% !important;
}

/* صورة المكان */
.popup-header {
    position: relative;
    width: 100%;
    overflow: hidden;
    background-color: #f0f0f0;
}

.popup-image-container {
    position: relative;
}

.popup-header .main-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
}

.popup-image-gallery {
    display: flex;
    overflow-x: auto;
    scrollbar-width: thin;
    padding: 4px 0;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
}

.gallery-thumbnail {
    flex: 0 0 60px;
    height: 60px;
    margin-right: 4px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.gallery-thumbnail:hover {
    opacity: 1;
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popup-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0) 100%);
    z-index: 1;
}

/* معلومات المكان */
.popup-body {
    padding: 16px;
}

.popup-title {
    margin: 0 0 4px 0;
    font-size: 20px;
    font-weight: bold;
    color: #202124;
}

.popup-subtitle {
    font-size: 14px;
    color: #5f6368;
    margin-bottom: 12px;
}

.popup-info {
    margin-bottom: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 12px;
}

.popup-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.popup-info-item i {
    margin-left: 8px;
    color: #5f6368;
    width: 16px;
    text-align: center;
}

.popup-info-item a {
    color: #1a73e8;
    text-decoration: none;
}

.popup-info-item a:hover {
    text-decoration: underline;
}

.popup-facts {
    margin-bottom: 16px;
}

.popup-facts h4 {
    font-size: 16px;
    margin: 0 0 8px 0;
    color: #202124;
}

.popup-facts p {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    color: #5f6368;
}

.more-link {
    display: inline-block;
    color: #1a73e8;
    font-size: 14px;
    margin-top: 4px;
    text-decoration: none;
}

.more-link:hover {
    text-decoration: underline;
}

.popup-info-item {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.popup-info-item i {
    color: #5f6368;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.popup-info-item span {
    font-size: 14px;
    color: #3c4043;
}

/* أزرار الإجراءات */
.popup-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    overflow-x: auto;
    padding-bottom: 8px;
    padding-top: 5px;
}

.popup-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    color: #1a73e8;
    font-size: 12px;
    flex: 1;
    padding: 8px 4px;
    transition: background-color 0.2s;
}

.popup-action-btn:hover {
    background-color: #f5f8ff;
}

.popup-action-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

/* حقائق سريعة */
.popup-facts {
    margin: 16px 0 12px 0;
    padding-top: 12px;
    border-top: 1px solid #e8eaed;
}

.popup-facts h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #202124;
}

.popup-facts p {
    font-size: 13px;
    color: #3c4043;
    line-height: 1.4;
    margin: 0;
}

.popup-facts .more-link {
    color: #1a73e8;
    font-size: 13px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin-top: 4px;
}

/* للتوافق مع الهواتف المحمولة */
@media (max-width: 480px) {
    .custom-popup {
        min-width: 250px;
        max-width: 320px;
    }
    
    .popup-header {
        height: 140px;
    }
}

/* Loading Indicator */
#loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Current Location Marker */
.current-location-marker {
    position: relative;
}

.pulse {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(26, 115, 232, 0.7);
    border: 3px solid white;
    cursor: pointer;
    box-shadow: 0 0 0 rgba(26, 115, 232, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(26, 115, 232, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(26, 115, 232, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(26, 115, 232, 0);
    }
}

/* Directions Panel */
#directions-panel {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background-color: white;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

#directions-panel.active {
    transform: translateY(0);
}

.directions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

.directions-content {
    padding: 16px;
    height: calc(100% - 50px);
    overflow-y: auto;
}

.direction-step {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: flex-start;
}

.direction-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f1f3f4;
    border-radius: 50%;
    flex-shrink: 0;
}

.direction-text {
    flex: 1;
}

.step-distance {
    font-size: 12px;
    color: #777;
    margin-top: 4px;
}

.direction-summary {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #eee;
    font-size: 14px;
    color: #555;
}

/* Notifications */
.notification {
    position: fixed;
    bottom: 24px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    padding: 12px 24px;
    background-color: #333;
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2000;
    transition: transform 0.3s ease;
    opacity: 0;
}

.notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.notification.success {
    background-color: #4caf50;
}

.notification.warning {
    background-color: #ff9800;
}

.notification.error {
    background-color: #f44336;
}

/* Saved Places Panel */
#saved-places-panel {
    position: absolute;
    top: 16px;
    right: 16px;
    bottom: 24px;
    width: 300px;
    background-color: white;
    z-index: 1500;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transform: translateX(320px);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

#saved-places-panel.active {
    transform: translateX(0);
}

.saved-places-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.saved-places-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.saved-place-item {
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 8px;
    background-color: #f9f9f9;
    cursor: pointer;
}

.saved-place-item:hover {
    background-color: #f0f7ff;
}

.saved-place-name {
    font-weight: bold;
    margin-bottom: 4px;
}

.saved-place-address {
    font-size: 12px;
    color: #555;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        max-width: 100%;
        top: 8px;
        right: 8px;
        left: 8px;
    }
    
    .layer-controls {
        top: auto;
        bottom: 80px;
        left: 16px;
    }
    
    #saved-places-panel {
        width: 100%;
        max-width: 100%;
        transform: translateX(100%);
    }
}

/* Offline Mode Indicator */
.offline-indicator {
    position: absolute;
    top: 80px;
    right: 16px;
    background-color: #ff9800;
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 6px;
}

.offline-indicator i {
    font-size: 14px;
}

/* نافذة معلومات المكان المفصلة (Google Maps Style) */
.place-details-panel {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    max-width: 90%;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    z-index: 1500;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.place-details-panel.active {
    transform: translateX(0);
}

.place-details-header {
    background-color: #f8f8f8;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.place-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.place-header-btn {
    background: none;
    border: none;
    cursor: pointer;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #444;
    transition: background-color 0.2s;
}

.place-header-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.place-search-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0 8px;
    height: 44px;
}

#place-search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 0 8px;
    height: 100%;
    background: transparent;
}

#place-search-clear,
#place-search-btn {
    background: none;
    border: none;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.place-details-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 24px;
    scrollbar-width: thin;
}

.place-image-container {
    position: relative;
    height: 220px;
    background-color: #f0f0f0;
    overflow: hidden;
}

#place-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s;
}

.place-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
}

.place-image-gallery {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 5px;
    scrollbar-width: none;
}

.place-image-gallery::-webkit-scrollbar {
    display: none;
}

.gallery-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.7);
    flex-shrink: 0;
}

.gallery-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-thumbnail.active {
    border-color: #4285F4;
}

.place-basic-info {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

#place-name {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #202124;
}

.place-category {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #5f6368;
    margin-bottom: 8px;
    font-size: 14px;
}

.place-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.place-rating .stars {
    color: #fbbc04;
    letter-spacing: -2px;
}

.place-rating .rating-value {
    font-weight: 500;
}

.place-rating .review-count {
    color: #5f6368;
    font-size: 14px;
}

.place-address {
    color: #5f6368;
    font-size: 14px;
    margin-top: 4px;
}

.place-quick-actions {
    display: flex;
    padding: 8px;
    border-bottom: 1px solid #eee;
    justify-content: space-around;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    color: #1a73e8;
    transition: background-color 0.2s;
}

.quick-action-btn:hover {
    background-color: rgba(26, 115, 232, 0.1);
}

.quick-action-btn i {
    font-size: 20px;
    margin-bottom: 4px;
}

.quick-action-btn span {
    font-size: 12px;
}

.place-info-section {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.place-info-section h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #202124;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    color: #5f6368;
}

.contact-item i {
    width: 20px;
    text-align: center;
}

.contact-item a {
    color: #1a73e8;
    text-decoration: none;
}

.hours-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.hours-item.current-day {
    font-weight: 500;
    color: #202124;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.review-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.review-author {
    display: flex;
    align-items: center;
    gap: 8px;
}

.review-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
}

.review-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.review-author-name {
    font-weight: 500;
}

.review-rating {
    display: flex;
    align-items: center;
    gap: 6px;
}

.review-text {
    font-size: 14px;
    line-height: 1.5;
    color: #202124;
}

.review-date {
    font-size: 12px;
    color: #5f6368;
}

/* أنماط الطباعة */
@media print {
    .search-container, 
    .layer-controls, 
    .map-controls,
    #loading-indicator,
    .notification,
    .offline-indicator,
    .place-details-panel {
        display: none !important;
    }
}

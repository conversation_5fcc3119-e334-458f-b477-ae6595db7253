package com.yemengps.app.ui;

import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.PolygonOptions;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.offline.ConnectivityMonitor;
import com.yemengps.app.offline.OfflineMapManager;
import com.yemengps.app.ui.adapters.OfflineRegionAdapter;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;

/**
 * Activity for managing offline maps
 */
public class OfflineMapsActivity extends AppCompatActivity implements OnMapReadyCallback {

    private GoogleMap map;
    private OfflineMapManager offlineMapManager;
    private ConnectivityMonitor connectivityMonitor;
    private OfflineRegionAdapter adapter;
    
    private RecyclerView recyclerView;
    private TextView emptyView;
    private ProgressBar progressBar;
    private TextView progressText;
    private Button downloadButton;
    private FloatingActionButton drawRegionButton;
    
    private boolean isDrawingRegion = false;
    private LatLngBounds selectedRegion = null;
    private final ArrayList<LatLng> drawnPoints = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_offline_maps);
        
        // Initialize toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(R.string.offline_maps);
        
        // Initialize components
        recyclerView = findViewById(R.id.recycler_view);
        emptyView = findViewById(R.id.empty_view);
        progressBar = findViewById(R.id.progress_bar);
        progressText = findViewById(R.id.progress_text);
        downloadButton = findViewById(R.id.download_button);
        drawRegionButton = findViewById(R.id.draw_region_button);
        
        // Initialize map
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager()
                .findFragmentById(R.id.map);
        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        }
        
        // Initialize managers
        offlineMapManager = YemenGpsApplication.getInstance().getOfflineMapManager();
        connectivityMonitor = new ConnectivityMonitor(this);
        
        // Set up RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new OfflineRegionAdapter(new ArrayList<>(), this::onRegionAction);
        recyclerView.setAdapter(adapter);
        
        // Set up button listeners
        downloadButton.setOnClickListener(v -> downloadSelectedRegion());
        drawRegionButton.setOnClickListener(v -> toggleDrawMode());
        
        // Observe offline regions
        offlineMapManager.getOfflineRegions().observe(this, regions -> {
            adapter.updateRegions(regions);
            
            if (regions.isEmpty()) {
                recyclerView.setVisibility(View.GONE);
                emptyView.setVisibility(View.VISIBLE);
            } else {
                recyclerView.setVisibility(View.VISIBLE);
                emptyView.setVisibility(View.GONE);
            }
        });
        
        // Observe download progress
        offlineMapManager.getDownloadProgress().observe(this, progress -> {
            if (progress == null) {
                progressBar.setVisibility(View.GONE);
                progressText.setVisibility(View.GONE);
                return;
            }
            
            if (progress.isDownloading) {
                progressBar.setVisibility(View.VISIBLE);
                progressText.setVisibility(View.VISIBLE);
                
                int percent = (int) ((progress.progress * 100) / progress.totalBytes);
                progressBar.setProgress(percent);
                
                String text = progress.isUpdate
                        ? getString(R.string.updating_region, progress.regionName, percent)
                        : getString(R.string.downloading_region, progress.regionName, percent);
                progressText.setText(text);
            } else {
                progressBar.setVisibility(View.GONE);
                progressText.setVisibility(View.GONE);
                
                if (progress.hasError) {
                    Toast.makeText(this, getString(R.string.error_download_region, progress.errorMessage), Toast.LENGTH_LONG).show();
                } else if (progress.regionId != null) {
                    String message = progress.isUpdate
                            ? getString(R.string.region_updated, progress.regionName)
                            : getString(R.string.region_downloaded, progress.regionName);
                    Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        // Update UI based on connectivity
        updateConnectivityUI();
        connectivityMonitor.registerConnectivityCallback(isConnected -> runOnUiThread(this::updateConnectivityUI));
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        map = googleMap;
        
        // Set map settings
        map.setMapType(GoogleMap.MAP_TYPE_NORMAL);
        map.getUiSettings().setZoomControlsEnabled(true);
        
        // Center on Yemen
        LatLng yemen = new LatLng(15.5527, 48.5164);
        map.moveCamera(CameraUpdateFactory.newLatLngZoom(yemen, 6));
        
        // Set up map click listener for drawing regions
        map.setOnMapClickListener(latLng -> {
            if (isDrawingRegion) {
                addPointToRegion(latLng);
            }
        });
        
        // Show existing regions on map
        showRegionsOnMap();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * Handle region actions (update, delete)
     * @param region The region
     * @param action The action (update, delete)
     */
    private void onRegionAction(OfflineMapManager.OfflineRegion region, String action) {
        if ("update".equals(action)) {
            if (!connectivityMonitor.isOnline()) {
                Toast.makeText(this, R.string.error_offline_update, Toast.LENGTH_SHORT).show();
                return;
            }
            offlineMapManager.updateRegion(region.id);
        } else if ("delete".equals(action)) {
            offlineMapManager.deleteRegion(region.id);
        } else if ("view".equals(action)) {
            // Center map on region
            map.animateCamera(CameraUpdateFactory.newLatLngBounds(region.bounds, 50));
        }
    }

    /**
     * Toggle draw mode for selecting a region
     */
    private void toggleDrawMode() {
        isDrawingRegion = !isDrawingRegion;
        
        if (isDrawingRegion) {
            // Start drawing mode
            drawnPoints.clear();
            selectedRegion = null;
            drawRegionButton.setImageResource(R.drawable.ic_check);
            Toast.makeText(this, R.string.draw_region_instructions, Toast.LENGTH_LONG).show();
        } else {
            // End drawing mode
            drawRegionButton.setImageResource(R.drawable.ic_draw);
            
            if (drawnPoints.size() >= 2) {
                // Create bounds from points
                LatLngBounds.Builder builder = new LatLngBounds.Builder();
                for (LatLng point : drawnPoints) {
                    builder.include(point);
                }
                selectedRegion = builder.build();
                
                // Show download button
                downloadButton.setVisibility(View.VISIBLE);
                
                // Show selected region on map
                showSelectedRegion();
            } else {
                Toast.makeText(this, R.string.error_not_enough_points, Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Add a point to the region being drawn
     * @param latLng The point to add
     */
    private void addPointToRegion(LatLng latLng) {
        drawnPoints.add(latLng);
        
        // Show marker
        map.addMarker(new MarkerOptions().position(latLng));
        
        // Show line if we have at least 2 points
        if (drawnPoints.size() >= 2) {
            LatLng prev = drawnPoints.get(drawnPoints.size() - 2);
            LatLng current = drawnPoints.get(drawnPoints.size() - 1);
            
            map.addPolyline(new com.google.android.gms.maps.model.PolylineOptions()
                    .add(prev, current)
                    .width(5)
                    .color(getResources().getColor(R.color.colorAccent)));
        }
    }

    /**
     * Show the selected region on the map
     */
    private void showSelectedRegion() {
        if (selectedRegion == null || map == null) return;
        
        // Clear map
        map.clear();
        
        // Show polygon
        PolygonOptions polygonOptions = new PolygonOptions()
                .strokeWidth(5)
                .strokeColor(getResources().getColor(R.color.colorAccent))
                .fillColor(getResources().getColor(R.color.colorAccentTransparent));
        
        for (LatLng point : drawnPoints) {
            polygonOptions.add(point);
        }
        
        map.addPolygon(polygonOptions);
        
        // Zoom to bounds
        map.animateCamera(CameraUpdateFactory.newLatLngBounds(selectedRegion, 50));
    }

    /**
     * Show existing regions on the map
     */
    private void showRegionsOnMap() {
        if (map == null) return;
        
        // Get regions
        offlineMapManager.getOfflineRegions().observe(this, regions -> {
            // Clear map
            map.clear();
            
            // Show each region
            for (OfflineMapManager.OfflineRegion region : regions) {
                // Show polygon
                PolygonOptions polygonOptions = new PolygonOptions()
                        .strokeWidth(3)
                        .strokeColor(getResources().getColor(R.color.colorPrimary))
                        .fillColor(getResources().getColor(R.color.colorPrimaryTransparent));
                
                // Convert bounds to polygon
                LatLng southwest = new LatLng(region.bounds.southwest.latitude, region.bounds.southwest.longitude);
                LatLng northwest = new LatLng(region.bounds.northeast.latitude, region.bounds.southwest.longitude);
                LatLng northeast = new LatLng(region.bounds.northeast.latitude, region.bounds.northeast.longitude);
                LatLng southeast = new LatLng(region.bounds.southwest.latitude, region.bounds.northeast.longitude);
                
                polygonOptions.add(southwest, northwest, northeast, southeast, southwest);
                
                map.addPolygon(polygonOptions);
            }
            
            // Show selected region if any
            if (selectedRegion != null) {
                showSelectedRegion();
            }
        });
    }

    /**
     * Download the selected region
     */
    private void downloadSelectedRegion() {
        if (selectedRegion == null) {
            Toast.makeText(this, R.string.error_no_region_selected, Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!connectivityMonitor.isOnline()) {
            Toast.makeText(this, R.string.error_offline_download, Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Generate region name
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        String regionName = "Region " + dateFormat.format(new Date());
        
        // Download region
        offlineMapManager.downloadRegion(selectedRegion, regionName);
        
        // Hide download button
        downloadButton.setVisibility(View.GONE);
        
        // Clear selection
        selectedRegion = null;
        drawnPoints.clear();
        
        // Show regions on map
        showRegionsOnMap();
    }

    /**
     * Update UI based on connectivity status
     */
    private void updateConnectivityUI() {
        boolean isOnline = connectivityMonitor.isOnline();
        
        // Show/hide offline indicator
        View offlineIndicator = findViewById(R.id.offline_indicator);
        offlineIndicator.setVisibility(isOnline ? View.GONE : View.VISIBLE);
        
        // Enable/disable buttons
        drawRegionButton.setEnabled(isOnline || isDrawingRegion);
        downloadButton.setEnabled(isOnline);
    }
}

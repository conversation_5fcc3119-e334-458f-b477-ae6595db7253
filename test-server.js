# إنشاء مهمة مجدولة لمراقبة خدمة محاكاة الموقع وإعادة تشغيلها إذا توقفت

$taskName = "MonitorYemenNavLocationService"
$taskExists = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue

if ($taskExists) {
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
    Write-Host "تم حذف مهمة المراقبة السابقة" -ForegroundColor Yellow
}

# إنشاء سكريبت المراقبة
$monitorScriptPath = "E:\yemen gps\monitor-location-service.ps1"
$monitorScriptContent = @'
# سكريبت لمراقبة خدمة محاكاة الموقع وإعادة تشغيلها إذا توقفت
$servicePath = "E:\yemen gps\location-simulator.js"
$servicePort = 3000

# التحقق مما إذا كانت الخدمة تعمل
try {
    $connection = New-Object System.Net.Sockets.TcpClient("localhost", $servicePort)
    if ($connection.Connected) {
        $connection.Close()
        Write-Host "خدمة محاكاة الموقع تعمل بشكل طبيعي" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "خدمة محاكاة الموقع متوقفة، جاري إعادة تشغيلها..." -ForegroundColor Yellow
}

# إعادة تشغيل الخدمة
try {
    $nodePath = "C:\Program Files\nodejs\node.exe"
    if (!(Test-Path $nodePath)) {
        $nodePath = "node"
    }
    
    # إيقاف أي عمليات سابقة
    $processes = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*location-simulator.js*" }
    if ($processes) {
        $processes | ForEach-Object { Stop-Process -Id $_.Id -Force }
        Write-Host "تم إيقاف العمليات السابقة" -ForegroundColor Yellow
    }
    
    # بدء الخدمة
    Start-Process -FilePath $nodePath -ArgumentList $servicePath -WorkingDirectory "E:\yemen gps" -WindowStyle Hidden
    Write-Host "تم إعادة تشغيل خدمة محاكاة الموقع" -ForegroundColor Green
} catch {
    Write-Host "حدث خطأ أثناء إعادة تشغيل الخدمة: $_" -ForegroundColor Red
    exit 1
}
'@

Set-Content -Path $monitorScriptPath -Value $monitorScriptContent -Encoding UTF8

# إنشاء المهمة المجدولة
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$monitorScriptPath`""
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5)
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings

Write-Host "تم إنشاء مهمة مجدولة لمراقبة خدمة محاكاة الموقع كل 5 دقائق" -ForegroundColor Green



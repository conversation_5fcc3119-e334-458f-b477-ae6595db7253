/**
 * تحسينات البحث والتنقل - يمن ناف
 * 
 * هذا الملف يقدم تحسينات متقدمة لوظائف البحث والتنقل في تطبيق يمن ناف.
 */

// متغيرات عامة للبحث
let activeSearchMarkers = [];
let lastSearchQuery = '';
let searchHistory = [];

class AdvancedSearchManager {
  constructor() {
    this.searchHistory = [];
    this.recentSearches = [];
    this.favoriteLocations = [];
    this.searchFilters = {
      type: null,       // نوع المكان (محطة وقود، مطعم، مستشفى، الخ)
      distance: null,   // المسافة القصوى (بالكيلومترات)
      rating: null,     // التقييم الأدنى
      openNow: false    // الأماكن المفتوحة حالياً فقط
    };
    
    // تحميل البيانات المخزنة محلياً
    this.loadSavedData();
  }
  
  /**
   * تحميل البيانات المخزنة محلياً
   */
  loadSavedData() {
    const searches = localStorage.getItem('yemenNavRecentSearches');
    if (searches) this.recentSearches = JSON.parse(searches);
    
    const favorites = localStorage.getItem('yemenNavFavoriteLocations');
    if (favorites) this.favoriteLocations = JSON.parse(favorites);
    
    const filters = localStorage.getItem('yemenNavSearchFilters');
    if (filters) this.searchFilters = JSON.parse(filters);
  }
  
  /**
   * حفظ البيانات محلياً
   */
  saveData() {
    localStorage.setItem('yemenNavRecentSearches', JSON.stringify(this.recentSearches));
    localStorage.setItem('yemenNavFavoriteLocations', JSON.stringify(this.favoriteLocations));
    localStorage.setItem('yemenNavSearchFilters', JSON.stringify(this.searchFilters));
  }
  
  /**
   * البحث عن موقع بالاسم أو العنوان
   * @param {string} query - عبارة البحث
   * @param {object} options - خيارات البحث
   * @return {Promise<Array>} - نتائج البحث
   */
  async search(query, options = {}) {
    // حفظ البحث في السجل
    this.searchHistory.push({
      query,
      timestamp: new Date().toISOString(),
      filters: { ...this.searchFilters }
    });

    // تحديث عمليات البحث الأخيرة
    if (query.trim() !== '') {
      // إزالة البحث إذا كان موجوداً مسبقاً لتجنب التكرار
      this.recentSearches = this.recentSearches.filter(q => q.toLowerCase() !== query.toLowerCase());
      
      // إضافة البحث الجديد في المقدمة
      this.recentSearches.unshift(query);
      
      // الاحتفاظ بآخر 10 عمليات بحث فقط
      if (this.recentSearches.length > 10) {
        this.recentSearches.pop();
      }
      
      // حفظ البيانات
      this.saveData();
    }

    // دمج الفلاتر مع الخيارات
    const searchParams = {
      ...this.searchFilters,
      ...options,
      query
    };

    try {
      // في البيئة الحقيقية، سنرسل طلب إلى واجهة برمجة التطبيقات
      // حالياً سنستخدم بيانات تجريبية للعرض
      const results = await this.mockSearchResults(searchParams);
      return results;
    } catch (error) {
      console.error('خطأ في البحث:', error);
      throw error;
    }
  }
  
  /**
   * محاكاة نتائج البحث (للعرض والتطوير فقط)
   * @param {object} params - معلمات البحث
   * @return {Promise<Array>} - نتائج البحث المحاكاة
   */
  async mockSearchResults(params) {
    // تأخير محاكاة لشبكة
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // بيانات محاكاة للمواقع
    const mockLocations = [
      { 
        id: 1, 
        name: 'مستشفى الثورة', 
        type: 'hospital',
        coordinates: [15.3525, 44.2005], 
        address: 'شارع الجزائر، صنعاء', 
        rating: 4.2,
        distance: 1.2,
        open: true
      },
      { 
        id: 2, 
        name: 'محطة الريان للوقود', 
        type: 'gas_station',
        coordinates: [15.3622, 44.2033], 
        address: 'شارع تعز، صنعاء', 
        rating: 3.8,
        distance: 2.5,
        open: true
      },
      { 
        id: 3, 
        name: 'مطعم حضرموت', 
        type: 'restaurant',
        coordinates: [15.3502, 44.2150], 
        address: 'شارع هائل، صنعاء', 
        rating: 4.5,
        distance: 3.1,
        open: false
      },
      { 
        id: 4, 
        name: 'جامعة صنعاء', 
        type: 'university',
        coordinates: [15.3482, 44.1910], 
        address: 'شارع الجامعة، صنعاء', 
        rating: 4.7,
        distance: 0.8,
        open: true
      },
      { 
        id: 5, 
        name: 'مكتبة اليمن الوطنية', 
        type: 'library',
        coordinates: [15.3430, 44.2085], 
        address: 'شارع الزبيري، صنعاء', 
        rating: 4.0,
        distance: 1.7,
        open: true
      }
    ];
    
    // تطبيق الفلترة
    let results = mockLocations;
    
    // فلترة بالاسم
    if (params.query) {
      const query = params.query.toLowerCase();
      results = results.filter(location => 
        location.name.toLowerCase().includes(query) || 
        location.address.toLowerCase().includes(query)
      );
    }
    
    // فلترة حسب النوع
    if (params.type) {
      results = results.filter(location => location.type === params.type);
    }
    
    // فلترة حسب المسافة
    if (params.distance) {
      results = results.filter(location => location.distance <= params.distance);
    }
    
    // فلترة حسب التقييم
    if (params.rating) {
      results = results.filter(location => location.rating >= params.rating);
    }
    
    // فلترة حسب حالة الفتح
    if (params.openNow) {
      results = results.filter(location => location.open === true);
    }
    
    return results;
  }
  
  /**
   * تعيين فلاتر البحث
   * @param {object} filters - الفلاتر الجديدة
   */
  setFilters(filters) {
    this.searchFilters = { ...this.searchFilters, ...filters };
    this.saveData();
  }
  
  /**
   * إعادة تعيين فلاتر البحث إلى الإعدادات الافتراضية
   */
  resetFilters() {
    this.searchFilters = {
      type: null,
      distance: null,
      rating: null,
      openNow: false
    };
    this.saveData();
  }
  
  /**
   * إضافة موقع إلى المفضلة
   * @param {object} location - الموقع المراد إضافته للمفضلة
   */
  addToFavorites(location) {
    // التحقق من عدم وجود الموقع مسبقاً في المفضلة
    const exists = this.favoriteLocations.some(fav => fav.id === location.id);
    if (!exists) {
      this.favoriteLocations.push(location);
      this.saveData();
      return true;
    }
    return false;
  }
  
  /**
   * إزالة موقع من المفضلة
   * @param {number} locationId - معرّف الموقع
   */
  removeFromFavorites(locationId) {
    this.favoriteLocations = this.favoriteLocations.filter(location => location.id !== locationId);
    this.saveData();
  }
  
  /**
   * الحصول على المفضلة
   * @return {Array} - قائمة المواقع المفضلة
   */
  getFavorites() {
    return this.favoriteLocations;
  }
  
  /**
   * الحصول على عمليات البحث الأخيرة
   * @param {number} limit - العدد الأقصى للنتائج
   * @return {Array} - قائمة عمليات البحث الأخيرة
   */
  getRecentSearches(limit = 10) {
    return this.recentSearches.slice(0, limit);
  }
  
  /**
   * حذف عمليات البحث الأخيرة
   */
  clearRecentSearches() {
    this.recentSearches = [];
    this.saveData();
  }
}

/**
 * مدير المسارات المتقدم
 */
class AdvancedRouteManager {
  constructor() {
    this.routePreferences = {
      routeType: 'fastest',  // أسرع، أقصر، أقل ازدحاماً
      avoidTolls: false,     // تجنب الطرق ذات الرسوم
      avoidHighways: false,  // تجنب الطرق السريعة
      avoidFerries: false,   // تجنب العبّارات
      avoidTraffic: false,   // تجنب الازدحام المروري
      vehicleType: 'car'     // نوع المركبة (سيارة، شاحنة، دراجة نارية، مشي)
    };
    
    // تحميل التفضيلات المخزنة
    this.loadPreferences();
  }
  
  /**
   * تحميل تفضيلات المسار المخزنة محلياً
   */
  loadPreferences() {
    const prefs = localStorage.getItem('yemenNavRoutePreferences');
    if (prefs) this.routePreferences = JSON.parse(prefs);
  }
  
  /**
   * حفظ تفضيلات المسار
   */
  savePreferences() {
    localStorage.setItem('yemenNavRoutePreferences', JSON.stringify(this.routePreferences));
  }
  
  /**
   * تحديث تفضيلات المسار
   * @param {object} preferences - التفضيلات الجديدة
   */
  updatePreferences(preferences) {
    this.routePreferences = { ...this.routePreferences, ...preferences };
    this.savePreferences();
  }
  
  /**
   * حساب المسار بين موقعين
   * @param {Array} start - مصفوفة تحتوي على خط العرض وخط الطول لنقطة البداية
   * @param {Array} end - مصفوفة تحتوي على خط العرض وخط الطول لنقطة النهاية
   * @param {Array} waypoints - مصفوفة اختيارية من النقاط الوسيطة
   * @return {Promise<object>} - المسار المحسوب
   */
  async calculateRoute(start, end, waypoints = []) {
    try {
      // في البيئة الحقيقية، سنرسل طلب إلى واجهة برمجة التطبيقات للمسارات
      // حالياً سنستخدم محاكاة للعرض
      
      // التحقق من وجود خوارزمية المسار
      if (window.Router && typeof window.Router.calculateRoute === 'function') {
        return window.Router.calculateRoute(start, end, waypoints, this.routePreferences);
      } else {
        // إذا لم تكن خوارزمية المسار متوفرة، استخدم المحاكاة
        return this.mockCalculateRoute(start, end, waypoints);
      }
    } catch (error) {
      console.error('خطأ في حساب المسار:', error);
      throw error;
    }
  }
  
  // تنفيذ مبسط لبقية الدوال
  mockCalculateRoute() {
    return { path: [], distance: 0, duration: 0, instructions: [] };
  }
  
  generateRoutePath() {
    return [];
  }
  
  incorporateWaypoints(path) {
    return path;
  }
  
  calculatePathDistance() {
    return 0;
  }
  
  haversineDistance() {
    return 0;
  }
  
  estimateTravelTime() {
    return 0;
  }
  
  generateRouteInstructions() {
    return [];
  }
}

/**
 * البحث عن موقع باستخدام خدمة Nominatim
 * @param {string} query - عبارة البحث
 * @param {object} options - خيارات البحث
 * @return {Promise<Array>} - نتائج البحث
 */
async function searchWithNominatim(query, options = {}) {
  if (!query || query.trim() === '') {
    console.warn('تم تقديم استعلام بحث فارغ');
    return Promise.reject(new Error('استعلام البحث مطلوب'));
  }
  
  // حفظ الاستعلام في سجل البحث
  lastSearchQuery = query;
  if (!searchHistory.includes(query)) {
    searchHistory.unshift(query);
    if (searchHistory.length > 10) {
      searchHistory.pop();
    }
    
    // حفظ سجل البحث في التخزين المحلي
    try {
      localStorage.setItem('yemenNavSearchHistory', JSON.stringify(searchHistory));
    } catch (e) {
      console.warn('تعذر حفظ سجل البحث في التخزين المحلي', e);
    }
  }
  
  // إضافة اليمن إلى الاستعلام للحصول على نتائج أكثر دقة
  let searchQuery = query;
  if (!query.includes('اليمن') && !query.includes('yemen') && !query.includes('يمن')) {
    searchQuery = `${query} اليمن`;
  }
  
  const searchParams = {
    format: 'json',
    q: searchQuery,
    limit: options.limit || 5,
    countrycodes: 'ye',
    addressdetails: 1,
    namedetails: 1
  };
  
  // إنشاء سلسلة الاستعلام
  const queryString = Object.keys(searchParams)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(searchParams[key])}`)
    .join('&');
  
  // إجراء طلب البحث
  return fetch(`https://nominatim.openstreetmap.org/search?${queryString}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`خطأ في استجابة البحث: ${response.status} ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('نتائج البحث:', data);
      return data;
    })
    .catch(error => {
      console.error('خطأ في طلب البحث:', error);
      throw error;
    });
}

/**
 * عرض نتائج البحث على الخريطة
 * @param {Array} results - نتائج البحث
 * @param {object} map - كائن الخريطة
 */
function displaySearchResultsOnMap(results, map) {
  // مسح العلامات السابقة
  clearSearchMarkers();
  
  if (!results || results.length === 0) {
    console.warn('لا توجد نتائج للعرض');
    return;
  }
  
  // عرض النتيجة الرئيسية أولاً
  const mainResult = results[0];
  const mainLat = parseFloat(mainResult.lat);
  const mainLng = parseFloat(mainResult.lon);
  
  // إضافة علامة للنتيجة الرئيسية
  const mainMarker = L.circleMarker([mainLat, mainLng], {
    radius: 12,
    fillColor: "#ff0000",
    color: "#fff",
    weight: 2,
    opacity: 1,
    fillOpacity: 0.8
  }).addTo(map);
  
  // تخزين العلامة في المصفوفة النشطة
  activeSearchMarkers.push(mainMarker);
  
  // تحريك الخريطة إلى النتيجة الرئيسية
  map.setView([mainLat, mainLng], 15);
  
  // إعداد محتوى النافذة المنبثقة
  let popupContent = `<div style="text-align: right; direction: rtl;">
                      <h3 style="margin: 0 0 5px 0;">${mainResult.display_name}</h3>`;
  
  // إضافة تفاصيل العنوان إذا كانت متوفرة
  if (mainResult.address) {
    const address = mainResult.address;
    popupContent += '<div style="font-size: 0.9em;">';
    
    if (address.city || address.town || address.village) {
      popupContent += `<div>المدينة: ${address.city || address.town || address.village}</div>`;
    }
    
    if (address.road) {
      popupContent += `<div>الشارع: ${address.road}</div>`;
    }
    
    if (address.country) {
      popupContent += `<div>البلد: ${address.country}</div>`;
    }
    
    popupContent += '</div>';
  }
  
  popupContent += '</div>';
  
  // إضافة النافذة المنبثقة
  mainMarker.bindPopup(popupContent).openPopup();
  
  // إضافة علامات للنتائج الإضافية
  if (results.length > 1) {
    for (let i = 1; i < results.length; i++) {
      const result = results[i];
      const lat = parseFloat(result.lat);
      const lng = parseFloat(result.lon);
      
      const marker = L.circleMarker([lat, lng], {
        radius: 8,
        fillColor: "#ff7700",
        color: "#fff",
        weight: 1,
        opacity: 1,
        fillOpacity: 0.6
      }).addTo(map);
      
      activeSearchMarkers.push(marker);
      
      // إعداد محتوى النافذة المنبثقة
      let altPopupContent = `<div style="text-align: right; direction: rtl;">
                           <h3 style="margin: 0 0 5px 0;">${result.display_name}</h3>`;
      
      // إضافة تفاصيل العنوان إذا كانت متوفرة
      if (result.address) {
        const address = result.address;
        altPopupContent += '<div style="font-size: 0.9em;">';
        
        if (address.city || address.town || address.village) {
          altPopupContent += `<div>المدينة: ${address.city || address.town || address.village}</div>`;
        }
        
        if (address.road) {
          altPopupContent += `<div>الشارع: ${address.road}</div>`;
        }
        
        if (address.country) {
          altPopupContent += `<div>البلد: ${address.country}</div>`;
        }
        
        altPopupContent += '</div>';
      }
      
      altPopupContent += '</div>';
      
      // إضافة النافذة المنبثقة
      marker.bindPopup(altPopupContent);
    }
  }
  
  return {
    mainResult: {
      name: mainResult.display_name,
      location: [mainLat, mainLng]
    },
    totalResults: results.length
  };
}

/**
 * مسح علامات البحث النشطة
 */
function clearSearchMarkers() {
  activeSearchMarkers.forEach(marker => {
    if (marker && marker.remove) {
      marker.remove();
    }
  });
  activeSearchMarkers = [];
}

/**
 * وظيفة لمعالجة البحث المباشر
 * @param {string} query - نص الاستعلام
 * @param {object} map - كائن الخريطة
 */
function handleSearch(query, map) {
  if (!query || !map) {
    console.error('استعلام البحث والخريطة مطلوبان');
    return Promise.reject(new Error('بيانات غير كافية'));
  }
  
  // إظهار مؤشر التحميل
  const searchInput = document.getElementById('search-input');
  const searchButton = document.getElementById('search-button');
  
  if (searchInput) {
    searchInput.disabled = true;
  }
  
  if (searchButton) {
    searchButton.disabled = true;
    searchButton.textContent = 'جاري البحث...';
  }
  
  // تنفيذ البحث
  return searchWithNominatim(query)
    .then(results => {
      if (results.length === 0) {
        alert('لم يتم العثور على نتائج للبحث: ' + query);
        return null;
      }
      
      // عرض النتائج على الخريطة
      return displaySearchResultsOnMap(results, map);
    })
    .catch(error => {
      console.error('خطأ في البحث:', error);
      alert('حدث خطأ أثناء البحث: ' + error.message);
      return null;
    })
    .finally(() => {
      // إعادة تمكين عناصر البحث
      if (searchInput) {
        searchInput.disabled = false;
      }
      
      if (searchButton) {
        searchButton.disabled = false;
        searchButton.textContent = 'بحث';
      }
    });
}

// إضافة المديرين إلى كائن النافذة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('تهيئة مديري البحث والمسارات المتقدمة');
  window.searchManager = new AdvancedSearchManager();
  window.routeManager = new AdvancedRouteManager();
  
  // مراقبة أوامر البحث الحالية
  watchSearchInputs();
  
  // إعداد وظائف البحث المتقدمة
  setupAdvancedSearch();
});

/**
 * مراقبة حقول البحث في الصفحة وربطها بمدير البحث المتقدم
 */
function watchSearchInputs() {
  // البحث عن حقول البحث في الصفحة
  const searchInputs = document.querySelectorAll('input[type="search"], input[name="search"], .search-input, #search-input');
  
  searchInputs.forEach(input => {
    input.addEventListener('keydown', async (event) => {
      if (event.key === 'Enter') {
        const query = input.value.trim();
        if (query) {
          try {
            // تعطيل حقل الإدخال أثناء البحث
            input.disabled = true;
            
            // إظهار مؤشر التحميل إذا كان متاحًا
            const loadingIndicator = document.querySelector('.search-loading');
            if (loadingIndicator) loadingIndicator.style.display = 'block';
            
            // إجراء البحث
            if (window.map) {
              await handleSearch(query, window.map);
            } else {
              const results = await window.searchManager.search(query);
              displaySearchResults(results);
            }
            
            // إخفاء مؤشر التحميل
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            
            // إعادة تفعيل حقل الإدخال
            input.disabled = false;
            
          } catch (error) {
            console.error('خطأ أثناء البحث:', error);
            
            // إخفاء مؤشر التحميل
            const loadingIndicator = document.querySelector('.search-loading');
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            
            // إعادة تفعيل حقل الإدخال
            input.disabled = false;
            
            // عرض رسالة خطأ
            alert('حدث خطأ أثناء البحث: ' + error.message);
          }
        }
      }
    });
  });
}

/**
 * إعداد وظائف البحث المتقدمة
 */
function setupAdvancedSearch() {
  // التحقق من وجود عناصر البحث
  const searchInput = document.getElementById('search-input');
  const searchButton = document.getElementById('search-button');
  
  if (searchInput && searchButton) {
    // إضافة مستمع أحداث لزر البحث
    searchButton.addEventListener('click', () => {
      const query = searchInput.value.trim();
      if (query && window.map) {
        handleSearch(query, window.map);
      }
    });
    
    // تعديل وظيفة عرض النتائج
    window.displaySearchResults = function(results) {
      console.log('عرض نتائج البحث:', results);
      
      if (window.map) {
        // محاكاة نتائج من Nominatim عند استخدام محاكاة البحث الداخلية
        const nominatimResults = results.map(result => {
          return {
            place_id: result.id,
            lat: result.coordinates[0],
            lon: result.coordinates[1],
            display_name: `${result.name}, ${result.address}`,
            address: {
              city: 'صنعاء',
              road: result.address.split('،')[0],
              country: 'اليمن'
            }
          };
        });
        
        displaySearchResultsOnMap(nominatimResults, window.map);
      } else {
        // عرض النتائج في واجهة المستخدم بطريقة أخرى
        // يمكن تنفيذ ذلك حسب احتياجات التطبيق
      }
    };
  }
}

// تصدير الوظائف لاستخدامها في ملفات JavaScript أخرى
window.yemenNavSearch = {
  search: searchWithNominatim,
  displayResults: displaySearchResultsOnMap,
  clearMarkers: clearSearchMarkers,
  handleSearch: handleSearch
};

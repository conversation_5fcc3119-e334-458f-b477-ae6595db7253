// إصلاح الأماكن المفقودة الإحداثيات
const { Pool } = require('pg');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// إحداثيات تقريبية للمحافظات اليمنية
const GOVERNORATE_COORDINATES = {
    'صنعاء': { lat: 15.3547, lng: 44.2066 },
    'عدن': { lat: 12.7797, lng: 45.0365 },
    'تعز': { lat: 13.5795, lng: 44.0205 },
    'الحديدة': { lat: 14.7978, lng: 42.9545 },
    'إب': { lat: 13.9667, lng: 44.1833 },
    'ذمار': { lat: 14.5426, lng: 44.4054 },
    'المكلا': { lat: 14.5425, lng: 49.1242 },
    'مأرب': { lat: 15.4694, lng: 45.3222 },
    'حضرموت': { lat: 14.5425, lng: 49.1242 },
    'لحج': { lat: 13.0582, lng: 44.8819 },
    'أبين': { lat: 13.9667, lng: 45.3667 },
    'شبوة': { lat: 14.5333, lng: 46.8333 },
    'المهرة': { lat: 16.7167, lng: 52.1667 },
    'الجوف': { lat: 16.6333, lng: 45.6333 },
    'صعدة': { lat: 16.9333, lng: 43.7667 },
    'حجة': { lat: 15.6833, lng: 43.6000 },
    'عمران': { lat: 15.6667, lng: 44.0000 },
    'البيضاء': { lat: 14.1667, lng: 45.5667 },
    'ريمة': { lat: 14.3833, lng: 43.5167 },
    'الضالع': { lat: 13.7000, lng: 44.7333 }
};

async function fixMissingCoordinates() {
    try {
        console.log('🔧 بدء إصلاح الإحداثيات المفقودة...\n');
        
        // البحث عن الأماكن بدون إحداثيات
        const missingCoords = await pool.query(`
            SELECT p.id, p.name_ar, p.name_en, p.latitude, p.longitude, g.name_ar as governorate_name
            FROM places p
            LEFT JOIN governorates g ON p.governorate_id = g.id
            WHERE p.latitude IS NULL OR p.longitude IS NULL OR p.latitude = 0 OR p.longitude = 0
            ORDER BY p.id
        `);
        
        if (missingCoords.rows.length === 0) {
            console.log('✅ جميع الأماكن تحتوي على إحداثيات صحيحة!');
            return;
        }
        
        console.log(`❌ تم العثور على ${missingCoords.rows.length} مكان بدون إحداثيات:`);
        
        let fixedCount = 0;
        
        for (const place of missingCoords.rows) {
            console.log(`\n📍 معالجة: ${place.name_ar}`);
            console.log(`   المحافظة: ${place.governorate_name || 'غير محدد'}`);
            
            let newLat = null;
            let newLng = null;
            
            // محاولة الحصول على إحداثيات من اسم المحافظة
            if (place.governorate_name && GOVERNORATE_COORDINATES[place.governorate_name]) {
                const coords = GOVERNORATE_COORDINATES[place.governorate_name];
                // إضافة تنويع عشوائي صغير لتجنب التطابق التام
                newLat = coords.lat + (Math.random() - 0.5) * 0.01; // تنويع ±0.005 درجة
                newLng = coords.lng + (Math.random() - 0.5) * 0.01;
                
                console.log(`   ✅ تم تعيين إحداثيات المحافظة: ${newLat.toFixed(6)}, ${newLng.toFixed(6)}`);
            } else {
                // إحداثيات افتراضية (صنعاء) مع تنويع
                newLat = 15.3547 + (Math.random() - 0.5) * 0.02;
                newLng = 44.2066 + (Math.random() - 0.5) * 0.02;
                
                console.log(`   ⚠️ تم تعيين إحداثيات افتراضية: ${newLat.toFixed(6)}, ${newLng.toFixed(6)}`);
            }
            
            // تحديث الإحداثيات في قاعدة البيانات
            try {
                await pool.query(`
                    UPDATE places 
                    SET latitude = $1, longitude = $2, updated_at = NOW()
                    WHERE id = $3
                `, [newLat, newLng, place.id]);
                
                fixedCount++;
                console.log(`   💾 تم التحديث بنجاح`);
                
            } catch (error) {
                console.error(`   ❌ خطأ في التحديث: ${error.message}`);
            }
        }
        
        console.log(`\n🎉 تم الانتهاء من الإصلاح!`);
        console.log(`✅ تم إصلاح: ${fixedCount} مكان`);
        console.log(`❌ فشل في: ${missingCoords.rows.length - fixedCount} مكان`);
        
        // التحقق من النتيجة النهائية
        const finalCheck = await pool.query(`
            SELECT COUNT(*) as total_places,
                   COUNT(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL AND latitude != 0 AND longitude != 0 THEN 1 END) as with_coords
            FROM places
        `);
        
        const stats = finalCheck.rows[0];
        console.log(`\n📊 الإحصائيات النهائية:`);
        console.log(`   إجمالي الأماكن: ${stats.total_places}`);
        console.log(`   مع إحداثيات: ${stats.with_coords} (${Math.round((stats.with_coords / stats.total_places) * 100)}%)`);
        
    } catch (error) {
        console.error('❌ خطأ في إصلاح الإحداثيات:', error);
    } finally {
        await pool.end();
    }
}

fixMissingCoordinates();

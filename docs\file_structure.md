# Yemen GPS Navigation System - File Structure

This document provides an overview of the project's file structure and the purpose of each file.

## Android Application

### Root Files

- `android/app/build.gradle`: Gradle build configuration for the Android app

### Java Source Files

#### Application

- `android/app/src/main/java/com/yemengps/app/YemenGpsApplication.java`: Main application class that initializes app components

#### Models

- `android/app/src/main/java/com/yemengps/app/model/LocationType.java`: Enum defining the types of locations (Traffic, Speedbump, Pothole, etc.)
- `android/app/src/main/java/com/yemengps/app/model/LocationPoint.java`: Entity class representing a location point on the map
- `android/app/src/main/java/com/yemengps/app/model/User.java`: Entity class representing a user in the system

#### Data Access

- `android/app/src/main/java/com/yemengps/app/data/AppDatabase.java`: Room database configuration
- `android/app/src/main/java/com/yemengps/app/data/LocationRepository.java`: Repository for accessing location data
- `android/app/src/main/java/com/yemengps/app/data/UserRepository.java`: Repository for accessing user data
- `android/app/src/main/java/com/yemengps/app/data/PreferenceManager.java`: Manager for handling user preferences

##### Data Access Objects (DAOs)

- `android/app/src/main/java/com/yemengps/app/data/dao/LocationDao.java`: DAO for location operations
- `android/app/src/main/java/com/yemengps/app/data/dao/UserDao.java`: DAO for user operations

##### Type Converters

- `android/app/src/main/java/com/yemengps/app/data/converters/DateConverter.java`: Converter for Date objects in Room
- `android/app/src/main/java/com/yemengps/app/data/converters/LocationTypeConverter.java`: Converter for LocationType enum in Room

#### Network

- `android/app/src/main/java/com/yemengps/app/network/ApiClient.java`: Client for API communication
- `android/app/src/main/java/com/yemengps/app/network/ApiService.java`: Retrofit service interface for API endpoints
- `android/app/src/main/java/com/yemengps/app/network/ApiResponse.java`: Wrapper class for API responses

#### Services

- `android/app/src/main/java/com/yemengps/app/services/LocationService.java`: Background service for location tracking and alerts

#### UI

- `android/app/src/main/java/com/yemengps/app/ui/MainActivity.java`: Main activity with map and navigation
- `android/app/src/main/java/com/yemengps/app/ui/AddLocationActivity.java`: Activity for adding new locations

##### Authentication UI

- `android/app/src/main/java/com/yemengps/app/ui/auth/LoginActivity.java`: Activity for user login
- `android/app/src/main/java/com/yemengps/app/ui/auth/RegisterActivity.java`: Activity for user registration

### Resource Files

#### Layouts

- `android/app/src/main/res/layout/activity_main.xml`: Layout for the main activity
- `android/app/src/main/res/layout/activity_add_location.xml`: Layout for the add location activity
- `android/app/src/main/res/layout/activity_login.xml`: Layout for the login activity
- `android/app/src/main/res/layout/activity_register.xml`: Layout for the registration activity
- `android/app/src/main/res/layout/nav_header_main.xml`: Layout for the navigation drawer header

#### Values

- `android/app/src/main/res/values/strings.xml`: String resources
- `android/app/src/main/res/values/colors.xml`: Color resources
- `android/app/src/main/res/values/styles.xml`: Style resources

#### Menus

- `android/app/src/main/res/menu/activity_main_drawer.xml`: Menu for the navigation drawer

### Manifest

- `android/app/src/main/AndroidManifest.xml`: Android manifest file defining app components and permissions

## Documentation

### Project Documentation

- `README.md`: Project overview and setup instructions
- `docs/project_summary.md`: Summary of the project, its features, and implementation

### Technical Documentation

- `docs/api_documentation.md`: Documentation of the API endpoints
- `docs/database_schema.md`: Documentation of the database schema
- `docs/system_architecture.md`: Documentation of the system architecture
- `docs/file_structure.md`: This file, documenting the project's file structure

### User Documentation

- `docs/user_guide.md`: Guide for end users
- `docs/admin_guide.md`: Guide for administrators

## File Descriptions

### Android Application Files

#### YemenGpsApplication.java

The main application class that initializes app components such as the database, preferences, and API client. It also creates notification channels for location tracking and alerts.

#### Model Files

- **LocationType.java**: Enum defining the types of locations that can be marked on the map (Traffic, Speedbump, Pothole, Dirt Road, Checkpoint).
- **LocationPoint.java**: Entity class representing a location point on the map, with properties such as name, coordinates, type, and approval status.
- **User.java**: Entity class representing a user in the system, with properties such as name, email, and authentication information.

#### Data Access Files

- **AppDatabase.java**: Room database configuration that defines the database schema and provides access to DAOs.
- **LocationRepository.java**: Repository that provides a clean API for accessing location data from both local database and remote API.
- **UserRepository.java**: Repository that provides a clean API for accessing user data from both local database and remote API.
- **PreferenceManager.java**: Manager for handling user preferences such as login state, alert settings, and map settings.

##### DAO Files

- **LocationDao.java**: Data Access Object for location operations, providing methods for CRUD operations and queries.
- **UserDao.java**: Data Access Object for user operations, providing methods for CRUD operations and queries.

##### Converter Files

- **DateConverter.java**: Type converter for Room to convert between Date objects and Long timestamps.
- **LocationTypeConverter.java**: Type converter for Room to convert between LocationType enum and String.

#### Network Files

- **ApiClient.java**: Client for API communication, handling authentication, request building, and response processing.
- **ApiService.java**: Retrofit service interface defining the API endpoints.
- **ApiResponse.java**: Generic wrapper class for API responses, providing a standardized format.

#### Service Files

- **LocationService.java**: Background service for continuous location tracking and triggering alerts when approaching marked locations.

#### UI Files

- **MainActivity.java**: Main activity that displays the map, handles navigation, and provides access to app features.
- **AddLocationActivity.java**: Activity for adding new locations, allowing users to select a point on the map and provide details.

##### Authentication UI Files

- **LoginActivity.java**: Activity for user login, handling authentication and navigation to the main activity.
- **RegisterActivity.java**: Activity for user registration, handling account creation and validation.

#### Resource Files

- **Layout Files**: XML layouts defining the UI structure for activities and components.
- **Values Files**: Resources for strings, colors, and styles used throughout the app.
- **Menu Files**: XML definitions for menus such as the navigation drawer.

#### Manifest File

- **AndroidManifest.xml**: Defines app components, permissions, and configuration.

### Documentation Files

- **README.md**: Project overview, features, and setup instructions.
- **project_summary.md**: Comprehensive summary of the project, its features, and implementation details.
- **api_documentation.md**: Detailed documentation of the API endpoints, request/response formats, and authentication.
- **database_schema.md**: Documentation of the database schema, tables, relationships, and indexes.
- **system_architecture.md**: Documentation of the system architecture, components, and interactions.
- **user_guide.md**: Guide for end users on how to use the application.
- **admin_guide.md**: Guide for administrators on how to use the admin panel.
- **file_structure.md**: This file, documenting the project's file structure and purpose of each file.

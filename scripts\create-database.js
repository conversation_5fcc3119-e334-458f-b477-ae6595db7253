// يمن ناف - ملف إنشاء قاعدة البيانات
const { Client } = require('pg');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// تحميل متغيرات البيئة
dotenv.config();

// طباعة معلومات الاتصال للتصحيح
console.log('معلومات الاتصال بقاعدة البيانات:');
console.log('DB_USER:', process.env.DB_USER || 'postgres');
console.log('DB_HOST:', process.env.DB_HOST || 'localhost');
console.log('DB_PORT:', process.env.DB_PORT || 5432);

// إعدادات الاتصال بقاعدة البيانات الرئيسية (postgres)
const pgConfig = {
    user: 'postgres',
    password: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres' // الاتصال بقاعدة البيانات الافتراضية postgres
};

// اسم قاعدة البيانات المراد إنشاؤها
const dbName = process.env.DB_NAME || 'yemen_gps';

// دالة للتحقق من وجود قاعدة البيانات وإنشائها إذا لم تكن موجودة
async function createDatabase() {
    const client = new Client(pgConfig);

    try {
        // الاتصال بقاعدة البيانات الرئيسية
        await client.connect();
        console.log('تم الاتصال بقاعدة البيانات الرئيسية بنجاح');

        // التحقق من وجود قاعدة البيانات
        const checkDbQuery = `SELECT 1 FROM pg_database WHERE datname = $1`;
        const checkResult = await client.query(checkDbQuery, [dbName]);

        if (checkResult.rows.length === 0) {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            console.log(`قاعدة البيانات ${dbName} غير موجودة، جاري إنشاؤها...`);
            await client.query(`CREATE DATABASE ${dbName}`);
            console.log(`تم إنشاء قاعدة البيانات ${dbName} بنجاح`);
        } else {
            console.log(`قاعدة البيانات ${dbName} موجودة بالفعل`);
        }

        return true;
    } catch (error) {
        console.error('خطأ في إنشاء قاعدة البيانات:', error);
        throw error;
    } finally {
        await client.end();
    }
}

// دالة رئيسية لتنفيذ العملية
async function main() {
    try {
        // إنشاء قاعدة البيانات
        await createDatabase();

        // تنفيذ ملف تهيئة قاعدة البيانات
        console.log('جاري تنفيذ ملف تهيئة قاعدة البيانات...');

        // استدعاء ملف تهيئة قاعدة البيانات
        require('../backend/src/init-database');

        console.log('تمت عملية إنشاء وتهيئة قاعدة البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في عملية إنشاء وتهيئة قاعدة البيانات:', error);
        process.exit(1);
    }
}

// تنفيذ الدالة الرئيسية
main();

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن المستقلة - تعمل بدون إنترنت</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="assets/css/leaflet.css" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            padding: 10px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            position: relative;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            font-size: 20px;
            margin: 0;
        }

        .status-badge {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            margin: 0 20px;
        }

        #searchInput {
            width: 100%;
            padding: 8px 40px 8px 15px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #e74c3c;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
        }

        .header-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            background: rgba(255,255,255,0.1);
            padding: 4px 8px;
            border-radius: 10px;
        }

        .map-container {
            height: calc(100vh - 60px);
            position: relative;
        }

        #map {
            height: 100%;
            width: 100%;
        }

        .map-controls {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group {
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            overflow: hidden;
        }

        .control-btn {
            background: white;
            border: none;
            padding: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #eee;
        }

        .control-btn:last-child {
            border-bottom: none;
        }

        .control-btn:hover {
            background: #3498db;
            color: white;
        }

        .control-btn.active {
            background: #2c3e50;
            color: white;
        }

        .sidebar {
            position: absolute;
            top: 0;
            right: -300px;
            width: 300px;
            height: 100%;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 999;
            overflow-y: auto;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: #34495e;
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .close-sidebar {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        .category-list {
            padding: 10px;
        }

        .category-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
            margin-bottom: 5px;
        }

        .category-item:hover {
            background: #ecf0f1;
        }

        .category-item.active {
            background: #3498db;
            color: white;
        }

        .category-icon {
            font-size: 18px;
            width: 25px;
            text-align: center;
        }

        .category-name {
            flex: 1;
            font-weight: 500;
        }

        .category-count {
            background: #95a5a6;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
        }

        .category-item.active .category-count {
            background: rgba(255,255,255,0.3);
        }

        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding: 6px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            z-index: 1000;
        }

        .status-left {
            display: flex;
            gap: 15px;
        }

        .status-right {
            display: flex;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .popup-content {
            max-width: 280px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
        }

        .popup-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .popup-subtitle {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 10px;
            font-style: italic;
        }

        .popup-photos {
            display: flex;
            gap: 4px;
            margin: 8px 0;
            overflow-x: auto;
        }

        .popup-photo {
            width: 50px;
            height: 38px;
            object-fit: cover;
            border-radius: 3px;
            cursor: pointer;
            transition: transform 0.2s;
            flex-shrink: 0;
        }

        .popup-photo:hover {
            transform: scale(1.1);
        }

        .popup-details {
            font-size: 12px;
            color: #34495e;
            margin: 8px 0;
        }

        .popup-detail {
            margin: 3px 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .popup-actions {
            margin-top: 10px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .popup-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .directions-btn { background: #3498db; color: white; }
        .share-btn { background: #2ecc71; color: white; }
        .save-btn { background: #f39c12; color: white; }

        .popup-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            flex-direction: column;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #ecf0f1;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .loading-progress {
            width: 250px;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 11px;
            color: #7f8c8d;
        }

        .toast {
            position: fixed;
            top: 80px;
            right: 20px;
            background: #2c3e50;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            z-index: 10001;
            animation: slideIn 0.3s ease;
            font-size: 13px;
        }

        .toast.success { background: #27ae60; }
        .toast.error { background: #e74c3c; }
        .toast.warning { background: #f39c12; }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .search-result-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }

        .search-result-item:hover {
            background: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
            font-size: 13px;
        }

        .result-description {
            font-size: 11px;
            color: #7f8c8d;
        }

        /* Custom marker styles */
        .custom-marker {
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            position: relative;
        }

        .custom-marker::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .search-container {
                max-width: 100%;
                margin: 0;
            }
            
            .header-stats {
                display: none;
            }
            
            .sidebar {
                width: 250px;
                right: -250px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🇾🇪 خرائط اليمن</h1>
                <span class="status-badge">مستقل 100%</span>
            </div>
            
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="البحث في الأماكن..." autocomplete="off">
                <button id="searchBtn" class="search-btn">🔍</button>
                <div id="searchResults" class="search-results"></div>
            </div>
            
            <div class="header-stats">
                <div class="stat-item">
                    <span>📍</span>
                    <span id="placesCount">0</span>
                </div>
                <div class="stat-item">
                    <span>📷</span>
                    <span id="photosCount">0</span>
                </div>
                <div class="stat-item">
                    <span>🗺️</span>
                    <span id="tilesCount">محلي</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
        
        <!-- Map Controls -->
        <div class="map-controls">
            <div class="control-group">
                <button id="zoomIn" class="control-btn" title="تكبير">+</button>
                <button id="zoomOut" class="control-btn" title="تصغير">-</button>
            </div>
            
            <div class="control-group">
                <button id="locationBtn" class="control-btn" title="موقعي">📍</button>
                <button id="layersBtn" class="control-btn" title="الطبقات">🗂️</button>
                <button id="categoriesBtn" class="control-btn" title="الفئات">📋</button>
            </div>
            
            <div class="control-group">
                <button id="infoBtn" class="control-btn" title="معلومات">ℹ️</button>
                <button id="settingsBtn" class="control-btn" title="الإعدادات">⚙️</button>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3>فئات الأماكن</h3>
                <button id="closeSidebar" class="close-sidebar">&times;</button>
            </div>
            
            <div class="category-list">
                <div class="category-item active" data-category="all">
                    <span class="category-icon">🗺️</span>
                    <span class="category-name">جميع الأماكن</span>
                    <span class="category-count" id="count-all">0</span>
                </div>
                <div class="category-item" data-category="1">
                    <span class="category-icon">🏛️</span>
                    <span class="category-name">معالم سياحية</span>
                    <span class="category-count" id="count-1">0</span>
                </div>
                <div class="category-item" data-category="2">
                    <span class="category-icon">🕌</span>
                    <span class="category-name">أماكن دينية</span>
                    <span class="category-count" id="count-2">0</span>
                </div>
                <div class="category-item" data-category="3">
                    <span class="category-icon">🍽️</span>
                    <span class="category-name">مطاعم</span>
                    <span class="category-count" id="count-3">0</span>
                </div>
                <div class="category-item" data-category="4">
                    <span class="category-icon">🏨</span>
                    <span class="category-name">فنادق</span>
                    <span class="category-count" id="count-4">0</span>
                </div>
                <div class="category-item" data-category="5">
                    <span class="category-icon">🏥</span>
                    <span class="category-name">صحة</span>
                    <span class="category-count" id="count-5">0</span>
                </div>
                <div class="category-item" data-category="6">
                    <span class="category-icon">🎓</span>
                    <span class="category-name">تعليم</span>
                    <span class="category-count" id="count-6">0</span>
                </div>
                <div class="category-item" data-category="7">
                    <span class="category-icon">🏛️</span>
                    <span class="category-name">خدمات</span>
                    <span class="category-count" id="count-7">0</span>
                </div>
                <div class="category-item" data-category="8">
                    <span class="category-icon">🛍️</span>
                    <span class="category-name">تسوق</span>
                    <span class="category-count" id="count-8">0</span>
                </div>
                <div class="category-item" data-category="9">
                    <span class="category-icon">🚗</span>
                    <span class="category-name">نقل</span>
                    <span class="category-count" id="count-9">0</span>
                </div>
                <div class="category-item" data-category="10">
                    <span class="category-icon">📍</span>
                    <span class="category-name">أخرى</span>
                    <span class="category-count" id="count-10">0</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-left">
            <div class="status-item">
                <span>🌐</span>
                <span>مستقل - لا يحتاج إنترنت</span>
            </div>
            <div class="status-item">
                <span>⚡</span>
                <span id="loadTime">جاري التحميل...</span>
            </div>
        </div>
        
        <div class="status-right">
            <div class="status-item">
                <span>📍</span>
                <span id="coordinates">15.3547, 44.2066</span>
            </div>
            <div class="status-item">
                <span>🔍</span>
                <span id="zoomLevel">7</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل خرائط اليمن المستقلة...</div>
        <div class="loading-progress">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div class="progress-text" id="progressText">0%</div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/leaflet.js"></script>
    <script src="assets/js/offline-yemen-app.js"></script>
</body>
</html>

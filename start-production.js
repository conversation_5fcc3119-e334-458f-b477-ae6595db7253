#!/usr/bin/env node

// سكريبت تشغيل خادم الإنتاج لنظام يمن GPS
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 ========================================');
console.log('🗺️  بدء تشغيل نظام يمن GPS');
console.log('🚀 ========================================');

// التحقق من وجود الملفات المطلوبة
const requiredFiles = [
    'production-server.js',
    'public/index.html',
    'public/assets/js/google-maps-app.js'
];

console.log('📋 فحص الملفات المطلوبة...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ الملف المطلوب غير موجود: ${file}`);
        process.exit(1);
    }
    console.log(`✅ ${file}`);
}

// التحقق من المتطلبات
console.log('\n📦 فحص المتطلبات...');
try {
    require('express');
    console.log('✅ Express.js');
} catch (error) {
    console.error('❌ Express.js غير مثبت. قم بتشغيل: npm install express');
    process.exit(1);
}

try {
    require('compression');
    console.log('✅ Compression');
} catch (error) {
    console.log('⚠️ Compression غير مثبت. قم بتشغيل: npm install compression');
}

try {
    require('helmet');
    console.log('✅ Helmet');
} catch (error) {
    console.log('⚠️ Helmet غير مثبت. قم بتشغيل: npm install helmet');
}

// إعداد متغيرات البيئة
process.env.NODE_ENV = 'production';
process.env.PORT = process.env.PORT || '5000';
process.env.HOST = process.env.HOST || '0.0.0.0';

console.log('\n🔧 إعدادات الخادم:');
console.log(`   البيئة: ${process.env.NODE_ENV}`);
console.log(`   المنفذ: ${process.env.PORT}`);
console.log(`   المضيف: ${process.env.HOST}`);
console.log(`   الرابط: http://***********:${process.env.PORT}/`);

console.log('\n🚀 بدء تشغيل الخادم...');
console.log('🚀 ========================================\n');

// تشغيل الخادم
const server = spawn('node', ['production-server.js'], {
    stdio: 'inherit',
    env: process.env
});

// معالجة الأحداث
server.on('error', (error) => {
    console.error('❌ خطأ في تشغيل الخادم:', error);
    process.exit(1);
});

server.on('exit', (code) => {
    if (code !== 0) {
        console.error(`❌ الخادم توقف برمز الخطأ: ${code}`);
        process.exit(code);
    } else {
        console.log('✅ تم إيقاف الخادم بنجاح');
    }
});

// معالجة إيقاف السكريبت
process.on('SIGINT', () => {
    console.log('\n🛑 تم استقبال إشارة الإيقاف...');
    server.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 تم استقبال إشارة الإنهاء...');
    server.kill('SIGTERM');
});

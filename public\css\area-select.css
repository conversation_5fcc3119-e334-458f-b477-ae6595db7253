/* area-select.css - Estilos para la selección de áreas de mapas sin conexión */

.area-select-help {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px 20px;
  border-radius: 5px;
  z-index: 10000;
  direction: rtl;
  font-family: Arial, sans-serif;
  text-align: center;
}

#selected-area-info {
  background-color: #f0f9ff;
  border: 1px solid #b3e5fc;
  border-radius: 5px;
  padding: 10px;
  margin: 10px 0;
  font-size: 14px;
  direction: rtl;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.progress-container {
  width: 100%;
  height: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 5px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4CAF50;
  width: 0%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

#progress-text {
  text-align: center;
  font-size: 14px;
}

#regions-list {
  list-style: none;
  padding: 0;
  margin: 10px 0;
}

#regions-list li {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 10px;
}

.view-region, .delete-region {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 5px;
  margin-top: 5px;
}

.delete-region {
  background-color: #f44336;
}

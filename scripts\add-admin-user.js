const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function createAdminUser() {
  const username = 'admin';
  const password = 'admin123';
  const email = '<EMAIL>';
  const fullName = 'مدير النظام';
  const phone = '+967712345678';
  
  const hashedPassword = await bcrypt.hash(password, 10);
  
  try {
    await pool.query('BEGIN');
    
    const userRes = await pool.query(
      `INSERT INTO users 
      (username, password, email, full_name, phone, is_active, is_admin)
      VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
      [username, hashedPassword, email, fullName, phone, true, true]
    );
    
    await pool.query('COMMIT');
    console.log('تم إنشاء مستخدم المدير بنجاح!');
  } catch (error) {
    await pool.query('ROLLBACK');
    console.error('خطأ في إنشاء المستخدم:', error);
  } finally {
    await pool.end();
  }
}

createAdminUser();

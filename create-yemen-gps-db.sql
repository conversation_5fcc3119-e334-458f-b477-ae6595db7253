-- إن<PERSON>ا<PERSON> قاعدة بيانات yemen_gps
CREATE DATABASE yemen_gps;

-- الاتصال بقاعدة البيانات yemen_gps
\c yemen_gps;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    role_id INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT TRUE,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    type VARCHAR(20) DEFAULT 'user',
    permissions JSONB
);

-- <PERSON>ن<PERSON>اء جدول الأدوار
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الصلاحيات
CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    permission_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول العلاقة بين الأدوار والصلاحيات
CREATE TABLE role_permissions (
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)
);

-- إنشاء جدول العملاء
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    deviceSN VARCHAR(50) UNIQUE,
    licenseN VARCHAR(50) UNIQUE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول التصنيفات
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    icon VARCHAR(100),
    color VARCHAR(20),
    parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المواقع
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    lat DECIMAL(10, 8) NOT NULL,
    lng DECIMAL(11, 8) NOT NULL,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    address TEXT,
    phone VARCHAR(20),
    website VARCHAR(255),
    opening_hours JSONB,
    added_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المفضلة
CREATE TABLE favorites (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, location_id)
);

-- إنشاء جدول التقييمات
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, location_id)
);

-- إنشاء جدول المسارات
CREATE TABLE routes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    start_point JSONB NOT NULL,
    end_point JSONB NOT NULL,
    waypoints JSONB,
    distance INTEGER,
    duration INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول سجلات النظام
CREATE TABLE logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    details JSONB,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدخال بيانات الأدوار الافتراضية
INSERT INTO roles (role_name, description) VALUES
('admin', 'مدير النظام مع كامل الصلاحيات'),
('user', 'مستخدم عادي'),
('developer', 'مطور النظام');

-- إدخال بيانات الصلاحيات الافتراضية
INSERT INTO permissions (permission_name, description) VALUES
('view_dashboard', 'عرض لوحة التحكم'),
('manage_users', 'إدارة المستخدمين'),
('manage_clients', 'إدارة العملاء'),
('manage_locations', 'إدارة المواقع'),
('manage_categories', 'إدارة التصنيفات'),
('manage_settings', 'إدارة الإعدادات');

-- ربط الأدوار بالصلاحيات
-- المدير لديه جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- المستخدم العادي لديه صلاحية عرض لوحة التحكم فقط
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE permission_name = 'view_dashboard';

-- المطور لديه جميع الصلاحيات
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions;

-- إنشاء مستخدم المدير الافتراضي (كلمة المرور: yemen123)
INSERT INTO users (username, password_hash, full_name, email, role_id, type, permissions)
VALUES ('admin', '$2b$10$X/8.Y5JVzWV5Oa.Yd6Kg9OqQi4JMZdFJO.jkVn1AAVEbQJ1dO2.Hy', 'مدير النظام', '<EMAIL>', 1, 'admin', '[{"code":"view_dashboard","name":"عرض لوحة التحكم"},{"code":"manage_users","name":"إدارة المستخدمين"},{"code":"manage_clients","name":"إدارة العملاء"},{"code":"manage_locations","name":"إدارة المواقع"},{"code":"manage_settings","name":"إدارة الإعدادات"},{"code":"manage_categories","name":"إدارة التصنيفات"}]');

-- إنشاء بعض التصنيفات الافتراضية
INSERT INTO categories (name, icon, color) VALUES
('مطاعم', 'restaurant', '#FF5722'),
('فنادق', 'hotel', '#2196F3'),
('مستشفيات', 'hospital', '#F44336'),
('محطات وقود', 'gas_station', '#4CAF50'),
('مراكز تسوق', 'shopping', '#9C27B0'),
('مدارس وجامعات', 'school', '#FF9800'),
('مساجد', 'mosque', '#795548'),
('حدائق', 'park', '#8BC34A'),
('مكاتب حكومية', 'government', '#607D8B');

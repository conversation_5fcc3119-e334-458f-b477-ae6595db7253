-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    role_id INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    permissions_json JSONB DEFAULT '[]',
    can_access_admin BOOLEAN DEFAULT FALSE
);

-- Create admin user if it doesn't exist
INSERT INTO users (username, email, password, full_name, role_id, is_active, is_verified, can_access_admin, permissions_json)
SELECT 
    'admin', 
    '<EMAIL>', 
    '$2b$10$X7tPj4cjLsVUoA/2o8yw3.Qi.Ym5OJbR8vQzPYxbniQTgPOxRVSJi', -- password: yemen123
    'Admin User', 
    1, 
    TRUE, 
    TRUE, 
    TRUE, 
    '[
        {"code": "view_dashboard", "name": "View Dashboard"},
        {"code": "manage_users", "name": "Manage Users"},
        {"code": "manage_clients", "name": "Manage Clients"},
        {"code": "manage_locations", "name": "Manage Locations"},
        {"code": "manage_settings", "name": "Manage Settings"},
        {"code": "manage_categories", "name": "Manage Categories"}
    ]'::jsonb
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE username = 'admin'
);

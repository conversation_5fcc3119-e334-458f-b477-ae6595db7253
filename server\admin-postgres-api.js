// يمن ناف - ملف واجهة برمجة التطبيق (API) لقاعدة بيانات PostgreSQL
const express = require('express');
const router = express.Router();
const db = require('../backend/src/postgres-db');
const usersService = require('../backend/src/postgres-users');
const clientsService = require('../backend/src/postgres-clients');
const categoriesService = require('../backend/src/postgres-categories');
const locationsService = require('../backend/src/postgres-locations');

// لا حاجة للمصادقة في لوحة التحكم البسيطة

// دالة بسيطة للسماح بالوصول بدون مصادقة
function checkAuth(req, res, next) {
    // طباعة معلومات الطلب للتشخيص
    console.log(`\n===== طلب API للمسار: ${req.method} ${req.originalUrl} =====`);
    
    // إضافة بيانات المستخدم الافتراضية لتجنب الأخطاء
    req.user = {
        id: 1,
        user_id: 1,
        userId: 1,
        username: 'admin',
        name: 'Admin User',
        role: 1,
        role_id: 1,
        roleId: 1,
        email: '<EMAIL>'
    };
    
    // المتابعة إلى المسار التالي
    return next();
}

// واجهة برمجة التطبيق (API) للمستخدمين
// الحصول على جميع المستخدمين - متاح للجميع في وضع التطوير
router.get('/users', checkAuth, async (req, res) => {
    try {
        const users = await usersService.getAllUsers();
        res.json(users);
    } catch (err) {
        console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
    }
});

// الحصول على مستخدم بواسطة المعرف
router.get('/users/:id', checkAuth, async (req, res) => {
    try {
        const userId = req.params.id;
        const user = await usersService.getUserById(userId);

        if (!user) {
            return res.status(404).json({ message: 'المستخدم غير موجود' });
        }

        res.json(user);
    } catch (err) {
        console.error('خطأ في الحصول على المستخدم:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
    }
});

// إنشاء مستخدم جديد
router.post('/users', checkAuth, async (req, res) => {
    try {
        const userData = req.body;

        // التحقق من البيانات المطلوبة
        if (!userData.username || !userData.email || !userData.password) {
            return res.status(400).json({ message: 'يرجى توفير اسم المستخدم والبريد الإلكتروني وكلمة المرور' });
        }

        const newUser = await usersService.createUser(userData);
        res.status(201).json(newUser);
    } catch (err) {
        console.error('خطأ في إنشاء المستخدم:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء إنشاء المستخدم' });
    }
});

// تحديث بيانات مستخدم
router.put('/users/:id', checkAuth, async (req, res) => {
    try {
        const userId = req.params.id;
        const userData = req.body;

        // التحقق من وجود المستخدم
        const existingUser = await usersService.getUserById(userId);
        if (!existingUser) {
            return res.status(404).json({ message: 'المستخدم غير موجود' });
        }

        // منع تعديل المدير الرئيسي من قبل غير المدير الرئيسي
        if (parseInt(userId) === 1 && req.user.id !== 1) {
            return res.status(403).json({ message: 'لا يمكن تعديل بيانات المدير الرئيسي' });
        }

        const updatedUser = await usersService.updateUser(userId, userData);
        res.json(updatedUser);
    } catch (err) {
        console.error('خطأ في تحديث المستخدم:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء تحديث بيانات المستخدم' });
    }
});

// حذف مستخدم
router.delete('/users/:id', checkAuth, async (req, res) => {
    try {
        const userId = req.params.id;

        // التحقق من وجود المستخدم
        const existingUser = await usersService.getUserById(userId);
        if (!existingUser) {
            return res.status(404).json({ message: 'المستخدم غير موجود' });
        }

        // منع حذف المدير الرئيسي
        if (parseInt(userId) === 1) {
            return res.status(403).json({ message: 'لا يمكن حذف المدير الرئيسي' });
        }

        await usersService.deleteUser(userId);
        res.json({ message: 'تم حذف المستخدم بنجاح' });
    } catch (err) {
        console.error('خطأ في حذف المستخدم:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء حذف المستخدم' });
    }
});

// الحصول على جميع الأدوار - متاح للجميع في وضع التطوير
router.get('/roles', async (req, res) => {
    try {
        const roles = await usersService.getAllRoles();
        res.json(roles);
    } catch (err) {
        console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
    }
});

// واجهة برمجة التطبيق (API) للعملاء
// الحصول على جميع العملاء
router.get('/clients', checkAuth, async (req, res) => {
    try {
        const clients = await clientsService.getAllClients();
        res.json(clients);
    } catch (err) {
        console.error('خطأ في الحصول على قائمة العملاء:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات العملاء' });
    }
});

// الحصول على عميل بواسطة المعرف
router.get('/clients/:id', checkAuth, async (req, res) => {
    try {
        const clientId = req.params.id;
        const client = await clientsService.getClientById(clientId);

        if (!client) {
            return res.status(404).json({ message: 'العميل غير موجود' });
        }

        res.json(client);
    } catch (err) {
        console.error('خطأ في الحصول على العميل:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات العميل' });
    }
});

// إنشاء عميل جديد
router.post('/clients', checkAuth, async (req, res) => {
    try {
        const clientData = req.body;

        // التحقق من البيانات المطلوبة
        if (!clientData.name) {
            return res.status(400).json({ message: 'يرجى توفير اسم العميل' });
        }

        const newClient = await clientsService.createClient(clientData);
        res.status(201).json(newClient);
    } catch (err) {
        console.error('خطأ في إنشاء العميل:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء إنشاء العميل' });
    }
});

// تحديث بيانات عميل
router.put('/clients/:id', checkAuth, async (req, res) => {
    try {
        const clientId = req.params.id;
        const clientData = req.body;

        // التحقق من وجود العميل
        const existingClient = await clientsService.getClientById(clientId);
        if (!existingClient) {
            return res.status(404).json({ message: 'العميل غير موجود' });
        }

        const updatedClient = await clientsService.updateClient(clientId, clientData);
        res.json(updatedClient);
    } catch (err) {
        console.error('خطأ في تحديث العميل:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء تحديث بيانات العميل' });
    }
});

// حذف عميل
router.delete('/clients/:id', checkAuth, async (req, res) => {
    try {
        const clientId = req.params.id;

        // التحقق من وجود العميل
        const existingClient = await clientsService.getClientById(clientId);
        if (!existingClient) {
            return res.status(404).json({ message: 'العميل غير موجود' });
        }

        await clientsService.deleteClient(clientId);
        res.json({ message: 'تم حذف العميل بنجاح' });
    } catch (err) {
        console.error('خطأ في حذف العميل:', err.message);
        res.status(500).json({ message: 'حدث خطأ أثناء حذف العميل' });
    }
});

// واجهة برمجة التطبيق (API) لحالة قاعدة البيانات
// التحقق من حالة اتصال قاعدة البيانات - متاح للجميع في وضع التطوير
router.get('/db-status', async (req, res) => {
    try {
        console.log('جاري التحقق من حالة اتصال قاعدة البيانات...');
        const isConnected = await db.checkConnection();
        console.log('نتيجة التحقق من اتصال قاعدة البيانات:', isConnected);
        res.json({
            connected: isConnected,
            message: isConnected ? 'قاعدة البيانات متصلة' : 'قاعدة البيانات غير متصلة'
        });
    } catch (err) {
        console.error('خطأ في التحقق من حالة اتصال قاعدة البيانات:', err.message);
        res.status(500).json({
            connected: false,
            message: 'حدث خطأ أثناء التحقق من حالة اتصال قاعدة البيانات'
        });
    }
});

// الحصول على معلومات قاعدة البيانات
router.get('/db-info', checkAuth, async (req, res) => {
    try {
        const dbInfo = await db.getDatabaseInfo();
        res.json({
            success: true,
            info: dbInfo
        });
    } catch (err) {
        console.error('خطأ في الحصول على معلومات قاعدة البيانات:', err.message);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ أثناء الحصول على معلومات قاعدة البيانات'
        });
    }
});

// تصدير الراوتر
module.exports = router;

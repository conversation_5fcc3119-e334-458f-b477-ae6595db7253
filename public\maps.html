<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن - تجربة Google Maps</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Enhanced Google Styles -->
    <link rel="stylesheet" href="assets/css/google-enhanced.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', 'Cairo', sans-serif;
            background: #f8f9fa;
            overflow: hidden;
        }

        /* Google Maps Style Header */
        .maps-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 16px;
        }

        .maps-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-right: 24px;
        }

        .maps-logo img {
            width: 32px;
            height: 32px;
        }

        .maps-logo h1 {
            font-size: 22px;
            color: #5f6368;
            font-weight: 400;
        }

        /* Google Maps Style Search Box */
        .search-container {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-box {
            width: 100%;
            height: 48px;
            border: 1px solid #dadce0;
            border-radius: 24px;
            padding: 0 20px 0 48px;
            font-size: 16px;
            outline: none;
            transition: all 0.2s ease;
            background: #fff;
        }

        .search-box:focus {
            border-color: #1a73e8;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9aa0a6;
            font-size: 20px;
        }

        /* User Menu */
        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-left: 24px;
        }

        .menu-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .menu-btn:hover {
            background: #f1f3f4;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1a73e8;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        /* Map Container */
        .map-container {
            position: fixed;
            top: 64px;
            left: 0;
            right: 0;
            bottom: 0;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        /* Google Maps Style Controls */
        .map-controls {
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            overflow: hidden;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s ease;
            color: #5f6368;
        }

        .control-btn:hover {
            background: #f8f9fa;
        }

        .control-btn:not(:last-child) {
            border-bottom: 1px solid #e8eaed;
        }

        /* Layer Switcher - Google Style */
        .layer-switcher {
            position: absolute;
            bottom: 16px;
            right: 16px;
            z-index: 1000;
        }

        .layer-toggle {
            background: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #3c4043;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .layer-toggle:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .layer-menu {
            position: absolute;
            bottom: 100%;
            right: 0;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            padding: 8px;
            min-width: 200px;
            display: none;
        }

        .layer-menu.show {
            display: block;
        }

        .layer-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .layer-option:hover {
            background: #f8f9fa;
        }

        .layer-option.active {
            background: #e8f0fe;
            color: #1a73e8;
        }

        .layer-preview {
            width: 48px;
            height: 48px;
            border-radius: 4px;
            margin-left: 12px;
            background-size: cover;
            background-position: center;
        }

        .layer-info h4 {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .layer-info p {
            font-size: 12px;
            color: #5f6368;
        }

        /* Sidebar */
        .sidebar {
            position: absolute;
            top: 0;
            right: 0;
            width: 400px;
            height: 100%;
            background: white;
            box-shadow: -2px 0 8px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1001;
            overflow-y: auto;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaed;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-content {
            padding: 20px;
        }

        /* Location Card */
        .location-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            overflow: hidden;
        }

        .location-image {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: center;
            background-color: #f8f9fa;
        }

        .location-info {
            padding: 16px;
        }

        .location-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #3c4043;
        }

        .location-description {
            font-size: 14px;
            color: #5f6368;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .location-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 8px 16px;
            border: 1px solid #dadce0;
            border-radius: 20px;
            background: white;
            color: #1a73e8;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f8f9fa;
        }

        .action-btn.primary {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }

        .action-btn.primary:hover {
            background: #1557b0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .maps-header {
                padding: 0 12px;
            }

            .maps-logo h1 {
                display: none;
            }

            .search-container {
                max-width: none;
                margin: 0 12px;
            }

            .sidebar {
                width: 100%;
            }

            .map-controls {
                top: 12px;
                right: 12px;
            }

            .layer-switcher {
                bottom: 12px;
                right: 12px;
            }
        }

        /* Loading Animation */
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2000;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Marker Styles */
        .custom-marker {
            background: #ea4335;
            width: 24px;
            height: 24px;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .custom-marker::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="maps-header">
        <div class="maps-logo">
            <i class="fas fa-map-marked-alt" style="color: #1a73e8; font-size: 24px;"></i>
            <h1>خرائط اليمن</h1>
        </div>

        <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input type="text" class="search-box" placeholder="البحث في خرائط اليمن" id="searchInput">
        </div>

        <div class="user-menu">
            <button class="menu-btn" title="القائمة">
                <i class="fas fa-bars"></i>
            </button>
            <button class="menu-btn" title="الإعدادات">
                <i class="fas fa-cog"></i>
            </button>
            <div class="user-avatar">ي</div>
        </div>
    </header>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>

        <!-- Loading -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
        </div>

        <!-- Map Controls -->
        <div class="map-controls">
            <div class="control-group">
                <button class="control-btn" id="zoomIn" title="تكبير">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="control-btn" id="zoomOut" title="تصغير">
                    <i class="fas fa-minus"></i>
                </button>
            </div>

            <div class="control-group">
                <button class="control-btn" id="myLocation" title="موقعي">
                    <i class="fas fa-crosshairs"></i>
                </button>
            </div>

            <div class="control-group">
                <button class="control-btn" id="fullscreen" title="ملء الشاشة">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>

        <!-- Layer Switcher -->
        <div class="layer-switcher">
            <button class="layer-toggle" id="layerToggle">
                <span id="currentLayerName">خريطة</span>
                <i class="fas fa-chevron-up"></i>
            </button>

            <div class="layer-menu" id="layerMenu">
                <div class="layer-option active" data-layer="streets">
                    <div class="layer-info">
                        <h4>خريطة</h4>
                        <p>الشوارع والطرق</p>
                    </div>
                    <div class="layer-preview" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\"><rect fill=\"%23f5f5f5\" width=\"48\" height=\"48\"/><path fill=\"%23666\" d=\"M8 8h32v32H8z\"/></svg>')"></div>
                </div>

                <div class="layer-option" data-layer="satellite">
                    <div class="layer-info">
                        <h4>القمر الصناعي</h4>
                        <p>صور جوية</p>
                    </div>
                    <div class="layer-preview" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\"><rect fill=\"%234CAF50\" width=\"48\" height=\"48\"/><circle fill=\"%23FFC107\" cx=\"24\" cy=\"24\" r=\"8\"/></svg>')"></div>
                </div>

                <div class="layer-option" data-layer="terrain">
                    <div class="layer-info">
                        <h4>التضاريس</h4>
                        <p>الجبال والوديان</p>
                    </div>
                    <div class="layer-preview" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\"><rect fill=\"%23E8F5E8\" width=\"48\" height=\"48\"/><path fill=\"%234CAF50\" d=\"M8 32l8-8 8 4 8-12 8 8v16H8z\"/></svg>')"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>تفاصيل الموقع</h3>
            <button class="menu-btn" id="closeSidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="sidebar-content" id="sidebarContent">
            <!-- Content will be loaded here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="assets/js/google-assets-loader.js"></script>
    <script src="assets/js/google-maps-style.js"></script>
</body>
</html>

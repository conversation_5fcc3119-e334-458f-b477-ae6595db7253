// اختبار مفتاح Google Places API
const axios = require('axios');

const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0';

async function testGoogleAPI() {
    console.log('🔍 اختبار مفتاح Google Places API...\n');
    
    try {
        // اختبار بسيط للبحث عن مكان في صنعاء
        const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
        const response = await axios.get(url, {
            params: {
                key: GOOGLE_API_KEY,
                location: '15.3547,44.2066', // صنعاء
                radius: 1000,
                type: 'restaurant'
            }
        });

        console.log('📊 حالة الاستجابة:', response.data.status);
        
        if (response.data.status === 'OK') {
            console.log('✅ مفتاح API يعمل بشكل صحيح!');
            console.log(`📍 تم العثور على ${response.data.results.length} مطعم`);
            
            if (response.data.results.length > 0) {
                console.log('\n🏪 أول مطعم:');
                const place = response.data.results[0];
                console.log(`   الاسم: ${place.name}`);
                console.log(`   التقييم: ${place.rating || 'غير متوفر'}`);
                console.log(`   العنوان: ${place.vicinity || 'غير متوفر'}`);
            }
        } else {
            console.log('❌ مشكلة في مفتاح API:');
            console.log('   الحالة:', response.data.status);
            console.log('   رسالة الخطأ:', response.data.error_message || 'غير محددة');
            
            // اقتراحات للحلول
            console.log('\n🔧 الحلول المقترحة:');
            
            if (response.data.status === 'REQUEST_DENIED') {
                console.log('   1. تأكد من تفعيل Places API في Google Cloud Console');
                console.log('   2. تحقق من صحة مفتاح API');
                console.log('   3. تأكد من عدم وجود قيود على المفتاح');
                console.log('   4. تحقق من إعدادات الفوترة');
            } else if (response.data.status === 'OVER_QUERY_LIMIT') {
                console.log('   1. تم تجاوز حد الاستعلامات اليومي');
                console.log('   2. انتظر حتى إعادة تعيين الحد أو قم بزيادة الحد');
            } else if (response.data.status === 'INVALID_REQUEST') {
                console.log('   1. تحقق من معاملات الطلب');
                console.log('   2. تأكد من صحة الإحداثيات');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error.message);
        
        if (error.response) {
            console.log('📊 تفاصيل الخطأ:');
            console.log('   كود الحالة:', error.response.status);
            console.log('   البيانات:', error.response.data);
        }
    }
}

// اختبار إضافي لـ Place Details API
async function testPlaceDetailsAPI() {
    console.log('\n🔍 اختبار Place Details API...');
    
    try {
        // استخدام place_id معروف (مطار صنعاء)
        const url = 'https://maps.googleapis.com/maps/api/place/details/json';
        const response = await axios.get(url, {
            params: {
                key: GOOGLE_API_KEY,
                place_id: 'ChIJX8XrAQAGAhYRYIVkNvFuBAQ', // مثال place_id
                fields: 'name,formatted_address,geometry'
            }
        });

        console.log('📊 حالة Place Details:', response.data.status);
        
        if (response.data.status === 'OK') {
            console.log('✅ Place Details API يعمل!');
        } else {
            console.log('❌ مشكلة في Place Details API:', response.data.status);
        }
        
    } catch (error) {
        console.log('❌ خطأ في Place Details API:', error.message);
    }
}

// اختبار Photo API
async function testPhotoAPI() {
    console.log('\n🔍 اختبار Photo API...');
    
    try {
        // اختبار بسيط لـ Photo API
        const url = 'https://maps.googleapis.com/maps/api/place/photo';
        const response = await axios.get(url, {
            params: {
                key: GOOGLE_API_KEY,
                photoreference: 'test', // مرجع وهمي للاختبار
                maxwidth: 400
            },
            validateStatus: () => true // قبول جميع أكواد الحالة
        });

        if (response.status === 400) {
            console.log('✅ Photo API متاح (خطأ متوقع بسبب المرجع الوهمي)');
        } else {
            console.log('📊 حالة Photo API:', response.status);
        }
        
    } catch (error) {
        console.log('❌ خطأ في Photo API:', error.message);
    }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🧪 بدء اختبار شامل لـ Google Places API\n');
    console.log('🔑 المفتاح المستخدم:', GOOGLE_API_KEY.substring(0, 20) + '...');
    console.log('=' .repeat(50));
    
    await testGoogleAPI();
    await testPlaceDetailsAPI();
    await testPhotoAPI();
    
    console.log('\n' + '='.repeat(50));
    console.log('🏁 انتهى الاختبار');
    
    console.log('\n📋 خطوات تفعيل Google Places API:');
    console.log('1. اذهب إلى: https://console.cloud.google.com/');
    console.log('2. اختر مشروعك أو أنشئ مشروع جديد');
    console.log('3. فعّل Places API من قسم APIs & Services');
    console.log('4. أنشئ API Key من قسم Credentials');
    console.log('5. تأكد من إعداد الفوترة (Billing)');
    console.log('6. قم بإزالة أي قيود على المفتاح إذا لزم الأمر');
}

if (require.main === module) {
    runAllTests();
}

module.exports = { testGoogleAPI, testPlaceDetailsAPI, testPhotoAPI };

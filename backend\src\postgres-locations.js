// يمن ناف - ملف إدارة نقاط الموقع باستخدام قاعدة بيانات PostgreSQL
const db = require('./postgres-db');

// دالة للحصول على جميع نقاط الموقع
async function getAllLocations() {
    try {
        const query = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            ORDER BY l.created_at DESC
        `;
        
        const result = await db.query(query);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على نقاط الموقع:', error);
        throw error;
    }
}

// دالة للحصول على نقطة موقع بواسطة المعرف
async function getLocationById(locationId) {
    try {
        const query = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            WHERE l.id = $1
        `;
        
        const result = await db.query(query, [locationId]);
        return result.rows[0] || null;
    } catch (error) {
        console.error('خطأ في الحصول على نقطة الموقع:', error);
        throw error;
    }
}

// دالة لإنشاء نقطة موقع جديدة
async function createLocation(locationData) {
    const client = await db.getClient();
    
    try {
        await client.query('BEGIN');
        
        // إدراج نقطة الموقع الجديدة
        const insertQuery = `
            INSERT INTO locations (
                name,
                description,
                latitude,
                longitude,
                category_id,
                user_id,
                is_approved,
                is_active,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
            RETURNING id
        `;
        
        const values = [
            locationData.name,
            locationData.description || null,
            locationData.latitude,
            locationData.longitude,
            locationData.category_id,
            locationData.user_id,
            locationData.is_approved !== undefined ? locationData.is_approved : false,
            locationData.is_active !== undefined ? locationData.is_active : true
        ];
        
        const result = await client.query(insertQuery, values);
        const locationId = result.rows[0].id;
        
        // الحصول على بيانات نقطة الموقع المدرجة
        const locationQuery = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            WHERE l.id = $1
        `;
        
        const locationResult = await client.query(locationQuery, [locationId]);
        
        await client.query('COMMIT');
        
        return locationResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في إنشاء نقطة الموقع:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لتحديث بيانات نقطة موقع
async function updateLocation(locationId, locationData) {
    const client = await db.getClient();
    
    try {
        await client.query('BEGIN');
        
        // بناء استعلام التحديث
        let updateQuery = 'UPDATE locations SET ';
        const values = [];
        const updateFields = [];
        let paramIndex = 1;
        
        if (locationData.name !== undefined) {
            updateFields.push(`name = $${paramIndex++}`);
            values.push(locationData.name);
        }
        
        if (locationData.description !== undefined) {
            updateFields.push(`description = $${paramIndex++}`);
            values.push(locationData.description);
        }
        
        if (locationData.latitude !== undefined) {
            updateFields.push(`latitude = $${paramIndex++}`);
            values.push(locationData.latitude);
        }
        
        if (locationData.longitude !== undefined) {
            updateFields.push(`longitude = $${paramIndex++}`);
            values.push(locationData.longitude);
        }
        
        if (locationData.category_id !== undefined) {
            updateFields.push(`category_id = $${paramIndex++}`);
            values.push(locationData.category_id);
        }
        
        if (locationData.is_approved !== undefined) {
            updateFields.push(`is_approved = $${paramIndex++}`);
            values.push(locationData.is_approved);
        }
        
        if (locationData.is_active !== undefined) {
            updateFields.push(`is_active = $${paramIndex++}`);
            values.push(locationData.is_active);
        }
        
        // إضافة تاريخ التحديث
        updateFields.push(`updated_at = $${paramIndex++}`);
        values.push(new Date());
        
        // إذا لم تكن هناك حقول للتحديث
        if (updateFields.length === 0) {
            return await getLocationById(locationId);
        }
        
        // إكمال استعلام التحديث
        updateQuery += updateFields.join(', ');
        updateQuery += ` WHERE id = $${paramIndex}`;
        values.push(locationId);
        
        // تنفيذ استعلام التحديث
        await client.query(updateQuery, values);
        
        // الحصول على بيانات نقطة الموقع المحدثة
        const locationQuery = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            WHERE l.id = $1
        `;
        
        const locationResult = await client.query(locationQuery, [locationId]);
        
        await client.query('COMMIT');
        
        return locationResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في تحديث نقطة الموقع:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لحذف نقطة موقع
async function deleteLocation(locationId) {
    try {
        const query = 'DELETE FROM locations WHERE id = $1';
        await db.query(query, [locationId]);
        
        return true;
    } catch (error) {
        console.error('خطأ في حذف نقطة الموقع:', error);
        throw error;
    }
}

// دالة للبحث عن نقاط الموقع
async function searchLocations(searchTerm) {
    try {
        const query = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            WHERE 
                l.name ILIKE $1 OR
                l.description ILIKE $1 OR
                c.name ILIKE $1 OR
                u.username ILIKE $1
            ORDER BY l.created_at DESC
        `;
        
        const result = await db.query(query, [`%${searchTerm}%`]);
        return result.rows;
    } catch (error) {
        console.error('خطأ في البحث عن نقاط الموقع:', error);
        throw error;
    }
}

// دالة للحصول على نقاط الموقع بانتظار الموافقة
async function getPendingLocations() {
    try {
        const query = `
            SELECT 
                l.id as location_id,
                l.name,
                l.description,
                l.latitude,
                l.longitude,
                l.category_id,
                c.name as category_name,
                c.icon as category_icon,
                c.color as category_color,
                l.user_id,
                u.username as user_name,
                l.is_approved,
                l.is_active,
                l.created_at,
                l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.user_id = u.id
            WHERE l.is_approved = false
            ORDER BY l.created_at DESC
        `;
        
        const result = await db.query(query);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على نقاط الموقع بانتظار الموافقة:', error);
        throw error;
    }
}

// دالة للموافقة على نقطة موقع
async function approveLocation(locationId) {
    try {
        const query = 'UPDATE locations SET is_approved = true, updated_at = NOW() WHERE id = $1';
        await db.query(query, [locationId]);
        
        return await getLocationById(locationId);
    } catch (error) {
        console.error('خطأ في الموافقة على نقطة الموقع:', error);
        throw error;
    }
}

// دالة للحصول على عدد نقاط الموقع
async function getLocationsCount() {
    try {
        const query = 'SELECT COUNT(*) as count FROM locations';
        const result = await db.query(query);
        return parseInt(result.rows[0].count);
    } catch (error) {
        console.error('خطأ في الحصول على عدد نقاط الموقع:', error);
        throw error;
    }
}

// تصدير الدوال
module.exports = {
    getAllLocations,
    getLocationById,
    createLocation,
    updateLocation,
    deleteLocation,
    searchLocations,
    getPendingLocations,
    approveLocation,
    getLocationsCount
};

// خادم Express أساسي للتعامل مع واجهة برمجة التطبيقات لإدارة المستخدمين
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const { Pool } = require('pg');

// إنشاء تطبيق Express
const app = express();

// إعداد اتصال قاعدة البيانات
const dbConfig = {
  host: 'localhost',
  port: '5432',
  database: 'yemen_nav',
  user: 'yemen',
  password: 'yemen123' // استخدم كلمة المرور الصحيحة هنا
};

console.log('جاري الاتصال بقاعدة البيانات:', {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  user: dbConfig.user
});

const pool = new Pool(dbConfig);

// اختبار الاتصال بقاعدة البيانات
pool.query('SELECT NOW()', (err, res) => {
  if (err) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', err);
  } else {
    console.log('تم الاتصال بقاعدة البيانات بنجاح:', res.rows[0]);
  }
});

// CORS
app.use(cors());

// JSON parsing
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Set up a logger middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to Yemen Nav API' });
});

// الحصول على جميع المستخدمين
app.get('/api/admin/users', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT u.user_id, u.username, u.email, u.full_name, u.phone, u.profile_image, 
              u.account_type, r.role_name, u.registration_date, u.last_login, 
              u.is_active, u.is_verified 
       FROM users u 
       LEFT JOIN roles r ON u.role_id = r.role_id 
       ORDER BY u.registration_date DESC
    `);
    
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد
app.post('/api/admin/users', async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const userCheck = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
    if (userCheck.rows.length > 0) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const result = await pool.query(`
      INSERT INTO users (username, password, email, full_name, account_type, role_id, registration_date, is_active) 
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), true) 
      RETURNING user_id, username, email, full_name, account_type, role_id
    `, [username, password, email, fullName, accountType || 'local', roleId || 2]);
    
    const newUser = result.rows[0];
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.user_id,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.full_name,
        accountType: newUser.account_type,
        roleId: newUser.role_id
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم
app.put('/api/admin/users/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    const { username, email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const userCheck = await pool.query('SELECT * FROM users WHERE user_id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // بناء استعلام التحديث
    let query = 'UPDATE users SET ';
    const values = [];
    const updateFields = [];
    let paramIndex = 1;
    
    if (email) {
      updateFields.push(`email = $${paramIndex++}`);
      values.push(email);
    }
    
    if (fullName) {
      updateFields.push(`full_name = $${paramIndex++}`);
      values.push(fullName);
    }
    
    if (password) {
      updateFields.push(`password = $${paramIndex++}`);
      values.push(password);
    }
    
    if (roleId) {
      updateFields.push(`role_id = $${paramIndex++}`);
      values.push(roleId);
    }
    
    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramIndex++}`);
      values.push(isActive);
    }
    
    // إذا لم يكن هناك حقول للتحديث
    if (updateFields.length === 0) {
      return res.status(400).json({ message: 'لم يتم تحديد أي حقول للتحديث' });
    }
    
    query += updateFields.join(', ');
    query += ` WHERE user_id = $${paramIndex} RETURNING user_id, username, email, full_name, account_type, role_id, is_active`;
    values.push(userId);
    
    // تنفيذ الاستعلام
    const result = await pool.query(query, values);
    const updatedUser = result.rows[0];
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.user_id,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.full_name,
        accountType: updatedUser.account_type,
        roleId: updatedUser.role_id,
        isActive: updatedUser.is_active
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم
app.delete('/api/admin/users/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من وجود المستخدم
    const userCheck = await pool.query('SELECT * FROM users WHERE user_id = $1', [userId]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await pool.query('DELETE FROM users WHERE user_id = $1', [userId]);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM roles ORDER BY role_id');
    res.json(result.rows);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// إغلاق الاتصال بقاعدة البيانات عند إيقاف الخادم
function gracefulShutdown() {
  console.log('إغلاق الاتصال بقاعدة البيانات...');
  pool.end(() => {
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
    process.exit(0);
  });
}

// التقاط إشارات إيقاف التشغيل
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// تشغيل الخادم
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

# دليل تشغيل نظام "يمن ناف"

## مقدمة

هذا الدليل يشرح خطوات تشغيل نظام "يمن ناف" بعد تثبيت المتطلبات الأساسية. سنقوم بشرح كيفية إعداد قاعدة البيانات، وتشغيل الخادم الخلفي، والوصول إلى واجهة المستخدم.

## الخطوات الأساسية لتشغيل النظام

### 1. إعداد قاعدة البيانات

أول خطوة هي إعداد قاعدة بيانات PostgreSQL. يمكنك استخدام ملف الدفعة `create_db.bat` الذي قمنا بإنشائه:

1. تأكد من تعديل المتغيرات في الملف `create_db.bat` لتناسب إعدادات PostgreSQL على جهازك:
   - `PSQL_PATH`: مسار برنامج psql.exe
   - `PGPASSWORD`: كلمة مرور مستخدم postgres

2. انقر نقرًا مزدوجًا على الملف `create_db.bat` لتشغيله، أو قم بتشغيله من موجه الأوامر:
   ```
   create_db.bat
   ```

3. انتظر حتى تظهر رسالة "Yemen Nav database is ready to use"

### 2. تثبيت حزم Node.js المطلوبة

قبل تشغيل الخادم الخلفي، تحتاج إلى تثبيت حزم Node.js المطلوبة:

1. افتح موجه الأوامر (Command Prompt) أو PowerShell
2. انتقل إلى المجلد الذي يحتوي على ملف `server.js`:
   ```
   cd e:\yemen gps
   ```
3. قم بتثبيت الحزم المطلوبة:
   ```
   npm init -y
   npm install express cors body-parser dotenv pg
   ```

### 3. تشغيل الخادم الخلفي

بعد إعداد قاعدة البيانات وتثبيت الحزم المطلوبة، يمكنك تشغيل الخادم الخلفي:

1. تأكد من أن ملف `.env` موجود في نفس المجلد مع ملف `server.js`
2. قم بتشغيل الخادم:
   ```
   node server.js
   ```
3. يجب أن ترى رسالة "Server is running on port 3000" أو رقم المنفذ الذي حددته في ملف `.env`

### 4. إنشاء ملف تشغيل سريع (اختياري)

يمكنك إنشاء ملف دفعة لتشغيل الخادم بسرعة:

1. قم بإنشاء ملف جديد باسم `run_server.bat`
2. أضف المحتوى التالي:
   ```batch
   @echo off
   echo Starting Yemen Nav Server...
   node server.js
   pause
   ```
3. احفظ الملف
4. يمكنك الآن تشغيل الخادم بالنقر المزدوج على هذا الملف

## تشغيل النظام بالكامل

لتشغيل النظام بالكامل، اتبع الخطوات التالية:

### 1. تشغيل قاعدة البيانات

تأكد من أن خدمة PostgreSQL قيد التشغيل:

1. افتح "Services" من لوحة التحكم (Control Panel)
2. ابحث عن خدمة "postgresql-x64-14" أو اسم مشابه
3. تأكد من أن حالتها "Running"
4. إذا لم تكن قيد التشغيل، انقر بزر الماوس الأيمن واختر "Start"

### 2. تشغيل الخادم الخلفي

1. قم بتشغيل ملف `run_server.bat` أو قم بتنفيذ الأمر `node server.js` من موجه الأوامر
2. تأكد من ظهور رسالة "Server is running on port 3000"

### 3. الوصول إلى واجهة المستخدم

في المرحلة الحالية، يمكنك الوصول إلى واجهة API الخاصة بالنظام:

1. افتح متصفح الويب
2. انتقل إلى `http://localhost:3000`
3. يجب أن ترى رسالة ترحيب بتنسيق JSON: `{"message":"Welcome to Yemen Nav API"}`

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء تشغيل النظام، تحقق من الأمور التالية:

### مشاكل قاعدة البيانات

1. **خطأ في الاتصال بقاعدة البيانات**:
   - تأكد من أن خدمة PostgreSQL قيد التشغيل
   - تحقق من صحة معلومات الاتصال في ملف `.env`
   - تأكد من إنشاء قاعدة البيانات `yemen_nav` بنجاح

2. **خطأ في امتدادات PostGIS**:
   - تأكد من تثبيت PostGIS بشكل صحيح
   - حاول تنفيذ الأمر التالي يدويًا:
     ```
     "C:\Program Files\PostgreSQL\14\bin\psql.exe" -h localhost -p 5432 -d yemen_nav -U postgres -c "CREATE EXTENSION postgis; CREATE EXTENSION postgis_topology;"
     ```

### مشاكل الخادم الخلفي

1. **خطأ "module not found"**:
   - تأكد من تثبيت جميع الحزم المطلوبة باستخدام `npm install`
   - تحقق من وجود ملف `package.json` وأنه يحتوي على جميع التبعيات

2. **خطأ "EADDRINUSE"**:
   - هذا يعني أن المنفذ (3000 افتراضيًا) قيد الاستخدام بالفعل
   - قم بتغيير المنفذ في ملف `.env` أو أغلق التطبيق الذي يستخدم هذا المنفذ

3. **خطأ في ملف `.env`**:
   - تأكد من وجود ملف `.env` في نفس المجلد مع ملف `server.js`
   - تحقق من صحة تنسيق الملف (لا توجد مسافات حول علامة =)

## تشغيل النظام في بيئة الإنتاج

للتشغيل في بيئة الإنتاج، يُفضل استخدام مدير عمليات مثل PM2:

1. قم بتثبيت PM2 عالميًا:
   ```
   npm install -g pm2
   ```

2. قم بتشغيل الخادم باستخدام PM2:
   ```
   pm2 start server.js --name "yemen-nav"
   ```

3. للتأكد من تشغيل الخادم تلقائيًا عند إعادة تشغيل النظام:
   ```
   pm2 startup
   pm2 save
   ```

## الخطوات التالية

بعد تشغيل النظام بنجاح، يمكنك:

1. **تطوير واجهة المستخدم**: إنشاء واجهة مستخدم تتفاعل مع API
2. **إضافة المزيد من الميزات**: توسيع وظائف API لدعم المزيد من الميزات
3. **تحسين الأمان**: إضافة المصادقة والتفويض لحماية البيانات
4. **تحسين الأداء**: تحسين أداء قاعدة البيانات والخادم

## ملخص الأوامر الرئيسية

```
# إعداد قاعدة البيانات
create_db.bat

# تثبيت الحزم المطلوبة
npm init -y
npm install express cors body-parser dotenv pg

# تشغيل الخادم
node server.js

# تشغيل الخادم في بيئة الإنتاج
npm install -g pm2
pm2 start server.js --name "yemen-nav"
pm2 startup
pm2 save

// Yemen Nav Backend Server - نسخة مصححة V3
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const db = require('./backend/src/database');
const userService = require('./backend/src/users');
const jwt = require('jsonwebtoken');

const app = express();

// إعدادات JWT
const JWT_SECRET = process.env.JWT_SECRET || 'yemen_nav_secret_key';
const JWT_EXPIRES_IN = '24h';

// CORS
app.use(cors());

// JSON parsing
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Set up a logger middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
  
  try {
    const user = userService.verifyToken(token);
    req.user = user;
    next();
  } catch (err) {
    return res.status(403).json({ message: 'رمز غير صالح أو منتهي الصلاحية' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.role !== 1 && req.user.role !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to Yemen Nav API' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// ========================
// مسارات إدارة المستخدمين (للمشرفين)
// ========================

// الحصول على جميع المستخدمين
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
    }
    
    const user = await userService.createUser(username, password, email, fullName, accountType);
    
    // تحديث الدور إذا تم تحديده
    if (roleId) {
      await userService.updateUserByAdmin(user.user_id, { roleId });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
    }
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        accountType: user.account_type,
        roleId: user.role_id
      }
    });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { username, email, fullName, password, roleId, isActive } = req.body;
    
    const userData = { username, email, fullName, password, roleId, isActive };
    const updatedUser = await userService.updateUserByAdmin(userId, userData);
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.user_id,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.full_name,
        accountType: updatedUser.account_type,
        roleId: updatedUser.role_id,
        isActive: updatedUser.is_active
      }
    });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
    }
    
    await userService.deleteUser(userId);
    res.json({ message: 'تم حذف المستخدم بنجاح' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  }
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// إغلاق الاتصال بقاعدة البيانات عند إيقاف الخادم
function gracefulShutdown() {
  console.log('إغلاق الاتصال بقاعدة البيانات...');
  if (db.pool) {
    db.pool.end(() => {
      console.log('تم إغلاق الاتصال بقاعدة البيانات');
      process.exit(0);
    });

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});
  } else {
    process.exit(0);
  }
}

// التقاط إشارات إيقاف التشغيل
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// تشغيل الخادم
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'الرمز غير صالح أو منتهي الصلاحية' });
    }
    
    req.user = user;
    next();
  });
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.roleId !== 1 && req.user.roleId !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'مرحبًا بك في واجهة برمجة تطبيقات Yemen Nav' });
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.authenticateUser(username, password);
    
    if (!user) {
      return res.status(401).json({ message: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }
    
    if (!user.isActive) {
      return res.status(403).json({ message: 'حسابك غير نشط. يرجى الاتصال بالمسؤول' });
    }
    
    // تحديث آخر تسجيل دخول
    await userService.updateLastLogin(user.userId);
    
    // إنشاء رمز JWT
    const token = jwt.sign(
      { 
        userId: user.userId, 
        username: user.username, 
        roleId: user.roleId,
        roleName: user.roleName 
      }, 
      JWT_SECRET, 
      { expiresIn: JWT_EXPIRES_IN }
    );
    
    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        userId: user.userId,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        roleId: user.roleId,
        roleName: user.roleName
      }
    });
  } catch (err) {
    console.error('خطأ في تسجيل الدخول:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء تسجيل الدخول' });
  }
});

// الحصول على بيانات المستخدم الحالي
app.get('/api/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await userService.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    res.json({
      userId: user.userId,
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      roleId: user.roleId,
      roleName: user.roleName,
      profileImage: user.profileImage
    });
  } catch (err) {
    console.error('خطأ في الحصول على بيانات المستخدم:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدم' });
  }
});

// الحصول على جميع المستخدمين (للمشرفين)
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
    const existingUser = await userService.getUserByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }
    
    // إنشاء المستخدم الجديد
    const newUser = await userService.createUser({
      username,
      password,
      email,
      fullName,
      accountType: accountType || 'local',
      roleId: roleId || 2, // افتراضيًا مستخدم عادي
      isActive: true
    });
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: newUser.userId,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        accountType: newUser.accountType,
        roleId: newUser.roleId
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { email, fullName, password, roleId, isActive } = req.body;
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // تحديث المستخدم
    const updatedUser = await userService.updateUserByAdmin(userId, {
      email,
      fullName,
      password,
      roleId,
      isActive
    });
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.userId,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        accountType: updatedUser.accountType,
        roleId: updatedUser.roleId,
        isActive: updatedUser.isActive
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    // التحقق من وجود المستخدم
    const user = await userService.getUserById(userId);
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }
    
    // حذف المستخدم
    await userService.deleteUser(userId);
    
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

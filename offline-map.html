<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خرائط اليمن المستقلة - Yemen GPS Offline</title>
    
    <!-- Leaflet CSS (محلي) -->
    <link rel="stylesheet" href="assets/css/leaflet.css" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .subtitle {
            font-size: 12px;
            opacity: 0.8;
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 10px;
        }

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        #searchInput {
            padding: 10px 15px;
            border: none;
            border-radius: 25px;
            width: 300px;
            font-size: 14px;
            outline: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .search-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }

        .search-btn:hover {
            background: #c0392b;
            transform: scale(1.05);
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .search-result-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }

        .search-result-item:hover {
            background: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .result-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .result-description {
            font-size: 12px;
            color: #7f8c8d;
        }

        .map-container {
            height: calc(100vh - 80px);
            position: relative;
        }

        #map {
            height: 100%;
            width: 100%;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-group {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .control-btn {
            background: white;
            border: none;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 16px;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            background: #3498db;
            color: white;
        }

        .control-btn.active {
            background: #2c3e50;
            color: white;
        }

        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(44, 62, 80, 0.9);
            color: white;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            z-index: 1000;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .offline-indicator {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }

        .popup-content {
            max-width: 300px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .popup-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .popup-description {
            font-size: 13px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .popup-photos {
            display: flex;
            gap: 5px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .popup-photo {
            width: 60px;
            height: 45px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .popup-photo:hover {
            transform: scale(1.1);
        }

        .popup-details {
            font-size: 12px;
            color: #34495e;
        }

        .popup-detail {
            margin: 3px 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .popup-actions {
            margin-top: 10px;
            display: flex;
            gap: 8px;
        }

        .popup-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }

        .directions-btn {
            background: #3498db;
            color: white;
        }

        .share-btn {
            background: #2ecc71;
            color: white;
        }

        .save-btn {
            background: #f39c12;
            color: white;
        }

        .popup-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            flex-direction: column;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .loading-progress {
            width: 300px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            height: 8px;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            color: #7f8c8d;
        }

        .toast {
            position: fixed;
            top: 100px;
            right: 20px;
            background: #2c3e50;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10001;
            animation: slideIn 0.3s ease;
        }

        .toast.success {
            background: #27ae60;
        }

        .toast.error {
            background: #e74c3c;
        }

        .toast.warning {
            background: #f39c12;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Custom marker styles */
        .custom-marker {
            background: #e74c3c;
            width: 30px;
            height: 30px;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            border: 3px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .custom-marker::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
        }

        /* Category colors */
        .marker-tourism { background: #e74c3c; }
        .marker-religious { background: #9b59b6; }
        .marker-restaurant { background: #f39c12; }
        .marker-hotel { background: #3498db; }
        .marker-health { background: #2ecc71; }
        .marker-education { background: #f1c40f; }
        .marker-service { background: #34495e; }
        .marker-shopping { background: #e67e22; }
        .marker-transport { background: #95a5a6; }
        .marker-other { background: #7f8c8d; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🇾🇪 خرائط اليمن المستقلة</h1>
                <span class="subtitle">تعمل بدون إنترنت - مستقلة تمام<|im_start|></span>
            </div>
            
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="البحث في الأماكن..." autocomplete="off">
                <button id="searchBtn" class="search-btn">🔍</button>
                <div id="searchResults" class="search-results"></div>
            </div>
        </div>
    </header>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
        
        <!-- Map Controls -->
        <div class="map-controls">
            <div class="control-group">
                <button id="zoomIn" class="control-btn" title="تكبير">+</button>
                <button id="zoomOut" class="control-btn" title="تصغير">-</button>
            </div>
            
            <div class="control-group">
                <button id="locationBtn" class="control-btn" title="موقعي">📍</button>
                <button id="layersBtn" class="control-btn" title="الطبقات">🗂️</button>
                <button id="infoBtn" class="control-btn" title="معلومات">ℹ️</button>
            </div>
        </div>
    </div>

    <!-- Status Bar -->
    <div class="status-bar">
        <div class="status-item">
            <span class="offline-indicator">مستقل</span>
            <span>لا يحتاج إنترنت</span>
        </div>
        <div class="status-item">
            <span>📍</span>
            <span id="coordinates">15.3547, 44.2066</span>
        </div>
        <div class="status-item">
            <span>🔍</span>
            <span id="zoomLevel">7</span>
        </div>
        <div class="status-item">
            <span>📊</span>
            <span id="placesCount">0 مكان</span>
        </div>
    </div>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="spinner"></div>
        <div class="loading-text">جاري تحميل الخريطة المستقلة...</div>
        <div class="loading-progress">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div class="progress-text" id="progressText">0%</div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/leaflet.js"></script>
    <script src="assets/js/offline-yemen-map.js"></script>
</body>
</html>

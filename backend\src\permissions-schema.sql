-- يمن ناف - مخطط نظام الصلاحيات

-- إنشاء جدول الصلاحيات
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول الربط بين الأدوار والصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

-- إن<PERSON>اء جدول الصلاحيات الخاصة بالمستخدمين
CREATE TABLE IF NOT EXISTS user_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
    is_granted BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, permission_id)
);

-- إنشاء محفزات لتحديث تاريخ التحديث
CREATE TRIGGER update_permissions_updated_at
BEFORE UPDATE ON permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_permissions_updated_at
BEFORE UPDATE ON user_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات أولية للصلاحيات
INSERT INTO permissions (name, code, description) VALUES
    ('إدارة المستخدمين', 'manage_users', 'إنشاء وتعديل وحذف المستخدمين'),
    ('إدارة العملاء', 'manage_clients', 'إنشاء وتعديل وحذف العملاء'),
    ('إدارة المواقع', 'manage_locations', 'إنشاء وتعديل وحذف المواقع'),
    ('إدارة الإعدادات', 'manage_settings', 'تعديل إعدادات النظام'),
    ('عرض لوحة التحكم', 'view_dashboard', 'الوصول إلى لوحة التحكم'),
    ('إدارة النسخ الاحتياطي', 'manage_backups', 'إنشاء واستعادة النسخ الاحتياطي'),
    ('إدارة التصنيفات', 'manage_categories', 'إنشاء وتعديل وحذف التصنيفات'),
    ('إدارة المسارات', 'manage_routes', 'إنشاء وتعديل وحذف المسارات'),
    ('إدارة التقارير', 'manage_reports', 'إنشاء وعرض التقارير'),
    ('إدارة الأدوار', 'manage_roles', 'إنشاء وتعديل وحذف الأدوار والصلاحيات')
ON CONFLICT (code) DO NOTHING;

-- ربط الصلاحيات بالأدوار الافتراضية
-- إعطاء جميع الصلاحيات لدور المدير
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions
ON CONFLICT DO NOTHING;

-- إعطاء صلاحيات محدودة لدور المستخدم العادي
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE code IN ('view_dashboard')
ON CONFLICT DO NOTHING;

-- إعطاء صلاحيات للمطور
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions
ON CONFLICT DO NOTHING;

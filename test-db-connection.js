// يمن ناف - سكريبت اختبار الاتصال بقاعدة البيانات
const { Client } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة بيانات PostgreSQL
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'yemen123',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps',
};

console.log('إعدادات الاتصال بقاعدة البيانات:');
console.log(dbConfig);

async function testConnection() {
    const client = new Client(dbConfig);
    
    try {
        await client.connect();
        console.log('تم الاتصال بقاعدة بيانات PostgreSQL بنجاح');
        
        // اختبار استعلام بسيط
        console.log('اختبار استعلام بسيط...');
        const result = await client.query('SELECT version()');
        console.log('إصدار PostgreSQL:', result.rows[0].version);
        
        // التحقق من وجود جدول المستخدمين
        console.log('التحقق من وجود جدول المستخدمين...');
        const tablesResult = await client.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);
        
        const tables = tablesResult.rows.map(row => row.table_name);
        console.log('الجداول الموجودة:', tables.join(', '));
        
        if (tables.includes('users')) {
            console.log('جدول المستخدمين موجود، جاري استعلام البيانات...');
            
            // استعلام بيانات المستخدمين
            const usersResult = await client.query(`
                SELECT
                    u.id as user_id,
                    u.username,
                    u.email,
                    u.full_name,
                    u.phone,
                    u.profile_image,
                    u.account_type,
                    r.name as role_name,
                    r.id as role_id,
                    u.created_at as registration_date,
                    u.last_login,
                    u.is_active,
                    u.is_verified
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                ORDER BY u.id
            `);
            
            console.log(`تم العثور على ${usersResult.rows.length} مستخدم:`);
            console.log(JSON.stringify(usersResult.rows, null, 2));
        } else {
            console.log('جدول المستخدمين غير موجود!');
        }
        
        // إغلاق الاتصال
        await client.end();
        console.log('تم إغلاق الاتصال بقاعدة بيانات PostgreSQL');
        
    } catch (err) {
        console.error('خطأ في اختبار الاتصال بقاعدة البيانات:', err);
    }
}

// تنفيذ الدالة الرئيسية
testConnection().catch(err => {
    console.error('خطأ في تنفيذ السكريبت:', err);
});

// سكريبت بسيط لإعادة تعيين كلمة مرور المستخدم المسؤول
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  host: 'localhost',
  port: 5433,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function resetAdminPassword() {
  try {
    console.log('بدء عملية إعادة تعيين كلمة مرور المستخدم المسؤول...');
    
    // كلمة المرور الجديدة: admin (بدون تشفير)
    // نستخدم كلمة مرور بسيطة غير مشفرة للتأكد من أنها تعمل
    const newPassword = 'admin';
    
    // تحديث كلمة المرور في قاعدة البيانات
    // نستخدم أسلوب التحديث المباشر لتجنب Triggers
    await pool.query(`
      UPDATE users 
      SET password = $1 
      WHERE username = 'admin'
    `, [newPassword]);
    
    console.log('تم إعادة تعيين كلمة مرور المستخدم المسؤول بنجاح!');
    console.log('\nيمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: admin');
    console.log('- كلمة المرور: admin');
    
  } catch (error) {
    console.error('حدث خطأ أثناء إعادة تعيين كلمة المرور:', error);
  } finally {
    await pool.end();
  }
}

resetAdminPassword();

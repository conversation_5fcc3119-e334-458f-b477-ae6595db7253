// قاعدة بيانات الأماكن اليمنية
const YEMEN_PLACES_DATA = {
    // صنعاء
    sanaa: {
        hotels: [
            {
                id: 'movenpick_sanaa',
                name: 'فندق موفنبيك صنعاء',
                nameEn: 'Movenpick Hotel Sanaa',
                category: 'hotel',
                rating: 4.2,
                reviewsCount: 156,
                priceRange: '$$$$',
                coordinates: [15.3547, 44.2066],
                address: 'شارع الزبيري، صنعاء، اليمن',
                phone: '+967-1-546000',
                website: 'https://movenpick.com',
                images: [
                    'assets/images/places/movenpick_sanaa_1.jpg',
                    'assets/images/places/movenpick_sanaa_2.jpg',
                    'assets/images/places/movenpick_sanaa_3.jpg'
                ],
                amenities: ['واي فاي مجاني', 'مطعم', 'مسبح', 'صالة رياضية', 'موقف سيارات'],
                description: 'فندق فاخر في قلب صنعاء يوفر إقامة مريحة مع خدمات عالية الجودة',
                openingHours: {
                    monday: '24 ساعة',
                    tuesday: '24 ساعة',
                    wednesday: '24 ساعة',
                    thursday: '24 ساعة',
                    friday: '24 ساعة',
                    saturday: '24 ساعة',
                    sunday: '24 ساعة'
                },
                reviews: [
                    {
                        author: 'أحمد محمد',
                        rating: 5,
                        date: '2024-01-15',
                        text: 'فندق ممتاز وخدمة راقية، أنصح به بشدة'
                    },
                    {
                        author: 'فاطمة علي',
                        rating: 4,
                        date: '2024-01-10',
                        text: 'موقع جيد وغرف نظيفة، لكن الأسعار مرتفعة قليلاً'
                    }
                ]
            },
            {
                id: 'sheba_hotel',
                name: 'فندق سبأ',
                nameEn: 'Sheba Hotel',
                category: 'hotel',
                rating: 3.8,
                reviewsCount: 89,
                priceRange: '$$$',
                coordinates: [15.3521, 44.2134],
                address: 'شارع الستين، صنعاء، اليمن',
                phone: '+967-1-274000',
                images: [
                    'assets/images/places/sheba_hotel_1.jpg',
                    'assets/images/places/sheba_hotel_2.jpg'
                ],
                amenities: ['واي فاي مجاني', 'مطعم', 'موقف سيارات'],
                description: 'فندق متوسط المستوى في موقع مناسب بصنعاء',
                openingHours: {
                    monday: '24 ساعة',
                    tuesday: '24 ساعة',
                    wednesday: '24 ساعة',
                    thursday: '24 ساعة',
                    friday: '24 ساعة',
                    saturday: '24 ساعة',
                    sunday: '24 ساعة'
                }
            }
        ],
        restaurants: [
            {
                id: 'hadramout_restaurant',
                name: 'مطعم حضرموت',
                nameEn: 'Hadramout Restaurant',
                category: 'restaurant',
                rating: 4.5,
                reviewsCount: 234,
                priceRange: '$$',
                coordinates: [15.3498, 44.2087],
                address: 'شارع الحدة، صنعاء، اليمن',
                phone: '+967-1-123456',
                images: [
                    'assets/images/places/hadramout_restaurant_1.jpg',
                    'assets/images/places/hadramout_restaurant_2.jpg'
                ],
                cuisine: 'يمني تقليدي',
                amenities: ['تكييف', 'واي فاي', 'توصيل منزلي'],
                description: 'مطعم يمني أصيل يقدم أشهى الأطباق التقليدية',
                openingHours: {
                    monday: '12:00 - 23:00',
                    tuesday: '12:00 - 23:00',
                    wednesday: '12:00 - 23:00',
                    thursday: '12:00 - 23:00',
                    friday: '14:00 - 23:00',
                    saturday: '12:00 - 23:00',
                    sunday: '12:00 - 23:00'
                },
                menu: [
                    { name: 'مندي لحم', price: '3000 ريال' },
                    { name: 'كبسة دجاج', price: '2500 ريال' },
                    { name: 'فحسة', price: '2000 ريال' }
                ]
            }
        ],
        attractions: [
            {
                id: 'old_city_sanaa',
                name: 'المدينة القديمة صنعاء',
                nameEn: 'Old City of Sanaa',
                category: 'attraction',
                rating: 4.8,
                reviewsCount: 567,
                coordinates: [15.3547, 44.2066],
                address: 'المدينة القديمة، صنعاء، اليمن',
                images: [
                    'assets/images/places/old_sanaa_1.jpg',
                    'assets/images/places/old_sanaa_2.jpg',
                    'assets/images/places/old_sanaa_3.jpg'
                ],
                description: 'موقع تراث عالمي يضم أقدم المباني في العالم',
                openingHours: {
                    monday: '08:00 - 18:00',
                    tuesday: '08:00 - 18:00',
                    wednesday: '08:00 - 18:00',
                    thursday: '08:00 - 18:00',
                    friday: '14:00 - 18:00',
                    saturday: '08:00 - 18:00',
                    sunday: '08:00 - 18:00'
                },
                entryFee: 'مجاني',
                highlights: ['العمارة اليمنية التقليدية', 'الأسواق التراثية', 'المساجد التاريخية']
            }
        ]
    },
    
    // عدن
    aden: {
        hotels: [
            {
                id: 'gold_mohur_aden',
                name: 'فندق جولد موهور',
                nameEn: 'Gold Mohur Hotel',
                category: 'hotel',
                rating: 4.0,
                reviewsCount: 123,
                priceRange: '$$$',
                coordinates: [12.7797, 45.0365],
                address: 'كريتر، عدن، اليمن',
                phone: '+967-2-254000',
                images: [
                    'assets/images/places/gold_mohur_1.jpg',
                    'assets/images/places/gold_mohur_2.jpg'
                ],
                amenities: ['واي فاي مجاني', 'مطعم', 'إطلالة على البحر'],
                description: 'فندق مطل على البحر في منطقة كريتر التاريخية'
            }
        ],
        restaurants: [
            {
                id: 'fish_market_aden',
                name: 'سوق السمك',
                nameEn: 'Fish Market Restaurant',
                category: 'restaurant',
                rating: 4.3,
                reviewsCount: 189,
                priceRange: '$$',
                coordinates: [12.7856, 45.0293],
                address: 'الميناء، عدن، اليمن',
                phone: '+967-2-123789',
                images: [
                    'assets/images/places/fish_market_1.jpg'
                ],
                cuisine: 'مأكولات بحرية',
                description: 'مطعم متخصص في المأكولات البحرية الطازجة'
            }
        ]
    },
    
    // تعز
    taiz: {
        attractions: [
            {
                id: 'cairo_castle',
                name: 'قلعة القاهرة',
                nameEn: 'Cairo Castle',
                category: 'attraction',
                rating: 4.6,
                reviewsCount: 234,
                coordinates: [13.5795, 44.0205],
                address: 'جبل صبر، تعز، اليمن',
                images: [
                    'assets/images/places/cairo_castle_1.jpg',
                    'assets/images/places/cairo_castle_2.jpg'
                ],
                description: 'قلعة تاريخية تطل على مدينة تعز',
                entryFee: '500 ريال',
                highlights: ['إطلالة بانورامية', 'العمارة الإسلامية', 'المتحف التاريخي']
            }
        ]
    }
};

// دالة البحث في قاعدة البيانات
function searchYemenPlaces(query, category = null, city = null) {
    const results = [];
    
    Object.keys(YEMEN_PLACES_DATA).forEach(cityKey => {
        if (city && cityKey !== city) return;
        
        const cityData = YEMEN_PLACES_DATA[cityKey];
        Object.keys(cityData).forEach(categoryKey => {
            if (category && categoryKey !== category) return;
            
            cityData[categoryKey].forEach(place => {
                const searchText = `${place.name} ${place.nameEn} ${place.description}`.toLowerCase();
                if (searchText.includes(query.toLowerCase())) {
                    results.push({
                        ...place,
                        city: cityKey,
                        categoryType: categoryKey
                    });
                }
            });
        });
    });
    
    return results;
}

// دالة الحصول على مكان بالمعرف
function getPlaceById(id) {
    for (const cityKey of Object.keys(YEMEN_PLACES_DATA)) {
        const cityData = YEMEN_PLACES_DATA[cityKey];
        for (const categoryKey of Object.keys(cityData)) {
            const place = cityData[categoryKey].find(p => p.id === id);
            if (place) {
                return {
                    ...place,
                    city: cityKey,
                    categoryType: categoryKey
                };
            }
        }
    }
    return null;
}

// دالة الحصول على الأماكن القريبة
function getNearbyPlaces(lat, lng, radius = 5000) {
    const results = [];
    
    Object.keys(YEMEN_PLACES_DATA).forEach(cityKey => {
        const cityData = YEMEN_PLACES_DATA[cityKey];
        Object.keys(cityData).forEach(categoryKey => {
            cityData[categoryKey].forEach(place => {
                const distance = calculateDistance(lat, lng, place.coordinates[0], place.coordinates[1]);
                if (distance <= radius) {
                    results.push({
                        ...place,
                        city: cityKey,
                        categoryType: categoryKey,
                        distance: distance
                    });
                }
            });
        });
    });
    
    return results.sort((a, b) => a.distance - b.distance);
}

// دالة حساب المسافة
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371000; // نصف قطر الأرض بالمتر
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

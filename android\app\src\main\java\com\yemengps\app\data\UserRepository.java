package com.yemengps.app.data;

import androidx.lifecycle.LiveData;

import com.yemengps.app.data.dao.UserDao;
import com.yemengps.app.model.User;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Repository for accessing user data from the database and network
 */
public class UserRepository {

    private final UserDao userDao;
    private final Executor executor;

    /**
     * Constructor
     * @param database The database to use
     */
    public UserRepository(AppDatabase database) {
        this.userDao = database.userDao();
        this.executor = Executors.newSingleThreadExecutor();
    }

    /**
     * Insert a new user
     * @param user The user to insert
     */
    public void insert(User user) {
        executor.execute(() -> userDao.insert(user));
    }

    /**
     * Update a user
     * @param user The user to update
     */
    public void update(User user) {
        executor.execute(() -> userDao.update(user));
    }

    /**
     * Delete a user
     * @param user The user to delete
     */
    public void delete(User user) {
        executor.execute(() -> userDao.delete(user));
    }

    /**
     * Get a user by ID
     * @param id The ID of the user
     * @return The user
     */
    public LiveData<User> getUserById(String id) {
        return userDao.getUserById(id);
    }

    /**
     * Get a user by email
     * @param email The email of the user
     * @return The user
     */
    public LiveData<User> getUserByEmail(String email) {
        return userDao.getUserByEmail(email);
    }

    /**
     * Get a user by device ID
     * @param deviceId The device ID of the user
     * @return The user
     */
    public LiveData<User> getUserByDeviceId(String deviceId) {
        return userDao.getUserByDeviceId(deviceId);
    }

    /**
     * Get all users
     * @return All users
     */
    public LiveData<List<User>> getAllUsers() {
        return userDao.getAllUsers();
    }

    /**
     * Get all admin users
     * @return All admin users
     */
    public LiveData<List<User>> getAllAdmins() {
        return userDao.getAllAdmins();
    }

    /**
     * Check if a user exists with the given email
     * @param email The email to check
     * @return True if a user exists with the given email, false otherwise
     */
    public boolean userExistsByEmail(String email) {
        // This needs to be run on a background thread
        // For simplicity, we're using a blocking call here
        // In a real app, you'd want to use a callback or RxJava
        return userDao.userExistsByEmail(email);
    }

    /**
     * Check if a user exists with the given device ID
     * @param deviceId The device ID to check
     * @return True if a user exists with the given device ID, false otherwise
     */
    public boolean userExistsByDeviceId(String deviceId) {
        // This needs to be run on a background thread
        // For simplicity, we're using a blocking call here
        // In a real app, you'd want to use a callback or RxJava
        return userDao.userExistsByDeviceId(deviceId);
    }

    /**
     * Update a user's authentication token
     * @param id The ID of the user
     * @param authToken The new authentication token
     */
    public void updateAuthToken(String id, String authToken) {
        executor.execute(() -> userDao.updateAuthToken(id, authToken));
    }

    /**
     * Update a user's last login date
     * @param id The ID of the user
     */
    public void updateLastLoginDate(String id) {
        executor.execute(() -> userDao.updateLastLoginDate(id));
    }

    /**
     * Deactivate a user
     * @param id The ID of the user to deactivate
     */
    public void deactivateUser(String id) {
        executor.execute(() -> userDao.deactivateUser(id));
    }

    /**
     * Activate a user
     * @param id The ID of the user to activate
     */
    public void activateUser(String id) {
        executor.execute(() -> userDao.activateUser(id));
    }

    /**
     * Make a user an administrator
     * @param id The ID of the user to make an administrator
     */
    public void makeAdmin(String id) {
        executor.execute(() -> userDao.makeAdmin(id));
    }

    /**
     * Remove administrator privileges from a user
     * @param id The ID of the user to remove administrator privileges from
     */
    public void removeAdmin(String id) {
        executor.execute(() -> userDao.removeAdmin(id));
    }

    /**
     * Register a new user
     * @param name The user's name
     * @param email The user's email
     * @param phone The user's phone number
     * @param deviceId The user's device ID
     * @return The new user
     */
    public User registerUser(String name, String email, String phone, String deviceId) {
        User user = new User(name, email, phone, deviceId);
        executor.execute(() -> userDao.insert(user));
        return user;
    }

    /**
     * Login a user
     * @param email The user's email
     * @param deviceId The user's device ID
     * @return True if login was successful, false otherwise
     */
    public boolean loginUser(String email, String deviceId) {
        // This would typically involve authentication with a server
        // For now, we'll just check if the user exists
        boolean userExists = userExistsByEmail(email) && userExistsByDeviceId(deviceId);
        
        if (userExists) {
            // Update the user's last login date
            // This is a simplification; in a real app, you'd get the user ID first
            // For now, we'll just assume the login was successful
            return true;
        }
        
        return false;
    }

    /**
     * Logout a user
     * @param id The ID of the user to logout
     */
    public void logoutUser(String id) {
        // This would typically involve invalidating the user's authentication token
        // For now, we'll just update the auth token to null
        executor.execute(() -> userDao.updateAuthToken(id, null));
    }
}

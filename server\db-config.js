/**
 * ملف تكوين قاعدة البيانات
 */

const { Pool } = require('pg');

// تكوين قاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'yemen123',
  port: 5432,
  max: 20, // الحد الأقصى لعدد الاتصالات في المجمع
  idleTimeoutMillis: 30000, // الوقت الذي يتم فيه إغلاق الاتصال غير المستخدم
  connectionTimeoutMillis: 10000, // وقت انتهاء مهلة محاولة الاتصال
});

// اختبار الاتصال بقاعدة البيانات
pool.on('connect', () => {
  console.log('تم الاتصال بقاعدة البيانات بنجاح');
});

pool.on('error', (err) => {
  console.error('خطأ في الاتصال بقاعدة البيانات:', err);
});

module.exports = { pool };

# خرائط اليمن - Yemen GPS Web

تطبيق خرائط تفاعلي مستوحى من Google Maps مصمم خصيص<|im_end|> لليمن مع دعم كامل للغة العربية والعمل المحلي.

## الميزات

### 🗺️ ميزات الخريطة
- خرائط تفاعلية مع دعم التكبير والتصغير
- عرض متعدد الطبقات (شوارع، أقمار صناعية، تضاريس)
- تحديد النقاط والمواقع
- عرض الإحداثيات الدقيقة

### 🔍 البحث والملاحة
- بحث متقدم عن المواقع والعناوين
- اقتراحات البحث التلقائية
- تحديد الموقع الحالي باستخدام GPS
- الاتجاهات والملاحة

### 💾 إدارة البيانات
- حفظ الأماكن المفضلة محلي<|im_end|>
- مشاركة المواقع عبر الروابط
- تخزين البيانات في المتصفح (LocalStorage)
- عمل بدون إنترنت للخرائط المحملة مسبق<|im_end|>

### 🎨 واجهة المستخدم
- تصميم عصري مشابه لـ Google Maps
- دعم كامل للغة العربية (RTL)
- واجهة متجاوبة تعمل على جميع الأجهزة
- أيقونات وعناصر تحكم بديهية

## التثبيت والإعداد

### 1. تحميل المكتبات المطلوبة

يجب تحميل الملفات التالية ووضعها في المجلدات المناسبة:

#### Leaflet (مكتبة الخرائط)
```bash
# تحميل Leaflet CSS
curl -o assets/css/leaflet.css https://unpkg.com/leaflet@1.9.4/dist/leaflet.css

# تحميل Leaflet JS
curl -o assets/js/leaflet.js https://unpkg.com/leaflet@1.9.4/dist/leaflet.js
```

#### Font Awesome (الأيقونات)
```bash
# تحميل Font Awesome CSS
curl -o assets/css/fontawesome.min.css https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css

# تحميل ملفات الخطوط
mkdir -p assets/webfonts
curl -o assets/webfonts/fa-solid-900.woff2 https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2
curl -o assets/webfonts/fa-regular-400.woff2 https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-regular-400.woff2
curl -o assets/webfonts/fa-brands-400.woff2 https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.woff2
```

### 2. إنشاء صورة الشعار
```bash
# إنشاء مجلد الصور
mkdir -p assets/images

# يمكنك وضع شعار التطبيق في:
# assets/images/logo.png
```

### 3. تشغيل التطبيق

بعد تحميل جميع الملفات، يمكنك فتح `index.html` في المتصفح مباشرة أو تشغيل خادم محلي:

```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# ثم افتح http://localhost:8000 في المتصفح
```

## بنية المشروع

```
yemen-gps/
├── index.html              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   ├── style.css       # أنماط التطبيق
│   │   ├── leaflet.css     # أنماط مكتبة الخرائط
│   │   └── fontawesome.min.css # أنماط الأيقونات
│   ├── js/
│   │   ├── app.js          # منطق التطبيق الرئيسي
│   │   └── leaflet.js      # مكتبة الخرائط
│   ├── images/
│   │   └── logo.png        # شعار التطبيق
│   └── webfonts/           # ملفات خطوط الأيقونات
└── WEB_README.md           # هذا الملف
```

## الاستخدام

### البحث عن المواقع
1. اكتب اسم المكان في مربع البحث
2. اختر من النتائج المقترحة
3. سيتم عرض الموقع على الخريطة

### حفظ الأماكن
1. انقر على أي موقع في الخريطة
2. ستظهر نافذة معلومات الموقع
3. انقر على "حفظ الموقع" وأدخل اسم<|im_end|>
4. سيتم حفظ الموقع في قائمة الأماكن المحفوظة

### مشاركة المواقع
1. حدد موقع<|im_end|> على الخريطة
2. انقر على زر "مشاركة" في نافذة المعلومات
3. سيتم نسخ رابط الموقع أو مشاركته مباشرة

### تغيير طبقات الخريطة
1. انقر على زر "طبقات الخريطة"
2. اختر نوع العرض المطلوب (شوارع، أقمار صناعية، تضاريس)
3. يمكنك أيض<|im_end|> تفعيل طبقات إضافية مثل حركة المرور

## المتطلبات التقنية

- متصفح حديث يدعم HTML5 و CSS3 و JavaScript ES6
- اتصال بالإنترنت للبحث والخرائط الجديدة (اختياري للخرائط المحملة مسبق<|im_end|>)
- دعم Geolocation API لتحديد الموقع الحالي

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## المساهمة

نرحب بالمساهمات لتحسين التطبيق:
1. Fork المشروع
2. أنشئ فرع<|im_end|> جديد للميزة
3. اعمل التغييرات المطلوبة
4. أرسل Pull Request

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.

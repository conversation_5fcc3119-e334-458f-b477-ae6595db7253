// يمن ناف - ملف إدارة الإشعارات العامة

// دالة عرض إشعار للمستخدم
function showGlobalNotification(message, type = 'success') {
    // تجنب تكرار الإشعارات
    const existingNotifications = document.querySelectorAll('.notification');
    for (const existing of existingNotifications) {
        if (existing.textContent === message) {
            return; // تجاهل الإشعار المكرر
        }
    }
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // إضافة زر إغلاق
    const closeBtn = document.createElement('span');
    closeBtn.className = 'notification-close';
    closeBtn.innerHTML = '&times;';
    closeBtn.onclick = function() {
        notification.classList.add('hide');
        setTimeout(() => {
            notification.remove();
        }, 300);
    };
    
    notification.appendChild(closeBtn);
    document.body.appendChild(notification);

    // إخفاء الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.add('hide');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 3000);
}

// تصدير دالة showGlobalNotification كدالة عامة
window.showGlobalNotification = showGlobalNotification;

// إضافة CSS للإشعارات
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        }
        
        .notification-close {
            position: absolute;
            top: 5px;
            right: 10px;
            cursor: pointer;
            font-size: 18px;
            opacity: 0.7;
        }
        
        .notification-close:hover {
            opacity: 1;
        }
        
        .notification.success {
            background-color: #4CAF50;
        }
        
        .notification.error {
            background-color: #F44336;
        }
        
        .notification.warning {
            background-color: #FF9800;
        }
        
        .notification.info {
            background-color: #2196F3;
        }
        
        .notification.hide {
            animation: slideOut 0.3s ease-in forwards;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});

// سكريبت للتحقق من وجود المستخدم admin في قاعدة البيانات
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('معلومات الاتصال بقاعدة البيانات:', dbConfig);

// إنشاء مجمع اتصال بقاعدة البيانات
const pool = new Pool(dbConfig);

// اختبار الاتصال والتحقق من المستخدم
async function checkAdminUser() {
    let client;
    try {
        console.log('محاولة الاتصال بقاعدة البيانات...');
        client = await pool.connect();
        console.log('تم الاتصال بقاعدة البيانات بنجاح');

        // التحقق من وجود جدول المستخدمين
        const tableResult = await client.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            );
        `);
        
        const tableExists = tableResult.rows[0].exists;
        console.log('هل يوجد جدول users؟', tableExists);
        
        if (!tableExists) {
            console.log('جدول المستخدمين غير موجود!');
            return;
        }
        
        // التحقق من هيكل جدول المستخدمين
        console.log('هيكل جدول المستخدمين:');
        const structureResult = await client.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'users';
        `);
        console.log(structureResult.rows);
        
        // البحث عن المستخدم admin
        console.log('البحث عن المستخدم admin...');
        const userResult = await client.query(`
            SELECT * FROM users WHERE username = 'admin';
        `);
        
        if (userResult.rows.length === 0) {
            console.log('المستخدم admin غير موجود!');
        } else {
            console.log('تم العثور على المستخدم admin:');
            // إخفاء كلمة المرور في الطباعة
            const user = userResult.rows[0];
            console.log('معرف المستخدم:', user.user_id);
            console.log('اسم المستخدم:', user.username);
            console.log('البريد الإلكتروني:', user.email);
            console.log('كلمة المرور (مشفرة):', user.password);
            console.log('معرف الدور:', user.role_id);
            console.log('حالة النشاط:', user.is_active);
        }
        
        return true;
    } catch (error) {
        console.error('خطأ في الاتصال بقاعدة البيانات أو الاستعلام:', error.message);
        return false;
    } finally {
        if (client) {
            client.release();
        }
        await pool.end();
    }
}

// تنفيذ الاختبار
checkAdminUser().then(success => {
    console.log('اكتمل الاختبار، النجاح:', success);
    process.exit(0);
}).catch(err => {
    console.error('خطأ غير متوقع:', err);
    process.exit(1);
});

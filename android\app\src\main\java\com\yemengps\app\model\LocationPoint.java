package com.yemengps.app.model;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.yemengps.app.data.converters.DateConverter;
import com.yemengps.app.data.converters.LocationTypeConverter;

import java.util.Date;

/**
 * Entity representing a location point on the map
 */
@Entity(tableName = "locations")
@TypeConverters({DateConverter.class, LocationTypeConverter.class})
public class LocationPoint {

    @PrimaryKey
    @NonNull
    private String id;
    
    private String name;
    private double latitude;
    private double longitude;
    private LocationType type;
    private String description;
    private String userId;
    private String userName;
    private Date createdAt;
    private Date updatedAt;
    private boolean approved;
    private boolean active;

    /**
     * Default constructor
     */
    public LocationPoint() {
        this.id = java.util.UUID.randomUUID().toString();
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.approved = false;
        this.active = true;
    }

    /**
     * Constructor with parameters
     * 
     * @param name The name of the location
     * @param latitude The latitude coordinate
     * @param longitude The longitude coordinate
     * @param type The type of location
     * @param description A description of the location
     * @param userId The ID of the user who created the location
     * @param userName The name of the user who created the location
     */
    public LocationPoint(String name, double latitude, double longitude, LocationType type,
                        String description, String userId, String userName) {
        this();
        this.name = name;
        this.latitude = latitude;
        this.longitude = longitude;
        this.type = type;
        this.description = description;
        this.userId = userId;
        this.userName = userName;
    }

    /**
     * Get the ID of the location
     * @return The ID
     */
    @NonNull
    public String getId() {
        return id;
    }

    /**
     * Set the ID of the location
     * @param id The ID
     */
    public void setId(@NonNull String id) {
        this.id = id;
    }

    /**
     * Get the name of the location
     * @return The name
     */
    public String getName() {
        return name;
    }

    /**
     * Set the name of the location
     * @param name The name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Get the latitude coordinate
     * @return The latitude
     */
    public double getLatitude() {
        return latitude;
    }

    /**
     * Set the latitude coordinate
     * @param latitude The latitude
     */
    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    /**
     * Get the longitude coordinate
     * @return The longitude
     */
    public double getLongitude() {
        return longitude;
    }

    /**
     * Set the longitude coordinate
     * @param longitude The longitude
     */
    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    /**
     * Get the type of location
     * @return The location type
     */
    public LocationType getType() {
        return type;
    }

    /**
     * Set the type of location
     * @param type The location type
     */
    public void setType(LocationType type) {
        this.type = type;
    }

    /**
     * Get the description of the location
     * @return The description
     */
    public String getDescription() {
        return description;
    }

    /**
     * Set the description of the location
     * @param description The description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * Get the ID of the user who created the location
     * @return The user ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Set the ID of the user who created the location
     * @param userId The user ID
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * Get the name of the user who created the location
     * @return The user name
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Set the name of the user who created the location
     * @param userName The user name
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * Get the creation date of the location
     * @return The creation date
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * Set the creation date of the location
     * @param createdAt The creation date
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * Get the last update date of the location
     * @return The update date
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * Set the last update date of the location
     * @param updatedAt The update date
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Check if the location is approved
     * @return True if approved, false otherwise
     */
    public boolean isApproved() {
        return approved;
    }

    /**
     * Set whether the location is approved
     * @param approved True if approved, false otherwise
     */
    public void setApproved(boolean approved) {
        this.approved = approved;
    }

    /**
     * Check if the location is active
     * @return True if active, false otherwise
     */
    public boolean isActive() {
        return active;
    }

    /**
     * Set whether the location is active
     * @param active True if active, false otherwise
     */
    public void setActive(boolean active) {
        this.active = active;
    }

    /**
     * Get the alert message for this location type
     * @return The alert message
     */
    public String getAlertMessage() {
        switch (type) {
            case TRAFFIC:
                return "Attention ahead: traffic congestion";
            case SPEEDBUMP:
                return "Attention ahead: speed bump";
            case POTHOLE:
                return "Attention ahead: pothole";
            case DIRT_ROAD:
                return "Attention ahead: dirt road";
            case CHECKPOINT:
                return "Attention ahead: military checkpoint";
            default:
                return "Attention ahead";
        }
    }
}

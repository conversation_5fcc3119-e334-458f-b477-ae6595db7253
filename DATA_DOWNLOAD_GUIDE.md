# 📥 دليل تحميل بيانات الأماكن اليمنية

## 🎯 نظرة عامة

تم إنشاء عدة سكريبتات لتحميل بيانات الأماكن اليمنية من مصادر مختلفة. يمكنك اختيار السكريبت المناسب حسب احتياجاتك وإمكانياتك.

## 📁 السكريبتات المتوفرة

### 1. 🚀 السكريبت الشامل (Google Places API)
**الملف:** `scripts/download-places-data.js`

**المميزات:**
- ✅ بيانات عالية الجودة من Google Places
- ✅ تحميل الصور تلقائياً
- ✅ معلومات مفصلة (تقييمات، مراجعات، ساعات العمل)
- ✅ تغطية شاملة لجميع المحافظات اليمنية
- ✅ أنواع متعددة من الأماكن

**المتطلبات:**
- مفتاح Google Places API (مدفوع)
- حساب Google Cloud Platform
- حد أدنى 1000$ رصيد API

**الاستخدام:**
```bash
# 1. احصل على مفتاح API من Google Cloud Console
# 2. عدّل المفتاح في بداية الملف
# 3. شغّل السكريبت
node scripts/download-places-data.js
```

### 2. ⚡ السكريبت السريع (Google Places API)
**الملف:** `scripts/quick-download.js`

**المميزات:**
- ✅ تحميل سريع للمدن الرئيسية فقط
- ✅ صورة واحدة لكل مكان
- ✅ أنواع مهمة من الأماكن فقط
- ✅ وقت تحميل أقل

**المتطلبات:**
- مفتاح Google Places API
- رصيد API أقل (حوالي 100-200$)

**الاستخدام:**
```bash
# عدّل GOOGLE_API_KEY في بداية الملف
node scripts/quick-download.js
```

### 3. 🆓 السكريبت المجاني (OpenStreetMap)
**الملف:** `scripts/free-data-download.js`

**المميزات:**
- ✅ مجاني 100%
- ✅ لا يحتاج مفاتيح API
- ✅ بيانات من OpenStreetMap
- ✅ يعمل فوراً بدون إعداد

**العيوب:**
- ❌ بيانات أقل تفصيلاً
- ❌ لا يحمل صور
- ❌ تغطية محدودة

**الاستخدام:**
```bash
# يعمل مباشرة بدون إعداد
node scripts/free-data-download.js
```

## 🔧 الإعداد والتحضير

### 1. تثبيت المتطلبات
```bash
# تثبيت المكتبات المطلوبة
npm install axios pg
```

### 2. إعداد قاعدة البيانات
تأكد من أن قاعدة البيانات تعمل وتحتوي على الجداول المطلوبة:
```sql
-- إضافة أعمدة جديدة إذا لم تكن موجودة
ALTER TABLE places ADD COLUMN IF NOT EXISTS google_place_id VARCHAR(255) UNIQUE;
ALTER TABLE places ADD COLUMN IF NOT EXISTS osm_id BIGINT;
ALTER TABLE places ADD COLUMN IF NOT EXISTS osm_type VARCHAR(20);
ALTER TABLE places ADD COLUMN IF NOT EXISTS photos JSONB;
ALTER TABLE places ADD COLUMN IF NOT EXISTS opening_hours JSONB;
ALTER TABLE places ADD COLUMN IF NOT EXISTS price_level INTEGER;
ALTER TABLE places ADD COLUMN IF NOT EXISTS place_types JSONB;
```

### 3. إعداد مجلدات الصور
```bash
# إنشاء مجلد الصور
mkdir -p public/images/places
```

## 🔑 الحصول على مفتاح Google Places API

### الخطوات:
1. **إنشاء مشروع Google Cloud:**
   - اذهب إلى https://console.cloud.google.com/
   - أنشئ مشروع جديد

2. **تفعيل Places API:**
   - في لوحة التحكم، اذهب إلى "APIs & Services"
   - ابحث عن "Places API" وفعّله

3. **إنشاء مفتاح API:**
   - اذهب إلى "Credentials"
   - أنشئ "API Key"
   - قيّد المفتاح لـ Places API فقط

4. **إعداد الفوترة:**
   - أضف بطاقة ائتمان
   - احصل على رصيد مجاني 300$

### التكلفة المتوقعة:
- **البحث الأساسي:** 0.017$ لكل طلب
- **تفاصيل المكان:** 0.017$ لكل طلب  
- **تحميل الصور:** 0.007$ لكل صورة
- **المجموع المتوقع:** 500-1000$ للتغطية الكاملة

## 🚀 تشغيل السكريبتات

### السكريبت الشامل:
```bash
# تحرير المفتاح
nano scripts/download-places-data.js
# البحث عن: const GOOGLE_API_KEY = 'YOUR_GOOGLE_PLACES_API_KEY';
# استبدال YOUR_GOOGLE_PLACES_API_KEY بمفتاحك

# تشغيل السكريبت
node scripts/download-places-data.js
```

### السكريبت السريع:
```bash
# تحرير المفتاح
nano scripts/quick-download.js
# البحث عن: const GOOGLE_API_KEY = 'YOUR_API_KEY_HERE';

# تشغيل السكريبت
node scripts/quick-download.js
```

### السكريبت المجاني:
```bash
# يعمل مباشرة
node scripts/free-data-download.js
```

## 📊 مراقبة التقدم

### أثناء التشغيل ستشاهد:
```
🚀 بدء تحميل بيانات الأماكن اليمنية...

🏛️ معالجة محافظة: صنعاء
🔍 البحث عن tourist_attraction في صنعاء...
   ✅ تم العثور على 15 مكان
📍 معالجة: المتحف الوطني
   📷 تحميل 3 صور...
   📷 تم تحميل الصورة: place123_0.jpg
   💾 تم حفظ المكان: المتحف الوطني
✅ 1: المتحف الوطني
```

### في نهاية التشغيل:
```
📊 ملخص عملية التحميل
==================================================
⏱️  الوقت المستغرق: 3600 ثانية
📍 إجمالي الأماكن المكتشفة: 2500
✅ تم تحميلها بنجاح: 2350
❌ فشل في التحميل: 150
📷 مجلد الصور: /path/to/public/images/places
💾 قاعدة البيانات: yemen_gps
==================================================
```

## ⚠️ نصائح مهمة

### للسكريبتات المدفوعة:
1. **راقب التكلفة:** تحقق من استهلاك API بانتظام
2. **ضع حدود:** استخدم quotas في Google Cloud
3. **اختبر أولاً:** جرب على مدينة واحدة قبل التشغيل الكامل
4. **احفظ التقدم:** السكريبت يتجنب التكرار تلقائياً

### للسكريبت المجاني:
1. **كن صبوراً:** البيانات المجانية تحتاج وقت أطول
2. **تحقق من الجودة:** راجع البيانات المحملة
3. **أضف يدوياً:** قد تحتاج لإضافة بيانات مهمة يدوياً

## 🔄 إعادة التشغيل

جميع السكريبتات تدعم إعادة التشغيل الآمن:
- ✅ تتجنب تحميل البيانات المكررة
- ✅ تحدث البيانات الموجودة
- ✅ تكمل من حيث توقفت

## 📈 بعد التحميل

### تحقق من النتائج:
```sql
-- عدد الأماكن المحملة
SELECT COUNT(*) FROM places;

-- الأماكن مع الصور
SELECT COUNT(*) FROM places WHERE photos IS NOT NULL AND photos != '[]';

-- توزيع الأماكن حسب المحافظة
SELECT g.name_ar, COUNT(p.id) 
FROM places p 
JOIN governorates g ON p.governorate_id = g.id 
GROUP BY g.name_ar;
```

### تحسين الأداء:
```sql
-- إنشاء فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_places_location ON places(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_places_governorate ON places(governorate_id);
CREATE INDEX IF NOT EXISTS idx_places_category ON places(category_id);
```

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### خطأ في الاتصال بقاعدة البيانات:
```bash
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**الحل:** تأكد من تشغيل PostgreSQL

#### خطأ في مفتاح API:
```bash
Error: REQUEST_DENIED
```
**الحل:** تحقق من صحة مفتاح API وتفعيل Places API

#### نفاد الرصيد:
```bash
Error: OVER_QUERY_LIMIT
```
**الحل:** أضف رصيد أو انتظر إعادة تعيين الحد اليومي

#### مساحة القرص ممتلئة:
```bash
Error: ENOSPC: no space left on device
```
**الحل:** احذف ملفات غير ضرورية أو استخدم قرص أكبر

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من ملف السجل (console output)
2. راجع إعدادات قاعدة البيانات
3. تأكد من صحة مفاتيح API
4. جرب السكريبت المجاني أولاً

---

**ملاحظة:** يُنصح بالبدء بالسكريبت المجاني لاختبار النظام، ثم الانتقال للسكريبتات المدفوعة للحصول على بيانات أكثر تفصيلاً.

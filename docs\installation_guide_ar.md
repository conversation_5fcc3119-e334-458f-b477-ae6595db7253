# دليل تثبيت المتطلبات الأساسية لنظام "يمن ناف"

## مقدمة

هذا الدليل يشرح خطوات تثبيت المتطلبات الأساسية اللازمة لتشغيل نظام "يمن ناف" على نظام ويندوز. سنقوم بتثبيت البرامج التالية:

1. Node.js
2. PostgreSQL وPostGIS
3. Git
4. خدمة معلومات الإنترنت (IIS)

## 1. تثبيت Node.js

Node.js هو بيئة تشغيل JavaScript تستخدم لتشغيل الواجهة الخلفية (Backend) لنظام "يمن ناف".

### خطوات التثبيت:

1. قم بزيارة الموقع الرسمي لـ Node.js: [https://nodejs.org/](https://nodejs.org/)

2. انقر على زر التنزيل للإصدار LTS (الدعم طويل الأمد) - الإصدار 16.x أو أحدث
   ![تنزيل Node.js](https://nodejs.org/static/images/logo.svg)

3. بعد اكتمال التنزيل، قم بتشغيل ملف التثبيت (مثال: `node-v16.15.1-x64.msi`)

4. اتبع خطوات معالج التثبيت:
   - انقر على "Next" في شاشة الترحيب
   - اقبل اتفاقية الترخيص وانقر على "Next"
   - اختر مجلد التثبيت (يُفضل ترك المجلد الافتراضي) وانقر على "Next"
   - في شاشة "Custom Setup"، تأكد من تحديد جميع المكونات وانقر على "Next"
   - في شاشة "Tools for Native Modules"، تأكد من تحديد خيار "Automatically install the necessary tools..." وانقر على "Next"
   - انقر على "Install" لبدء التثبيت

5. بعد اكتمال التثبيت، انقر على "Finish"

6. للتحقق من التثبيت، افتح موجه الأوامر (Command Prompt) وأدخل الأوامر التالية:
   ```
   node --version
   npm --version
   ```
   يجب أن ترى إصدار Node.js وNPM المثبتين على جهازك.

## 2. تثبيت PostgreSQL وPostGIS

PostgreSQL هو نظام إدارة قواعد البيانات، وPostGIS هو امتداد يضيف دعمًا للبيانات الجغرافية.

### خطوات التثبيت:

1. قم بزيارة الموقع الرسمي لـ PostgreSQL: [https://www.postgresql.org/download/windows/](https://www.postgresql.org/download/windows/)

2. انقر على زر "Download the installer" للإصدار 13 أو أحدث

3. بعد اكتمال التنزيل، قم بتشغيل ملف التثبيت

4. اتبع خطوات معالج التثبيت:
   - انقر على "Next" في شاشة الترحيب
   - اختر مجلد التثبيت وانقر على "Next"
   - اختر المكونات التالية:
     - PostgreSQL Server
     - pgAdmin 4
     - Stack Builder
     - Command Line Tools
   - انقر على "Next"
   - اختر مجلد البيانات وانقر على "Next"
   - أدخل كلمة مرور لمستخدم postgres (تذكر هذه الكلمة لأنك ستحتاجها لاحقًا)
   - اترك منفذ الاتصال الافتراضي (5432) وانقر على "Next"
   - اختر الإعدادات الإقليمية الافتراضية وانقر على "Next"
   - انقر على "Next" في شاشة الملخص
   - انقر على "Next" في شاشة "Ready to Install"
   - انتظر حتى يكتمل التثبيت وانقر على "Finish"

5. بعد اكتمال تثبيت PostgreSQL، سيتم تشغيل Stack Builder تلقائيًا
   - اختر الخادم المثبت من القائمة المنسدلة وانقر على "Next"
   - في قسم "Spatial Extensions"، حدد "PostGIS Bundle" وانقر على "Next"
   - انقر على "Next" في شاشة التنزيل
   - اقبل اتفاقية الترخيص وانقر على "Next"
   - اختر المكونات الافتراضية وانقر على "Next"
   - انقر على "Next" لبدء التثبيت
   - انتظر حتى يكتمل التثبيت وانقر على "Next"
   - انقر على "Finish" لإنهاء التثبيت

6. للتحقق من التثبيت، افتح pgAdmin 4 من قائمة ابدأ:
   - أدخل كلمة المرور التي حددتها أثناء التثبيت
   - انقر بزر الماوس الأيمن على "Servers" > "PostgreSQL" وأدخل كلمة المرور
   - يجب أن ترى الخادم متصلاً وجاهزًا للاستخدام

## 3. إنشاء قاعدة البيانات

بعد تثبيت PostgreSQL وPostGIS، نحتاج إلى إنشاء قاعدة بيانات لنظام "يمن ناف".

### خطوات إنشاء قاعدة البيانات:

1. افتح pgAdmin 4 من قائمة ابدأ

2. قم بتوصيل الخادم المحلي (أدخل كلمة المرور التي حددتها أثناء التثبيت)

3. انقر بزر الماوس الأيمن على "Databases" واختر "Create" > "Database"

4. أدخل "yemen_nav" كاسم لقاعدة البيانات وانقر على "Save"

5. انقر بزر الماوس الأيمن على قاعدة البيانات الجديدة واختر "Query Tool"

6. أدخل الأوامر التالية وانقر على "Execute":
   ```sql
   CREATE EXTENSION postgis;
   CREATE EXTENSION postgis_topology;
   
   CREATE USER yemen_nav_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE yemen_nav TO yemen_nav_user;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yemen_nav_user;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yemen_nav_user;
   ```

## 4. تثبيت Git

Git هو نظام التحكم في الإصدارات الذي نستخدمه لإدارة الكود المصدري.

### خطوات التثبيت:

1. قم بزيارة الموقع الرسمي لـ Git: [https://git-scm.com/download/win](https://git-scm.com/download/win)

2. سيبدأ التنزيل تلقائيًا. إذا لم يبدأ، انقر على الرابط "click here to download manually"

3. بعد اكتمال التنزيل، قم بتشغيل ملف التثبيت

4. اتبع خطوات معالج التثبيت:
   - انقر على "Next" في شاشة الترحيب
   - اقبل اتفاقية الترخيص وانقر على "Next"
   - اختر مجلد التثبيت وانقر على "Next"
   - اختر المكونات التي تريد تثبيتها (يُفضل ترك الإعدادات الافتراضية) وانقر على "Next"
   - اختر اسم قائمة ابدأ وانقر على "Next"
   - في شاشة "Adjusting your PATH environment"، اختر "Git from the command line and also from 3rd-party software" وانقر على "Next"
   - اختر "Use the OpenSSL library" وانقر على "Next"
   - اختر "Checkout Windows-style, commit Unix-style line endings" وانقر على "Next"
   - اختر "Use MinTTY" وانقر على "Next"
   - اترك الخيارات الافتراضية في الشاشات المتبقية وانقر على "Next"
   - انقر على "Install" لبدء التثبيت
   - بعد اكتمال التثبيت، انقر على "Finish"

5. للتحقق من التثبيت، افتح موجه الأوامر وأدخل الأمر التالي:
   ```
   git --version
   ```
   يجب أن ترى إصدار Git المثبت على جهازك.

## 5. تثبيت وإعداد خدمة معلومات الإنترنت (IIS)

IIS هو خادم الويب المدمج في نظام ويندوز، وسنستخدمه لاستضافة واجهة الويب لنظام "يمن ناف".

### خطوات التثبيت:

1. افتح "Control Panel" > "Programs" > "Programs and Features" > "Turn Windows features on or off"

2. حدد "Internet Information Services" وتأكد من تحديد الميزات التالية:
   - Web Management Tools
   - World Wide Web Services
     - Application Development Features
       - .NET Extensibility
       - ASP.NET
       - CGI
       - ISAPI Extensions
       - ISAPI Filters
     - Common HTTP Features (all)
     - Health and Diagnostics (all)
     - Performance Features (all)
     - Security (all)

3. انقر على "OK" وانتظر حتى يتم تثبيت الميزات

4. للتحقق من التثبيت، افتح متصفح الويب وانتقل إلى `http://localhost/`
   يجب أن ترى صفحة ترحيب IIS الافتراضية.

## 6. تثبيت URL Rewrite Module

هذه الوحدة ضرورية لتوجيه الطلبات في IIS.

### خطوات التثبيت:

1. قم بزيارة الموقع الرسمي: [https://www.iis.net/downloads/microsoft/url-rewrite](https://www.iis.net/downloads/microsoft/url-rewrite)

2. انقر على "Download URL Rewrite Module 2.1"

3. بعد اكتمال التنزيل، قم بتشغيل ملف التثبيت

4. اتبع خطوات معالج التثبيت:
   - اقبل اتفاقية الترخيص وانقر على "Install"
   - انتظر حتى يكتمل التثبيت وانقر على "Finish"

## 7. تثبيت PM2 لإدارة العمليات

PM2 هو مدير عمليات لتطبيقات Node.js، وسنستخدمه لإدارة الواجهة الخلفية لنظام "يمن ناف".

### خطوات التثبيت:

1. بعد تثبيت Node.js، افتح موجه الأوامر بصلاحيات المسؤول

2. أدخل الأمر التالي لتثبيت PM2 عالميًا:
   ```
   npm install -g pm2
   ```

3. للتحقق من التثبيت، أدخل الأمر التالي:
   ```
   pm2 --version
   ```
   يجب أن ترى إصدار PM2 المثبت على جهازك.

4. قم بتثبيت PM2-Windows-Startup لتشغيل PM2 تلقائيًا عند بدء تشغيل Windows:
   ```
   npm install -g pm2-windows-startup
   pm2-startup install
   ```

## الخطوات التالية

بعد تثبيت جميع المتطلبات الأساسية، يمكنك الآن متابعة إعداد وتشغيل نظام "يمن ناف" باتباع الخطوات الموضحة في ملف `docs/windows_server_setup_ar.md`.

للمساعدة أو الاستفسارات، يرجى الاتصال بفريق الدعم الفني.

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتجاهات المحدثة - يمن GPS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.success {
            background-color: #28a745;
        }
        .place-info {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .coordinates {
            font-family: monospace;
            color: #666;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار الاتجاهات المحدثة</h1>
        
        <div class="status success">
            <strong>✅ تم الإصلاح!</strong> الآن عند الضغط على "الاتجاهات" ستنتقل إلى الخريطة المحلية وليس Google Maps.
        </div>
        
        <h2>📋 التحديثات المطبقة:</h2>
        <ul>
            <li>✅ إضافة معالجة معاملات URL في الخريطة الرئيسية</li>
            <li>✅ تحديث دالة <code>handleDirectionsParams()</code></li>
            <li>✅ ربط الدالة بتهيئة التطبيق</li>
            <li>✅ تحديث ملف <code>assets/js/app.js</code></li>
            <li>✅ نسخ التحديثات إلى <code>public/assets/js/app.js</code></li>
        </ul>
        
        <h2>🔗 اختبار الاتجاهات:</h2>
        
        <div class="place-info">
            <h3>اختبار مباشر للاتجاهات</h3>
            <p>هذه الروابط تحاكي ما يحدث عند الضغط على "الاتجاهات" في صفحة الأماكن:</p>
            
            <a href="http://localhost:8000/?lat=15.3547&lng=44.2066&place=فندق موفنبيك صنعاء&directions=true" 
               class="test-button success" target="_blank">
                🧭 اختبار: فندق موفنبيك صنعاء
            </a>
            
            <a href="http://localhost:8000/?lat=12.7797&lng=45.0365&place=فندق جولد موهور عدن&directions=true" 
               class="test-button success" target="_blank">
                🧭 اختبار: فندق جولد موهور عدن
            </a>
            
            <a href="http://localhost:8000/?lat=15.30021430&lng=44.19296050&place=Albustan Luxurious Suites&directions=true" 
               class="test-button success" target="_blank">
                🧭 اختبار: Albustan Luxurious Suites
            </a>
        </div>
        
        <h2>📱 اختبار من صفحة الأماكن:</h2>
        
        <div class="place-info">
            <h3>الخطوات:</h3>
            <ol>
                <li>اذهب إلى <a href="http://localhost:8000/places" target="_blank" class="test-button">📍 صفحة الأماكن</a></li>
                <li>اختر أي مكان من القائمة</li>
                <li>اضغط على زر "الاتجاهات" (في البطاقة أو في النافذة المنبثقة)</li>
                <li><strong>النتيجة المتوقعة:</strong> ستنتقل إلى الخريطة المحلية مع عرض الاتجاهات</li>
            </ol>
        </div>
        
        <h2>🎯 ما يجب أن يحدث الآن:</h2>
        
        <div class="status success">
            <h3>✅ السلوك الصحيح:</h3>
            <ul>
                <li>الضغط على "الاتجاهات" ينتقل إلى <code>http://localhost:8000/</code></li>
                <li>تظهر الخريطة مع علامة حمراء على الوجهة</li>
                <li>يتغير عنوان الصفحة إلى "الاتجاهات إلى [اسم المكان]"</li>
                <li>يتحدث مربع البحث ليعرض "الاتجاهات إلى [اسم المكان]"</li>
                <li>تفتح نافذة الاتجاهات تلقائياً</li>
                <li>يمكن حساب المسار من الموقع الحالي إلى الوجهة</li>
            </ul>
        </div>
        
        <div class="status warning">
            <h3>❌ السلوك القديم (تم إصلاحه):</h3>
            <ul>
                <li><del>الانتقال إلى Google Maps خارجياً</del></li>
                <li><del>فتح نافذة جديدة في المتصفح</del></li>
                <li><del>عدم استخدام الخريطة المحلية</del></li>
            </ul>
        </div>
        
        <h2>🔧 التفاصيل التقنية:</h2>
        
        <div class="place-info">
            <h3>معاملات URL المستخدمة:</h3>
            <ul>
                <li><code>lat</code> - خط العرض للوجهة</li>
                <li><code>lng</code> - خط الطول للوجهة</li>
                <li><code>place</code> - اسم المكان (مُرمز)</li>
                <li><code>directions=true</code> - تفعيل وضع الاتجاهات</li>
            </ul>
            
            <h3>مثال على URL:</h3>
            <code class="coordinates">
                http://localhost:8000/?lat=15.3547&lng=44.2066&place=فندق%20موفنبيك%20صنعاء&directions=true
            </code>
        </div>
        
        <h2>📊 حالة النظام:</h2>
        
        <div class="status success">
            <strong>🟢 الخادم:</strong> يعمل على المنفذ 8000<br>
            <strong>🟢 الخريطة الرئيسية:</strong> محدثة مع معالجة الاتجاهات<br>
            <strong>🟢 صفحة الأماكن:</strong> تنتقل للخريطة المحلية<br>
            <strong>🟢 ملفات JavaScript:</strong> محدثة ومنسوخة
        </div>
        
        <h2>🚀 جاهز للاختبار!</h2>
        
        <p>الآن يمكنك اختبار الوظيفة والتأكد من أن الاتجاهات تعمل داخل التطبيق المحلي بدلاً من الانتقال إلى Google Maps.</p>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:8000/places" class="test-button success" style="font-size: 18px; padding: 15px 30px;">
                🧪 ابدأ الاختبار الآن
            </a>
        </div>
    </div>
    
    <script>
        // إضافة معلومات إضافية للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            // عرض معلومات المتصفح
            const info = document.createElement('div');
            info.style.cssText = 'margin-top: 20px; padding: 10px; background: #e9ecef; border-radius: 5px; font-size: 12px;';
            info.innerHTML = `
                <strong>معلومات الاختبار:</strong><br>
                الوقت: ${new Date().toLocaleString('ar')}<br>
                المتصفح: ${navigator.userAgent.split(' ')[0]}<br>
                دعم الموقع الجغرافي: ${navigator.geolocation ? 'متوفر ✅' : 'غير متوفر ❌'}
            `;
            document.querySelector('.test-container').appendChild(info);
        });
    </script>
</body>
</html>

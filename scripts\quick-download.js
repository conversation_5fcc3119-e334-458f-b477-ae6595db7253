// سكريبت التحميل السريع للأماكن اليمنية
const { Pool } = require('pg');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// إعدادات سريعة
const GOOGLE_API_KEY = 'AIzaSyD8S6CJpsAHMR5dG-J_ecX2By_87y-sAv0'; // مفتاح Google Places API
const MAX_PLACES_PER_CITY = 20; // حد أقصى للأماكن لكل مدينة
const DOWNLOAD_PHOTOS = true; // تحميل الصور أم لا

// قاعدة البيانات
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

// المدن الرئيسية في اليمن
const MAIN_CITIES = [
    { name: 'صنعاء', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', lat: 14.7978, lng: 42.9545 },
    { name: 'إب', lat: 13.9667, lng: 44.1833 }
];

// أنواع الأماكن المهمة
const IMPORTANT_TYPES = [
    'tourist_attraction',
    'restaurant',
    'lodging',
    'hospital',
    'mosque',
    'museum'
];

class QuickDownloader {
    constructor() {
        this.count = 0;
        this.errors = 0;

        // إنشاء مجلد الصور
        this.imagesDir = path.join(__dirname, '..', 'public', 'images', 'places');
        if (!fs.existsSync(this.imagesDir)) {
            fs.mkdirSync(this.imagesDir, { recursive: true });
        }
    }

    async searchPlaces(city, type) {
        try {
            const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    location: `${city.lat},${city.lng}`,
                    radius: 25000,
                    type: type,
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                return response.data.results.slice(0, MAX_PLACES_PER_CITY);
            }
            return [];
        } catch (error) {
            console.error(`خطأ في البحث: ${error.message}`);
            return [];
        }
    }

    async getPlaceDetails(placeId) {
        try {
            const url = 'https://maps.googleapis.com/maps/api/place/details/json';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    place_id: placeId,
                    fields: 'name,formatted_address,geometry,photos,rating,formatted_phone_number,website',
                    language: 'ar'
                }
            });

            if (response.data.status === 'OK') {
                return response.data.result;
            }
            return null;
        } catch (error) {
            console.error(`خطأ في التفاصيل: ${error.message}`);
            return null;
        }
    }

    async downloadPhoto(photoRef, placeId) {
        if (!DOWNLOAD_PHOTOS) return null;

        try {
            const url = 'https://maps.googleapis.com/maps/api/place/photo';
            const response = await axios.get(url, {
                params: {
                    key: GOOGLE_API_KEY,
                    photoreference: photoRef,
                    maxwidth: 400
                },
                responseType: 'stream'
            });

            const fileName = `${placeId}_0.jpg`;
            const filePath = path.join(this.imagesDir, fileName);

            const writer = fs.createWriteStream(filePath);
            response.data.pipe(writer);

            return new Promise((resolve) => {
                writer.on('finish', () => resolve(`/images/places/${fileName}`));
                writer.on('error', () => resolve(null));
            });
        } catch (error) {
            return null;
        }
    }

    async savePlace(place, cityName) {
        try {
            // تحميل صورة واحدة فقط
            let photoPath = null;
            if (place.photos && place.photos.length > 0) {
                photoPath = await this.downloadPhoto(place.photos[0].photo_reference, place.place_id);
            }

            const query = `
                INSERT INTO places (
                    name_ar, name_en, description_ar,
                    latitude, longitude, governorate_id,
                    phone, website, rating, google_place_id,
                    photos, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
                ON CONFLICT (google_place_id) DO NOTHING
            `;

            const photos = photoPath ? [photoPath] : [];

            await pool.query(query, [
                place.name,
                place.name,
                place.formatted_address || '',
                place.geometry.location.lat,
                place.geometry.location.lng,
                1, // صنعاء افتراضي
                place.formatted_phone_number || null,
                place.website || null,
                place.rating || null,
                place.place_id,
                JSON.stringify(photos)
            ]);

            this.count++;
            console.log(`✅ ${this.count}: ${place.name}`);

        } catch (error) {
            this.errors++;
            console.error(`❌ خطأ في الحفظ: ${error.message}`);
        }
    }

    async run() {
        console.log('🚀 بدء التحميل السريع...\n');

        if (GOOGLE_API_KEY === 'YOUR_API_KEY_HERE') {
            console.error('❌ يرجى إضافة مفتاح Google API في بداية الملف');
            return;
        }

        try {
            for (const city of MAIN_CITIES) {
                console.log(`\n🏙️ ${city.name}:`);

                for (const type of IMPORTANT_TYPES) {
                    console.log(`  🔍 البحث عن ${type}...`);

                    const places = await this.searchPlaces(city, type);

                    for (const place of places) {
                        const details = await this.getPlaceDetails(place.place_id);
                        if (details) {
                            await this.savePlace(details, city.name);
                        }

                        // تأخير قصير
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }
            }

            console.log(`\n🎉 انتهى التحميل!`);
            console.log(`✅ تم تحميل: ${this.count} مكان`);
            console.log(`❌ أخطاء: ${this.errors}`);

        } catch (error) {
            console.error('❌ خطأ عام:', error);
        } finally {
            await pool.end();
        }
    }
}

// تشغيل السكريبت
if (require.main === module) {
    const downloader = new QuickDownloader();
    downloader.run();
}

module.exports = QuickDownloader;

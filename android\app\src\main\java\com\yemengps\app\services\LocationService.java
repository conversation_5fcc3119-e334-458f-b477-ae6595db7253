package com.yemengps.app.services;

import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.location.Location;
import android.media.AudioAttributes;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.IBinder;
import android.os.Looper;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.speech.tts.TextToSpeech;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.Priority;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.data.LocationRepository;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.ui.MainActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Service for tracking the user's location and providing alerts
 */
public class LocationService extends Service implements TextToSpeech.OnInitListener {

    private static final String TAG = "LocationService";
    private static final int NOTIFICATION_ID = 1001;
    private static final int ALERT_NOTIFICATION_ID = 1002;
    private static final long UPDATE_INTERVAL = 10000; // 10 seconds
    private static final long FASTEST_INTERVAL = 5000; // 5 seconds
    private static final float MIN_DISTANCE = 10; // 10 meters

    private FusedLocationProviderClient fusedLocationClient;
    private LocationCallback locationCallback;
    private LocationRepository locationRepository;
    private PreferenceManager preferenceManager;
    private TextToSpeech textToSpeech;
    private Vibrator vibrator;
    private MediaPlayer mediaPlayer;
    private boolean ttsInitialized = false;
    
    private Location currentLocation;
    private List<LocationPoint> nearbyLocations = new ArrayList<>();
    private Map<String, Boolean> alertedLocations = new HashMap<>();

    @Override
    public void onCreate() {
        super.onCreate();
        
        // Initialize components
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        locationRepository = new LocationRepository(YemenGpsApplication.getInstance().getDatabase());
        preferenceManager = YemenGpsApplication.getInstance().getPreferenceManager();
        textToSpeech = new TextToSpeech(this, this);
        vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        
        // Create location callback
        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(LocationResult locationResult) {
                if (locationResult == null) return;
                
                for (Location location : locationResult.getLocations()) {
                    onLocationChanged(location);
                }
            }
        };
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // Start as a foreground service
        startForeground(NOTIFICATION_ID, createNotification());
        
        // Start location updates
        startLocationUpdates();
        
        // Return sticky so the service restarts if killed
        return START_STICKY;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        // Stop location updates
        stopLocationUpdates();
        
        // Shutdown TTS
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
        }
        
        // Release media player
        if (mediaPlayer != null) {
            mediaPlayer.release();
            mediaPlayer = null;
        }
        
        super.onDestroy();
    }

    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            // Set language
            int result = textToSpeech.setLanguage(new Locale(preferenceManager.getLanguage()));
            
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.e(TAG, "Language not supported");
            } else {
                ttsInitialized = true;
            }
        } else {
            Log.e(TAG, "TTS initialization failed");
        }
    }

    /**
     * Start location updates
     */
    private void startLocationUpdates() {
        try {
            LocationRequest locationRequest = new LocationRequest.Builder(Priority.PRIORITY_HIGH_ACCURACY, UPDATE_INTERVAL)
                    .setMinUpdateDistanceMeters(MIN_DISTANCE)
                    .setMinUpdateIntervalMillis(FASTEST_INTERVAL)
                    .build();
            
            fusedLocationClient.requestLocationUpdates(
                    locationRequest,
                    locationCallback,
                    Looper.getMainLooper());
        } catch (SecurityException e) {
            Log.e(TAG, "Error starting location updates", e);
        }
    }

    /**
     * Stop location updates
     */
    private void stopLocationUpdates() {
        fusedLocationClient.removeLocationUpdates(locationCallback);
    }

    /**
     * Handle location changes
     * @param location The new location
     */
    private void onLocationChanged(Location location) {
        currentLocation = location;
        
        // Get nearby locations
        double alertDistanceKm = preferenceManager.getAlertDistance() / 1000.0; // Convert to km
        locationRepository.getLocationsWithinRadius(
                location.getLatitude(),
                location.getLongitude(),
                alertDistanceKm)
                .observeForever(locations -> {
                    if (locations != null) {
                        nearbyLocations = locations;
                        checkForAlerts();
                    }
                });
    }

    /**
     * Check for alerts
     */
    private void checkForAlerts() {
        if (currentLocation == null || nearbyLocations.isEmpty()) return;
        
        for (LocationPoint locationPoint : nearbyLocations) {
            // Skip if already alerted
            if (alertedLocations.containsKey(locationPoint.getId()) && alertedLocations.get(locationPoint.getId())) {
                continue;
            }
            
            // Calculate distance
            float[] results = new float[1];
            Location.distanceBetween(
                    currentLocation.getLatitude(),
                    currentLocation.getLongitude(),
                    locationPoint.getLatitude(),
                    locationPoint.getLongitude(),
                    results);
            float distance = results[0];
            
            // Check if within alert distance
            if (distance <= preferenceManager.getAlertDistance()) {
                // Trigger alert
                triggerAlert(locationPoint);
                
                // Mark as alerted
                alertedLocations.put(locationPoint.getId(), true);
            }
        }
    }

    /**
     * Trigger an alert for a location
     * @param location The location to alert for
     */
    private void triggerAlert(LocationPoint location) {
        // Show notification
        NotificationManager notificationManager = getSystemService(NotificationManager.class);
        notificationManager.notify(ALERT_NOTIFICATION_ID, createAlertNotification(location));
        
        // Speak alert if enabled
        if (preferenceManager.isVoiceAlertsEnabled() && ttsInitialized) {
            textToSpeech.speak(location.getAlertMessage(), TextToSpeech.QUEUE_FLUSH, null, location.getId());
        }
        
        // Vibrate if enabled
        if (preferenceManager.isVibrationAlertsEnabled() && vibrator != null && vibrator.hasVibrator()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(500, VibrationEffect.DEFAULT_AMPLITUDE));
            } else {
                vibrator.vibrate(500);
            }
        }
    }

    /**
     * Create the service notification
     * @return The notification
     */
    private Notification createNotification() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this,
                0,
                notificationIntent,
                PendingIntent.FLAG_IMMUTABLE);
        
        return new NotificationCompat.Builder(this, YemenGpsApplication.LOCATION_CHANNEL_ID)
                .setContentTitle(getString(R.string.app_name))
                .setContentText("Location tracking is active")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
    }

    /**
     * Create an alert notification
     * @param location The location to alert for
     * @return The notification
     */
    private Notification createAlertNotification(LocationPoint location) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this,
                0,
                notificationIntent,
                PendingIntent.FLAG_IMMUTABLE);
        
        return new NotificationCompat.Builder(this, YemenGpsApplication.ALERT_CHANNEL_ID)
                .setContentTitle(location.getName())
                .setContentText(location.getAlertMessage())
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setAutoCancel(true)
                .build();
    }
}

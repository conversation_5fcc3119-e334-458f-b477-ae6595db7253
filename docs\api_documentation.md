# Yemen GPS Navigation System - API Documentation

This document outlines the API endpoints for the Yemen GPS Navigation System backend.

## Base URL

```
https://api.yemengps.com/
```

## Authentication

All API requests (except registration and login) require authentication using a Bearer token.

```
Authorization: Bearer <token>
```

## Error Handling

All endpoints return a standardized error response:

```json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

## Endpoints

### Authentication

#### Register User

```
POST /users/register
```

Request:
```json
{
  "name": "User Name",
  "email": "<EMAIL>",
  "phone": "+967123456789",
  "deviceId": "device-unique-id"
}
```

Response:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "authToken": "jwt-token",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Login User

```
POST /users/login
```

Request:
```json
{
  "email": "<EMAIL>",
  "deviceId": "device-unique-id"
}
```

Response:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "authToken": "jwt-token",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z"
  }
}
```

### User Management

#### Get User Profile

```
GET /users/profile
```

Response:
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z"
  }
}
```

#### Update User Profile

```
PUT /users/profile
```

Request:
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>",
  "phone": "+967987654321"
}
```

Response:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "user-id",
    "name": "Updated Name",
    "email": "<EMAIL>",
    "phone": "+967987654321",
    "deviceId": "device-unique-id",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z"
  }
}
```

### Location Management

#### Get All Locations

```
GET /locations
```

Response:
```json
{
  "success": true,
  "message": "Locations retrieved successfully",
  "data": [
    {
      "id": "location-id-1",
      "name": "Location Name",
      "latitude": 15.3694,
      "longitude": 44.1910,
      "type": "TRAFFIC",
      "description": "Heavy traffic area",
      "userId": "user-id",
      "userName": "User Name",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "approved": true,
      "active": true
    },
    // More locations...
  ]
}
```

#### Get Locations by Type

```
GET /locations/type/{type}
```

Parameters:
- `type`: Location type (TRAFFIC, SPEEDBUMP, POTHOLE, DIRT_ROAD, CHECKPOINT)

Response:
```json
{
  "success": true,
  "message": "Locations retrieved successfully",
  "data": [
    {
      "id": "location-id-1",
      "name": "Location Name",
      "latitude": 15.3694,
      "longitude": 44.1910,
      "type": "TRAFFIC",
      "description": "Heavy traffic area",
      "userId": "user-id",
      "userName": "User Name",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "approved": true,
      "active": true
    },
    // More locations of the same type...
  ]
}
```

#### Get Nearby Locations

```
GET /locations/nearby?lat={latitude}&lng={longitude}&radius={radiusKm}
```

Parameters:
- `latitude`: Latitude coordinate
- `longitude`: Longitude coordinate
- `radiusKm`: Radius in kilometers

Response:
```json
{
  "success": true,
  "message": "Locations retrieved successfully",
  "data": [
    {
      "id": "location-id-1",
      "name": "Location Name",
      "latitude": 15.3694,
      "longitude": 44.1910,
      "type": "TRAFFIC",
      "description": "Heavy traffic area",
      "userId": "user-id",
      "userName": "User Name",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "approved": true,
      "active": true
    },
    // More nearby locations...
  ]
}
```

#### Add Location

```
POST /locations
```

Request:
```json
{
  "name": "New Location",
  "latitude": 15.3694,
  "longitude": 44.1910,
  "type": "POTHOLE",
  "description": "Large pothole on main road"
}
```

Response:
```json
{
  "success": true,
  "message": "Location added successfully",
  "data": {
    "id": "location-id",
    "name": "New Location",
    "latitude": 15.3694,
    "longitude": 44.1910,
    "type": "POTHOLE",
    "description": "Large pothole on main road",
    "userId": "user-id",
    "userName": "User Name",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z",
    "approved": false,
    "active": true
  }
}
```

#### Update Location

```
PUT /locations/{id}
```

Request:
```json
{
  "name": "Updated Location",
  "description": "Updated description"
}
```

Response:
```json
{
  "success": true,
  "message": "Location updated successfully",
  "data": {
    "id": "location-id",
    "name": "Updated Location",
    "latitude": 15.3694,
    "longitude": 44.1910,
    "type": "POTHOLE",
    "description": "Updated description",
    "userId": "user-id",
    "userName": "User Name",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z",
    "approved": false,
    "active": true
  }
}
```

#### Delete Location

```
DELETE /locations/{id}
```

Response:
```json
{
  "success": true,
  "message": "Location deleted successfully"
}
```

### Admin Endpoints

#### Get All Users (Admin Only)

```
GET /admin/users
```

Response:
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": [
    {
      "id": "user-id-1",
      "name": "User Name",
      "email": "<EMAIL>",
      "phone": "+967123456789",
      "deviceId": "device-unique-id",
      "isAdmin": false,
      "createdAt": "2023-01-01T00:00:00Z",
      "lastLoginAt": "2023-01-01T00:00:00Z",
      "active": true
    },
    // More users...
  ]
}
```

#### Make User Admin (Admin Only)

```
PUT /admin/users/{id}/make-admin
```

Response:
```json
{
  "success": true,
  "message": "User is now an admin",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "isAdmin": true,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z",
    "active": true
  }
}
```

#### Remove Admin Privileges (Admin Only)

```
PUT /admin/users/{id}/remove-admin
```

Response:
```json
{
  "success": true,
  "message": "Admin privileges removed",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z",
    "active": true
  }
}
```

#### Deactivate User (Admin Only)

```
PUT /admin/users/{id}/deactivate
```

Response:
```json
{
  "success": true,
  "message": "User deactivated successfully"
}
```

#### Activate User (Admin Only)

```
PUT /admin/users/{id}/activate
```

Response:
```json
{
  "success": true,
  "message": "User activated successfully",
  "data": {
    "id": "user-id",
    "name": "User Name",
    "email": "<EMAIL>",
    "phone": "+967123456789",
    "deviceId": "device-unique-id",
    "isAdmin": false,
    "createdAt": "2023-01-01T00:00:00Z",
    "lastLoginAt": "2023-01-01T00:00:00Z",
    "active": true
  }
}
```

#### Get Pending Locations (Admin Only)

```
GET /admin/locations/pending
```

Response:
```json
{
  "success": true,
  "message": "Pending locations retrieved successfully",
  "data": [
    {
      "id": "location-id-1",
      "name": "Location Name",
      "latitude": 15.3694,
      "longitude": 44.1910,
      "type": "TRAFFIC",
      "description": "Heavy traffic area",
      "userId": "user-id",
      "userName": "User Name",
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z",
      "approved": false,
      "active": true
    },
    // More pending locations...
  ]
}
```

#### Approve Location (Admin Only)

```
PUT /admin/locations/{id}/approve
```

Response:
```json
{
  "success": true,
  "message": "Location approved successfully",
  "data": {
    "id": "location-id",
    "name": "Location Name",
    "latitude": 15.3694,
    "longitude": 44.1910,
    "type": "TRAFFIC",
    "description": "Heavy traffic area",
    "userId": "user-id",
    "userName": "User Name",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z",
    "approved": true,
    "active": true
  }
}
```

#### Reject Location (Admin Only)

```
PUT /admin/locations/{id}/reject
```

Response:
```json
{
  "success": true,
  "message": "Location rejected successfully"
}
```

## Data Models

### User

```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "phone": "string",
  "deviceId": "string",
  "authToken": "string",
  "isAdmin": "boolean",
  "createdAt": "datetime",
  "lastLoginAt": "datetime",
  "active": "boolean"
}
```

### Location

```json
{
  "id": "string",
  "name": "string",
  "latitude": "number",
  "longitude": "number",
  "type": "enum(TRAFFIC, SPEEDBUMP, POTHOLE, DIRT_ROAD, CHECKPOINT)",
  "description": "string",
  "userId": "string",
  "userName": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "approved": "boolean",
  "active": "boolean"
}
```

## Rate Limiting

API requests are limited to 100 requests per minute per user. Exceeding this limit will result in a 429 Too Many Requests response.

## Versioning

The current API version is v1. The version is included in the base URL.

```
https://api.yemengps.com/v1/

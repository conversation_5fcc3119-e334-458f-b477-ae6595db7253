// خادم بسيط لنظام يمن GPS على البورت 5000
const express = require('express');
const path = require('path');

const app = express();

// إعداد الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

// إعداد JSON parsing
app.use(express.json());

// إعداد CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    next();
});

// بيانات تجريبية للأماكن
const samplePlaces = [
    {
        id: 1,
        name_ar: 'مسجد الصالح',
        name_en: 'Al-Saleh Mosque',
        latitude: 15.3547,
        longitude: 44.2066,
        category_id: 2,
        description_ar: 'أكبر مسجد في اليمن ويقع في العاصمة صنعاء',
        rating: 4.8,
        photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Al-Saleh+Mosque'])
    },
    {
        id: 2,
        name_ar: 'سوق الملح',
        name_en: 'Salt Market',
        latitude: 15.3500,
        longitude: 44.2100,
        category_id: 8,
        description_ar: 'سوق تقليدي في صنعاء القديمة',
        rating: 4.5,
        photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Salt+Market'])
    },
    {
        id: 3,
        name_ar: 'قلعة صيرة',
        name_en: 'Sira Fortress',
        latitude: 12.7855,
        longitude: 45.0187,
        category_id: 1,
        description_ar: 'قلعة تاريخية في عدن',
        rating: 4.3,
        photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Sira+Fortress'])
    },
    {
        id: 4,
        name_ar: 'الجامع الكبير',
        name_en: 'Great Mosque of Sanaa',
        latitude: 15.3520,
        longitude: 44.2080,
        category_id: 2,
        description_ar: 'من أقدم المساجد في صنعاء',
        rating: 4.7,
        photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Great+Mosque'])
    },
    {
        id: 5,
        name_ar: 'ميناء عدن',
        name_en: 'Port of Aden',
        latitude: 12.7794,
        longitude: 45.0367,
        category_id: 9,
        description_ar: 'ميناء تجاري مهم في الجنوب',
        rating: 4.0,
        photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Port+of+Aden'])
    }
];

// API الأماكن
app.get('/api/places', (req, res) => {
    console.log('📡 طلب API للأماكن');
    const { limit = 50 } = req.query;
    const limitedPlaces = samplePlaces.slice(0, parseInt(limit));

    res.json({
        success: true,
        data: limitedPlaces,
        count: limitedPlaces.length
    });
});

app.get('/api/places/:id', (req, res) => {
    console.log(`📡 طلب API للمكان ${req.params.id}`);
    const place = samplePlaces.find(p => p.id === parseInt(req.params.id));
    if (place) {
        res.json({ success: true, data: place });
    } else {
        res.status(404).json({ success: false, message: 'المكان غير موجود' });
    }
});

// الصفحات
app.get('/', (req, res) => {
    console.log('🗺️ طلب الصفحة الرئيسية');
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/places', (req, res) => {
    console.log('📍 طلب صفحة الأماكن');
    res.sendFile(path.join(__dirname, 'public', 'places.html'));
});

app.get('/admin', (req, res) => {
    console.log('⚙️ طلب لوحة التحكم');
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// معالجة الأخطاء
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'الصفحة غير موجودة',
        path: req.path
    });
});

// بدء الخادم
const PORT = 5002;
const HOST = '0.0.0.0';

const server = app.listen(PORT, HOST, () => {
    console.log('🚀 ========================================');
    console.log('🗺️  نظام يمن GPS - خادم بسيط');
    console.log('🚀 ========================================');
    console.log(`📡 الخادم يعمل على: ${HOST}:${PORT}`);
    console.log(`🌐 الرابط المحلي: http://localhost:${PORT}/`);
    console.log(`🌐 الرابط الخارجي: http://***********:${PORT}/`);
    console.log('📋 الروابط المتاحة:');
    console.log(`   🗺️  الخريطة: http://***********:${PORT}/`);
    console.log(`   📍 الأماكن: http://***********:${PORT}/places`);
    console.log(`   ⚙️  الإدارة: http://***********:${PORT}/admin`);
    console.log(`   📡 API: http://***********:${PORT}/api/places`);
    console.log('🚀 ========================================');
});

// معالجة إيقاف الخادم
process.on('SIGTERM', () => {
    console.log('🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

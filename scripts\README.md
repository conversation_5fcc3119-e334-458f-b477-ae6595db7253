# 📥 سكريبتات تحميل بيانات الأماكن اليمنية

## 🚀 البدء السريع

### 1. تثبيت المتطلبات
```bash
cd scripts
npm install
```

### 2. تحضير قاعدة البيانات
```bash
npm run prepare
```

### 3. تشغيل التحميل
```bash
# مجاني (بدون مفاتيح API)
npm run download:free

# سريع (يحتاج Google API Key)
npm run download:quick

# شامل (يحتاج Google API Key)
npm run download:full
```

## 📁 الملفات

| الملف | الوصف | المتطلبات |
|-------|--------|-----------|
| `prepare-database.js` | تحضير قاعدة البيانات | - |
| `free-data-download.js` | تحميل مجاني من OSM | - |
| `quick-download.js` | تحميل سريع | Google API |
| `download-places-data.js` | تحميل شامل | Google API |
| `check-coordinates.js` | فحص الإحداثيات | - |
| `fix-missing-coordinates.js` | إصلاح الإحداثيات | - |

## 🔑 إعداد Google API

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد
3. فعّل Places API
4. أنشئ API Key
5. ضع المفتاح في السكريبت

## 📊 النتائج المتوقعة

### التحميل المجاني:
- 📍 500-1000 مكان
- 🆓 مجاني 100%
- ⏱️ 30-60 دقيقة

### التحميل السريع:
- 📍 1000-2000 مكان
- 💰 100-200$ تكلفة
- ⏱️ 2-4 ساعات

### التحميل الشامل:
- 📍 5000-10000 مكان
- 💰 500-1000$ تكلفة
- ⏱️ 8-12 ساعة

## 🛠️ استكشاف الأخطاء

### خطأ في قاعدة البيانات:
```bash
Error: connect ECONNREFUSED
```
**الحل:** تأكد من تشغيل PostgreSQL

### خطأ في API:
```bash
Error: REQUEST_DENIED
```
**الحل:** تحقق من مفتاح Google API

### نفاد المساحة:
```bash
Error: ENOSPC
```
**الحل:** احذف ملفات غير ضرورية

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل، راجع `DATA_DOWNLOAD_GUIDE.md`

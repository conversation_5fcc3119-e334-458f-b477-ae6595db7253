@echo off
echo ===== استعادة قاعدة بيانات Yemen GPS على جهاز جديد =====
echo.

REM طلب مسار ملف النسخة الاحتياطية
set /p BACKUP_PATH="أدخل المسار الكامل لملف النسخة الاحتياطية: "

REM التحقق من وجود الملف
if not exist "%BACKUP_PATH%" (
    echo خطأ: الملف غير موجود في المسار المحدد
    echo مثال على المسار الصحيح: C:\temp\yemen_gps_backup.sql
    pause
    exit /b 1
)

echo.
echo جاري استعادة قاعدة البيانات...
echo الملف: %BACKUP_PATH%
echo.

REM تحديد متغيرات قاعدة البيانات
set PGUSER=yemen
set PGPASSWORD=admin
set PGHOST=localhost
set PGPORT=5432

REM التحقق من وجود PostgreSQL
where psql >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: PostgreSQL غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PostgreSQL أولاً من: https://www.postgresql.org/download/
    pause
    exit /b 1
)

echo التحقق من الاتصال بخادم PostgreSQL...
psql -h %PGHOST% -p %PGPORT% -U postgres -d postgres -c "SELECT version();" >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: لا يمكن الاتصال بخادم PostgreSQL
    echo تأكد من:
    echo 1. تشغيل خدمة PostgreSQL
    echo 2. صحة كلمة مرور المستخدم postgres
    pause
    exit /b 1
)

echo التحقق من وجود المستخدم yemen...
psql -h %PGHOST% -p %PGPORT% -U postgres -d postgres -c "SELECT 1 FROM pg_roles WHERE rolname='yemen';" | find "1" >nul
if %ERRORLEVEL% NEQ 0 (
    echo إنشاء المستخدم yemen...
    psql -h %PGHOST% -p %PGPORT% -U postgres -d postgres -c "CREATE USER yemen WITH PASSWORD 'admin';"
    psql -h %PGHOST% -p %PGPORT% -U postgres -d postgres -c "ALTER USER yemen CREATEDB;"
    psql -h %PGHOST% -p %PGPORT% -U postgres -d postgres -c "ALTER USER yemen WITH SUPERUSER;"
    echo تم إنشاء المستخدم yemen بنجاح
)

echo.
echo بدء عملية الاستعادة...
echo.

REM تنفيذ أمر الاستعادة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -f "%BACKUP_PATH%" --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===== تم استعادة قاعدة البيانات بنجاح =====
    echo.
    
    echo التحقق من الجداول المستعادة...
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d yemen_gps -c "\dt"
    
    echo.
    echo التحقق من البيانات...
    echo عدد المستخدمين:
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d yemen_gps -c "SELECT COUNT(*) FROM users;"
    
    echo عدد المواقع:
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d yemen_gps -c "SELECT COUNT(*) FROM locations;"
    
    echo عدد التصنيفات:
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d yemen_gps -c "SELECT COUNT(*) FROM categories;"
    
    echo.
    echo ===== تمت الاستعادة بنجاح =====
    echo يمكنك الآن استخدام قاعدة البيانات yemen_gps
    
) else (
    echo.
    echo ===== فشل في استعادة قاعدة البيانات =====
    echo.
    echo الأسباب المحتملة:
    echo 1. ملف النسخة الاحتياطية تالف
    echo 2. مشكلة في صلاحيات المستخدم
    echo 3. إصدار PostgreSQL غير متوافق
    echo 4. مساحة القرص الصلب غير كافية
    echo.
    echo للمساعدة، راجع ملف TRANSFER-BACKUP-GUIDE.md
)

echo.
pause

{"name": "yemen-gps-data-downloader", "version": "1.0.0", "description": "سكريبتات تحميل بيانات الأماكن اليمنية من مصادر مختلفة", "main": "download-places-data.js", "scripts": {"prepare": "node prepare-database.js", "download:free": "node free-data-download.js", "download:quick": "node quick-download.js", "download:full": "node download-places-data.js", "check:coords": "node check-coordinates.js", "fix:coords": "node fix-missing-coordinates.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["yemen", "gps", "places", "google-places", "openstreetmap", "data-download"], "author": "Yemen GPS Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "pg": "^8.11.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/yemen-gps/data-downloader.git"}, "bugs": {"url": "https://github.com/yemen-gps/data-downloader/issues"}, "homepage": "https://github.com/yemen-gps/data-downloader#readme"}
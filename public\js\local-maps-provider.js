/**
 * مزود خرائط محلي - Yemen Nav
 * يقوم هذا الملف بتوفير وظائف لاستخدام بلاطات الخرائط المحلية
 * ويعمل بطريقة مشابهة لـ Google Maps مع خرائط Leaflet
 */

// تكوين مسارات الخرائط المحلية
const localMapsConfig = {
    // مسارات الخرائط المحلية
    mapTiles: {
        streets: '/map-tiles/streets/{z}/{x}/{y}.png',
        satellite: '/map-tiles/satellite/{z}/{x}/{y}.png',
        terrain: '/map-tiles/terrain/{z}/{x}/{y}.png'
    },
    
    // النطاق المتاح للخرائط المحلية (تزويم ومنطقة)
    availability: {
        minZoom: 5,
        maxZoom: 15,
        bounds: L.latLngBounds(
            <PERSON><PERSON>lat<PERSON>ng(12.1, 41.6),  // الزاوية الجنوبية الغربية لليمن
            L.latLng(19.0, 54.5)   // الزاوية الشمالية الشرقية لليمن
        )
    },
    
    // إعدادات الخريطة الافتراضية
    fallback: {
        streets: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        terrain: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}',
        transport: 'https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey=6170aad10dfd42a38d4d8c709a536f38'
    }
};

/**
 * فحص إذا كانت البلاطة متاحة محليًا
 * @param {string} type - نوع الخريطة (streets, satellite, terrain)
 * @param {number} z - مستوى التكبير
 * @param {number} x - إحداثي x
 * @param {number} y - إحداثي y
 * @returns {boolean} - إذا كانت البلاطة متاحة محليًا
 */
function isTileAvailableLocally(type, z, x, y) {
    // التحقق من أن المستوى ضمن النطاق المدعوم
    if (z < localMapsConfig.availability.minZoom || z > localMapsConfig.availability.maxZoom) {
        return false;
    }
    
    // يمكن هنا إضافة منطق إضافي للتحقق من وجود البلاطة المحددة
    // على سبيل المثال، التحقق من وجود ملف معين أو قائمة مسبقة
    
    // في الإصدار الحالي، سنفترض أن البلاطات المحلية متاحة فقط للمستويات المدعومة
    return true;
}

/**
 * تكوين طبقة خريطة هجينة تستخدم البلاطات المحلية عندما تكون متاحة
 * وتعود إلى المصادر عبر الإنترنت عندما تكون غير متاحة
 * @param {string} type - نوع الخريطة (streets, satellite, terrain)
 * @param {Object} options - خيارات إضافية لطبقة الخريطة
 * @returns {L.TileLayer} - طبقة بلاطات Leaflet
 */
function createHybridTileLayer(type, options = {}) {
    // التأكد من أن النوع صالح
    if (!localMapsConfig.mapTiles[type]) {
        console.error(`نوع الخريطة غير صالح: ${type}`);
        type = 'streets'; // استخدام النوع الافتراضي
    }
    
    // إنشاء طبقة مخصصة تتحقق من توفر البلاطات المحلية
    const hybridLayer = L.TileLayer.extend({
        getTileUrl: function(coords) {
            const z = coords.z;
            const x = coords.x;
            const y = coords.y;
            
            // التحقق مما إذا كانت البلاطة متاحة محليًا
            if (isTileAvailableLocally(type, z, x, y)) {
                // استخدام البلاطة المحلية
                return L.Util.template(localMapsConfig.mapTiles[type], coords);
            } else {
                // استخدام البلاطة عبر الإنترنت
                return L.Util.template(localMapsConfig.fallback[type], coords);
            }
        }
    });
    
    // إرجاع طبقة جديدة باستخدام الامتداد المخصص
    return new hybridLayer('', options);
}

/**
 * إنشاء طبقات الخريطة للاستخدام في التطبيق
 * @returns {Object} - كائن يحتوي على طبقات الخريطة المختلفة
 */
function createMapLayers() {
    return {
        streets: createHybridTileLayer('streets', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Yemen Nav',
            maxZoom: 19
        }),
        
        satellite: createHybridTileLayer('satellite', {
            attribution: '&copy; <a href="https://www.esri.com/">Esri</a> | Yemen Nav',
            maxZoom: 19
        }),
        
        terrain: createHybridTileLayer('terrain', {
            attribution: '&copy; <a href="https://www.esri.com/">Esri</a> | Yemen Nav',
            maxZoom: 19
        })
    };
}

/**
 * تحميل البلاطات المحلية (يمكن استخدام هذه الوظيفة لتنزيل البلاطات مستقبلاً)
 * @param {L.LatLngBounds} bounds - حدود المنطقة
 * @param {number} minZoom - أدنى مستوى تكبير
 * @param {number} maxZoom - أعلى مستوى تكبير
 * @param {string} type - نوع الخريطة
 */
function downloadLocalTiles(bounds, minZoom, maxZoom, type) {
    console.log(`بدء تنزيل بلاطات ${type} من مستوى ${minZoom} إلى ${maxZoom}`);
    
    // هذه الوظيفة ستُستكمل لاحقًا لتنزيل البلاطات فعليًا
    // في الإصدار الحالي، هي مجرد واجهة
}

// تصدير الوظائف للاستخدام في ملفات أخرى
window.localMapsProvider = {
    config: localMapsConfig,
    createMapLayers: createMapLayers,
    downloadLocalTiles: downloadLocalTiles
};

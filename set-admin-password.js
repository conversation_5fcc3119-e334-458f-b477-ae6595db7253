// سكريبت لتعيين كلمة مرور المستخدم admin إلى yemen123
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'yemen123'
});

async function setAdminPassword() {
  try {
    console.log('بدء عملية تعيين كلمة مرور المستخدم المسؤول...');
    
    // كلمة المرور الجديدة: yemen123
    const newPassword = 'yemen123';
    
    // تشفير كلمة المرور باستخدام bcrypt
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // التحقق من وجود المستخدم
    const checkResult = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      ['admin']
    );
    
    if (checkResult.rows.length === 0) {
      // إذا لم يكن المستخدم موجوداً، قم بإنشائه
      console.log('المستخدم المسؤول غير موجود. جاري إنشاء مستخدم جديد...');
      
      // محاولة إدراج المستخدم
      await pool.query(
        `INSERT INTO users 
        (username, password, email, full_name, role_id, is_active, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
        ['admin', hashedPassword, '<EMAIL>', 'مدير النظام', 1, true]
      );
      
      console.log('تم إنشاء حساب المسؤول بنجاح!');
    } else {
      // إذا كان المستخدم موجوداً، قم بتحديث كلمة المرور
      console.log('المستخدم المسؤول موجود. جاري تحديث كلمة المرور...');
      
      // تحديث كلمة المرور (نحذف أي محفزات قبل التحديث)
      try {
        // حذف المحفزات المحتملة
        const triggers = await pool.query(`
          SELECT trigger_name 
          FROM information_schema.triggers 
          WHERE event_object_table = 'users'
        `);
        
        if (triggers.rows.length > 0) {
          console.log('جاري حذف المحفزات قبل التحديث...');
          for (const trigger of triggers.rows) {
            await pool.query(`DROP TRIGGER IF EXISTS ${trigger.trigger_name} ON users`);
            console.log(`- تم حذف المحفز ${trigger.trigger_name}`);
          }
        }
        
        // تحديث كلمة المرور
        await pool.query(
          'UPDATE users SET password = $1 WHERE username = $2',
          [hashedPassword, 'admin']
        );
        
        console.log('تم تحديث كلمة مرور المستخدم المسؤول بنجاح!');
      } catch (error) {
        console.error('خطأ في تحديث كلمة المرور:', error);
      }
    }
    
    console.log('\nيمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: admin');
    console.log('- كلمة المرور: yemen123');
    
  } catch (error) {
    console.error('حدث خطأ أثناء تعيين كلمة المرور:', error);
  } finally {
    await pool.end();
  }
}

setAdminPassword();

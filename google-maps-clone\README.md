# 🗺️ نظام الخرائط المحاكي لـ Google Maps

## 🎯 نظرة عامة

هذا نظام خرائط متقدم يحاكي Google Maps بالكامل، مصمم خصيص<|im_start|> للعمل مع قاعدة البيانات والسيرفر الموجودين مسبق<|im_start|>. يوفر تجربة مستخدم مطابقة لـ Google Maps مع ميزات متقدمة للملاحة والبحث.

## ✨ الميزات الرئيسية

### 🗺️ طبقات الخرائط المتعددة
- **خرائط الشوارع**: OpenStreetMap عالية الجودة
- **صور الأقمار الصناعية**: صور فضائية واضحة
- **خرائط التضاريس**: عرض التضاريس والارتفاعات

### 🎨 واجهة مشابهة لـ Google Maps
- تصميم مطابق لـ Google Maps
- أزرار تحكم بنفس الأسلوب
- ألوان وخطوط مشابهة
- تجربة مستخدم مألوفة

### 🔍 نظام البحث المتقدم
- بحث فوري في الأماكن
- البحث بالاسم العربي والإنجليزي
- البحث في الوصف
- نتائج دقيقة وسريعة

### 📍 إدارة النقاط والأماكن
- عرض النقاط من قاعدة البيانات
- أيقونات مختلفة حسب نوع المكان
- نوافذ معلومات تفاعلية
- صور وتفاصيل شاملة

### 🧭 نظام الملاحة المتقدم
- حساب المسارات الذكي
- تعليمات صوتية باللغة العربية
- شريط ملاحة تفاعلي
- تقدير المسافة والوقت

### 📱 دعم الأجهزة المحمولة
- تصميم متجاوب بالكامل
- تحسين للمس
- واجهة محسنة للجوال
- أداء ممتاز على جميع الأجهزة

## 🚀 التثبيت والتشغيل

### المتطلبات:
- خادم ويب (Apache/Nginx/Node.js)
- قاعدة بيانات موجودة مع API `/api/places`
- متصفح حديث يدعم HTML5

### خطوات التثبيت:

1. **نسخ الملفات:**
```bash
# نسخ مجلد google-maps-clone إلى مجلد الويب
cp -r google-maps-clone/ /var/www/html/maps/
```

2. **التأكد من API:**
```bash
# التأكد من عمل API الأماكن
curl http://your-server.com/api/places
```

3. **فتح الخريطة:**
```
http://your-server.com/maps/
```

## 📁 هيكل الملفات

```
google-maps-clone/
├── index.html              # الصفحة الرئيسية
├── assets/
│   └── js/
│       └── maps-app.js     # السكريبت الرئيسي
└── README.md               # هذا الملف
```

## 🔧 التخصيص والإعدادات

### تغيير الموقع الافتراضي:
```javascript
// في ملف maps-app.js
this.defaultCenter = [44.2066, 15.3547]; // [longitude, latitude]
this.defaultZoom = 12;
```

### إضافة طبقات خرائط جديدة:
```javascript
// إضافة طبقة جديدة
this.layers.newLayer = new ol.layer.Tile({
    source: new ol.source.XYZ({
        url: 'https://your-tile-server.com/{z}/{x}/{y}.png'
    }),
    visible: false
});
```

### تخصيص الأيقونات:
```javascript
// تغيير أيقونات الأماكن
getIconForCategory(categoryId) {
    const icons = {
        1: 'path/to/tourist-icon.png',
        2: 'path/to/religious-icon.png',
        // إضافة المزيد...
    };
    return icons[categoryId] || icons.default;
}
```

## 🎵 التنبيهات الصوتية

### الميزات المدعومة:
- تحويل النص إلى كلام (TTS)
- دعم اللغة العربية
- تعليمات ملاحة واضحة
- إمكانية كتم الصوت

### إضافة أصوات مخصصة:
```javascript
// تشغيل ملف صوتي مخصص
playCustomSound(audioFile) {
    const audio = document.getElementById('audioPlayer');
    audio.src = audioFile;
    audio.play();
}
```

## 🔗 التكامل مع قاعدة البيانات

### تنسيق البيانات المطلوب:
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name_ar": "اسم المكان بالعربية",
            "name_en": "Place Name in English",
            "latitude": 15.3547,
            "longitude": 44.2066,
            "category_id": 2,
            "description_ar": "وصف المكان",
            "rating": 4.8,
            "photos": ["url1.jpg", "url2.jpg"]
        }
    ]
}
```

### API المطلوبة:
- `GET /api/places` - جلب جميع الأماكن
- `GET /api/places/:id` - جلب مكان محدد
- `POST /api/places` - إضافة مكان جديد (اختياري)

## 🎨 التخصيص المرئي

### تغيير الألوان:
```css
:root {
    --gm-primary: #1a73e8;        /* اللون الأساسي */
    --gm-secondary: #34a853;      /* اللون الثانوي */
    --gm-danger: #ea4335;         /* لون التحذير */
    --gm-warning: #fbbc04;        /* لون التنبيه */
}
```

### تخصيص الخطوط:
```css
body {
    font-family: 'Tajawal', 'Roboto', sans-serif;
}
```

## 📱 الاستجابة للجوال

### نقاط التوقف:
- **الجوال**: أقل من 768px
- **الجوال الصغير**: أقل من 480px
- **الكمبيوتر اللوحي**: 768px - 1024px
- **سطح المكتب**: أكبر من 1024px

### تحسينات الجوال:
- أزرار أكبر للمس
- قوائم محسنة للجوال
- نوافذ منبثقة ملء الشاشة
- تحكم باللمس المتعدد

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **الخرائط لا تظهر:**
   - تحقق من الاتصال بالإنترنت
   - تأكد من تحميل OpenLayers
   - فحص وحدة تحكم المطور

2. **البيانات لا تحمل:**
   - تحقق من API `/api/places`
   - فحص CORS headers
   - تأكد من تنسيق JSON

3. **الصوت لا يعمل:**
   - تحقق من دعم المتصفح لـ TTS
   - تأكد من عدم كتم الصوت
   - فحص إعدادات المتصفح

### أدوات التطوير:
```javascript
// تفعيل وضع التطوير
window.yemenMaps.debugMode = true;

// عرض معلومات إضافية
console.log('الأماكن المحملة:', window.yemenMaps.places);
console.log('الطبقة الحالية:', window.yemenMaps.currentLayer);
```

## 🚀 الأداء والتحسين

### نصائح للأداء:
- استخدام CDN للمكتبات
- ضغط الصور والأيقونات
- تحميل البيانات تدريجي<|im_start|>
- تخزين مؤقت ذكي

### مراقبة الأداء:
```javascript
// قياس وقت التحميل
console.time('Map Load Time');
// ... كود التحميل
console.timeEnd('Map Load Time');
```

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. فحص وحدة تحكم المطور
2. مراجعة هذا الدليل
3. التأكد من متطلبات النظام
4. فحص إعدادات الخادم

### الإبلاغ عن المشاكل:
- وصف تفصيلي للمشكلة
- خطوات إعادة الإنتاج
- معلومات المتصفح والنظام
- لقطات شاشة إن أمكن

## 🎉 الخلاصة

هذا النظام يوفر تجربة خرائط متكاملة ومشابهة لـ Google Maps، مع دعم كامل للغة العربية وتكامل سلس مع قاعدة البيانات الموجودة. يمكن تخصيصه وتطويره بسهولة حسب الاحتياجات.

---

**🌟 استمتع بتجربة خرائط احترافية مع نظام يمن GPS!**

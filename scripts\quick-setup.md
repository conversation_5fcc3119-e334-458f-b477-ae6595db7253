# دليل سحب البيانات من Google Maps - الإعداد السريع

## 🚀 البدء السريع

### 1. تثبيت المكتبات الأساسية
```bash
npm install axios puppeteer
```

### 2. الطريقة الأولى: Google Places API (الموصى بها)

#### أ. الحصول على مفتاح API:
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Places API من مكتبة APIs
4. اذهب إلى "Credentials" وأنشئ API Key
5. قيّد المفتاح لـ Places API فقط (للأمان)

#### ب. تشغيل السكريبت:
```bash
# عدّل المفتاح في الملف أولاً
node scripts/google-places-scraper.js
```

### 3. الطريقة الثانية: Web Scraping (مجاني)

```bash
# تشغيل مباشر
node scripts/google-maps-web-scraper.js
```

## 📊 النتائج المتوقعة

### البيانات المسحوبة:
- ✅ **الأسماء** (عربي وإنجليزي)
- ✅ **الإحداثيات** (خط الطول والعرض)
- ✅ **العناوين** الكاملة
- ✅ **أرقام الهواتف**
- ✅ **المواقع الإلكترونية**
- ✅ **التقييمات** والمراجعات
- ✅ **الصور** (حتى 5 صور لكل مكان)
- ✅ **أوقات العمل**
- ✅ **المرافق والخدمات**

### الملفات المُنتجة:
- `data/google_places_raw.json` - البيانات الخام
- `database/google_places_import.sql` - ملف SQL للاستيراد
- `public/images/places/` - مجلد الصور المحملة

## 🎯 الأماكن المستهدفة

### المحافظات:
- صنعاء، عدن، تعز، الحديدة، إب، ذمار، المكلا، مأرب

### فئات الأماكن:
- 🏨 **الفنادق** (جميع الفئات)
- 🍽️ **المطاعم** (يمنية، عربية، عالمية)
- 🏛️ **المعالم السياحية** (تاريخية، ثقافية)
- 🏥 **المستشفيات** والمراكز الطبية
- 🎓 **الجامعات** والمدارس
- 🛍️ **مراكز التسوق**
- 🏦 **البنوك** والمؤسسات المالية
- 🕌 **المساجد** والمراكز الدينية
- 🏛️ **المتاحف** والمراكز الثقافية
- 🌳 **الحدائق** والمنتزهات

## ⚙️ إعدادات متقدمة

### تخصيص البحث:
```javascript
// في ملف google-places-scraper.js
const SEARCH_RADIUS = 10000; // نطاق البحث بالمتر
const MAX_RESULTS = 20;      // عدد النتائج لكل بحث
const DELAY_BETWEEN_REQUESTS = 1000; // تأخير بين الطلبات
```

### فلترة النتائج:
```javascript
// إضافة شروط للفلترة
const MIN_RATING = 3.0;      // تقييم أدنى
const MIN_REVIEWS = 5;       // عدد تقييمات أدنى
const VERIFIED_ONLY = true;  // الأماكن المؤكدة فقط
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في API Key:
```
Error: API key not valid
```
**الحل:** تأكد من صحة المفتاح وتفعيل Places API

#### 2. تجاوز الحدود:
```
Error: You have exceeded your daily quota
```
**الحل:** انتظر حتى اليوم التالي أو ارفع الحصة

#### 3. خطأ في Web Scraping:
```
Error: Navigation timeout
```
**الحل:** تحقق من اتصال الإنترنت وجرب تقليل السرعة

### نصائح للحصول على أفضل النتائج:

1. **استخدم كلمات بحث متنوعة:**
   - "فنادق صنعاء"
   - "Hotels Sanaa"
   - "مطاعم يمنية عدن"

2. **اضبط نطاق البحث:**
   - مدن كبيرة: 15-20 كم
   - مدن صغيرة: 5-10 كم

3. **راقب الحدود:**
   - Google Places API: 1000 طلب/يوم (مجاني)
   - Web Scraping: لا توجد حدود لكن كن حذراً

## 📈 تحسين الأداء

### للحصول على بيانات أكثر:
1. **استخدم مفاتيح API متعددة**
2. **وزع البحث على أيام متعددة**
3. **استخدم VPN لـ Web Scraping**
4. **احفظ البيانات بانتظام**

### لتجنب الحظر:
1. **أضف تأخير بين الطلبات**
2. **استخدم User Agents مختلفة**
3. **لا تفرط في الطلبات**
4. **احترم robots.txt**

## 🎉 بعد السحب

### استيراد البيانات:
```bash
# تشغيل ملف SQL المُنتج
psql -U yemen -d yemen_gps -f database/google_places_import.sql
```

### التحقق من النتائج:
```bash
# فحص البيانات المستوردة
node database/check_tables.js
```

### تحديث الخريطة:
```bash
# إعادة تشغيل الخادم
node server.js
```

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع ملف `scripts/README.md`
2. تحقق من logs الأخطاء
3. جرب الطريقة البديلة
4. اتصل بفريق التطوير

---

**ملاحظة مهمة:** احترم دائماً شروط الخدمة لـ Google Maps وتجنب الإفراط في الطلبات.

// يمن ناف - إنشاء قاعدة البيانات
const { Client } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات الرئيسية
const mainDbConfig = {
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres' // الاتصال بقاعدة البيانات الافتراضية
};

// اسم قاعدة البيانات المراد إنشاؤها
const dbName = process.env.DB_NAME || 'progsql';

async function createDatabase() {
    const client = new Client(mainDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات الرئيسية...');
        await client.connect();
        
        // التحقق من وجود قاعدة البيانات
        const checkDbQuery = `SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = '${dbName}');`;
        const checkResult = await client.query(checkDbQuery);
        
        if (checkResult.rows[0].exists) {
            console.log(`قاعدة البيانات '${dbName}' موجودة بالفعل.`);
        } else {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            console.log(`جاري إنشاء قاعدة البيانات '${dbName}'...`);
            await client.query(`CREATE DATABASE ${dbName};`);
            console.log(`تم إنشاء قاعدة البيانات '${dbName}' بنجاح!`);
        }
    } catch (err) {
        console.error('خطأ في إنشاء قاعدة البيانات:', err.message);
    } finally {
        await client.end();
    }
}

// تنفيذ الدالة
createDatabase();

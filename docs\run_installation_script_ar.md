# دليل تشغيل ملف التثبيت التلقائي لنظام "يمن ناف"

## مقدمة

لقد قمنا بإنشاء ملف نصي (script) يقوم بتثبيت جميع المتطلبات الأساسية وإعداد نظام "يمن ناف" بشكل تلقائي. هذا الدليل يشرح كيفية تشغيل هذا الملف النصي.

## متطلبات مسبقة

- نظام تشغيل Windows 10 أو Windows 11
- اتصال إنترنت مستقر وسريع
- مساحة تخزين كافية (على الأقل 10 جيجابايت)
- صلاحيات المسؤول (Administrator)

## خطوات تشغيل ملف التثبيت

### 1. فتح PowerShell بصلاحيات المسؤول

1. انقر بزر الماوس الأيمن على زر ابدأ (Start) أو اضغط على `Win + X`
2. اختر "Windows PowerShell (Admin)" أو "Terminal (Admin)"
3. إذا ظهرت نافذة "User Account Control"، انقر على "Yes"

### 2. تعيين سياسة التنفيذ

قبل تشغيل الملف النصي، يجب تعيين سياسة التنفيذ لتسمح بتشغيل البرامج النصية. أدخل الأمر التالي:

```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
```

### 3. الانتقال إلى المجلد الذي يحتوي على ملف التثبيت

استخدم الأمر `cd` للانتقال إلى المجلد الذي يحتوي على ملف `install_yemen_nav.ps1`:

```powershell
cd "e:\yemen gps"
```

### 4. تشغيل ملف التثبيت

الآن قم بتشغيل ملف التثبيت باستخدام الأمر التالي:

```powershell
.\install_yemen_nav.ps1
```

### 5. متابعة عملية التثبيت

- سيبدأ الملف النصي بإنشاء المجلدات اللازمة وتنزيل البرامج المطلوبة
- قد تظهر بعض النوافذ أثناء عملية التثبيت، اتركها تعمل ولا تغلقها
- قد تستغرق عملية التثبيت بعض الوقت (30-60 دقيقة) اعتمادًا على سرعة الإنترنت وأداء الجهاز
- سيعرض الملف النصي تقدم العملية وسيخبرك بكل خطوة يتم تنفيذها

### 6. إكمال التثبيت

- عند اكتمال التثبيت، سيتم فتح المتصفح تلقائيًا وعرض نظام "يمن ناف"
- ستظهر رسالة "تم تثبيت وتشغيل نظام يمن ناف بنجاح!" في نافذة PowerShell
- يمكنك الوصول إلى النظام في أي وقت من خلال المتصفح على العنوان: `http://yemen-nav.local` أو `http://localhost`

## ماذا يفعل ملف التثبيت؟

ملف التثبيت يقوم بالخطوات التالية:

1. **إنشاء المجلدات اللازمة**:
   - `C:\yemen-nav`: المجلد الرئيسي للنظام
   - `C:\yemen-nav\backend`: مجلد الواجهة الخلفية
   - `C:\yemen-nav\frontend`: مجلد الواجهة الأمامية
   - `C:\yemen-nav\storage`: مجلد تخزين البيانات

2. **تنزيل وتثبيت البرامج المطلوبة**:
   - Node.js: بيئة تشغيل JavaScript
   - PostgreSQL: نظام إدارة قواعد البيانات
   - PostGIS: امتداد للبيانات الجغرافية
   - Git: نظام التحكم في الإصدارات
   - IIS: خدمة معلومات الإنترنت
   - URL Rewrite Module: وحدة لتوجيه الطلبات
   - PM2: مدير عمليات لتطبيقات Node.js

3. **إعداد قاعدة البيانات**:
   - إنشاء قاعدة بيانات "yemen_nav"
   - تفعيل امتدادات PostGIS
   - إنشاء مستخدم قاعدة البيانات ومنحه الصلاحيات اللازمة

4. **إعداد الواجهة الخلفية**:
   - إنشاء مشروع Node.js
   - تثبيت الحزم اللازمة
   - إنشاء ملفات التكوين
   - إعداد خادم Express

5. **إعداد الواجهة الأمامية**:
   - إنشاء تطبيق React
   - تثبيت الحزم اللازمة
   - إنشاء ملفات التكوين
   - بناء المشروع للإنتاج

6. **إعداد IIS**:
   - إنشاء موقع ويب جديد
   - تكوين إعادة توجيه الطلبات
   - إضافة سجل إلى ملف hosts

7. **تشغيل النظام**:
   - تشغيل الواجهة الخلفية باستخدام PM2
   - فتح المتصفح وعرض النظام

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل أثناء عملية التثبيت، يمكنك محاولة الخطوات التالية:

1. **إعادة تشغيل الملف النصي**:
   - أغلق نافذة PowerShell
   - افتح PowerShell بصلاحيات المسؤول مرة أخرى
   - انتقل إلى المجلد الذي يحتوي على ملف التثبيت
   - شغل الملف النصي مرة أخرى

2. **التحقق من متطلبات النظام**:
   - تأكد من أن لديك اتصال إنترنت مستقر
   - تأكد من أن لديك مساحة تخزين كافية
   - تأكد من أنك تستخدم نظام Windows 10 أو Windows 11

3. **التحقق من الخدمات**:
   - افتح "Services" من لوحة التحكم
   - تأكد من أن خدمات PostgreSQL وIIS قيد التشغيل

4. **التحقق من المنافذ**:
   - تأكد من أن المنافذ 80 (للويب) و5432 (لقاعدة البيانات) غير مستخدمة بالفعل

5. **التثبيت اليدوي**:
   - إذا استمرت المشاكل، يمكنك اتباع دليل التثبيت اليدوي في ملف `docs/installation_guide_ar.md`

## الخلاصة

باستخدام ملف التثبيت التلقائي، يمكنك تثبيت وتشغيل نظام "يمن ناف" بسهولة وسرعة. بعد اكتمال التثبيت، يمكنك الوصول إلى النظام من خلال المتصفح واستخدام جميع ميزاته.

للمزيد من المعلومات حول استخدام النظام، يرجى الاطلاع على دليل المستخدم في ملف `docs/user_guide.md`.

/**
 * وحدة الخرائط دون اتصال المحسنة
 * تتيح هذه الوحدة تحميل وتخزين خرائط Google للاستخدام دون اتصال
 */

const EnhancedOfflineMaps = (function() {
    // المتغيرات الخاصة
    let map = null;
    let dbPromise = null;
    let currentOfflineRegions = [];
    
    // تهيئة قاعدة بيانات IndexedDB
    function initDatabase() {
        if (!window.indexedDB) {
            console.error("متصفحك لا يدعم IndexedDB");
            return null;
        }
        
        dbPromise = idb.openDB('yemen-gps-maps', 1, {
            upgrade(db) {
                // مخزن بيانات الخرائط
                if (!db.objectStoreNames.contains('map-tiles')) {
                    const tileStore = db.createObjectStore('map-tiles', { keyPath: 'url' });
                    tileStore.createIndex('timestamp', 'timestamp');
                }
                
                // مخزن بيانات المناطق المحفوظة
                if (!db.objectStoreNames.contains('offline-regions')) {
                    db.createObjectStore('offline-regions', { keyPath: 'id' });
                }
                
                // مخزن بيانات النقاط المهمة
                if (!db.objectStoreNames.contains('pois')) {
                    const poiStore = db.createObjectStore('pois', { keyPath: 'id' });
                    poiStore.createIndex('region', 'region');
                    poiStore.createIndex('category', 'category');
                }
            }
        });
        
        return dbPromise;
    }
    
    // تهيئة الخريطة
    function init(mapInstance) {
        map = mapInstance;
        
        // تهيئة قاعدة البيانات
        initDatabase();
        
        // تحميل المناطق المحفوظة
        loadSavedRegions();
        
        // إضافة أزرار التحكم في الخرائط دون اتصال
        addOfflineMapsControls();
        
        return {
            downloadRegion,
            deleteRegion,
            getSavedRegions,
            isRegionSaved
        };
    }
    
    // إضافة أزرار التحكم في الخرائط دون اتصال
    function addOfflineMapsControls() {
        // إنشاء حاوية أزرار التحكم
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'offline-maps-controls';
        
        // زر تحميل المنطقة
        const downloadButton = document.createElement('button');
        downloadButton.id = 'download-region-btn';
        downloadButton.className = 'control-btn';
        downloadButton.innerHTML = '<i class="fas fa-download"></i><span>تحميل المنطقة</span>';
        downloadButton.addEventListener('click', function() {
            // الحصول على الحدود الحالية للخريطة
            const bounds = map.getBounds();
            
            // عرض مربع حوار لتأكيد التحميل
            showDownloadConfirmDialog(bounds);
        });
        
        // زر إدارة المناطق المحفوظة
        const manageButton = document.createElement('button');
        manageButton.id = 'manage-regions-btn';
        manageButton.className = 'control-btn';
        manageButton.innerHTML = '<i class="fas fa-map"></i><span>إدارة المناطق المحفوظة</span>';
        manageButton.addEventListener('click', function() {
            showSavedRegionsDialog();
        });
        
        // إضافة الأزرار إلى الحاوية
        controlsContainer.appendChild(downloadButton);
        controlsContainer.appendChild(manageButton);
        
        // إضافة الحاوية إلى المستند
        document.body.appendChild(controlsContainer);
        
        // إنشاء مربع حوار إدارة المناطق المحفوظة
        createSavedRegionsDialog();
        
        // إنشاء مربع حوار تأكيد التحميل
        createDownloadConfirmDialog();
    }
    
    // إنشاء مربع حوار إدارة المناطق المحفوظة
    function createSavedRegionsDialog() {
        // التحقق من وجود مربع الحوار
        if (document.getElementById('regions-modal')) {
            return;
        }
        
        // إنشاء مربع الحوار
        const modal = document.createElement('div');
        modal.id = 'regions-modal';
        modal.className = 'modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>المناطق المحفوظة</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="saved-regions-list" class="regions-list">
                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                    </div>
                </div>
            </div>
        `;
        
        // إضافة مربع الحوار إلى المستند
        document.body.appendChild(modal);
        
        // إضافة مستمع لزر الإغلاق
        modal.querySelector('.close-btn').addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        // إغلاق مربع الحوار عند النقر خارجه
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // إنشاء مربع حوار تأكيد التحميل
    function createDownloadConfirmDialog() {
        // التحقق من وجود مربع الحوار
        if (document.getElementById('download-confirm-modal')) {
            return;
        }
        
        // إنشاء مربع الحوار
        const modal = document.createElement('div');
        modal.id = 'download-confirm-modal';
        modal.className = 'modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>تحميل منطقة للاستخدام دون اتصال</h3>
                    <button class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p>هل تريد تحميل المنطقة الحالية للاستخدام دون اتصال؟</p>
                    <p>سيتم تحميل حوالي <span id="tiles-count">0</span> بلاطة خريطة بحجم تقريبي <span id="estimated-size">0</span>.</p>
                    
                    <div class="form-group">
                        <label for="region-name">اسم المنطقة:</label>
                        <input type="text" id="region-name" class="form-control" placeholder="أدخل اسماً للمنطقة">
                    </div>
                    
                    <div class="form-group">
                        <label>مستويات التكبير:</label>
                        <div class="zoom-levels">
                            <label><input type="checkbox" value="8" checked> 8</label>
                            <label><input type="checkbox" value="9" checked> 9</label>
                            <label><input type="checkbox" value="10" checked> 10</label>
                            <label><input type="checkbox" value="11" checked> 11</label>
                            <label><input type="checkbox" value="12" checked> 12</label>
                            <label><input type="checkbox" value="13" checked> 13</label>
                            <label><input type="checkbox" value="14"> 14</label>
                            <label><input type="checkbox" value="15"> 15</label>
                        </div>
                    </div>
                    
                    <div class="actions">
                        <button id="confirm-download" class="action-btn primary">تحميل</button>
                        <button id="cancel-download" class="action-btn">إلغاء</button>
                    </div>
                </div>
            </div>
        `;
        
        // إضافة مربع الحوار إلى المستند
        document.body.appendChild(modal);
        
        // إضافة مستمع لزر الإغلاق
        modal.querySelector('.close-btn').addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        // إضافة مستمع لزر الإلغاء
        modal.querySelector('#cancel-download').addEventListener('click', function() {
            modal.style.display = 'none';
        });
        
        // إضافة مستمع لزر التأكيد
        modal.querySelector('#confirm-download').addEventListener('click', function() {
            // الحصول على اسم المنطقة
            const regionName = document.getElementById('region-name').value || `منطقة ${new Date().toLocaleString('ar-YE')}`;
            
            // الحصول على مستويات التكبير المحددة
            const zoomLevels = [];
            document.querySelectorAll('.zoom-levels input:checked').forEach(input => {
                zoomLevels.push(parseInt(input.value));
            });
            
            // الحصول على الحدود
            const bounds = window.downloadBounds;
            
            // إغلاق مربع الحوار
            modal.style.display = 'none';
            
            // تحميل المنطقة
            downloadRegion(bounds, zoomLevels, regionName);
        });
        
        // إغلاق مربع الحوار عند النقر خارجه
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // عرض مربع حوار تأكيد التحميل
    function showDownloadConfirmDialog(bounds) {
        // حفظ الحدود
        window.downloadBounds = bounds;
        
        // حساب عدد البلاطات والحجم التقريبي
        let totalTiles = 0;
        const zoomLevels = [8, 9, 10, 11, 12, 13];
        
        for (const zoom of zoomLevels) {
            totalTiles += calculateTilesCount(bounds, zoom);
        }
        
        // تحديث معلومات التحميل
        document.getElementById('tiles-count').textContent = totalTiles;
        document.getElementById('estimated-size').textContent = formatSize(totalTiles * 15 * 1024); // تقدير 15 كيلوبايت لكل بلاطة
        
        // تعيين اسم المنطقة الافتراضي
        document.getElementById('region-name').value = `منطقة ${new Date().toLocaleString('ar-YE')}`;
        
        // عرض مربع الحوار
        document.getElementById('download-confirm-modal').style.display = 'block';
    }
    
    // عرض مربع حوار إدارة المناطق المحفوظة
    function showSavedRegionsDialog() {
        // تحديث قائمة المناطق المحفوظة
        updateSavedRegionsList();
        
        // عرض مربع الحوار
        document.getElementById('regions-modal').style.display = 'block';
    }
    
    // تحديث قائمة المناطق المحفوظة
    async function updateSavedRegionsList() {
        // الحصول على المناطق المحفوظة
        const regions = await getSavedRegions();
        
        // الحصول على حاوية القائمة
        const listContainer = document.getElementById('saved-regions-list');
        
        // مسح المحتوى الحالي
        listContainer.innerHTML = '';
        
        if (regions.length === 0) {
            listContainer.innerHTML = '<p>لا توجد مناطق محفوظة</p>';
            return;
        }
        
        // إضافة كل منطقة إلى القائمة
        regions.forEach(region => {
            const regionItem = document.createElement('div');
            regionItem.className = 'region-item';
            
            regionItem.innerHTML = `
                <div class="region-info">
                    <div class="region-name">${region.name}</div>
                    <div class="region-meta">
                        ${formatSize(region.size || 0)} - ${new Date(region.timestamp).toLocaleString('ar-YE')}
                    </div>
                </div>
                <div class="region-actions">
                    <button class="region-action-btn view-btn" data-id="${region.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="region-action-btn delete-btn" data-id="${region.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            // إضافة مستمع لزر العرض
            regionItem.querySelector('.view-btn').addEventListener('click', function() {
                // الانتقال إلى المنطقة
                const bounds = L.latLngBounds(
                    L.latLng(region.bounds.south, region.bounds.west),
                    L.latLng(region.bounds.north, region.bounds.east)
                );
                
                map.fitBounds(bounds);
                
                // إغلاق مربع الحوار
                document.getElementById('regions-modal').style.display = 'none';
            });
            
            // إضافة مستمع لزر الحذف
            regionItem.querySelector('.delete-btn').addEventListener('click', function() {
                // تأكيد الحذف
                if (confirm(`هل أنت متأكد من حذف منطقة "${region.name}"؟`)) {
                    deleteRegion(region.id);
                }
            });
            
            // إضافة العنصر إلى القائمة
            listContainer.appendChild(regionItem);
        });
    }
    
    // تحميل المناطق المحفوظة
    async function loadSavedRegions() {
        if (!dbPromise) return [];
        
        try {
            const db = await dbPromise;
            currentOfflineRegions = await db.getAll('offline-regions');
            return currentOfflineRegions;
        } catch (error) {
            console.error('خطأ في تحميل المناطق المحفوظة:', error);
            return [];
        }
    }
    
    // الحصول على المناطق المحفوظة
    async function getSavedRegions() {
        return await loadSavedRegions();
    }
    
    // التحقق مما إذا كانت المنطقة محفوظة
    async function isRegionSaved(regionId) {
        if (!dbPromise) return false;
        
        try {
            const db = await dbPromise;
            const region = await db.get('offline-regions', regionId);
            return !!region;
        } catch (error) {
            console.error('خطأ في التحقق من المنطقة:', error);
            return false;
        }
    }
    
    // تحميل منطقة للاستخدام دون اتصال
    async function downloadRegion(bounds, zoomLevels, name) {
        if (!map || !dbPromise) {
            showNotification('حدث خطأ في تهيئة الخرائط دون اتصال', 'error');
            return;
        }
        
        const regionId = `${bounds.getSouth()}_${bounds.getWest()}_${bounds.getNorth()}_${bounds.getEast()}`;
        const region = {
            id: regionId,
            name: name || `منطقة ${new Date().toLocaleString('ar-YE')}`,
            bounds: {
                south: bounds.getSouth(),
                west: bounds.getWest(),
                north: bounds.getNorth(),
                east: bounds.getEast()
            },
            zoomLevels: zoomLevels || [8, 9, 10, 11, 12, 13],
            timestamp: Date.now(),
            tilesCount: 0,
            size: 0
        };
        
        // عرض مؤشر التحميل
        showLoadingIndicator(`جاري تحميل خرائط منطقة ${region.name}...`);
        
        try {
            // حساب عدد البلاطات المطلوبة
            let totalTiles = 0;
            for (const zoom of region.zoomLevels) {
                const tilesCount = calculateTilesCount(bounds, zoom);
                totalTiles += tilesCount;
            }
            
            // تحديث معلومات المنطقة
            region.tilesCount = totalTiles;
            
            // تحميل البلاطات
            let downloadedTiles = 0;
            const db = await dbPromise;
            
            // حفظ معلومات المنطقة
            await db.put('offline-regions', region);
            
            // تحميل البلاطات لكل مستوى تكبير
            for (const zoom of region.zoomLevels) {
                await downloadTilesForZoom(bounds, zoom, (progress) => {
                    const totalProgress = (downloadedTiles + progress) / totalTiles;
                    updateLoadingProgress(Math.round(totalProgress * 100));
                });
                downloadedTiles += calculateTilesCount(bounds, zoom);
            }
            
            // تحميل نقاط الاهتمام في المنطقة
            await downloadPOIs(bounds);
            
            // تحديث حجم المنطقة
            const size = await calculateRegionSize(regionId);
            region.size = size;
            await db.put('offline-regions', region);
            
            // تحديث قائمة المناطق المحفوظة
            currentOfflineRegions = await loadSavedRegions();
            
            // إخفاء مؤشر التحميل
            hideLoadingIndicator();
            
            // عرض إشعار نجاح
            showNotification(`تم تحميل منطقة ${region.name} بنجاح (${formatSize(size)})`, 'success');
            
            return region;
        } catch (error) {
            console.error('خطأ في تحميل المنطقة:', error);
            hideLoadingIndicator();
            showNotification('حدث خطأ أثناء تحميل المنطقة', 'error');
            
            // حذف المنطقة في حالة الفشل
            try {
                const db = await dbPromise;
                await db.delete('offline-regions', regionId);
            } catch (e) {
                console.error('خطأ في حذف المنطقة بعد الفشل:', e);
            }
            
            throw error;
        }
    }
    
    // حساب عدد البلاطات المطلوبة لمنطقة ومستوى تكبير محددين
    function calculateTilesCount(bounds, zoom) {
        const northEast = bounds.getNorthEast();
        const southWest = bounds.getSouthWest();
        
        const neTile = latLngToTile(northEast.lat, northEast.lng, zoom);
        const swTile = latLngToTile(southWest.lat, southWest.lng, zoom);
        
        const xCount = Math.abs(neTile.x - swTile.x) + 1;
        const yCount = Math.abs(neTile.y - swTile.y) + 1;
        
        return xCount * yCount;
    }
    
    // تحويل الإحداثيات إلى رقم البلاطة
    function latLngToTile(lat, lng, zoom) {
        const n = Math.pow(2, zoom);
        const x = Math.floor((lng + 180) / 360 * n);
        const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
        return { x, y };
    }
    
    // تحميل البلاطات لمستوى تكبير محدد
    async function downloadTilesForZoom(bounds, zoom, progressCallback) {
        const northEast = bounds.getNorthEast();
        const southWest = bounds.getSouthWest();
        
        const neTile = latLngToTile(northEast.lat, northEast.lng, zoom);
        const swTile = latLngToTile(southWest.lat, southWest.lng, zoom);
        
        const minX = Math.min(neTile.x, swTile.x);
        const maxX = Math.max(neTile.x, swTile.x);
        const minY = Math.min(neTile.y, swTile.y);
        const maxY = Math.max(neTile.y, swTile.y);
        
        const totalTiles = (maxX - minX + 1) * (maxY - minY + 1);
        let downloadedTiles = 0;
        
        const db = await dbPromise;
        const tx = db.transaction('map-tiles', 'readwrite');
        const tileStore = tx.objectStore('map-tiles');
        
        const promises = [];
        
        for (let x = minX; x <= maxX; x++) {
            for (let y = minY; y <= maxY; y++) {
                const url = getTileUrl(x, y, zoom);
                promises.push(
                    downloadAndStoreTile(url, tileStore)
                        .then(() => {
                            downloadedTiles++;
                            if (progressCallback) {
                                progressCallback(downloadedTiles / totalTiles);
                            }
                        })
                );
            }
        }
        
        await Promise.all(promises);
        await tx.done;
    }
    
    // الحصول على عنوان URL للبلاطة
    function getTileUrl(x, y, zoom) {
        // استخدام خرائط Google
        return `https://mt1.google.com/vt/lyrs=m&x=${x}&y=${y}&z=${zoom}`;
    }
    
    // تحميل وتخزين بلاطة
    async function downloadAndStoreTile(url, tileStore) {
        // التحقق من وجود البلاطة في التخزين
        const existingTile = await tileStore.get(url);
        if (existingTile) {
            // تحديث الطابع الزمني
            existingTile.timestamp = Date.now();
            return tileStore.put(existingTile);
        }
        
        // تحميل البلاطة
        const response = await fetch(url, { mode: 'cors' });
        if (!response.ok) {
            throw new Error(`فشل تحميل البلاطة: ${response.status} ${response.statusText}`);
        }
        
        const blob = await response.blob();
        
        // تخزين البلاطة
        return tileStore.put({
            url,
            data: blob,
            timestamp: Date.now()
        });
    }
    
    // تحميل نقاط الاهتمام في المنطقة
    async function downloadPOIs(bounds) {
        try {
            // الحصول على نقاط الاهتمام من الخادم
            const response = await fetch(`/api/pois?south=${bounds.getSouth()}&west=${bounds.getWest()}&north=${bounds.getNorth()}&east=${bounds.getEast()}`);
            if (!response.ok) {
                throw new Error(`فشل تحميل نقاط الاهتمام: ${response.status} ${response.statusText}`);
            }
            
            const pois = await response.json();
            
            // تخزين نقاط الاهتمام
            const db = await dbPromise;
            const tx = db.transaction('pois', 'readwrite');
            const poiStore = tx.objectStore('pois');
            
            for (const poi of pois) {
                await poiStore.put(poi);
            }
            
            await tx.done;
            
            return pois;
        } catch (error) {
            console.error('خطأ في تحميل نقاط الاهتمام:', error);
            // نستمر حتى لو فشل تحميل نقاط الاهتمام
            return [];
        }
    }
    
    // حساب حجم المنطقة
    async function calculateRegionSize(regionId) {
        const db = await dbPromise;
        const region = await db.get('offline-regions', regionId);
        if (!region) {
            return 0;
        }
        
        // الحصول على جميع البلاطات
        const tiles = await db.getAll('map-tiles');
        
        // حساب الحجم الإجمالي
        let totalSize = 0;
        for (const tile of tiles) {
            if (tile.data) {
                totalSize += tile.data.size;
            }
        }
        
        return totalSize;
    }
    
    // تنسيق حجم الملف
    function formatSize(bytes) {
        if (bytes < 1024) {
            return `${bytes} بايت`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} كيلوبايت`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(1)} ميجابايت`;
        }
    }
    
    // حذف منطقة محفوظة
    async function deleteRegion(regionId) {
        try {
            const db = await dbPromise;
            
            // الحصول على معلومات المنطقة
            const region = await db.get('offline-regions', regionId);
            if (!region) {
                throw new Error('المنطقة غير موجودة');
            }
            
            // حذف المنطقة من قاعدة البيانات
            await db.delete('offline-regions', regionId);
            
            // حذف نقاط الاهتمام المرتبطة بالمنطقة
            const poiTx = db.transaction('pois', 'readwrite');
            const poiStore = poiTx.objectStore('pois');
            const poiIndex = poiStore.index('region');
            const poisToDelete = await poiIndex.getAllKeys(regionId);
            
            for (const poiId of poisToDelete) {
                await poiStore.delete(poiId);
            }
            
            await poiTx.done;
            
            // تحديث قائمة المناطق المحفوظة
            currentOfflineRegions = await loadSavedRegions();
            
            // تحديث قائمة المناطق المحفوظة في واجهة المستخدم
            updateSavedRegionsList();
            
            // عرض إشعار نجاح
            showNotification(`تم حذف منطقة ${region.name} بنجاح`, 'success');
            
            return true;
        } catch (error) {
            console.error('خطأ في حذف المنطقة:', error);
            showNotification('حدث خطأ أثناء حذف المنطقة', 'error');
            throw error;
        }
    }
    
    // تصدير الواجهة العامة
    return {
        init
    };
})();

package com.yemengps.app.data.converters;

import androidx.room.TypeConverter;

import java.util.Date;

/**
 * Type converter for Room database to convert between Date and Long
 */
public class DateConverter {
    
    /**
     * Convert from Date to Long timestamp
     * @param date The date to convert
     * @return The timestamp in milliseconds, or null if date is null
     */
    @TypeConverter
    public static Long fromDate(Date date) {
        return date == null ? null : date.getTime();
    }
    
    /**
     * Convert from Long timestamp to Date
     * @param timestamp The timestamp in milliseconds
     * @return The date, or null if timestamp is null
     */
    @TypeConverter
    public static Date toDate(Long timestamp) {
        return timestamp == null ? null : new Date(timestamp);
    }
}

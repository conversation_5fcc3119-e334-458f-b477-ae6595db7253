/* Font Awesome Icons - Minimal Version */
@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("../webfonts/fa-solid-900.woff2") format("woff2");
}

.fas,
.fa-solid {
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
}

.fa-users:before {
  content: "\f0c0";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-tachometer-alt:before {
  content: "\f3fd";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-clock:before {
  content: "\f017";
}

.fa-cog:before {
  content: "\f013";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-map:before {
  content: "\f279";
}

.fa-plus:before {
  content: "\f067";
}

.fa-sync-alt:before {
  content: "\f2f1";
}

.fa-search:before {
  content: "\f002";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-shield-alt:before {
  content: "\f3ed";
}

.fa-flask:before {
  content: "\f0c3";
}

/* Common styles for Font Awesome icons */
.fas {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

// سكريبت لإزالة المحفز (trigger) وتحديث كلمة مرور المستخدم المسؤول
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5433,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function fixTriggerAndPassword() {
  try {
    console.log('بدء عملية إصلاح المحفز وكلمة المرور...');
    
    // 1. الحصول على قائمة المحفزات (triggers) في قاعدة البيانات
    console.log('جاري التحقق من المحفزات الموجودة...');
    const triggersQuery = `
      SELECT trigger_name, event_object_table
      FROM information_schema.triggers
      WHERE event_object_table = 'users';
    `;
    
    const triggersResult = await pool.query(triggersQuery);
    
    console.log('المحفزات الموجودة في جدول المستخدمين:');
    if (triggersResult.rows.length === 0) {
      console.log('- لا توجد محفزات');
    } else {
      triggersResult.rows.forEach(trigger => {
        console.log(`- ${trigger.trigger_name} على جدول ${trigger.event_object_table}`);
      });
      
      // 2. إزالة المحفزات
      console.log('\nجاري إزالة المحفزات...');
      for (const trigger of triggersResult.rows) {
        try {
          const dropTriggerQuery = `
            DROP TRIGGER IF EXISTS ${trigger.trigger_name} ON users;
          `;
          await pool.query(dropTriggerQuery);
          console.log(`- تم إزالة المحفز ${trigger.trigger_name} بنجاح`);
        } catch (triggerError) {
          console.error(`- خطأ في إزالة المحفز ${trigger.trigger_name}:`, triggerError);
        }
      }
    }
    
    // 3. تحديث كلمة مرور المستخدم المسؤول
    console.log('\nجاري تحديث كلمة مرور المستخدم المسؤول...');
    
    // كلمة المرور الجديدة: admin
    const newPassword = 'admin';
    
    // تحديث كلمة المرور في قاعدة البيانات
    await pool.query(`
      UPDATE users 
      SET password = $1 
      WHERE username = 'admin'
    `, [newPassword]);
    
    console.log('تم تحديث كلمة مرور المستخدم المسؤول بنجاح!');
    console.log('\nيمكنك الآن تسجيل الدخول باستخدام:');
    console.log('- اسم المستخدم: admin');
    console.log('- كلمة المرور: admin');
    
  } catch (error) {
    console.error('حدث خطأ أثناء العملية:', error);
  } finally {
    await pool.end();
  }
}

fixTriggerAndPassword();

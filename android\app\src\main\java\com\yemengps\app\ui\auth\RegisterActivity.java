package com.yemengps.app.ui.auth;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.data.UserRepository;
import com.yemengps.app.model.User;
import com.yemengps.app.network.ApiClient;
import com.yemengps.app.network.ApiResponse;
import com.yemengps.app.ui.MainActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Activity for user registration
 */
public class RegisterActivity extends AppCompatActivity {

    private TextInputLayout nameLayout;
    private TextInputEditText nameInput;
    private TextInputLayout emailLayout;
    private TextInputEditText emailInput;
    private TextInputLayout phoneLayout;
    private TextInputEditText phoneInput;
    private TextInputLayout passwordLayout;
    private TextInputEditText passwordInput;
    private TextInputLayout confirmPasswordLayout;
    private TextInputEditText confirmPasswordInput;
    private Button registerButton;
    private TextView loginLink;
    private View progressBar;

    private ApiClient apiClient;
    private UserRepository userRepository;
    private PreferenceManager preferenceManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);

        // Initialize toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(R.string.register);

        // Initialize components
        nameLayout = findViewById(R.id.name_layout);
        nameInput = findViewById(R.id.name_input);
        emailLayout = findViewById(R.id.email_layout);
        emailInput = findViewById(R.id.email_input);
        phoneLayout = findViewById(R.id.phone_layout);
        phoneInput = findViewById(R.id.phone_input);
        passwordLayout = findViewById(R.id.password_layout);
        passwordInput = findViewById(R.id.password_input);
        confirmPasswordLayout = findViewById(R.id.confirm_password_layout);
        confirmPasswordInput = findViewById(R.id.confirm_password_input);
        registerButton = findViewById(R.id.register_button);
        loginLink = findViewById(R.id.login_link);
        progressBar = findViewById(R.id.progress_bar);

        // Initialize repositories
        apiClient = YemenGpsApplication.getInstance().getApiClient();
        userRepository = new UserRepository(YemenGpsApplication.getInstance().getDatabase());
        preferenceManager = YemenGpsApplication.getInstance().getPreferenceManager();

        // Set click listeners
        registerButton.setOnClickListener(v -> register());
        loginLink.setOnClickListener(v -> navigateToLogin());
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * Register a new user
     */
    private void register() {
        // Validate input
        String name = nameInput.getText().toString().trim();
        String email = emailInput.getText().toString().trim();
        String phone = phoneInput.getText().toString().trim();
        String password = passwordInput.getText().toString().trim();
        String confirmPassword = confirmPasswordInput.getText().toString().trim();

        if (name.isEmpty()) {
            nameLayout.setError("Please enter your name");
            return;
        }

        if (email.isEmpty()) {
            emailLayout.setError("Please enter your email");
            return;
        }

        if (phone.isEmpty()) {
            phoneLayout.setError("Please enter your phone number");
            return;
        }

        if (password.isEmpty()) {
            passwordLayout.setError("Please enter a password");
            return;
        }

        if (confirmPassword.isEmpty()) {
            confirmPasswordLayout.setError("Please confirm your password");
            return;
        }

        if (!password.equals(confirmPassword)) {
            confirmPasswordLayout.setError("Passwords do not match");
            return;
        }

        // Show progress
        showProgress(true);

        // Get device ID
        String deviceId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        // Create user
        User user = new User(name, email, phone, deviceId);

        // Register with API
        apiClient.registerUser(user, new Callback<ApiResponse<User>>() {
            @Override
            public void onResponse(Call<ApiResponse<User>> call, Response<ApiResponse<User>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    User registeredUser = response.body().getData();
                    if (registeredUser != null) {
                        // Save user to local database
                        userRepository.insert(registeredUser);

                        // Save login info to preferences
                        preferenceManager.saveUserLogin(
                                registeredUser.getId(),
                                registeredUser.getName(),
                                registeredUser.getEmail(),
                                registeredUser.getAuthToken(),
                                registeredUser.isAdmin()
                        );

                        // Navigate to main activity
                        runOnUiThread(() -> {
                            showProgress(false);
                            Toast.makeText(RegisterActivity.this, R.string.success_register, Toast.LENGTH_SHORT).show();
                            navigateToMainActivity();
                        });
                    } else {
                        runOnUiThread(() -> {
                            showProgress(false);
                            Toast.makeText(RegisterActivity.this, R.string.error_register, Toast.LENGTH_LONG).show();
                        });
                    }
                } else {
                    runOnUiThread(() -> {
                        showProgress(false);
                        Toast.makeText(RegisterActivity.this, R.string.error_register, Toast.LENGTH_LONG).show();
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<User>> call, Throwable t) {
                runOnUiThread(() -> {
                    showProgress(false);
                    Toast.makeText(RegisterActivity.this, R.string.error_network, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * Navigate to the login activity
     */
    private void navigateToLogin() {
        finish();
    }

    /**
     * Navigate to the main activity
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * Show or hide the progress bar
     * @param show True to show the progress bar, false to hide it
     */
    private void showProgress(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        registerButton.setEnabled(!show);
        loginLink.setEnabled(!show);
    }
}

// إنشاء بيانات تجريبية لاختبار لوحة التحكم
const { Pool } = require('pg');

// إعداد اتصال قاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'admin',
  port: 5432,
});

async function createSampleData() {
  const client = await pool.connect();

  try {
    console.log('بدء إنشاء البيانات التجريبية...');

    // إنشاء مستخدمين تجريبيين
    console.log('إنشاء مستخدمين تجريبيين...');
    try {
      await client.query(`
        INSERT INTO users (username, password, full_name, email, phone, role_id, is_active, registration_date)
        VALUES
          ('admin', 'yemen123', 'مدير النظام', '<EMAIL>', '967123456789', 1, true, NOW()),
          ('user1', 'password123', 'أحمد محمد', '<EMAIL>', '967123456788', 2, true, NOW()),
          ('user2', 'password123', 'فاطمة علي', '<EMAIL>', '967123456787', 2, true, NOW())
      `);
    } catch (e) {
      console.log('المستخدمون موجودون بالفعل أو حدث خطأ:', e.message);
    }

    // إنشاء تصنيفات تجريبية
    console.log('إنشاء تصنيفات تجريبية...');
    try {
      await client.query(`
        INSERT INTO categories (name, icon, color)
        VALUES
          ('مطاعم', 'fa-utensils', '#FF6B6B'),
          ('مستشفيات', 'fa-hospital', '#4ECDC4'),
          ('محطات وقود', 'fa-gas-pump', '#45B7D1'),
          ('مدارس', 'fa-school', '#96CEB4'),
          ('بنوك', 'fa-university', '#FFEAA7')
      `);
    } catch (e) {
      console.log('التصنيفات موجودة بالفعل أو حدث خطأ:', e.message);
    }

    // إنشاء مواقع تجريبية
    console.log('إنشاء مواقع تجريبية...');
    try {
      await client.query(`
        INSERT INTO locations (name, description, lat, lng, category_id, status, created_at)
        VALUES
          ('مطعم الشاهي', 'مطعم يمني تقليدي', 15.3694, 44.1910, 1, 'active', NOW()),
          ('مستشفى الثورة', 'مستشفى حكومي', 15.3700, 44.1920, 2, 'active', NOW()),
          ('محطة وقود النصر', 'محطة وقود رئيسية', 15.3680, 44.1900, 3, 'active', NOW()),
          ('جامعة صنعاء', 'الجامعة الرئيسية', 15.3720, 44.1950, 4, 'active', NOW()),
          ('البنك الأهلي', 'فرع رئيسي', 15.3690, 44.1915, 5, 'active', NOW())
      `);
    } catch (e) {
      console.log('المواقع موجودة بالفعل أو حدث خطأ:', e.message);
    }

    // إنشاء عملاء تجريبيين
    console.log('إنشاء عملاء تجريبيين...');
    try {
      await client.query(`
        INSERT INTO clients (name, email, phone, address, devicesn, licensen, status, created_at)
        VALUES
          ('شركة النقل السريع', '<EMAIL>', '967111111111', 'صنعاء - شارع الزبيري', 'DEV001', 'LIC001', 'active', NOW()),
          ('مؤسسة التوصيل', '<EMAIL>', '967222222222', 'عدن - كريتر', 'DEV002', 'LIC002', 'active', NOW()),
          ('شركة السياحة', '<EMAIL>', '967333333333', 'تعز - وسط المدينة', 'DEV003', 'LIC003', 'active', NOW())
      `);
    } catch (e) {
      console.log('العملاء موجودون بالفعل أو حدث خطأ:', e.message);
    }

    console.log('تم إنشاء البيانات التجريبية بنجاح!');

  } catch (error) {
    console.error('خطأ في إنشاء البيانات التجريبية:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل الدالة
createSampleData();

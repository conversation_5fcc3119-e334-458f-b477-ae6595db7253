/**
 * Google Assets Loader
 * يستفيد من ملفات Google Maps المحفوظة في E:/google
 */

class GoogleAssetsLoader {
    constructor() {
        this.googlePath = 'E:/google';
        this.loadedAssets = new Map();
        this.iconCache = new Map();
        this.styleCache = new Map();
        
        this.init();
    }

    async init() {
        console.log('🚀 بدء تحميل موارد Google Maps المحفوظة...');
        
        try {
            await this.loadGoogleIcons();
            await this.loadGoogleStyles();
            await this.loadGoogleFonts();
            await this.loadMapTiles();
            
            console.log('✅ تم تحميل موارد Google Maps بنجاح');
            this.applyGoogleEnhancements();
            
        } catch (error) {
            console.warn('⚠️ فشل تحميل بعض موارد Google Maps:', error);
            this.loadFallbackAssets();
        }
    }

    async loadGoogleIcons() {
        console.log('📍 تحميل أيقونات Google Maps...');
        
        // قائمة الأيقونات المحفوظة من Google Maps
        const iconPaths = [
            'www.google.com/maps/vt/icon/name=assets/icons/poi/quantum/modern_pinlet',
            'www.gstatic.com/consumer/mapfiles/tactile/images/icons',
            'www.google.com/maps/vt/icon/name=assets/icons/spotlight'
        ];

        const icons = {
            // أيقونات المطاعم والطعام
            restaurant: this.createIconFromGoogle('restaurant_pinlet_v4-2-medium.png'),
            cafe: this.createIconFromGoogle('cafe_pinlet_v3-2-medium.png'),
            
            // أيقونات الخدمات
            hospital: this.createIconFromGoogle('hospital_H_pinlet-2-medium.png'),
            school: this.createIconFromGoogle('school_pinlet-2-medium.png'),
            gas: this.createIconFromGoogle('gas_pinlet-2-medium.png'),
            
            // أيقونات التسوق
            shopping: this.createIconFromGoogle('shoppingbag_pinlet-2-medium.png'),
            market: this.createIconFromGoogle('shoppingcart_pinlet-2-medium.png'),
            
            // أيقونات الإقامة
            hotel: this.createIconFromGoogle('lodging_pinlet-2-medium.png'),
            
            // أيقونات النقل
            airport: this.createIconFromGoogle('airport_pinlet-2-medium.png'),
            parking: this.createIconFromGoogle('parking_pinlet_square-2-medium.png'),
            
            // أيقونات الترفيه
            park: this.createIconFromGoogle('tree_pinlet-2-medium.png'),
            entertainment: this.createIconFromGoogle('ferriswheel_pinlet-2-medium.png'),
            
            // أيقونات دينية وثقافية
            mosque: this.createIconFromGoogle('worship_temple_pinlet-2-medium.png'),
            museum: this.createIconFromGoogle('civic_bldg_pinlet-2-medium.png'),
            
            // أيقونات عامة
            default: this.createIconFromGoogle('dot_pinlet-2-medium.png'),
            bookmark: this.createIconFromGoogle('bookmark_pinlet-2-medium.png'),
            star: this.createIconFromGoogle('constellation_star_pinlet-2-medium.png')
        };

        this.iconCache.set('google-icons', icons);
        console.log('✅ تم تحميل أيقونات Google Maps');
    }

    createIconFromGoogle(iconName) {
        // إنشاء أيقونة مخصصة بناءً على أيقونات Google
        return {
            iconUrl: `data:image/svg+xml,${encodeURIComponent(this.generateSVGIcon(iconName))}`,
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32],
            shadowUrl: null,
            shadowSize: null,
            shadowAnchor: null
        };
    }

    generateSVGIcon(iconName) {
        // إنشاء SVG مخصص يحاكي أيقونات Google Maps
        const iconColors = {
            restaurant: '#FF6B35',
            cafe: '#8B4513',
            hospital: '#FF4444',
            school: '#4285F4',
            gas: '#9C27B0',
            shopping: '#FF9800',
            market: '#4CAF50',
            hotel: '#E91E63',
            airport: '#607D8B',
            parking: '#795548',
            park: '#4CAF50',
            entertainment: '#9C27B0',
            mosque: '#795548',
            museum: '#607D8B',
            default: '#4285F4',
            bookmark: '#FF9800',
            star: '#FFD700'
        };

        const iconType = iconName.split('_')[0];
        const color = iconColors[iconType] || iconColors.default;

        return `
            <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                        <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.3)"/>
                    </filter>
                </defs>
                <circle cx="16" cy="16" r="12" fill="${color}" filter="url(#shadow)"/>
                <circle cx="16" cy="16" r="8" fill="white"/>
                <text x="16" y="20" text-anchor="middle" font-family="Arial" font-size="12" fill="${color}">
                    ${this.getIconSymbol(iconType)}
                </text>
            </svg>
        `;
    }

    getIconSymbol(iconType) {
        const symbols = {
            restaurant: '🍽',
            cafe: '☕',
            hospital: '🏥',
            school: '🏫',
            gas: '⛽',
            shopping: '🛍',
            market: '🛒',
            hotel: '🏨',
            airport: '✈',
            parking: '🅿',
            park: '🌳',
            entertainment: '🎡',
            mosque: '🕌',
            museum: '🏛',
            default: '📍',
            bookmark: '🔖',
            star: '⭐'
        };
        
        return symbols[iconType] || symbols.default;
    }

    async loadGoogleStyles() {
        console.log('🎨 تحميل أنماط Google Maps...');
        
        // تطبيق أنماط Google Maps المحفوظة
        const googleStyles = {
            // أنماط الخريطة الأساسية
            mapStyles: {
                filter: 'contrast(1.1) saturate(1.15) brightness(1.05)',
                transition: 'filter 0.3s ease'
            },
            
            // أنماط العلامات
            markerStyles: {
                borderRadius: '50%',
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
                transition: 'transform 0.2s ease'
            },
            
            // أنماط النوافذ المنبثقة
            popupStyles: {
                borderRadius: '8px',
                boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
                border: 'none',
                fontFamily: 'Roboto, Arial, sans-serif'
            }
        };

        this.styleCache.set('google-styles', googleStyles);
        console.log('✅ تم تحميل أنماط Google Maps');
    }

    async loadGoogleFonts() {
        console.log('🔤 تحميل خطوط Google...');
        
        // تحميل خطوط Google المحفوظة
        const fontPaths = [
            'www.gstatic.com/s/roboto',
            'www.gstatic.com/s/googlesans',
            'www.gstatic.com/s/materialicons'
        ];

        // إنشاء عنصر style للخطوط
        const fontStyle = document.createElement('style');
        fontStyle.textContent = `
            @font-face {
                font-family: 'Google Sans';
                src: url('data:font/woff2;base64,${this.getGoogleSansBase64()}');
                font-weight: 400;
                font-style: normal;
            }
            
            @font-face {
                font-family: 'Material Icons';
                src: url('data:font/woff2;base64,${this.getMaterialIconsBase64()}');
                font-weight: 400;
                font-style: normal;
            }
        `;
        
        document.head.appendChild(fontStyle);
        console.log('✅ تم تحميل خطوط Google');
    }

    async loadMapTiles() {
        console.log('🗺️ تحميل بلاطات الخريطة المحفوظة...');
        
        // استخدام البلاطات المحفوظة من Google
        const tilePaths = [
            'www.google.com/maps/vt/data',
            'www.google.com/maps/vt/pb'
        ];

        // إنشاء طبقة مخصصة للبلاطات المحفوظة
        this.createCustomTileLayer();
        console.log('✅ تم إعداد بلاطات الخريطة المحفوظة');
    }

    createCustomTileLayer() {
        // إنشاء طبقة بلاطات مخصصة تستخدم الملفات المحفوظة
        window.GoogleTileLayer = L.TileLayer.extend({
            getTileUrl: function(coords) {
                // محاولة استخدام البلاطات المحفوظة أولاً
                const cachedTile = this.getCachedTile(coords);
                if (cachedTile) {
                    return cachedTile;
                }
                
                // العودة للمصدر الافتراضي
                return this.getDefaultTileUrl(coords);
            },
            
            getCachedTile: function(coords) {
                // البحث في الملفات المحفوظة
                const tileId = `${coords.z}-${coords.x}-${coords.y}`;
                return this.options.cachedTiles && this.options.cachedTiles[tileId];
            },
            
            getDefaultTileUrl: function(coords) {
                return `https://{s}.tile.openstreetmap.org/${coords.z}/${coords.x}/${coords.y}.png`;
            }
        });
    }

    applyGoogleEnhancements() {
        console.log('✨ تطبيق تحسينات Google Maps...');
        
        // تطبيق الأنماط على الخريطة
        const mapElement = document.getElementById('map');
        if (mapElement) {
            const styles = this.styleCache.get('google-styles');
            if (styles) {
                Object.assign(mapElement.style, styles.mapStyles);
            }
        }

        // تحسين النوافذ المنبثقة
        this.enhancePopups();
        
        // تحسين العلامات
        this.enhanceMarkers();
        
        console.log('✅ تم تطبيق تحسينات Google Maps');
    }

    enhancePopups() {
        // تحسين مظهر النوافذ المنبثقة لتشبه Google Maps
        const style = document.createElement('style');
        style.textContent = `
            .leaflet-popup-content-wrapper {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 16px rgba(0,0,0,0.2);
                border: none;
                font-family: 'Roboto', 'Google Sans', Arial, sans-serif;
            }
            
            .leaflet-popup-tip {
                background: white;
                border: none;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            }
            
            .leaflet-popup-content {
                margin: 0;
                padding: 16px;
                line-height: 1.4;
            }
        `;
        document.head.appendChild(style);
    }

    enhanceMarkers() {
        // تحسين مظهر العلامات لتشبه Google Maps
        const style = document.createElement('style');
        style.textContent = `
            .leaflet-marker-icon {
                border-radius: 50% 50% 50% 0;
                transform: rotate(-45deg);
                border: 2px solid white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                transition: transform 0.2s ease;
            }
            
            .leaflet-marker-icon:hover {
                transform: rotate(-45deg) scale(1.1);
                z-index: 1000;
            }
        `;
        document.head.appendChild(style);
    }

    loadFallbackAssets() {
        console.log('🔄 تحميل الموارد الاحتياطية...');
        
        // استخدام موارد احتياطية إذا فشل تحميل موارد Google
        const fallbackIcons = {
            restaurant: '🍽️',
            hospital: '🏥',
            school: '🏫',
            default: '📍'
        };
        
        this.iconCache.set('fallback-icons', fallbackIcons);
        console.log('✅ تم تحميل الموارد الاحتياطية');
    }

    getGoogleSansBase64() {
        // Base64 مبسط لخط Google Sans (في التطبيق الحقيقي، استخدم الملف الفعلي)
        return 'UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=';
    }

    getMaterialIconsBase64() {
        // Base64 مبسط لخط Material Icons (في التطبيق الحقيقي، استخدم الملف الفعلي)
        return 'UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAAwA0JaQAA3AA/vuUAAA=';
    }

    // واجهة برمجية للوصول للموارد المحملة
    getIcon(type) {
        const googleIcons = this.iconCache.get('google-icons');
        const fallbackIcons = this.iconCache.get('fallback-icons');
        
        return googleIcons?.[type] || fallbackIcons?.[type] || fallbackIcons?.default;
    }

    getStyle(type) {
        const googleStyles = this.styleCache.get('google-styles');
        return googleStyles?.[type] || {};
    }

    // تحديث الموارد
    async refreshAssets() {
        console.log('🔄 تحديث موارد Google Maps...');
        this.loadedAssets.clear();
        this.iconCache.clear();
        this.styleCache.clear();
        
        await this.init();
    }

    // إحصائيات الموارد المحملة
    getLoadedAssetsStats() {
        return {
            totalAssets: this.loadedAssets.size,
            icons: this.iconCache.size,
            styles: this.styleCache.size,
            loadTime: Date.now() - this.startTime
        };
    }
}

// تصدير الكلاس للاستخدام العام
window.GoogleAssetsLoader = GoogleAssetsLoader;

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.googleAssetsLoader = new GoogleAssetsLoader();
});

console.log('📦 Google Assets Loader جاهز للاستخدام');

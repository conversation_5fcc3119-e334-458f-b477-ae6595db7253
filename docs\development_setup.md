# Yemen GPS Navigation System - Development Setup Guide

This document provides instructions for setting up the development environment for the Yemen GPS Navigation System.

## Prerequisites

### Java Development Kit (JDK)

The project uses Red Hat Build of OpenJDK 8. Follow these steps to install it:

1. Download the Red Hat Build of OpenJDK from the official website:
   - Visit [Red Hat OpenJDK Download Page](https://developers.redhat.com/products/openjdk/download)
   - Select the appropriate version for your operating system (Windows, macOS, or Linux)
   - Download the JDK 8 package

2. Install the JDK:
   - **Windows**: Run the installer (.msi file) and follow the installation wizard
   - **macOS**: Open the .dmg file and follow the installation instructions
   - **Linux**: Extract the archive and set up environment variables

3. Verify the installation:
   ```
   java -version
   ```
   You should see output indicating that you're using Red Hat OpenJDK.

### Android Studio

1. Download Android Studio from [developer.android.com/studio](https://developer.android.com/studio)
2. Install Android Studio following the installation wizard
3. During setup, ensure you install:
   - Android SDK
   - Android SDK Platform
   - Android Virtual Device

### Configure Android Studio to use Red Hat OpenJDK

1. Open Android Studio
2. Go to File > Settings (on Windows/Linux) or Android Studio > Preferences (on macOS)
3. Navigate to Build, Execution, Deployment > Build Tools > Gradle
4. Set "Gradle JDK" to the Red Hat OpenJDK installation
5. Click Apply and OK

## Project Setup

### Clone the Repository

```
git clone https://github.com/your-organization/yemen-gps.git
cd yemen-gps
```

### Import the Project

1. Open Android Studio
2. Select "Open an existing Android Studio project"
3. Navigate to the cloned repository and select it
4. Wait for the project to sync and index

### Configure Google Maps API Key

1. Obtain a Google Maps API key from the [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the following APIs:
   - Maps SDK for Android
   - Places API
   - Directions API
   - Geocoding API

3. Add your API key to `android/app/src/main/res/values/strings.xml`:
   ```xml
   <string name="google_maps_key">YOUR_API_KEY_HERE</string>
   ```

## Building the Project

### From Android Studio

1. Click the "Build" menu
2. Select "Make Project" (or press Ctrl+F9 / Cmd+F9)

### From Command Line

Navigate to the project directory and run:

```
./gradlew assembleDebug
```

## Running the Application

### On an Emulator

1. In Android Studio, click the "AVD Manager" button
2. Create a new virtual device if you don't have one
3. Select a device and click "Start"
4. Once the emulator is running, click the "Run" button in Android Studio

### On a Physical Device

1. Enable Developer Options and USB Debugging on your device
2. Connect your device to your computer via USB
3. Select your device from the dropdown menu in Android Studio
4. Click the "Run" button

## Troubleshooting

### Gradle Sync Issues

If you encounter Gradle sync issues:

1. Go to File > Settings > Build, Execution, Deployment > Gradle
2. Make sure "Gradle JDK" is set to the Red Hat OpenJDK
3. Try invalidating caches: File > Invalidate Caches / Restart

### Build Errors

If you encounter build errors related to Java version:

1. Check that the project is configured to use Java 8:
   - The `build.gradle` file should include:
     ```groovy
     compileOptions {
         sourceCompatibility JavaVersion.VERSION_1_8
         targetCompatibility JavaVersion.VERSION_1_8
     }
     ```

2. Verify that Android Studio is using the correct JDK:
   - File > Project Structure > SDK Location
   - Ensure "JDK Location" points to the Red Hat OpenJDK

### Google Maps Not Loading

If Google Maps doesn't load in the application:

1. Verify that your API key is correctly set in `strings.xml`
2. Check that the API key has the necessary permissions in the Google Cloud Console
3. Ensure the device or emulator has Google Play Services installed

## Development Guidelines

### Code Style

- Follow the [Android Code Style Guidelines](https://source.android.com/setup/contribute/code-style)
- Use meaningful variable and method names
- Add comments for complex logic

### Git Workflow

1. Create a new branch for each feature or bug fix
2. Make small, focused commits
3. Write descriptive commit messages
4. Create a pull request for review before merging

### Testing

- Write unit tests for repository and utility classes
- Write UI tests for critical user flows
- Test on multiple device sizes and API levels

## Additional Resources

- [Android Developer Documentation](https://developer.android.com/docs)
- [Google Maps API Documentation](https://developers.google.com/maps/documentation/android-sdk/overview)
- [Room Persistence Library](https://developer.android.com/training/data-storage/room)
- [Red Hat OpenJDK Documentation](https://access.redhat.com/documentation/en-us/openjdk)

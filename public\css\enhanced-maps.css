/* تصميم لوحة معلومات الموقع */
.location-info-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 350px;
  max-height: 80vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  display: none;
  z-index: 1000;
  direction: rtl;
}

.location-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.location-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.location-category {
  display: inline-block;
  padding: 3px 8px;
  background-color: #f0f0f0;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.close-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.location-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.location-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-nav {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.nav-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  margin: 0 2px;
}

.location-details {
  padding: 15px;
}

.detail-item {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.detail-item i {
  width: 20px;
  margin-left: 10px;
  color: #1a73e8;
}

.weather-info {
  padding: 15px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
}

.weather-icon {
  width: 50px;
  height: 50px;
  margin-left: 10px;
}

.weather-details {
  flex: 1;
}

.temperature {
  font-size: 18px;
  font-weight: bold;
}

.nearby-places {
  padding: 15px;
  border-top: 1px solid #eee;
}

.nearby-place {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
}

.place-icon {
  width: 30px;
  height: 30px;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.place-details {
  flex: 1;
}

.place-name {
  font-weight: bold;
}

.place-distance {
  font-size: 12px;
  color: #666;
}

.location-actions {
  display: flex;
  padding: 15px;
  border-top: 1px solid #eee;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  color: #1a73e8;
}

.action-btn i {
  font-size: 18px;
  margin-bottom: 5px;
}

/* تصميم لوحة معلومات المسار */
.route-info-panel {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 350px;
  max-height: 60vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  display: none;
  z-index: 1000;
  direction: rtl;
}

.route-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.route-summary {
  display: flex;
  padding: 15px;
  background-color: #f9f9f9;
}

.route-distance, .route-duration {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.route-distance i, .route-duration i {
  font-size: 18px;
  color: #1a73e8;
  margin-bottom: 5px;
}

.route-steps {
  padding: 15px;
}

.route-steps ol {
  padding-right: 20px;
  margin: 0;
}

.route-steps li {
  margin-bottom: 10px;
  line-height: 1.5;
}

.route-actions {
  display: flex;
  padding: 15px;
  border-top: 1px solid #eee;
}

/* تصميم قسم المسارات البديلة */
.route-alternatives {
  padding: 15px;
  border-top: 1px solid #eee;
}

.route-alternatives h4 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.alternatives-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alternative-route {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.alternative-route:hover {
  background-color: #f0f7ff;
}

.alternative-route.active {
  background-color: #e8f0fe;
  border: 1px solid #1a73e8;
}

.alternative-route .route-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.alternative-route .route-distance {
  font-weight: bold;
  color: #333;
  align-items: flex-start;
}

.alternative-route .route-duration {
  font-size: 14px;
  color: #666;
  align-items: flex-start;
}

.alternative-route .route-preview {
  width: 50px;
  height: 6px;
  border-radius: 3px;
  margin-right: 10px;
}

/* تحسينات على علامات المسار */
.start-marker .marker-icon, .end-marker .marker-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #1a73e8;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.start-marker .marker-icon::before {
  content: '';
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1a73e8;
}

.end-marker .marker-icon::before {
  content: '';
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #e53935;
}

.start-marker .marker-icon::after, .end-marker .marker-icon::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

/* تصميم أزرار وسائل النقل */
.travel-mode-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  z-index: 1000;
}

.travel-mode-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
}

.travel-mode-btn.active {
  color: #1a73e8;
  background-color: #f0f7ff;
}

.travel-mode-btn i {
  font-size: 18px;
  margin-bottom: 5px;
}

/* أنماط تحسين الخرائط */

/* أنماط مربع حوار تحديد الموقع */
.modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
  direction: rtl;
}

.modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 0;
  border: 1px solid #888;
  border-radius: 8px;
  width: 80%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s ease-in-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  background-color: #4CAF50;
  color: white;
  padding: 15px 20px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  color: white;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
}

.location-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 15px;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: #f0f0f0;
  color: #333;
}

.action-btn:hover {
  background-color: #e0e0e0;
}

.action-btn.primary {
  background-color: #4CAF50;
  color: white;
}

.action-btn.primary:hover {
  background-color: #45a049;
}

.city-selection {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-top: 10px;
}

.city-selection label {
  font-weight: bold;
}

.city-selection select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  font-size: 1rem;
  margin-bottom: 10px;
}

/* تصميم أزرار الخرائط دون اتصال */
.offline-maps-controls {
  position: absolute;
  bottom: 100px;
  left: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.control-btn {
  display: flex;
  align-items: center;
  padding: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
}

.control-btn i {
  margin-left: 8px;
}

/* تصميم النافذة المنبثقة */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
}

.modal-content {
  position: relative;
  margin: 10% auto;
  width: 80%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  direction: rtl;
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.modal-body {
  padding: 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.regions-list {
  display: flex;
  flex-direction: column;
}

.region-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.region-info {
  flex: 1;
}

.region-name {
  font-weight: bold;
}

.region-meta {
  font-size: 12px;
  color: #666;
}

.region-actions {
  display: flex;
}

.region-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #1a73e8;
  margin-right: 10px;
}

/* مؤشر التحميل */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  color: white;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid white;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  width: 200px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  margin-top: 10px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: white;
  width: 0;
  transition: width 0.3s;
}

/* الإشعارات */
.notification {
  position: fixed;
  bottom: 20px;
  left: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 4000;
  direction: rtl;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s, opacity 0.3s;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.notification.success {
  background-color: #d4edda;
  color: #155724;
}

.notification.error {
  background-color: #f8d7da;
  color: #721c24;
}

.notification.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.notification.warning {
  background-color: #fff3cd;
  color: #856404;
}

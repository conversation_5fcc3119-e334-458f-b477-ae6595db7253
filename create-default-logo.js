// سكريبت لإنشاء صورة شعار افتراضية
const fs = require('fs');
const { createCanvas } = require('canvas');

// التأكد من وجود مجلد img
const imgDir = './public/img';
if (!fs.existsSync(imgDir)) {
  fs.mkdirSync(imgDir, { recursive: true });
  console.log(`تم إنشاء مجلد ${imgDir}`);
}

// إنشاء صورة الشعار
const width = 200;
const height = 100;
const canvas = createCanvas(width, height);
const context = canvas.getContext('2d');

// خلفية بيضاء
context.fillStyle = '#ffffff';
context.fillRect(0, 0, width, height);

// رسم إطار
context.strokeStyle = '#cccccc';
context.lineWidth = 2;
context.strokeRect(5, 5, width - 10, height - 10);

// كتابة نص الشعار
context.font = 'bold 24px Arial';
context.textAlign = 'center';
context.textBaseline = 'middle';
context.fillStyle = '#333333';
context.fillText('يمن GPS', width / 2, height / 2 - 10);

// كتابة نص ثانوي
context.font = '16px Arial';
context.fillStyle = '#666666';
context.fillText('Yemen GPS', width / 2, height / 2 + 15);

// حفظ الصورة
const buffer = canvas.toBuffer('image/png');
fs.writeFileSync(`${imgDir}/default-logo.png`, buffer);
console.log(`تم إنشاء صورة الشعار الافتراضية في ${imgDir}/default-logo.png`);

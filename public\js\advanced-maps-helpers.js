// يمن ناف - دوال مساعدة للخرائط المتقدمة

// دوال مساعدة للتعامل مع نقاط الاهتمام
function setupSharingOptions() {
    console.log("تم إعداد خيارات المشاركة");
    // يتم تنفيذها في الصفحة HTML
}

function setupLocationInfo() {
    // إضافة مستمع للضغط على الخريطة
    if (advancedMap) {
        advancedMap.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(6);
            const lng = e.latlng.lng.toFixed(6);
            L.popup()
                .setLatLng(e.latlng)
                .setContent(`<div dir="rtl"><strong>الإحداثيات:</strong><br>${lat}, ${lng}</div>`)
                .openOn(advancedMap);
        });
    }
}

function setupIsotherms() {
    console.log("تم إعداد خطوط التساوي");
    // سيتم تطويرها لاحقاً
}

function setupMapExport() {
    console.log("تم إعداد تصدير الخريطة");
    // سيتم تطويرها لاحقاً
}

function setupGeolocation() {
    if (advancedMap) {
        // إضافة زر تحديد الموقع
        L.control.locate({
            position: 'topright',
            strings: {
                title: "إظهار موقعي الحالي"
            },
            locateOptions: {
                enableHighAccuracy: true
            }
        }).addTo(advancedMap);
    }
}

// تحميل نقاط اهتمام افتراضية للعرض
function loadDefaultPOIs() {
    if (!advancedMap || !markerClusters) return;
    
    // إنشاء بعض نقاط الاهتمام في مناطق مختلفة من اليمن
    const defaultPOIs = [
        { lat: 15.3694, lng: 44.1910, name: "صنعاء", type: "مدينة", icon: "🏙️" },
        { lat: 12.7794, lng: 45.0367, name: "عدن", type: "مدينة", icon: "🏙️" },
        { lat: 14.5482, lng: 44.4009, name: "ذمار", type: "مدينة", icon: "🏙️" },
        { lat: 14.7979, lng: 43.9876, name: "الحديدة", type: "مدينة", icon: "🏙️" },
        { lat: 14.8488, lng: 42.9512, name: "زبيد", type: "مدينة تاريخية", icon: "🏛️" },
        { lat: 15.4542, lng: 43.2002, name: "المخا", type: "ميناء", icon: "🚢" },
        { lat: 13.9646, lng: 45.4801, name: "المكلا", type: "ميناء", icon: "🚢" },
        { lat: 15.9175, lng: 43.5909, name: "نقطة تفتيش الكثيب", type: "نقطة تفتيش", icon: "🚧" },
        { lat: 15.6547, lng: 44.2271, name: "نقطة تفتيش شملان", type: "نقطة تفتيش", icon: "🚧" },
        { lat: 15.3401, lng: 44.2136, name: "المطار الدولي", type: "مطار", icon: "✈️" },
        { lat: 12.8298, lng: 45.0284, name: "مطار عدن", type: "مطار", icon: "✈️" },
        { lat: 15.3858, lng: 44.2138, name: "نقطة ازدحام شارع هائل", type: "ازدحام", icon: "🚗" },
        { lat: 15.3564, lng: 44.2075, name: "ازدحام الدائري", type: "ازدحام", icon: "🚗" },
        { lat: 15.3751, lng: 44.1895, name: "حفرة كبيرة", type: "حفرة", icon: "🕳️" },
        { lat: 15.3629, lng: 44.1986, name: "مطب صناعي", type: "مطب", icon: "📌" }
    ];
    
    // إضافة نقاط الاهتمام إلى الخريطة
    defaultPOIs.forEach(poi => {
        const marker = L.marker([poi.lat, poi.lng], {
            icon: L.divIcon({
                html: `<div class="poi-icon">${poi.icon}</div>`,
                className: `poi-marker poi-type-${poi.type.replace(/ /g, '-')}`,
                iconSize: [30, 30]
            })
        });
        
        marker.bindPopup(`
            <div dir="rtl" class="poi-popup">
                <h3>${poi.name}</h3>
                <p>النوع: ${poi.type}</p>
                <p>الإحداثيات: ${poi.lat.toFixed(4)}, ${poi.lng.toFixed(4)}</p>
            </div>
        `);
        
        markerClusters.addLayer(marker);
    });
    
    // إضافة CSS لتخصيص العلامات
    const style = document.createElement('style');
    style.textContent = `
        .poi-marker {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: white;
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
        }
        
        .poi-icon {
            font-size: 16px;
            text-align: center;
        }
        
        .poi-popup {
            text-align: right;
        }
        
        .poi-popup h3 {
            margin: 0 0 5px 0;
            color: #4CAF50;
        }
        
        .poi-popup p {
            margin: 3px 0;
        }
        
        .poi-type-مدينة {
            border: 2px solid #4CAF50;
        }
        
        .poi-type-مدينة-تاريخية {
            border: 2px solid #9C27B0;
        }
        
        .poi-type-ميناء {
            border: 2px solid #2196F3;
        }
        
        .poi-type-نقطة-تفتيش {
            border: 2px solid #FFC107;
        }
        
        .poi-type-مطار {
            border: 2px solid #607D8B;
        }
        
        .poi-type-ازدحام {
            border: 2px solid #F44336;
        }
        
        .poi-type-حفرة {
            border: 2px solid #795548;
        }
        
        .poi-type-مطب {
            border: 2px solid #FF9800;
        }
    `;
    document.head.appendChild(style);
    
    console.log("تم تحميل نقاط الاهتمام الافتراضية");
}

// تصدير الدوال لاستخدامها من ملفات أخرى
window.setupSharingOptions = setupSharingOptions;
window.setupLocationInfo = setupLocationInfo;
window.setupIsotherms = setupIsotherms;
window.setupMapExport = setupMapExport;
window.setupGeolocation = setupGeolocation;
window.loadDefaultPOIs = loadDefaultPOIs;

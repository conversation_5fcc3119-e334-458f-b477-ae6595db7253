// ملف للتحقق من الجداول في قاعدة البيانات
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('محاولة الاتصال بقاعدة البيانات باستخدام:');
console.log(`host: ${dbConfig.host}, port: ${dbConfig.port}, database: ${dbConfig.database}, user: ${dbConfig.user}`);

const pool = new Pool(dbConfig);

async function checkTables() {
    try {
        // الحصول على قائمة الجداول في قاعدة البيانات
        const tablesQuery = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        `);

        console.log('\nالجداول الموجودة في قاعدة البيانات:');
        if (tablesQuery.rows.length === 0) {
            console.log('لا توجد جداول في قاعدة البيانات.');
        } else {
            tablesQuery.rows.forEach(table => {
                console.log(table.table_name);
            });
        }

        // التحقق من جداول محددة
        const specificTables = ['users', 'clients', 'categories'];
        console.log('\nالتحقق من الجداول المحددة:');
        
        for (const tableName of specificTables) {
            const tableCheck = await pool.query(`
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = $1
                );
            `, [tableName]);
            
            const tableExists = tableCheck.rows[0].exists;
            console.log(`جدول ${tableName}: ${tableExists ? 'موجود' : 'غير موجود'}`);
            
            if (tableExists) {
                // الحصول على عدد الصفوف في الجدول
                const countQuery = await pool.query(`SELECT COUNT(*) FROM ${tableName};`);
                const rowCount = countQuery.rows[0].count;
                console.log(`  - عدد الصفوف: ${rowCount}`);
                
                // الحصول على هيكل الجدول
                const structureQuery = await pool.query(`
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = $1;
                `, [tableName]);
                
                console.log(`  - هيكل الجدول:`);
                structureQuery.rows.forEach(column => {
                    console.log(`    * ${column.column_name}: ${column.data_type}`);
                });
            }
        }
    } catch (err) {
        console.error('خطأ في التحقق من الجداول:', err.message);
    } finally {
        // إغلاق الاتصال
        pool.end();
    }
}

checkTables();

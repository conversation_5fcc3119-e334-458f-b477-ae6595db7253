// Yemen Nav Backend Server - نسخة مصححة
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const db = require('./backend/src/database');
const userService = require('./backend/src/users');

const app = express();

// CORS
app.use(cors());

// JSON parsing
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Set up a logger middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Middleware للتحقق من صحة JWT
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: 'غير مصرح به: يرجى تسجيل الدخول' });
  }
  
  try {
    const user = userService.verifyToken(token);
    req.user = user;
    next();
  } catch (err) {
    return res.status(403).json({ message: 'رمز غير صالح أو منتهي الصلاحية' });
  }
}

// التحقق من صلاحيات المشرف
function authenticateAdmin(req, res, next) {
  if (!req.user || (req.user.role !== 1 && req.user.role !== 3)) { // 1 للمدير، 3 للمطور
    return res.status(403).json({ message: 'غير مصرح لك بالوصول إلى هذه الصفحة' });
  }
  next();
}

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to Yemen Nav API' });
});

// الحصول على جميع المستخدمين
app.get('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const users = await userService.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة المستخدمين:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
  }
});

// إضافة مستخدم جديد (للمشرفين)
app.post('/api/admin/users', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, fullName, accountType, roleId } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: 'اسم المستخدم وكلمة المرور مطلوبان' });
    }
    
    const user = await userService.createUser(username, password, email, fullName, accountType);
    
    // تحديث الدور إذا تم تحديده
    if (roleId) {
      await userService.updateUserByAdmin(user.user_id, { roleId });
    }
    
    res.status(201).json({ 
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        accountType: user.account_type,
        roleId: user.role_id
      }
    });
  } catch (err) {
    console.error('خطأ في إنشاء المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// تحديث مستخدم (للمشرفين)
app.put('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    const { username, email, fullName, password, roleId, isActive } = req.body;
    
    const userData = { username, email, fullName, password, roleId, isActive };
    const updatedUser = await userService.updateUserByAdmin(userId, userData);
    
    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        userId: updatedUser.user_id,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.full_name,
        accountType: updatedUser.account_type,
        roleId: updatedUser.role_id,
        isActive: updatedUser.is_active
      }
    });
  } catch (err) {
    console.error('خطأ في تحديث المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// حذف مستخدم (للمشرفين)
app.delete('/api/admin/users/:userId', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // التحقق من أن المستخدم لا يحاول حذف نفسه
    if (req.user.userId == userId) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الحالي' });
    }
    
    await userService.deleteUser(userId);
    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (err) {
    console.error('خطأ في حذف المستخدم:', err.message);
    res.status(400).json({ message: err.message });
  }
});

// الحصول على قائمة الأدوار
app.get('/api/admin/roles', authenticateToken, authenticateAdmin, async (req, res) => {
  try {
    const roles = await userService.getAllRoles();
    res.json(roles);
  } catch (err) {
    console.error('خطأ في الحصول على قائمة الأدوار:', err.message);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب بيانات الأدوار' });
  }
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// إغلاق الاتصال بقاعدة البيانات عند إيقاف الخادم
function gracefulShutdown() {
  console.log('إغلاق الاتصال بقاعدة البيانات...');
  db.pool.end(() => {
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
    process.exit(0);
  });
}

// التقاط إشارات إيقاف التشغيل
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// تشغيل الخادم
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

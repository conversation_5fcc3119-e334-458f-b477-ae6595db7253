package com.yemengps.app.data.converters;

import androidx.room.TypeConverter;

import com.yemengps.app.model.LocationType;

/**
 * Type converter for Room database to convert between LocationType and String
 */
public class LocationTypeConverter {
    
    /**
     * Convert from LocationType to String
     * @param locationType The location type to convert
     * @return The string representation of the location type, or null if locationType is null
     */
    @TypeConverter
    public static String fromLocationType(LocationType locationType) {
        return locationType == null ? null : locationType.name();
    }
    
    /**
     * Convert from String to LocationType
     * @param value The string representation of the location type
     * @return The location type, or null if value is null
     */
    @TypeConverter
    public static LocationType toLocationType(String value) {
        return value == null ? null : LocationType.valueOf(value);
    }
}

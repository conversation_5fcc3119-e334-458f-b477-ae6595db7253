package com.yemengps.app.ui.adapters;

import android.text.format.Formatter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.yemengps.app.R;
import com.yemengps.app.offline.OfflineMapManager;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying offline regions in a RecyclerView
 */
public class OfflineRegionAdapter extends RecyclerView.Adapter<OfflineRegionAdapter.ViewHolder> {

    private List<OfflineMapManager.OfflineRegion> regions;
    private final RegionActionListener actionListener;
    private final DateFormat dateFormat;

    /**
     * Interface for region actions
     */
    public interface RegionActionListener {
        /**
         * Called when a region action is performed
         * @param region The region
         * @param action The action (update, delete, view)
         */
        void onRegionAction(OfflineMapManager.OfflineRegion region, String action);
    }

    /**
     * Constructor
     * @param regions The list of regions
     * @param actionListener The action listener
     */
    public OfflineRegionAdapter(List<OfflineMapManager.OfflineRegion> regions, RegionActionListener actionListener) {
        this.regions = regions;
        this.actionListener = actionListener;
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
    }

    /**
     * Update the list of regions
     * @param regions The new list of regions
     */
    public void updateRegions(List<OfflineMapManager.OfflineRegion> regions) {
        this.regions = regions;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_offline_region, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        OfflineMapManager.OfflineRegion region = regions.get(position);
        
        // Set region name
        holder.regionName.setText(region.name);
        
        // Set region size
        String size = Formatter.formatFileSize(holder.itemView.getContext(), region.sizeBytes);
        holder.regionSize.setText(size);
        
        // Set download date
        String downloadDate = dateFormat.format(region.downloadDate);
        holder.downloadDate.setText(holder.itemView.getContext().getString(R.string.downloaded_on, downloadDate));
        
        // Set last update date
        String lastUpdateDate = dateFormat.format(region.lastUpdateDate);
        holder.lastUpdateDate.setText(holder.itemView.getContext().getString(R.string.last_updated_on, lastUpdateDate));
        
        // Set button click listeners
        holder.viewButton.setOnClickListener(v -> {
            if (actionListener != null) {
                actionListener.onRegionAction(region, "view");
            }
        });
        
        holder.updateButton.setOnClickListener(v -> {
            if (actionListener != null) {
                actionListener.onRegionAction(region, "update");
            }
        });
        
        holder.deleteButton.setOnClickListener(v -> {
            if (actionListener != null) {
                actionListener.onRegionAction(region, "delete");
            }
        });
    }

    @Override
    public int getItemCount() {
        return regions.size();
    }

    /**
     * ViewHolder for offline region items
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        final TextView regionName;
        final TextView regionSize;
        final TextView downloadDate;
        final TextView lastUpdateDate;
        final Button viewButton;
        final Button updateButton;
        final Button deleteButton;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            regionName = itemView.findViewById(R.id.region_name);
            regionSize = itemView.findViewById(R.id.region_size);
            downloadDate = itemView.findViewById(R.id.download_date);
            lastUpdateDate = itemView.findViewById(R.id.last_update_date);
            viewButton = itemView.findViewById(R.id.view_button);
            updateButton = itemView.findViewById(R.id.update_button);
            deleteButton = itemView.findViewById(R.id.delete_button);
        }
    }
}

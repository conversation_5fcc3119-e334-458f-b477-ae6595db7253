<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة تحكم يمن GPS</title>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- <PERSON><PERSON>wal Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 15px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background-color: #f8f9fa;
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .logo {
            max-width: 150px;
            margin-bottom: 15px;
        }

        .card-body {
            padding: 20px;
        }

        .form-control {
            border-radius: 5px;
        }

        .btn-primary {
            border-radius: 5px;
            width: 100%;
            padding: 10px;
            font-weight: 500;
        }

        .password-container {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
        }

        #loginAlert {
            display: none;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            visibility: visible;
            opacity: 1;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- مؤشر التحميل -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <div class="login-container">
        <div class="card">
            <div class="card-header">
                <!-- استخدام صورة محلية بدلاً من الصورة عبر الإنترنت -->
                <img src="img/logo.png" alt="شعار يمن GPS" class="logo" onerror="this.src='img/default-logo.png'">
                <h4 class="mb-0">تسجيل الدخول إلى لوحة التحكم</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" id="loginAlert" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="alertMessage"></span>
                </div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="username" placeholder="أدخل اسم المستخدم" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group password-container">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" placeholder="أدخل كلمة المرور" required>
                            <span class="toggle-password" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">تذكرني</label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                    </button>
                </form>

                <div class="mt-3 text-center">
                    <a href="/" class="text-decoration-none">العودة للرئيسية</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // إظهار مؤشر التحميل
        function showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }

        // إخفاء مؤشر التحميل
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const alertElement = document.getElementById('loginAlert');
            const alertMessageElement = document.getElementById('alertMessage');

            alertMessageElement.textContent = message;
            alertElement.style.display = 'block';
        }

        // إخفاء رسالة الخطأ
        function hideError() {
            document.getElementById('loginAlert').style.display = 'none';
        }

        // تبديل عرض كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // إعادة توجيه تلقائية إلى صفحة تسجيل الدخول الجديدة
        console.log('إعادة توجيه إلى صفحة تسجيل الدخول الجديدة...');
        window.location.href = 'admin-login.html';
    </script>
</body>
</html>



// سكريبت لعرض بيانات المستخدمين والعملاء والمواقع من قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function showDatabaseData() {
  try {
    console.log('============ استعلام بيانات نظام Yemen GPS ============');

    // استعلام بيانات المستخدمين
    console.log('\n📊 جدول المستخدمين:');
    const usersQuery = `
      SELECT id, username, full_name, email, role_id, is_active, created_at, last_login 
      FROM users 
      ORDER BY id
    `;
    const usersResult = await pool.query(usersQuery);
    
    if (usersResult.rows.length === 0) {
      console.log('  لا توجد بيانات للمستخدمين');
    } else {
      console.log(`  تم العثور على ${usersResult.rows.length} مستخدم:`);
      usersResult.rows.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.username} (${user.full_name || 'بدون اسم'}) - ${user.email || 'بدون بريد إلكتروني'}`);
        console.log(`     الدور: ${user.role_id}, الحالة: ${user.is_active ? 'نشط' : 'غير نشط'}`);
        console.log(`     تاريخ التسجيل: ${user.created_at ? user.created_at.toLocaleString() : 'غير محدد'}`);
        console.log(`     آخر تسجيل دخول: ${user.last_login ? user.last_login.toLocaleString() : 'غير محدد'}`);
        console.log('    -------');
      });
    }

    // استعلام بيانات العملاء
    console.log('\n📊 جدول العملاء:');
    const clientsQuery = `
      SELECT * FROM clients ORDER BY id
    `;
    
    try {
      const clientsResult = await pool.query(clientsQuery);
      
      if (clientsResult.rows.length === 0) {
        console.log('  لا توجد بيانات للعملاء');
      } else {
        console.log(`  تم العثور على ${clientsResult.rows.length} عميل:`);
        clientsResult.rows.forEach((client, index) => {
          console.log(`  ${index + 1}. ${client.name || 'بدون اسم'}`);
          if (client.contact_person) console.log(`     الشخص المسؤول: ${client.contact_person}`);
          if (client.email) console.log(`     البريد الإلكتروني: ${client.email}`);
          if (client.phone) console.log(`     الهاتف: ${client.phone}`);
          if (client.address) console.log(`     العنوان: ${client.address}`);
          console.log('    -------');
        });
      }
    } catch (error) {
      console.log(`  خطأ في استعلام جدول العملاء: ${error.message}`);
      // التحقق من وجود الجدول
      console.log('  جاري التحقق من وجود جدول العملاء...');
      const checkTableQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'clients'
      `;
      const tableResult = await pool.query(checkTableQuery);
      if (tableResult.rows.length === 0) {
        console.log('  ⚠️ جدول العملاء غير موجود في قاعدة البيانات');
      }
    }

    // استعلام بيانات المواقع (النقاط)
    console.log('\n📊 جدول المواقع:');
    const locationsQuery = `
      SELECT * FROM locations ORDER BY id
    `;
    
    try {
      const locationsResult = await pool.query(locationsQuery);
      
      if (locationsResult.rows.length === 0) {
        console.log('  لا توجد بيانات للمواقع');
      } else {
        console.log(`  تم العثور على ${locationsResult.rows.length} موقع:`);
        locationsResult.rows.forEach((location, index) => {
          console.log(`  ${index + 1}. ${location.name || 'بدون اسم'}`);
          console.log(`     الإحداثيات: (${location.latitude}, ${location.longitude})`);
          if (location.description) console.log(`     الوصف: ${location.description}`);
          if (location.category_id) console.log(`     فئة التصنيف: ${location.category_id}`);
          console.log('    -------');
        });
      }
    } catch (error) {
      console.log(`  خطأ في استعلام جدول المواقع: ${error.message}`);
      // التحقق من وجود الجدول
      console.log('  جاري التحقق من وجود جدول المواقع...');
      const checkTableQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'locations'
      `;
      const tableResult = await pool.query(checkTableQuery);
      if (tableResult.rows.length === 0) {
        console.log('  ⚠️ جدول المواقع غير موجود في قاعدة البيانات');
      }
    }

    // استعلام بيانات التصنيفات
    console.log('\n📊 جدول التصنيفات:');
    const categoriesQuery = `
      SELECT * FROM categories ORDER BY id
    `;
    
    try {
      const categoriesResult = await pool.query(categoriesQuery);
      
      if (categoriesResult.rows.length === 0) {
        console.log('  لا توجد بيانات للتصنيفات');
      } else {
        console.log(`  تم العثور على ${categoriesResult.rows.length} تصنيف:`);
        categoriesResult.rows.forEach((category, index) => {
          console.log(`  ${index + 1}. ${category.name || 'بدون اسم'}`);
          if (category.description) console.log(`     الوصف: ${category.description}`);
          if (category.icon) console.log(`     الأيقونة: ${category.icon}`);
          console.log('    -------');
        });
      }
    } catch (error) {
      console.log(`  خطأ في استعلام جدول التصنيفات: ${error.message}`);
      // التحقق من وجود الجدول
      console.log('  جاري التحقق من وجود جدول التصنيفات...');
      const checkTableQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'categories'
      `;
      const tableResult = await pool.query(checkTableQuery);
      if (tableResult.rows.length === 0) {
        console.log('  ⚠️ جدول التصنيفات غير موجود في قاعدة البيانات');
      }
    }

    // استعلام هيكل قاعدة البيانات (الجداول)
    console.log('\n📋 هيكل قاعدة البيانات:');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    const tablesResult = await pool.query(tablesQuery);
    
    if (tablesResult.rows.length === 0) {
      console.log('  لا توجد جداول في قاعدة البيانات');
    } else {
      console.log(`  تم العثور على ${tablesResult.rows.length} جدول:`);
      tablesResult.rows.forEach((table, index) => {
        console.log(`  ${index + 1}. ${table.table_name}`);
      });
    }

  } catch (error) {
    console.error('حدث خطأ أثناء استعلام البيانات:', error);
  } finally {
    await pool.end();
  }
}

showDatabaseData();

// Font Awesome Loader
// This script ensures that Font Awesome icons are loaded correctly

document.addEventListener('DOMContentLoaded', function() {
    // Check if Font Awesome is loaded
    function isFontAwesomeLoaded() {
        var span = document.createElement('span');
        span.className = 'fas';
        span.style.display = 'none';
        document.body.appendChild(span);
        
        var loaded = window.getComputedStyle(span).getPropertyValue('font-family').indexOf('Font Awesome') !== -1;
        document.body.removeChild(span);
        
        return loaded;
    }
    
    // Load Font Awesome locally if CDN fails
    function loadLocalFontAwesome() {
        console.log('Loading Font Awesome locally...');
        
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = '/css/fontawesome/all.min.css';
        document.head.appendChild(link);
        
        // Force refresh icons
        setTimeout(function() {
            var icons = document.querySelectorAll('.fas, .far, .fab, .fa');
            icons.forEach(function(icon) {
                var className = icon.className;
                icon.className = className + ' ';
                setTimeout(function() {
                    icon.className = className;
                }, 10);
            });
        }, 100);
    }
    
    // Check after a delay to allow CDN to load
    setTimeout(function() {
        if (!isFontAwesomeLoaded()) {
            loadLocalFontAwesome();
        } else {
            console.log('Font Awesome loaded from CDN successfully');
        }
    }, 1000);
});

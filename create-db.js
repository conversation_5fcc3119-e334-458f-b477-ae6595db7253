/**
 * سكربت بسيط لإنشاء قاعدة بيانات Yemen GPS
 */

const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات الرئيسية
const mainDbConfig = {
    user: 'postgres',
    password: 'yemen123',  // كلمة المرور الصحيحة للمستخدم postgres
    host: 'localhost',
    port: 5432,
    database: 'postgres'
};

// اسم قاعدة البيانات المراد إنشاؤها
const dbName = 'yemen_gps';

// معلومات المستخدم yemen المراد إنشاؤه
const yemenUser = {
    username: 'postgres',
    password: 'yemen123'
};

async function createDatabase() {
    const client = new Client(mainDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات الرئيسية...');
        await client.connect();
        
        // التحقق من وجود المستخدم yemen
        console.log('التحقق من وجود المستخدم yemen...');
        const userExistsQuery = `SELECT 1 FROM pg_roles WHERE rolname = '${yemenUser.username}'`;
        const userResult = await client.query(userExistsQuery);
        
        if (userResult.rows.length === 0) {
            console.log('إنشاء المستخدم yemen...');
            await client.query(`CREATE USER ${yemenUser.username} WITH PASSWORD '${yemenUser.password}'`);
            console.log('تم إنشاء المستخدم yemen بنجاح');
        } else {
            console.log('المستخدم yemen موجود بالفعل');
        }
        
        // التحقق من وجود قاعدة البيانات
        console.log('التحقق من وجود قاعدة البيانات...');
        const dbExistsQuery = `SELECT 1 FROM pg_database WHERE datname = '${dbName}'`;
        const dbResult = await client.query(dbExistsQuery);
        
        if (dbResult.rows.length === 0) {
            console.log(`إنشاء قاعدة البيانات ${dbName}...`);
            await client.query(`CREATE DATABASE ${dbName}`);
            console.log(`تم إنشاء قاعدة البيانات ${dbName} بنجاح`);
        } else {
            console.log(`قاعدة البيانات ${dbName} موجودة بالفعل`);
        }
        
        // منح صلاحيات للمستخدم yemen على قاعدة البيانات
        console.log('منح صلاحيات للمستخدم yemen...');
        await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${yemenUser.username}`);
        console.log('تم منح الصلاحيات بنجاح');
        
        console.log('تم إعداد قاعدة البيانات والمستخدم بنجاح!');
    } catch (err) {
        console.error('خطأ:', err);
    } finally {
        await client.end();
    }
}

createDatabase();

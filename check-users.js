// ملف للتحقق من المستخدمين في قاعدة البيانات
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('محاولة الاتصال بقاعدة البيانات باستخدام:');
console.log(`host: ${dbConfig.host}, port: ${dbConfig.port}, database: ${dbConfig.database}, user: ${dbConfig.user}`);

const pool = new Pool(dbConfig);

async function checkUsers() {
    try {
        // التحقق من وجود جدول المستخدمين
        const tableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            );
        `);

        const usersTableExists = tableCheck.rows[0].exists;
        console.log(`جدول المستخدمين موجود: ${usersTableExists}`);

        if (!usersTableExists) {
            console.log('جدول المستخدمين غير موجود. قد تحتاج إلى إنشاء الجدول أولاً.');
            return;
        }

        // التحقق من هيكل جدول المستخدمين
        const structureCheck = await pool.query(`
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'users';
        `);

        console.log('\nهيكل جدول المستخدمين:');
        structureCheck.rows.forEach(column => {
            console.log(`${column.column_name}: ${column.data_type}`);
        });

        // عرض المستخدمين في الجدول
        const result = await pool.query('SELECT * FROM users');
        
        console.log('\nالمستخدمون في قاعدة البيانات:');
        if (result.rows.length === 0) {
            console.log('لا يوجد مستخدمون في الجدول.');
            console.log('يمكنك استخدام حساب المشرف الافتراضي: admin / yemen123');
        } else {
            result.rows.forEach(user => {
                // حذف كلمة المرور من المخرجات للأمان
                const { password, ...safeUser } = user;
                console.log(safeUser);
            });
        }
    } catch (err) {
        console.error('خطأ في التحقق من المستخدمين:', err.message);
    } finally {
        // إغلاق الاتصال
        pool.end();
    }
}

checkUsers();

// اختبار بسيط للخادم
const express = require('express');
const path = require('path');

const app = express();

// إعداد الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

// إعداد CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    next();
});

// اختبار API بسيط
app.get('/api/places', (req, res) => {
    console.log('تم استقبال طلب للأماكن');
    res.json({
        success: true,
        data: [
            {
                id: 1,
                name_ar: 'مسجد الصالح',
                name_en: 'Al-Saleh Mosque',
                latitude: 15.3547,
                longitude: 44.2066,
                category_id: 2,
                description_ar: 'أكبر مسجد في اليمن',
                rating: 4.8
            },
            {
                id: 2,
                name_ar: 'سوق الملح',
                name_en: 'Salt Market',
                latitude: 15.3500,
                longitude: 44.2100,
                category_id: 8,
                description_ar: 'سوق تقليدي في صنعاء القديمة',
                rating: 4.5
            }
        ],
        count: 2
    });
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// بدء الخادم
const PORT = 8002;
app.listen(PORT, () => {
    console.log(`🚀 خادم الاختبار يعمل على المنفذ ${PORT}`);
    console.log(`🗺️ الخريطة: http://localhost:${PORT}/`);
    console.log(`📡 API: http://localhost:${PORT}/api/places`);
});

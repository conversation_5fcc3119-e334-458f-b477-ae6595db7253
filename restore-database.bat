@echo off
echo ===== استعادة قاعدة بيانات Yemen GPS =====
echo.

REM تحديد متغيرات قاعدة البيانات
set PGUSER=yemen
set PGPASSWORD=admin
set PGHOST=localhost
set PGPORT=5432
set PGDATABASE=yemen_gps

REM عرض ملفات النسخ الاحتياطية المتاحة
echo الملفات المتاحة للاستعادة:
echo.
echo 1. yemen_gps_backup.sql (النسخة الحالية)
if exist "backups\" (
    echo.
    echo ملفات النسخ الاحتياطية في مجلد backups:
    dir /b backups\*.sql 2>nul
)
echo.

REM طلب اختيار الملف
set /p BACKUP_CHOICE="أدخل اسم الملف للاستعادة (أو اضغط Enter للملف الافتراضي): "

if "%BACKUP_CHOICE%"=="" (
    set BACKUP_FILE=yemen_gps_backup.sql
    echo تم اختيار الملف الافتراضي: yemen_gps_backup.sql
) else (
    set BACKUP_FILE=%BACKUP_CHOICE%
)

REM التحقق من وجود الملف
if not exist "%BACKUP_FILE%" (
    if exist "backups\%BACKUP_FILE%" (
        set BACKUP_FILE=backups\%BACKUP_FILE%
    ) else (
        echo.
        echo خطأ: الملف %BACKUP_FILE% غير موجود
        echo تحقق من اسم الملف والمسار
        pause
        exit /b 1
    )
)

echo.
echo تحذير: هذه العملية ستحذف البيانات الحالية وتستبدلها بالنسخة الاحتياطية
set /p CONFIRM="هل أنت متأكد؟ (y/N): "

if /i not "%CONFIRM%"=="y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo جاري استعادة قاعدة البيانات من: %BACKUP_FILE%
echo.

REM تنفيذ أمر الاستعادة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -f "%BACKUP_FILE%" --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===== تم استعادة قاعدة البيانات بنجاح =====
    echo.
    
    REM التحقق من الجداول المستعادة
    echo التحقق من الجداول المستعادة...
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d %PGDATABASE% -c "\dt"
    
) else (
    echo.
    echo ===== فشل في استعادة قاعدة البيانات =====
    echo تحقق من:
    echo 1. تشغيل خدمة PostgreSQL
    echo 2. صحة بيانات الاتصال
    echo 3. صلاحيات المستخدم
    echo 4. صحة ملف النسخة الاحتياطية
)

echo.
pause

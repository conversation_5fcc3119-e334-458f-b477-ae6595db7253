// خادم مبسط لاختبار صفحة الأماكن
const express = require('express');
const path = require('path');
const placesApi = require('./server/places-api');

const app = express();

// إعداد الملفات الثابتة
app.use(express.static(path.join(__dirname, 'public')));

// إعداد JSON parsing
app.use(express.json());

// إعداد CORS
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    next();
});

// استخدام API الأماكن
app.use('/api', placesApi);

// الصفحة الرئيسية - الخريطة الرسمية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// إعادة توجيه للخريطة الرسمية
app.get('/map', (req, res) => {
    res.redirect('/');
});

app.get('/official-map', (req, res) => {
    res.redirect('/');
});

// صفحة الأماكن
app.get('/places', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'places.html'));
});

// بدء الخادم
const PORT = 8000;
app.listen(PORT, () => {
    console.log(`الخادم المبسط يعمل على المنفذ ${PORT}`);
    console.log(`زيارة: http://localhost:${PORT}/places.html`);
    console.log(`الخريطة الرئيسية: http://localhost:${PORT}/`);
});

-- إنشاء المستخدم إذا لم يكن موجودًا
DO
$$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles WHERE rolname = 'yemen'
   ) THEN
      CREATE USER yemen WITH PASSWORD 'admin';
   ELSE
      ALTER USER yemen WITH PASSWORD 'admin';
   END IF;
END
$$;

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
CREATE DATABASE yemen_gps WITH OWNER = yemen ENCODING = 'UTF8' CONNECTION LIMIT = -1;

-- منح صلاحيات للمستخدم على قاعدة البيانات
GRANT ALL PRIVILEGES ON DATABASE yemen_gps TO yemen;

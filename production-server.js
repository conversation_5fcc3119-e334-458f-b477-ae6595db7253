// خادم الإنتاج لنظام يمن GPS
const express = require('express');
const path = require('path');
const compression = require('compression');
const helmet = require('helmet');

const app = express();

// إعدادات الأمان والضغط
app.use(helmet({
    contentSecurityPolicy: false, // تعطيل CSP للسماح بـ Google Maps
    crossOriginEmbedderPolicy: false
}));
app.use(compression());

// إعداد الملفات الثابتة مع cache
app.use(express.static(path.join(__dirname, 'public'), {
    maxAge: '1d', // cache لمدة يوم واحد
    etag: true
}));

// إعداد JSON parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// إعداد CORS للسماح بالوصول من أي مكان
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Credentials', 'true');

    // معالجة طلبات OPTIONS
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// إعداد قاعدة البيانات للخادم الخارجي
let placesApi;
try {
    placesApi = require('./server/places-api');
    console.log('✅ تم تحميل API قاعدة البيانات');
} catch (error) {
    console.log('⚠️ لم يتم العثور على قاعدة البيانات، سيتم استخدام البيانات التجريبية');

    // إنشاء API تجريبي
    const router = express.Router();

    // بيانات تجريبية للأماكن اليمنية
    const samplePlaces = [
        {
            id: 1,
            name_ar: 'مسجد الصالح',
            name_en: 'Al-Saleh Mosque',
            latitude: 15.3547,
            longitude: 44.2066,
            category_id: 2,
            description_ar: 'أكبر مسجد في اليمن ويقع في العاصمة صنعاء',
            rating: 4.8,
            photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Al-Saleh+Mosque'])
        },
        {
            id: 2,
            name_ar: 'سوق الملح',
            name_en: 'Salt Market',
            latitude: 15.3500,
            longitude: 44.2100,
            category_id: 8,
            description_ar: 'سوق تقليدي في صنعاء القديمة',
            rating: 4.5,
            photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Salt+Market'])
        },
        {
            id: 3,
            name_ar: 'قلعة صيرة',
            name_en: 'Sira Fortress',
            latitude: 12.7855,
            longitude: 45.0187,
            category_id: 1,
            description_ar: 'قلعة تاريخية في عدن',
            rating: 4.3,
            photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Sira+Fortress'])
        },
        {
            id: 4,
            name_ar: 'جامع الكبير',
            name_en: 'Great Mosque',
            latitude: 15.3520,
            longitude: 44.2080,
            category_id: 2,
            description_ar: 'من أقدم المساجد في صنعاء',
            rating: 4.7,
            photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Great+Mosque'])
        },
        {
            id: 5,
            name_ar: 'ميناء عدن',
            name_en: 'Aden Port',
            latitude: 12.7794,
            longitude: 45.0367,
            category_id: 9,
            description_ar: 'ميناء تجاري مهم في الجنوب',
            rating: 4.0,
            photos: JSON.stringify(['https://via.placeholder.com/300x200?text=Aden+Port'])
        }
    ];

    router.get('/places', (req, res) => {
        const { limit = 50 } = req.query;
        const limitedPlaces = samplePlaces.slice(0, parseInt(limit));

        res.json({
            success: true,
            data: limitedPlaces,
            count: limitedPlaces.length
        });
    });

    router.get('/places/:id', (req, res) => {
        const place = samplePlaces.find(p => p.id === parseInt(req.params.id));
        if (place) {
            res.json({ success: true, data: place });
        } else {
            res.status(404).json({ success: false, message: 'المكان غير موجود' });
        }
    });

    placesApi = router;
}

// استخدام API الأماكن
app.use('/api', placesApi);

// الصفحة الرئيسية - الخريطة الرسمية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// إعادة توجيه للخريطة الرسمية
app.get('/map', (req, res) => {
    res.redirect('/');
});

app.get('/official-map', (req, res) => {
    res.redirect('/');
});

// صفحة الأماكن
app.get('/places', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'places.html'));
});

// لوحة التحكم
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// معالجة الأخطاء 404
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'الصفحة غير موجودة',
        path: req.path
    });
});

// معالجة الأخطاء العامة
app.use((error, req, res, next) => {
    console.error('خطأ في الخادم:', error);
    res.status(500).json({
        success: false,
        message: 'خطأ داخلي في الخادم',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
});

// بدء الخادم
const PORT = process.env.PORT || 5001;
const HOST = process.env.HOST || '0.0.0.0';

app.listen(PORT, HOST, () => {
    console.log('🚀 ========================================');
    console.log('🗺️  نظام يمن GPS - خادم الإنتاج');
    console.log('🚀 ========================================');
    console.log(`📡 الخادم يعمل على: ${HOST}:${PORT}`);
    console.log(`🌐 الرابط العام: http://***********:${PORT}/`);
    console.log('📋 الروابط المتاحة:');
    console.log(`   🗺️  الخريطة الرئيسية: http://***********:${PORT}/`);
    console.log(`   📍 صفحة الأماكن: http://***********:${PORT}/places`);
    console.log(`   ⚙️  لوحة التحكم: http://***********:${PORT}/admin`);
    console.log(`   📡 API الأماكن: http://***********:${PORT}/api/places`);
    console.log('🚀 ========================================');
});

// معالجة إيقاف الخادم بشكل صحيح
process.on('SIGTERM', () => {
    console.log('🛑 تم استقبال إشارة SIGTERM، جاري إيقاف الخادم...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 تم استقبال إشارة SIGINT، جاري إيقاف الخادم...');
    process.exit(0);
});

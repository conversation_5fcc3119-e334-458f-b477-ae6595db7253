/**
 * نظام إعادة التحميل الإجباري - Yemen Nav
 * يمنع التخزين المؤقت للمتصفح ويضمن تحميل أحدث نسخ الملفات
 */

(function() {
    // إضافة معلمة عشوائية لعنوان الصفحة لإجبار إعادة التحميل
    if (window.location.href.indexOf('?v=') === -1 && window.location.href.indexOf('&v=') === -1) {
        const timestamp = new Date().getTime();
        const separator = window.location.href.indexOf('?') !== -1 ? '&' : '?';
        const newUrl = window.location.href + separator + 'v=' + timestamp;
        
        // استخدام history API بدلاً من إعادة التوجيه المباشر لتجنب التحميل المتكرر
        window.history.replaceState({}, document.title, newUrl);
    }

    // حذف ذاكرة التخزين المؤقت للخريطة
    if (window.map || window.appMap) {
        console.log('تنظيف كائنات الخريطة...');
        // إزالة أي خرائط موجودة مسبقًا
        if (window.map && typeof window.map.remove === 'function') {
            window.map.remove();
        }
        window.map = null;
        window.appMap = null;
    }

    // تعريف وظيفة لتحميل المكتبات بطريقة متزامنة
    window.loadScript = function(url, callback) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        
        // إضافة طابع زمني لمنع التخزين المؤقت
        const timestamp = new Date().getTime();
        const urlWithTimestamp = url.indexOf('?') !== -1 ? 
            url + '&t=' + timestamp : 
            url + '?t=' + timestamp;
            
        script.src = urlWithTimestamp;
        
        if (callback) {
            script.onload = callback;
        }
        
        script.onerror = function() {
            console.error('فشل تحميل المكتبة: ' + url);
        };
        
        document.head.appendChild(script);
        return script;
    };

    // تعريف وظيفة لإعادة تحميل الصفحة
    window.forceReload = function() {
        // إضافة معلمة عشوائية لإجبار إعادة التحميل
        const timestamp = new Date().getTime();
        window.location.href = window.location.pathname + '?v=' + timestamp;
    };

    // إعادة تحميل الصفحة إذا كانت هناك مشكلة
    window.fixMap = function() {
        console.log('جاري محاولة إصلاح الخريطة...');
        // حذف كائن الخريطة الحالي إذا كان موجودًا
        if (window.map && typeof window.map.remove === 'function') {
            window.map.remove();
        }
        
        // تأكد من أن عنصر الخريطة موجود
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('لم يتم العثور على عنصر الخريطة!');
            return;
        }
        
        // إعادة تهيئة عنصر الخريطة
        mapElement.innerHTML = '';
        
        // تهيئة خريطة بسيطة
        window.map = L.map('map').setView([15.3694, 44.1910], 12);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | Yemen Nav',
            maxZoom: 19
        }).addTo(window.map);
        
        console.log('تم إصلاح الخريطة!');
    };

    // إضافة زر إصلاح الخريطة بعد تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        const fixButton = document.createElement('button');
        fixButton.innerHTML = 'إصلاح الخريطة';
        fixButton.style.position = 'fixed';
        fixButton.style.bottom = '10px';
        fixButton.style.right = '10px';
        fixButton.style.zIndex = '9999';
        fixButton.style.backgroundColor = '#4285F4';
        fixButton.style.color = 'white';
        fixButton.style.border = 'none';
        fixButton.style.borderRadius = '4px';
        fixButton.style.padding = '10px 15px';
        fixButton.style.cursor = 'pointer';
        fixButton.style.display = 'none'; // مخفي افتراضيًا
        
        // إظهار الزر بعد 5 ثوان إذا لم تظهر الخريطة
        setTimeout(function() {
            const mapElement = document.getElementById('map');
            if (mapElement && (!window.map || mapElement.childElementCount === 0)) {
                fixButton.style.display = 'block';
            }
        }, 5000);
        
        fixButton.addEventListener('click', function() {
            window.fixMap();
            fixButton.style.display = 'none';
        });
        
        document.body.appendChild(fixButton);
    });
})();

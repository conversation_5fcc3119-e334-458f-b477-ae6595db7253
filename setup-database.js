/**
 * سكربت إنشاء قاعدة بيانات Yemen GPS مع جميع الجداول
 * 
 * هذا السكربت يقوم بإنشاء قاعدة بيانات PostgreSQL كاملة مع جميع الجداول المطلوبة
 * يستخدم إعدادات الاتصال من ملف .env
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات الرئيسية (postgres)
const mainDbConfig = {
    user: 'postgres',  // استخدام مستخدم postgres الافتراضي
    password: 'yemen123',  // استخدام كلمة المرور الصحيحة
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres' // الاتصال بقاعدة البيانات الافتراضية
};

// معلومات المستخدم yemen المراد إنشاؤه
const yemenUserConfig = {
    username: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin'
};

// اسم قاعدة البيانات المراد إنشاؤها
const dbName = process.env.DB_NAME || 'yemen_gps';

// إعدادات الاتصال بقاعدة البيانات الجديدة
const newDbConfig = {
    user: 'postgres',  // استخدام مستخدم postgres الافتراضي
    password: 'yemen123',  // استخدام كلمة المرور الصحيحة
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: dbName
};

// إعدادات الاتصال بقاعدة البيانات الجديدة باستخدام المستخدم yemen
const yemenDbConfig = {
    user: yemenUserConfig.username,
    password: yemenUserConfig.password,
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: dbName
};

/**
 * دالة لإنشاء قاعدة البيانات إذا لم تكن موجودة
 */
async function createDatabase() {
    const client = new Client(mainDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات الرئيسية...');
        await client.connect();
        
        // التحقق من وجود قاعدة البيانات
        const checkDbQuery = `SELECT EXISTS(SELECT 1 FROM pg_database WHERE datname = '${dbName}');`;
        const checkResult = await client.query(checkDbQuery);
        
        if (checkResult.rows[0].exists) {
            console.log(`قاعدة البيانات '${dbName}' موجودة بالفعل.`);
        } else {
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            console.log(`جاري إنشاء قاعدة البيانات '${dbName}'...`);
            await client.query(`CREATE DATABASE ${dbName};`);
            console.log(`تم إنشاء قاعدة البيانات '${dbName}' بنجاح!`);
        }
        
        // منح صلاحيات للمستخدم yemen على قاعدة البيانات
        await client.query(`GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${yemenUserConfig.username};`);
        console.log(`تم منح جميع الصلاحيات على قاعدة البيانات '${dbName}' للمستخدم '${yemenUserConfig.username}'.`);
        
        return !checkResult.rows[0].exists; // إرجاع true إذا تم إنشاء قاعدة بيانات جديدة
    } catch (err) {
        console.error('خطأ في إنشاء قاعدة البيانات:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء جميع الجداول في قاعدة البيانات
 */
async function createTables() {
    const client = new Client(newDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات الجديدة...');
        await client.connect();
        
        console.log('جاري إنشاء الجداول...');
        
        // إنشاء جدول المستخدمين
        await client.query(`
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE,
                phone VARCHAR(20),
                role_id INTEGER DEFAULT 2,
                is_active BOOLEAN DEFAULT TRUE,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                type VARCHAR(20) DEFAULT 'user',
                permissions JSONB
            );
        `);
        console.log('تم إنشاء جدول المستخدمين.');
        
        // إنشاء جدول الأدوار
        await client.query(`
            CREATE TABLE IF NOT EXISTS roles (
                id SERIAL PRIMARY KEY,
                role_name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الأدوار.');
        
        // إنشاء جدول الصلاحيات
        await client.query(`
            CREATE TABLE IF NOT EXISTS permissions (
                id SERIAL PRIMARY KEY,
                permission_name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول الصلاحيات.');
        
        // إنشاء جدول العلاقة بين الأدوار والصلاحيات
        await client.query(`
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER REFERENCES roles(id) ON DELETE CASCADE,
                permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
                PRIMARY KEY (role_id, permission_id)
            );
        `);
        console.log('تم إنشاء جدول العلاقة بين الأدوار والصلاحيات.');
        
        // إنشاء جدول العملاء
        await client.query(`
            CREATE TABLE IF NOT EXISTS clients (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE,
                phone VARCHAR(20),
                address TEXT,
                deviceSN VARCHAR(50) UNIQUE,
                licenseN VARCHAR(50) UNIQUE,
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول العملاء.');
        
        // إنشاء جدول التصنيفات
        await client.query(`
            CREATE TABLE IF NOT EXISTS categories (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                icon VARCHAR(100),
                color VARCHAR(20),
                parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول التصنيفات.');
        
        // إنشاء جدول المواقع
        await client.query(`
            CREATE TABLE IF NOT EXISTS locations (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                lat DECIMAL(10, 8) NOT NULL,
                lng DECIMAL(11, 8) NOT NULL,
                category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
                address TEXT,
                phone VARCHAR(20),
                website VARCHAR(255),
                opening_hours JSONB,
                added_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
                status VARCHAR(20) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول المواقع.');
        
        // إنشاء جدول المفضلة
        await client.query(`
            CREATE TABLE IF NOT EXISTS favorites (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, location_id)
            );
        `);
        console.log('تم إنشاء جدول المفضلة.');
        
        // إنشاء جدول التقييمات
        await client.query(`
            CREATE TABLE IF NOT EXISTS reviews (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                location_id INTEGER REFERENCES locations(id) ON DELETE CASCADE,
                rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(user_id, location_id)
            );
        `);
        console.log('تم إنشاء جدول التقييمات.');
        
        // إنشاء جدول المسارات
        await client.query(`
            CREATE TABLE IF NOT EXISTS routes (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                start_point JSONB NOT NULL,
                end_point JSONB NOT NULL,
                waypoints JSONB,
                distance INTEGER,
                duration INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول المسارات.');
        
        // إنشاء جدول سجلات النظام
        await client.query(`
            CREATE TABLE IF NOT EXISTS logs (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                action VARCHAR(100) NOT NULL,
                details JSONB,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        console.log('تم إنشاء جدول سجلات النظام.');
        
        console.log('تم إنشاء جميع الجداول بنجاح!');
    } catch (err) {
        console.error('خطأ في إنشاء الجداول:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإدخال البيانات الأولية في الجداول
 */
async function insertInitialData() {
    const client = new Client(newDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات لإدخال البيانات الأولية...');
        await client.connect();
        
        console.log('جاري إدخال البيانات الأولية...');
        
        // إدخال بيانات الأدوار الافتراضية
        await client.query(`
            INSERT INTO roles (role_name, description)
            VALUES 
                ('admin', 'مدير النظام مع كامل الصلاحيات'),
                ('user', 'مستخدم عادي'),
                ('developer', 'مطور النظام')
            ON CONFLICT (role_name) DO NOTHING;
        `);
        console.log('تم إدخال بيانات الأدوار الافتراضية.');
        
        // إدخال بيانات الصلاحيات الافتراضية
        await client.query(`
            INSERT INTO permissions (permission_name, description)
            VALUES
                ('view_dashboard', 'عرض لوحة التحكم'),
                ('manage_users', 'إدارة المستخدمين'),
                ('manage_clients', 'إدارة العملاء'),
                ('manage_locations', 'إدارة المواقع'),
                ('manage_categories', 'إدارة التصنيفات'),
                ('manage_settings', 'إدارة الإعدادات')
            ON CONFLICT (permission_name) DO NOTHING;
        `);
        console.log('تم إدخال بيانات الصلاحيات الافتراضية.');
        
        // ربط الأدوار بالصلاحيات
        // المدير لديه جميع الصلاحيات
        await client.query(`
            INSERT INTO role_permissions (role_id, permission_id)
            SELECT 1, id FROM permissions
            ON CONFLICT (role_id, permission_id) DO NOTHING;
        `);
        
        // المستخدم العادي لديه صلاحية عرض لوحة التحكم فقط
        await client.query(`
            INSERT INTO role_permissions (role_id, permission_id)
            SELECT 2, id FROM permissions WHERE permission_name = 'view_dashboard'
            ON CONFLICT (role_id, permission_id) DO NOTHING;
        `);
        
        // المطور لديه جميع الصلاحيات
        await client.query(`
            INSERT INTO role_permissions (role_id, permission_id)
            SELECT 3, id FROM permissions
            ON CONFLICT (role_id, permission_id) DO NOTHING;
        `);
        console.log('تم ربط الأدوار بالصلاحيات.');
        
        // إنشاء مستخدم المدير الافتراضي (كلمة المرور: yemen123)
        await client.query(`
            INSERT INTO users (username, password, full_name, email, role_id, type, permissions)
            VALUES (
                'admin', 
                '$2b$10$X/8.Y5JVzWV5Oa.Yd6Kg9OqQi4JMZdFJO.jkVn1AAVEbQJ1dO2.Hy', 
                'مدير النظام', 
                '<EMAIL>', 
                1, 
                'admin', 
                '[{"code":"view_dashboard","name":"عرض لوحة التحكم"},{"code":"manage_users","name":"إدارة المستخدمين"},{"code":"manage_clients","name":"إدارة العملاء"},{"code":"manage_locations","name":"إدارة المواقع"},{"code":"manage_settings","name":"إدارة الإعدادات"},{"code":"manage_categories","name":"إدارة التصنيفات"}]'
            )
            ON CONFLICT (username) DO NOTHING;
        `);
        console.log('تم إنشاء مستخدم المدير الافتراضي.');
        
        // إنشاء بعض التصنيفات الافتراضية
        await client.query(`
            INSERT INTO categories (name, icon, color)
            VALUES
                ('مطاعم', 'restaurant', '#FF5722'),
                ('فنادق', 'hotel', '#2196F3'),
                ('مستشفيات', 'hospital', '#F44336'),
                ('محطات وقود', 'gas_station', '#4CAF50'),
                ('مراكز تسوق', 'shopping', '#9C27B0'),
                ('مدارس وجامعات', 'school', '#FF9800'),
                ('مساجد', 'mosque', '#795548'),
                ('حدائق', 'park', '#8BC34A'),
                ('مكاتب حكومية', 'government', '#607D8B')
            ON CONFLICT DO NOTHING;
        `);
        console.log('تم إنشاء التصنيفات الافتراضية.');
        
        console.log('تم إدخال جميع البيانات الأولية بنجاح!');
    } catch (err) {
        console.error('خطأ في إدخال البيانات الأولية:', err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * دالة لإنشاء مستخدم yemen إذا لم يكن موجودًا
 */
async function createYemenUser() {
    const client = new Client(mainDbConfig);
    
    try {
        console.log('جاري الاتصال بقاعدة البيانات الرئيسية للتحقق من وجود المستخدم...');
        await client.connect();
        
        // التحقق من وجود المستخدم yemen
        const checkUserQuery = `SELECT 1 FROM pg_roles WHERE rolname = '${yemenUserConfig.username}';`;
        const checkResult = await client.query(checkUserQuery);
        
        if (checkResult.rows.length > 0) {
            console.log(`المستخدم '${yemenUserConfig.username}' موجود بالفعل.`);
            // تحديث كلمة المرور للمستخدم الموجود
            await client.query(`ALTER USER ${yemenUserConfig.username} WITH PASSWORD '${yemenUserConfig.password}';`);
            console.log(`تم تحديث كلمة المرور للمستخدم '${yemenUserConfig.username}'.`);
        } else {
            // إنشاء المستخدم إذا لم يكن موجودًا
            console.log(`جاري إنشاء المستخدم '${yemenUserConfig.username}'...`);
            await client.query(`CREATE USER ${yemenUserConfig.username} WITH PASSWORD '${yemenUserConfig.password}';`);
            await client.query(`ALTER USER ${yemenUserConfig.username} WITH CREATEDB;`);
            console.log(`تم إنشاء المستخدم '${yemenUserConfig.username}' بنجاح!`);
        }
    } catch (err) {
        console.error(`خطأ في إنشاء/تحديث المستخدم ${yemenUserConfig.username}:`, err.message);
        throw err;
    } finally {
        await client.end();
    }
}

/**
 * الدالة الرئيسية لتنفيذ جميع الخطوات
 */
async function setupDatabase() {
    try {
        console.log('بدء إعداد قاعدة البيانات Yemen GPS...');
        
        // إنشاء المستخدم yemen إذا لم يكن موجودًا
        await createYemenUser();
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        const isNewDb = await createDatabase();
        
        // إنشاء الجداول
        await createTables();
        
        // إدخال البيانات الأولية
        await insertInitialData();
        
        console.log('تم إعداد قاعدة البيانات Yemen GPS بنجاح!');
        console.log(`معلومات الاتصال بقاعدة البيانات:`);
        console.log(`- المضيف: ${process.env.DB_HOST || 'localhost'}`);
        console.log(`- المنفذ: ${process.env.DB_PORT || 5432}`);
        console.log(`- اسم قاعدة البيانات: ${dbName}`);
        console.log(`- اسم المستخدم: ${yemenUserConfig.username}`);
        
        if (isNewDb) {
            console.log('ملاحظة: تم إنشاء قاعدة بيانات جديدة.');
        } else {
            console.log('ملاحظة: تم استخدام قاعدة بيانات موجودة مسبقاً.');
        }
        
        console.log('بيانات تسجيل الدخول الافتراضية للمدير:');
        console.log('- اسم المستخدم: admin');
        console.log('- كلمة المرور: yemen123');
    } catch (err) {
        console.error('حدث خطأ أثناء إعداد قاعدة البيانات:', err);
        process.exit(1);
    }
}

// تنفيذ الدالة الرئيسية
setupDatabase();

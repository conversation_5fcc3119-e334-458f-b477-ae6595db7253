// سكريبت للتحقق من هيكل جداول قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function checkTableStructure(tableName) {
  try {
    console.log(`\n📋 هيكل جدول ${tableName}:`);
    const query = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position
    `;
    
    const result = await pool.query(query, [tableName]);
    
    if (result.rows.length === 0) {
      console.log(`  الجدول ${tableName} غير موجود في قاعدة البيانات`);
    } else {
      console.log(`  تم العثور على ${result.rows.length} عمود في جدول ${tableName}:`);
      result.rows.forEach((column, index) => {
        console.log(`  ${index + 1}. ${column.column_name} (${column.data_type}) - ${column.is_nullable === 'YES' ? 'يمكن أن يكون فارغاً' : 'مطلوب'}`);
      });
    }
  } catch (error) {
    console.error(`خطأ في التحقق من هيكل جدول ${tableName}:`, error);
  }
}

async function checkAllTables() {
  try {
    console.log('============ هيكل جداول قاعدة بيانات Yemen GPS ============');
    
    // استعلام أسماء الجداول في قاعدة البيانات
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `;
    
    const tablesResult = await pool.query(tablesQuery);
    
    if (tablesResult.rows.length === 0) {
      console.log('لا توجد جداول في قاعدة البيانات');
    } else {
      console.log(`تم العثور على ${tablesResult.rows.length} جدول في قاعدة البيانات:`);
      
      // التحقق من هيكل كل جدول
      for (const table of tablesResult.rows) {
        await checkTableStructure(table.table_name);
      }
    }
  } catch (error) {
    console.error('حدث خطأ أثناء التحقق من هيكل قاعدة البيانات:', error);
  } finally {
    await pool.end();
  }
}

checkAllTables();

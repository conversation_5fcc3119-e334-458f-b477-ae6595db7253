// سكريبت لاختبار نقطة نهاية API المستخدمين
const http = require('http');
const db = require('./db');

// التأكد من أن قاعدة البيانات متصلة
async function checkDatabaseConnection() {
  try {
    console.log('جاري فحص الاتصال بقاعدة البيانات...');
    const result = await db.query('SELECT COUNT(*) FROM users');
    console.log(`عدد المستخدمين في قاعدة البيانات: ${result.rows[0].count}`);
    return true;
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    return false;
  }
}

// اختبار نقطة نهاية API
function testApiEndpoint(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log(`جاري اختبار المسار: ${path}`);
    
    const req = http.request(options, (res) => {
      console.log(`رمز الحالة: ${res.statusCode}`);
      console.log(`الرؤوس: ${JSON.stringify(res.headers)}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`البيانات المستلمة: ${jsonData.length} عنصر`);
          console.log(`النوع: ${typeof jsonData}, مصفوفة: ${Array.isArray(jsonData)}`);
          resolve(jsonData);
        } catch (e) {
          console.error('فشل في تحليل البيانات كـ JSON:', e);
          console.log('البيانات المستلمة:', data);
          reject(e);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error(`خطأ في اختبار API: ${error.message}`);
      reject(error);
    });
    
    req.end();
  });
}

// تنفيذ الاختبارات
async function runTests() {
  try {
    // التحقق من الاتصال بقاعدة البيانات
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      console.error('فشل الاتصال بقاعدة البيانات. توقف الاختبار.');
      process.exit(1);
    }
    
    // اختبار نقطة نهاية المستخدمين
    console.log('\n=== اختبار نقطة نهاية المستخدمين ===');
    const users = await testApiEndpoint('/api/admin/users');
    console.log(`تم الحصول على ${users.length} مستخدم من API`);
    
    // إظهار بيانات المستخدم الأول للتحقق
    if (users.length > 0) {
      console.log('بيانات المستخدم الأول:', JSON.stringify(users[0], null, 2));
    }
    
    console.log('\nتم اكتمال الاختبارات بنجاح.');
  } catch (error) {
    console.error('حدث خطأ أثناء الاختبارات:', error);
  } finally {
    process.exit();
  }
}

runTests();

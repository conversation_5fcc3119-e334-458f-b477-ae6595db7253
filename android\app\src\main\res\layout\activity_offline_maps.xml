<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.OfflineMapsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Offline indicator -->
        <TextView
            android:id="@+id/offline_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorWarning"
            android:padding="8dp"
            android:text="@string/offline_mode_active"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Map container -->
        <androidx.cardview.widget.CardView
            android:id="@+id/map_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="8dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:layout_constraintBottom_toTopOf="@id/regions_title"
            app:layout_constraintTop_toBottomOf="@id/offline_indicator"
            app:layout_constraintVertical_weight="1">

            <fragment
                android:id="@+id/map"
                android:name="com.google.android.gms.maps.SupportMapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.cardview.widget.CardView>

        <!-- Download button -->
        <Button
            android:id="@+id/download_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/download_region"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/map_container"
            app:layout_constraintEnd_toEndOf="@id/map_container" />

        <!-- Draw region button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/draw_region_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:contentDescription="@string/draw_region"
            android:src="@drawable/ic_draw"
            app:layout_constraintBottom_toBottomOf="@id/map_container"
            app:layout_constraintStart_toStartOf="@id/map_container" />

        <!-- Regions title -->
        <TextView
            android:id="@+id/regions_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:text="@string/downloaded_regions"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/recycler_view"
            app:layout_constraintTop_toBottomOf="@id/map_container" />

        <!-- Regions list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="8dp"
            android:clipToPadding="false"
            android:padding="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/regions_title"
            app:layout_constraintVertical_weight="1"
            tools:listitem="@layout/item_offline_region" />

        <!-- Empty view -->
        <TextView
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="@string/no_regions_downloaded"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/recycler_view"
            app:layout_constraintTop_toTopOf="@id/recycler_view" />

        <!-- Progress bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/progress_text" />

        <!-- Progress text -->
        <TextView
            android:id="@+id/progress_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:gravity="center"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

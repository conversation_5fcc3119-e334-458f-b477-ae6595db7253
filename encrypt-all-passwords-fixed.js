// سكريبت لتشفير جميع كلمات المرور في قاعدة البيانات
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// إعدادات الاتصال بقاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'admin',
  port: 5432
});

async function encryptAllPasswords() {
  const client = await pool.connect();
  
  try {
    // بدء المعاملة
    await client.query('BEGIN');
    
    // تعطيل المشغلات مؤقتاً
    await client.query('ALTER TABLE users DISABLE TRIGGER ALL');
    
    // الحصول على جميع المستخدمين
    const usersQuery = 'SELECT id, username, password FROM users';
    const usersResult = await client.query(usersQuery);
    const users = usersResult.rows;
    
    console.log(`تم العثور على ${users.length} مستخدم في قاعدة البيانات`);
    
    // تحديث كلمات المرور لكل مستخدم
    for (const user of users) {
      // تخطي كلمات المرور المشفرة بالفعل
      if (user.password && user.password.startsWith('$2')) {
        console.log(`كلمة المرور للمستخدم ${user.username} مشفرة بالفعل، تخطي...`);
        continue;
      }
      
      // تشفير كلمة المرور
      const saltRounds = 10;
      const plainPassword = user.password || 'yemen123'; // استخدام كلمة مرور افتراضية إذا كانت فارغة
      const hashedPassword = await bcrypt.hash(plainPassword, saltRounds);
      
      // تحديث كلمة المرور في قاعدة البيانات
      const updateQuery = 'UPDATE users SET password = $1 WHERE id = $2';
      await client.query(updateQuery, [hashedPassword, user.id]);
      
      console.log(`تم تشفير كلمة المرور للمستخدم ${user.username} بنجاح`);
    }
    
    // إعادة تفعيل المشغلات
    await client.query('ALTER TABLE users ENABLE TRIGGER ALL');
    
    // تأكيد المعاملة
    await client.query('COMMIT');
    
    console.log('تم تشفير جميع كلمات المرور بنجاح');
  } catch (error) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    await client.query('ROLLBACK');
    console.error('خطأ في تشفير كلمات المرور:', error);
  } finally {
    // إعادة تفعيل المشغلات في حالة حدوث خطأ
    try {
      await client.query('ALTER TABLE users ENABLE TRIGGER ALL');
    } catch (err) {
      console.error('خطأ في إعادة تفعيل المشغلات:', err);
    }
    
    // إغلاق الاتصال بقاعدة البيانات
    client.release();
    await pool.end();
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
  }
}

// تنفيذ الدالة
encryptAllPasswords();

// public/js/offline-maps.js
class OfflineMapManager {
  constructor(map) {
    this.map = map;
    this.offlineRegions = [];
    this.isOnline = navigator.onLine;
    
    // تهيئة IndexedDB لتخزين المواقع والبيانات المحلية
    this.initDatabase();
    
    // الاستماع لأحداث الاتصال
    this._setupConnectionListeners();
    
    // واجهة المستخدم للإشعارات
    this._createNotificationUI();
  }
  
  // تهيئة قاعدة البيانات المحلية
  async initDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('YemenNavOfflineDB', 1);
      
      request.onerror = (event) => {
        console.error('فشل في فتح قاعدة البيانات المحلية:', event);
        reject(event);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // إنشاء مخزن للمواقع غير المتزامنة
        if (!db.objectStoreNames.contains('offlineLocations')) {
          const locationsStore = db.createObjectStore('offlineLocations', { keyPath: 'tempId', autoIncrement: true });
          locationsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
        
        // إنشاء مخزن لمناطق الخرائط المحفوظة
        if (!db.objectStoreNames.contains('mapRegions')) {
          const regionsStore = db.createObjectStore('mapRegions', { keyPath: 'id', autoIncrement: true });
          regionsStore.createIndex('name', 'name', { unique: false });
        }
        
        // إنشاء مخزن لمسارات التنقل المحفوظة
        if (!db.objectStoreNames.contains('savedRoutes')) {
          const routesStore = db.createObjectStore('savedRoutes', { keyPath: 'id', autoIncrement: true });
          routesStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        console.log('تم فتح قاعدة البيانات المحلية بنجاح');
        
        // استعادة المناطق المحفوظة من قاعدة البيانات
        this.loadSavedRegions();
        
        resolve(this.db);
      };
    });
  }
  
  // استعادة المناطق المحفوظة
  async loadSavedRegions() {
    if (!this.db) return;
    
    const transaction = this.db.transaction(['mapRegions'], 'readonly');
    const store = transaction.objectStore('mapRegions');
    const request = store.getAll();
    
    request.onsuccess = (event) => {
      this.offlineRegions = event.target.result || [];
      console.log(`تم تحميل ${this.offlineRegions.length} منطقة محفوظة`);
    };
    
    request.onerror = (event) => {
      console.error('فشل في تحميل المناطق المحفوظة:', event);
    };
  }
  
  // تنزيل منطقة من الخريطة للاستخدام دون اتصال
  async downloadRegion(bounds, name, zoomLevels = {}) {
    try {
      const minZoom = zoomLevels.min || 10;
      const maxZoom = zoomLevels.max || 16;
      
      // إظهار إشعار التقدم
      this.showNotification(`جاري تنزيل منطقة "${name}"...`, 'info', -1);
      
      // تحسين: التحقق من البلاطات المخزنة مسبقًا لتجنب إعادة تنزيلها
      const cache = await caches.open('yemen-nav-map-tiles-v1');
      const existingKeys = await cache.keys();
      const existingUrls = existingKeys.map(request => request.url);
      
      // الحصول على قائمة عناوين URL لبلاطات الخرائط ضمن الحدود المحددة
      const allUrls = this.getTileUrls(bounds, minZoom, maxZoom);
      
      // تصفية العناوين لاستبعاد البلاطات المخزنة مسبقًا
      const urls = allUrls.filter(url => !existingUrls.includes(url));
      
      console.log(`إجمالي البلاطات: ${allUrls.length}, البلاطات المطلوب تنزيلها: ${urls.length}, البلاطات المخزنة مسبقًا: ${allUrls.length - urls.length}`);
      
      // تحديث الإشعار بمعلومات التنزيل
      if (this.onProgressUpdate) {
        this.onProgressUpdate(0, `جاري تنزيل ${urls.length} بلاطة (${allUrls.length - urls.length} مخزنة مسبقًا)`);
      }
      
      // تنزيل البلاطات وتخزينها في التخزين المؤقت
      const cacheResult = await this.cacheTiles(urls, name);
      
      // حفظ معلومات المنطقة في قاعدة البيانات المحلية
      const region = {
        name,
        bounds: {
          north: bounds.getNorth(),
          east: bounds.getEast(),
          south: bounds.getSouth(),
          west: bounds.getWest()
        },
        zoomLevels: { min: minZoom, max: maxZoom },
        tileCount: allUrls.length,
        downloadedTileCount: cacheResult.successCount,
        cachedTileCount: allUrls.length - urls.length,
        timestamp: new Date().getTime(),
        lastUsed: new Date().getTime()
      };
      
      await this.saveRegion(region);
      
      // إظهار إشعار النجاح
      this.showNotification(`تم تنزيل منطقة "${name}" بنجاح (${cacheResult.successCount} بلاطة جديدة، ${allUrls.length - urls.length} مخزنة مسبقًا)`, 'success');
      
      return {
        success: true,
        region: region,
        totalTiles: allUrls.length,
        downloadedTiles: cacheResult.successCount,
        cachedTiles: allUrls.length - urls.length,
        failedTiles: cacheResult.failedCount
      };
    } catch (error) {
      console.error('فشل في تنزيل المنطقة:', error);
      
      // إظهار إشعار الخطأ
      this.showNotification(`فشل في تنزيل المنطقة: ${error.message}`, 'error');
      
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  // تحسين وظيفة الحصول على عناوين URL للبلاطات
  getTileUrls(bounds, minZoom, maxZoom) {
    const urls = [];
    const uniqueTiles = new Set(); // لتجنب التكرار
    
    // تحسين: تقليل عدد البلاطات في المستويات العالية من التكبير
    for (let zoom = minZoom; zoom <= maxZoom; zoom++) {
      const northEast = bounds.getNorthEast();
      const southWest = bounds.getSouthWest();
      
      const neTile = this.latLngToTile(northEast.lat, northEast.lng, zoom);
      const swTile = this.latLngToTile(southWest.lat, southWest.lng, zoom);
      
      // حساب عدد البلاطات في هذا المستوى
      const tilesCount = (neTile.x - swTile.x + 1) * (swTile.y - neTile.y + 1);
      
      // تحسين: تخطي المستويات ذات العدد الكبير جدًا من البلاطات
      const maxTilesPerZoomLevel = 1000; // الحد الأقصى للبلاطات في كل مستوى تكبير
      if (tilesCount > maxTilesPerZoomLevel && zoom > minZoom + 2) {
        console.warn(`تخطي مستوى التكبير ${zoom} لأنه يحتوي على عدد كبير جدًا من البلاطات (${tilesCount})`);
        continue;
      }
      
      // تحسين: تقليل كثافة البلاطات في المستويات العالية
      const skipFactor = zoom > maxZoom - 2 ? 2 : 1; // تخطي بعض البلاطات في المستويات العالية
      
      for (let x = swTile.x; x <= neTile.x; x += skipFactor) {
        for (let y = neTile.y; y <= swTile.y; y += skipFactor) {
          // تحسين: استخدام مصدر واحد فقط للبلاطات لكل موقع
          if (this.useLocalServer) {
            const localUrl = `${this.localTileServer}/${zoom}/${x}/${y}.png`;
            if (!uniqueTiles.has(localUrl)) {
              uniqueTiles.add(localUrl);
              urls.push(localUrl);
            }
          } else if (this.useExternalServers) {
            const osmUrl = `https://a.tile.openstreetmap.org/${zoom}/${x}/${y}.png`;
            if (!uniqueTiles.has(osmUrl)) {
              uniqueTiles.add(osmUrl);
              urls.push(osmUrl);
            }
            
            // إضافة مصادر بديلة للخرائط فقط للمستويات المنخفضة
            if (zoom <= 14 && zoom >= minZoom) {
              const arcgisUrl = `https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/${zoom}/${y}/${x}`;
              if (!uniqueTiles.has(arcgisUrl)) {
                uniqueTiles.add(arcgisUrl);
                urls.push(arcgisUrl);
              }
            }
          }
        }
      }
    }
    
    return urls;
  }
  
  // تحسين وظيفة تخزين البلاطات
  async cacheTiles(urls, regionName) {
    try {
      const cache = await caches.open('yemen-nav-map-tiles-v1');
      const batchSize = 20; // عدد البلاطات للتنزيل في وقت واحد
      const totalBatches = Math.ceil(urls.length / batchSize);
      let successCount = 0;
      let failedUrls = [];
      
      // تحسين: تنفيذ آلية إلغاء التنزيل
      this.isCancellingDownload = false;
      
      for (let i = 0; i < totalBatches; i++) {
        // التحقق من طلب الإلغاء
        if (this.isCancellingDownload) {
          console.log('تم إلغاء التنزيل بواسطة المستخدم');
          throw new Error('تم إلغاء التنزيل بواسطة المستخدم');
        }
        
        // تحديث إشعار التقدم
        const progress = Math.round((i / totalBatches) * 100);
        this.updateNotificationProgress(`جاري تنزيل منطقة "${regionName}"... (${progress}%)`, progress);
        
        // استدعاء دالة تحديث التقدم إذا كانت موجودة
        if (this.onProgressUpdate) {
          const downloadedSoFar = i * batchSize;
          const remainingTiles = urls.length - downloadedSoFar;
          this.onProgressUpdate(progress, `تم تنزيل ${downloadedSoFar} من ${urls.length} بلاطة (${successCount} ناجحة، ${failedUrls.length} فاشلة)`);
        }
        
        const batchStart = i * batchSize;
        const batchUrls = urls.slice(batchStart, batchStart + batchSize);
        
        // تحسين: استخدام وقت انتهاء صلاحية طويل للبلاطات
        const cacheOptions = {
          headers: {
            'Cache-Control': 'max-age=31536000' // سنة واحدة
          }
        };
        
        const batchPromises = batchUrls.map(async url => {
          try {
            // محاولة تنزيل البلاطة
            const response = await fetch(url, { 
              mode: 'cors',
              cache: 'no-cache',
              headers: {
                'Cache-Control': 'no-cache'
              }
            });
            
            if (response.ok) {
              await cache.put(url, response.clone());
              successCount++;
              return { success: true, url };
            } else {
              failedUrls.push(url);
              console.warn(`فشل في تنزيل البلاطة: ${url} - الحالة: ${response.status}`);
              return { success: false, url, status: response.status };
            }
          } catch (err) {
            failedUrls.push(url);
            console.warn(`خطأ في تنزيل البلاطة: ${url}`, err);
            return { success: false, url, error: err.message };
          }
        });
        
        const results = await Promise.all(batchPromises);
        
        // إعادة محاولة تنزيل البلاطات الفاشلة من مصدر بديل إذا كانت من الخادم المحلي
        const failedLocalTiles = results
          .filter(r => !r.success && r.url.startsWith(this.localTileServer))
          .map(r => {
            // استبدال المسار المحلي بمسار خارجي
            const urlParts = r.url.replace(this.localTileServer, '').split('/');
            if (urlParts.length >= 4) {
              const [, zoom, x, y] = urlParts;
              return `https://a.tile.openstreetmap.org/${zoom}/${x}/${y}`;
            }
            return null;
          })
          .filter(url => url !== null);
          
        if (failedLocalTiles.length > 0) {
          console.log(`إعادة محاولة تنزيل ${failedLocalTiles.length} بلاطة من مصدر بديل`);
          
          const retryPromises = failedLocalTiles.map(async url => {
            try {
              const response = await fetch(url, { mode: 'cors' });
              if (response.ok) {
                await cache.put(url, response.clone());
                successCount++;
                failedUrls = failedUrls.filter(u => u !== url);
                return true;
              }
              return false;
            } catch (err) {
              console.warn(`فشل في تنزيل البلاطة من المصدر البديل: ${url}`, err);
              return false;
            }
          });
          
          await Promise.all(retryPromises);
        }
      }
      
      // تحديث إشعار التقدم إلى 100%
      this.updateNotificationProgress(`تم تنزيل منطقة "${regionName}"... (100%)`, 100);
      
      console.log(`تم تنزيل ${successCount} بلاطة من أصل ${urls.length} بنجاح`);
      
      if (failedUrls.length > 0) {
        console.warn(`فشل في تنزيل ${failedUrls.length} بلاطة`);
      }
      
      return {
        success: true,
        totalTiles: urls.length,
        successCount: successCount,
        failedCount: failedUrls.length,
        failedUrls: failedUrls
      };
    } catch (error) {
      console.error('فشل في تخزين البلاطات في التخزين المؤقت:', error);
      throw error;
    }
  }
  
  // إضافة وظيفة لإلغاء التنزيل
  cancelDownload() {
    this.isCancellingDownload = true;
    this.showNotification('جاري إلغاء التنزيل...', 'warning');
  }
  
  // إضافة وظيفة لإدارة مساحة التخزين
  async manageStorageSpace(maxSizeMB = 500) {
    try {
      // الحصول على حجم التخزين المستخدم
      const cacheStorage = await caches.open('yemen-nav-map-tiles-v1');
      const keys = await cacheStorage.keys();
      
      // تقدير الحجم (متوسط حجم البلاطة حوالي 15 كيلوبايت)
      const estimatedSizeKB = keys.length * 15;
      const estimatedSizeMB = estimatedSizeKB / 1024;
      
      console.log(`الحجم التقديري للتخزين المؤقت: ${estimatedSizeMB.toFixed(2)} ميجابايت (${keys.length} بلاطة)`);
      
      // إذا كان الحجم أكبر من الحد الأقصى، قم بحذف البلاطات الأقدم
      if (estimatedSizeMB > maxSizeMB) {
        // الحصول على المناطق المحفوظة مرتبة حسب تاريخ آخر استخدام
        const regions = await this.getRegionsSortedByLastUsed();
        
        // حذف البلاطات من المناطق الأقدم حتى يصبح الحجم أقل من الحد الأقصى
        let deletedTiles = 0;
        let deletedRegions = 0;
        
        for (const region of regions) {
          // تخطي المناطق المستخدمة مؤخرًا
          const lastUsedDays = (new Date().getTime() - region.lastUsed) / (1000 * 60 * 60 * 24);
          if (lastUsedDays < 30) { // تخطي المناطق المستخدمة في آخر 30 يوم
            continue;
          }
          
          // حذف بلاطات المنطقة
          const regionBounds = L.latLngBounds(
            L.latLng(region.bounds.south, region.bounds.west),
            L.latLng(region.bounds.north, region.bounds.east)
          );
          
          const regionTileUrls = this.getTileUrls(regionBounds, region.zoomLevels.min, region.zoomLevels.max);
          
          for (const url of regionTileUrls) {
            await cacheStorage.delete(url);
            deletedTiles++;
          }
          
          // حذف المنطقة من قاعدة البيانات
          await this.deleteRegion(region.id);
          deletedRegions++;
          
          // التحقق من الحجم الجديد
          const remainingKeys = await cacheStorage.keys();
          const newSizeMB = (remainingKeys.length * 15) / 1024;
          
          if (newSizeMB < maxSizeMB * 0.8) { // ترك هامش 20%
            break;
          }
        }
        
        console.log(`تم تحرير مساحة التخزين: تم حذف ${deletedTiles} بلاطة من ${deletedRegions} منطقة`);
        
        // إظهار إشعار للمستخدم
        if (deletedRegions > 0) {
          this.showNotification(`تم تحرير مساحة التخزين تلقائيًا: تم حذف ${deletedRegions} منطقة غير مستخدمة`, 'info');
        }
      }
      
      return {
        success: true,
        sizeMB: estimatedSizeMB,
        tileCount: keys.length
      };
    } catch (error) {
      console.error('فشل في إدارة مساحة التخزين:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  // الحصول على المناطق مرتبة حسب تاريخ آخر استخدام
  async getRegionsSortedByLastUsed() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('قاعدة البيانات المحلية غير متاحة'));
        return;
      }
      
      const transaction = this.db.transaction(['mapRegions'], 'readonly');
      const store = transaction.objectStore('mapRegions');
      const request = store.getAll();
      
      request.onsuccess = (event) => {
        const regions = event.target.result || [];
        // ترتيب المناطق حسب تاريخ آخر استخدام (الأقدم أولاً)
        regions.sort((a, b) => (a.lastUsed || a.timestamp) - (b.lastUsed || b.timestamp));
        resolve(regions);
      };
      
      request.onerror = (event) => {
        reject(event);
      };
    });
  }
  
  // تحديث تاريخ آخر استخدام للمنطقة
  async updateRegionLastUsed(regionId) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('قاعدة البيانات المحلية غير متاحة'));
        return;
      }
      
      const transaction = this.db.transaction(['mapRegions'], 'readwrite');
      const store = transaction.objectStore('mapRegions');
      const getRequest = store.get(regionId);
      
      getRequest.onsuccess = (event) => {
        const region = event.target.result;
        if (region) {
          region.lastUsed = new Date().getTime();
          const updateRequest = store.put(region);
          
          updateRequest.onsuccess = () => {
            resolve(region);
          };
          
          updateRequest.onerror = (event) => {
            reject(event);
          };
        } else {
          reject(new Error('المنطقة غير موجودة'));
        }
      };
      
      getRequest.onerror = (event) => {
        reject(event);
      };
    });
  }
  
  // حفظ معلومات المنطقة في قاعدة البيانات المحلية
  async saveRegion(region) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('قاعدة البيانات المحلية غير متاحة'));
        return;
      }
      
      const transaction = this.db.transaction(['mapRegions'], 'readwrite');
      const store = transaction.objectStore('mapRegions');
      const request = store.add(region);
      
      request.onsuccess = (event) => {
        const id = event.target.result;
        region.id = id;
        this.offlineRegions.push(region);
        console.log(`تم حفظ المنطقة ${region.name} بمعرف ${id}`);
        resolve(region);
      };
      
      request.onerror = (event) => {
        console.error('فشل في حفظ المنطقة:', event);
        reject(event);
      };
    });
  }
  
  // حذف منطقة محفوظة
  async deleteRegion(regionId) {
    try {
      if (!this.db) {
        throw new Error('قاعدة البيانات المحلية غير متاحة');
      }
      
      // الحصول على معلومات المنطقة
      const region = this.offlineRegions.find(r => r.id === regionId);
      if (!region) {
        throw new Error('المنطقة غير موجودة');
      }
      
      // حذف المنطقة من قاعدة البيانات
      const transaction = this.db.transaction(['mapRegions'], 'readwrite');
      const store = transaction.objectStore('mapRegions');
      await new Promise((resolve, reject) => {
        const request = store.delete(regionId);
        request.onsuccess = resolve;
        request.onerror = reject;
      });
      
      // تحديث القائمة المحلية
      this.offlineRegions = this.offlineRegions.filter(r => r.id !== regionId);
      
      // إظهار إشعار النجاح
      this.showNotification(`تم حذف منطقة "${region.name}" بنجاح`, 'success');
      
      return { success: true };
    } catch (error) {
      console.error('فشل في حذف المنطقة:', error);
      this.showNotification(`فشل في حذف المنطقة: ${error.message}`, 'error');
      return { success: false, error: error.message };
    }
  }
  
  // الحصول على عناوين URL لبلاطات الخرائط
  latLngToTile(lat, lng, zoom) {
    const n = Math.pow(2, zoom);
    const x = Math.floor((lng + 180) / 360 * n);
    const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
    return { x, y };
  }
  
  // حفظ موقع في وضع عدم الاتصال
  async saveOfflineLocation(location) {
    if (!this.db) {
      throw new Error('قاعدة البيانات المحلية غير متاحة');
    }
    
    // إضافة طابع زمني وحالة المزامنة
    const locationWithMeta = {
      ...location,
      timestamp: new Date().getTime(),
      synced: false,
      attemptedSync: false
    };
    
    // حفظ في قاعدة البيانات المحلية
    const transaction = this.db.transaction(['offlineLocations'], 'readwrite');
    const store = transaction.objectStore('offlineLocations');
    
    return new Promise((resolve, reject) => {
      const request = store.add(locationWithMeta);
      
      request.onsuccess = (event) => {
        const tempId = event.target.result;
        locationWithMeta.tempId = tempId;
        
        // إظهار إشعار
        if (!this.isOnline) {
          this.showNotification('تم حفظ الموقع محلياً. ستتم المزامنة عند استعادة الاتصال.', 'info');
        }
        
        // محاولة المزامنة إذا كان متصلاً
        if (this.isOnline) {
          this.syncOfflineLocations();
        }
        
        resolve(locationWithMeta);
      };
      
      request.onerror = (event) => {
        console.error('فشل في حفظ الموقع محلياً:', event);
        reject(event);
      };
    });
  }
  
  // مزامنة المواقع المحفوظة محلياً
  async syncOfflineLocations() {
    if (!this.isOnline || !this.db) return;
    
    try {
      // الحصول على المواقع غير المتزامنة
      const transaction = this.db.transaction(['offlineLocations'], 'readonly');
      const store = transaction.objectStore('offlineLocations');
      const unsyncedLocations = await new Promise((resolve, reject) => {
        const request = store.index('timestamp').openCursor();
        const locations = [];
        
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const location = cursor.value;
            if (!location.synced) {
              locations.push(location);
            }
            cursor.continue();
          } else {
            resolve(locations);
          }
        };
        
        request.onerror = reject;
      });
      
      if (unsyncedLocations.length === 0) return;
      
      // محاولة المزامنة مع الخادم
      this.showNotification(`جاري مزامنة ${unsyncedLocations.length} موقع...`, 'info');
      
      const response = await fetch('/api/sync/locations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(unsyncedLocations)
      });
      
      if (!response.ok) {
        throw new Error(`فشل في المزامنة: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      // تحديث حالة المزامنة في قاعدة البيانات
      const updateTx = this.db.transaction(['offlineLocations'], 'readwrite');
      const updateStore = updateTx.objectStore('offlineLocations');
      
      for (const location of unsyncedLocations) {
        updateStore.put({
          ...location,
          synced: true,
          syncedAt: new Date().getTime()
        });
      }
      
      this.showNotification(`تمت مزامنة ${result.syncedCount} موقع بنجاح`, 'success');
    } catch (error) {
      console.error('فشل في مزامنة المواقع:', error);
      this.showNotification('فشل في مزامنة المواقع. سيتم إعادة المحاولة لاحقاً.', 'error');
      
      // وضع علامة على محاولة المزامنة
      const updateTx = this.db.transaction(['offlineLocations'], 'readwrite');
      const updateStore = updateTx.objectStore('offlineLocations');
      
      const unsyncedLocations = await new Promise((resolve, reject) => {
        const request = updateStore.index('timestamp').openCursor();
        const locations = [];
        
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const location = cursor.value;
            if (!location.synced) {
              updateStore.put({
                ...location,
                attemptedSync: true,
                lastSyncAttempt: new Date().getTime()
              });
            }
            cursor.continue();
          } else {
            resolve(locations);
          }
        };
        
        request.onerror = reject;
      });
    }
  }
  
  // إعداد مستمعي حدث الاتصال
  _setupConnectionListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
      
      // مزامنة البيانات عند استعادة الاتصال
      this.syncOfflineLocations();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showNotification('أنت الآن في وضع عدم الاتصال. سيتم حفظ التغييرات محلياً.', 'warning');
    });
  }
  
  // إنشاء واجهة المستخدم للإشعارات
  _createNotificationUI() {
    // إنشاء عنصر الإشعار
    const notificationElement = document.createElement('div');
    notificationElement.id = 'offline-notification';
    notificationElement.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      z-index: 10000;
      max-width: 300px;
      display: none;
      direction: rtl;
      font-family: Arial, sans-serif;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    `;
    
    // إضافة شريط التقدم
    const progressBar = document.createElement('div');
    progressBar.id = 'offline-notification-progress';
    progressBar.style.cssText = `
      height: 4px;
      background-color: #4CAF50;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0%;
      transition: width 0.3s;
    `;
    notificationElement.appendChild(progressBar);
    
    // إضافة زر الإغلاق
    const closeButton = document.createElement('span');
    closeButton.innerHTML = '&times;';
    closeButton.style.cssText = `
      position: absolute;
      top: 5px;
      left: 10px;
      cursor: pointer;
      font-size: 20px;
    `;
    closeButton.onclick = () => {
      notificationElement.style.display = 'none';
      if (this.notificationTimeout) {
        clearTimeout(this.notificationTimeout);
      }
    };
    notificationElement.appendChild(closeButton);
    
    // إضافة محتوى الإشعار
    const contentElement = document.createElement('div');
    contentElement.id = 'offline-notification-content';
    notificationElement.appendChild(contentElement);
    
    document.body.appendChild(notificationElement);
    this.notificationElement = notificationElement;
    this.notificationContentElement = contentElement;
    this.notificationProgressElement = progressBar;
  }
  
  // عرض إشعار للمستخدم
  showNotification(message, type = 'info', duration = 5000) {
    if (!this.notificationElement || !this.notificationContentElement) {
      this._createNotificationUI();
    }
    
    // تعيين اللون حسب النوع
    let color;
    switch (type) {
      case 'success':
        color = '#4CAF50'; // أخضر
        break;
      case 'error':
        color = '#F44336'; // أحمر
        break;
      case 'warning':
        color = '#FF9800'; // برتقالي
        break;
      default:
        color = '#2196F3'; // أزرق
    }
    
    this.notificationContentElement.textContent = message;
    this.notificationElement.style.backgroundColor = `rgba(0, 0, 0, 0.7)`;
    this.notificationElement.style.borderTop = `3px solid ${color}`;
    this.notificationElement.style.display = 'block';
    
    // إعادة تعيين شريط التقدم
    this.notificationProgressElement.style.width = '0%';
    this.notificationProgressElement.style.backgroundColor = color;
    
    // مسح المؤقت السابق
    if (this.notificationTimeout) {
      clearTimeout(this.notificationTimeout);
    }
    
    // إذا كانت المدة موجبة، قم بإخفاء الإشعار بعد المدة المحددة
    if (duration > 0) {
      this.notificationTimeout = setTimeout(() => {
        if (this.notificationElement) {
          this.notificationElement.style.display = 'none';
        }
      }, duration);
    }
  }
  
  // تحديث تقدم الإشعار
  updateNotificationProgress(message, percentage) {
    if (!this.notificationElement || !this.notificationContentElement) return;
    
    this.notificationContentElement.textContent = message;
    this.notificationProgressElement.style.width = `${percentage}%`;
    this.notificationElement.style.display = 'block';
  }
}

// إضافة الوحدة إلى النافذة العالمية
window.OfflineMapManager = OfflineMapManager;

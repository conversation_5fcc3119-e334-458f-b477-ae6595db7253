// ملف لإصلاح مشكلة المصادقة في لوحة التحكم
const { Pool } = require('pg');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');

// تحميل متغيرات البيئة
dotenv.config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'admin',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('محاولة الاتصال بقاعدة البيانات باستخدام:');
console.log(`host: ${dbConfig.host}, port: ${dbConfig.port}, database: ${dbConfig.database}, user: ${dbConfig.user}`);

const pool = new Pool(dbConfig);

// إصلاح ملف server.js للسماح بالوصول إلى مسارات API الإدارية
async function fixAdminAuth() {
    try {
        console.log('جاري إصلاح مشكلة المصادقة في ملفات الخادم...');

        // التحقق من صلاحيات المستخدم admin
        const userQuery = await pool.query(`
            SELECT * FROM users WHERE username = 'admin';
        `);

        if (userQuery.rows.length > 0) {
            const adminUser = userQuery.rows[0];
            
            // تحديث صلاحيات المستخدم admin
            await pool.query(`
                UPDATE users 
                SET permissions = '{"view_dashboard": true, "manage_users": true, "manage_locations": true, "manage_categories": true, "manage_clients": true}'::jsonb,
                    role_id = 1,
                    is_active = true
                WHERE username = 'admin';
            `);
            
            console.log('تم تحديث صلاحيات المستخدم admin بنجاح');
        } else {
            console.log('المستخدم admin غير موجود في قاعدة البيانات');
        }

        // إصلاح ملف admin.html لإرسال توكن المصادقة مع طلبات API
        const adminHtmlPath = path.join(__dirname, 'public', 'admin.html');
        if (fs.existsSync(adminHtmlPath)) {
            let adminHtml = fs.readFileSync(adminHtmlPath, 'utf8');
            
            // تحسين كود إرسال طلبات API مع إضافة رأس المصادقة
            const apiCallsFix = `
            // دالة مساعدة لإضافة رأس المصادقة إلى طلبات API
            function fetchWithAuth(url, options = {}) {
                // الحصول على توكن المصادقة من التخزين المحلي أو تخزين الجلسة
                const token = localStorage.getItem('yemenGpsToken') || sessionStorage.getItem('yemenGpsToken');
                
                // إعداد خيارات الطلب مع رأس المصادقة
                const fetchOptions = {
                    ...options,
                    headers: {
                        ...options.headers,
                        'Content-Type': 'application/json',
                        'Authorization': token ? \`Bearer \${token}\` : ''
                    }
                };
                
                return fetch(url, fetchOptions);
            }

            // تعديل دوال تحميل البيانات لاستخدام fetchWithAuth
            function loadUsers() {
                console.log('جاري تحميل المستخدمين...');
                return fetchWithAuth('/api/admin/users')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('فشل في تحميل المستخدمين');
                        }
                        return response.json();
                    })
                    .then(users => {
                        console.log('تم تحميل المستخدمين:', users);
                        displayUsers(users);
                        return users;
                    })
                    .catch(error => {
                        console.error('خطأ في تحميل المستخدمين:', error);
                    });
            }
            `;
            
            // إضافة الكود المحسن إلى ملف admin.html
            // هذه طريقة مبسطة، في العادة نحتاج إلى تحليل HTML بشكل أفضل
            if (!adminHtml.includes('fetchWithAuth')) {
                const scriptPos = adminHtml.lastIndexOf('</script>');
                if (scriptPos > 0) {
                    adminHtml = adminHtml.substring(0, scriptPos) + apiCallsFix + adminHtml.substring(scriptPos);
                    fs.writeFileSync(adminHtmlPath + '.new', adminHtml, 'utf8');
                    console.log(`تم إنشاء نسخة محسنة من ملف admin.html في: ${adminHtmlPath}.new`);
                    console.log('يرجى استبدال الملف الأصلي بالنسخة الجديدة يدويًا لتفادي أي مشاكل');
                }
            }
        }

        console.log('اكتمل إصلاح مشكلة المصادقة');
        console.log('يرجى إعادة تشغيل الخادم وتجربة تسجيل الدخول مرة أخرى');
    } catch (err) {
        console.error('خطأ في إصلاح مشكلة المصادقة:', err.message);
    } finally {
        // إغلاق الاتصال
        pool.end();
    }
}

// تنفيذ الإصلاح
fixAdminAuth();

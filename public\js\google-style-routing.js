// public/js/google-style-routing.js
// محاكاة نمط خرائط جوجل في عرض المسارات

(function() {
  'use strict';

  /**
   * كائن عام للتعامل مع عرض المسارات بنمط جوجل
   */
  const GoogleStyleRouting = {
    // المتغيرات العامة
    map: null,
    startMarker: null,
    endMarker: null,
    routePaths: [],
    activeRouteIndex: 0,
    routeInfoPanel: null,
    routeOverview: null,
    routeControls: null,
    travelModes: ['car', 'walk', 'bike'],
    activeTravelMode: 'car',
    routeColors: {
      active: '#0066FF',      // أزرق غامق للمسار النشط
      inactive: '#99B3E6',    // أزرق فاتح للمسارات البديلة
      highlight: '#4285F4',   // لون جوجل الأزرق عند التحويم
      shadow: '#7BAAF7'       // ظل خفيف
    },

    /**
     * تهيئة المكتبة مع الخريطة
     * @param {Object} mapInstance كائن الخريطة من Leaflet
     */
    init: function(mapInstance) {
      this.map = mapInstance;
      this.initMapListeners();
      return this;
    },

    /**
     * إضافة مستمعي الأحداث للخريطة
     */
    initMapListeners: function() {
      const self = this;

      // تتبع عند النقر على المسارات البديلة
      this.map.on('overlayclick', function(e) {
        if (e.layer && e.layer._googleRouteId !== undefined) {
          self.setActiveRoute(e.layer._googleRouteId);
        }
      });
    },

    /**
     * مسح كافة المسارات والعلامات من الخريطة
     */
    clearRoutes: function() {
      // إزالة مسارات الطرق
      this.routePaths.forEach(route => {
        if (route.path) {
          this.map.removeLayer(route.path);
        }
        if (route.shadow) {
          this.map.removeLayer(route.shadow);
        }
      });

      // إزالة العلامات
      if (this.startMarker) {
        this.map.removeLayer(this.startMarker);
      }

      if (this.endMarker) {
        this.map.removeLayer(this.endMarker);
      }

      // إزالة لوحة المعلومات
      this.removeRouteInfoPanel();

      // إعادة ضبط المتغيرات
      this.routePaths = [];
      this.activeRouteIndex = 0;
    },

    /**
     * تعيين نقطة البداية
     * @param {Array} latLng إحداثيات نقطة البداية [خط العرض، خط الطول]
     * @param {String} name اسم نقطة البداية (اختياري)
     */
    setStartPoint: function(latLng, name) {
      // إزالة العلامة السابقة إن وجدت
      if (this.startMarker) {
        this.map.removeLayer(this.startMarker);
      }

      // إنشاء عنصر HTML مخصص للعلامة
      const markerIcon = L.divIcon({
        html: '<div class="google-start-marker">A</div>',
        className: 'google-marker-container',
        iconSize: [30, 30],
        iconAnchor: [15, 30]
      });

      // إضافة العلامة إلى الخريطة
      this.startMarker = L.marker(latLng, {
        icon: markerIcon,
        zIndexOffset: 1000,
        draggable: true
      }).addTo(this.map);

      // إضافة حدث التحريك
      const self = this;
      this.startMarker.on('dragend', function(e) {
        // إذا كانت نقطة النهاية موجودة، أعد حساب المسار
        if (self.endMarker) {
          self.findRoutes(
            e.target.getLatLng(),
            self.endMarker.getLatLng()
          );
        }
      });

      return this.startMarker;
    },

    /**
     * تعيين نقطة النهاية
     * @param {Array} latLng إحداثيات نقطة النهاية [خط العرض، خط الطول]
     * @param {String} name اسم نقطة النهاية (اختياري)
     */
    setEndPoint: function(latLng, name) {
      // إزالة العلامة السابقة إن وجدت
      if (this.endMarker) {
        this.map.removeLayer(this.endMarker);
      }

      // إنشاء عنصر HTML مخصص للعلامة
      const markerIcon = L.divIcon({
        html: '<div class="google-end-marker">B</div>',
        className: 'google-marker-container',
        iconSize: [30, 30],
        iconAnchor: [15, 30]
      });

      // إضافة العلامة إلى الخريطة
      this.endMarker = L.marker(latLng, {
        icon: markerIcon,
        zIndexOffset: 1000,
        draggable: true
      }).addTo(this.map);

      // إضافة حدث التحريك
      const self = this;
      this.endMarker.on('dragend', function(e) {
        // إذا كانت نقطة البداية موجودة، أعد حساب المسار
        if (self.startMarker) {
          self.findRoutes(
            self.startMarker.getLatLng(),
            e.target.getLatLng()
          );
        }
      });

      return this.endMarker;
    },

    /**
     * إيجاد المسارات بين نقطتين
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع الانتقال (car, walk, bike)
     */
    findRoutes: function(startLatLng, endLatLng, mode) {
      this.clearRoutes();

      // تعيين العلامات
      this.setStartPoint(startLatLng);
      this.setEndPoint(endLatLng);

      // تعيين وضع التنقل
      if (mode) {
        this.activeTravelMode = mode;
      }

      // البحث عن المسارات (استخدام دالة تقليد لمحاكاة الاتصال بـ API)
      const self = this;
      this.mockRoutingRequest(startLatLng, endLatLng, this.activeTravelMode, function(routes) {
        self.displayRoutes(routes);
      });
    },

    /**
     * عرض المسارات على الخريطة
     * @param {Array} routes مصفوفة من كائنات المسارات
     */
    displayRoutes: function(routes) {
      if (!routes || routes.length === 0) {
        console.error('لم يتم العثور على مسارات');
        return;
      }

      const self = this;

      // تخزين جميع المسارات
      this.routePaths = routes.map((route, index) => {
        // إنشاء ظل للمسار (لتأثير بصري أفضل)
        const shadow = L.polyline(route.geometry, {
          color: this.routeColors.shadow,
          weight: 8,
          opacity: 0.4,
          lineCap: 'round',
          lineJoin: 'round'
        });

        // إنشاء المسار الرئيسي
        const path = L.polyline(route.geometry, {
          color: index === this.activeRouteIndex ? this.routeColors.active : this.routeColors.inactive,
          weight: 5,
          opacity: 0.9,
          lineCap: 'round',
          lineJoin: 'round'
        });

        // تخزين معرف المسار لاستخدامه في أحداث النقر
        path._googleRouteId = index;

        // إضافة أحداث التفاعل
        path.on('mouseover', function() {
          if (index !== self.activeRouteIndex) {
            this.setStyle({ color: self.routeColors.highlight });
          }
        });

        path.on('mouseout', function() {
          if (index !== self.activeRouteIndex) {
            this.setStyle({ color: self.routeColors.inactive });
          }
        });

        path.on('click', function() {
          self.setActiveRoute(index);
        });

        // إضافة الظل والمسار إلى الخريطة
        shadow.addTo(this.map);
        path.addTo(this.map);

        return {
          path: path,
          shadow: shadow,
          data: route
        };
      });

      // التأكد من أن activeRouteIndex صحيح
      this.activeRouteIndex = Math.min(this.activeRouteIndex, this.routePaths.length - 1);

      // عرض لوحة معلومات المسار
      this.displayRouteInfoPanel();

      // ضبط حدود الخريطة لعرض المسار كاملاً
      const bounds = this.routePaths[this.activeRouteIndex].path.getBounds();
      this.map.fitBounds(bounds, { padding: [50, 50] });
    },

    /**
     * تعيين المسار النشط
     * @param {Number} index رقم المسار في المصفوفة
     */
    setActiveRoute: function(index) {
      if (index < 0 || index >= this.routePaths.length) {
        return;
      }

      // إعادة تعيين ألوان جميع المسارات
      this.routePaths.forEach((route, i) => {
        route.path.setStyle({
          color: i === index ? this.routeColors.active : this.routeColors.inactive,
          weight: i === index ? 5 : 4
        });

        // تغيير ترتيب العرض ليظهر المسار النشط في المقدمة
        if (i === index) {
          route.path.bringToFront();
        }
      });

      // تحديث المسار النشط
      this.activeRouteIndex = index;

      // تحديث لوحة المعلومات
      this.updateRouteInfoPanel();
    },

    /**
     * عرض لوحة معلومات المسار
     */
    displayRouteInfoPanel: function() {
      // إزالة اللوحة السابقة إن وجدت
      this.removeRouteInfoPanel();

      // إنشاء عنصر DIV للوحة المعلومات
      const panel = document.createElement('div');
      panel.className = 'google-route-panel';
      panel.dir = 'rtl';

      // إنشاء شريط التبديل بين وسائل التنقل
      const travelModeBar = document.createElement('div');
      travelModeBar.className = 'google-travel-modes';

      this.travelModes.forEach(mode => {
        const btn = document.createElement('button');
        btn.className = 'google-travel-mode-btn' + (mode === this.activeTravelMode ? ' active' : '');

        let iconHtml = '🚗';
        if (mode === 'walk') iconHtml = '🚶';
        if (mode === 'bike') iconHtml = '🚲';

        btn.innerHTML = iconHtml;
        btn.dataset.mode = mode;

        btn.addEventListener('click', () => {
          this.activeTravelMode = mode;

          // تحديث تنسيق الأزرار
          document.querySelectorAll('.google-travel-mode-btn').forEach(el => {
            el.classList.remove('active');
          });
          btn.classList.add('active');

          // إعادة حساب المسار إذا كانت نقاط البداية والنهاية موجودة
          if (this.startMarker && this.endMarker) {
            this.findRoutes(
              this.startMarker.getLatLng(),
              this.endMarker.getLatLng(),
              mode
            );
          }
        });

        travelModeBar.appendChild(btn);
      });

      // إضافة شريط وسائل التنقل إلى اللوحة
      panel.appendChild(travelModeBar);

      // إنشاء عنصر لنظرة عامة على المسار
      this.routeOverview = document.createElement('div');
      this.routeOverview.className = 'google-route-overview';

      // إنشاء عنصر للمسارات البديلة
      this.routeControls = document.createElement('div');
      this.routeControls.className = 'google-route-controls';

      // إضافة معلومات المسار
      this.updateRouteInfoPanel();

      // إضافة العناصر إلى اللوحة
      panel.appendChild(this.routeOverview);
      panel.appendChild(this.routeControls);

      // إضافة اللوحة إلى DOM
      document.body.appendChild(panel);
      this.routeInfoPanel = panel;
    },

    /**
     * تحديث لوحة معلومات المسار
     */
    updateRouteInfoPanel: function() {
      if (!this.routeOverview || !this.routeControls) {
        return;
      }

      const activeRoute = this.routePaths[this.activeRouteIndex].data;

      // تحديث النظرة العامة
      let totalDistance = activeRoute.distance.toFixed(1);
      let totalDuration = Math.round(activeRoute.duration);

      // تنسيق الوقت
      let durationText = '';
      if (totalDuration >= 60) {
        const hours = Math.floor(totalDuration / 60);
        const minutes = totalDuration % 60;
        durationText = hours + ' ساعة و' + minutes + ' دقيقة';
      } else {
        durationText = totalDuration + ' دقيقة';
      }

      this.routeOverview.innerHTML = `
        <div class="route-overview-header">
          <div class="route-overview-icon">${this.activeTravelMode === 'walk' ? '🚶' : (this.activeTravelMode === 'bike' ? '🚲' : '🚗')}</div>
          <div class="route-overview-info">
            <div class="route-duration">${durationText}</div>
            <div class="route-distance">${totalDistance} كم</div>
          </div>
        </div>
      `;

      // تحديث المسارات البديلة
      this.routeControls.innerHTML = '';

      if (this.routePaths.length > 1) {
        // إنشاء عنوان
        const controlsHeader = document.createElement('div');
        controlsHeader.className = 'route-controls-header';
        controlsHeader.textContent = 'مسارات بديلة';
        this.routeControls.appendChild(controlsHeader);

        // إنشاء أزرار للمسارات البديلة
        const routeButtons = document.createElement('div');
        routeButtons.className = 'route-buttons';

        this.routePaths.forEach((route, index) => {
          const routeBtn = document.createElement('button');
          routeBtn.className = 'route-btn' + (index === this.activeRouteIndex ? ' active' : '');

          // حساب وقت إضافي للمسارات البديلة
          let timeText = '';
          if (index === this.activeRouteIndex) {
            timeText = 'الأسرع';
          } else {
            const timeDiff = Math.round(route.data.duration - this.routePaths[this.activeRouteIndex].data.duration);
            if (timeDiff > 0) {
              timeText = '+' + timeDiff + ' د';
            } else if (timeDiff < 0) {
              timeText = timeDiff + ' د';
            } else {
              timeText = 'نفس الوقت';
            }
          }

          routeBtn.innerHTML = `
            <span class="route-btn-letter">${String.fromCharCode(65 + index)}</span>
            <span class="route-btn-time">${timeText}</span>
          `;

          routeBtn.addEventListener('click', () => {
            this.setActiveRoute(index);
          });

          routeButtons.appendChild(routeBtn);
        });

        this.routeControls.appendChild(routeButtons);
      }

      // إنشاء قائمة التفاصيل
      const stepsList = document.createElement('ol');
      stepsList.className = 'route-steps';

      activeRoute.instructions.forEach((step, idx) => {
        if (idx === activeRoute.instructions.length - 1) return; // تجاهل آخر خطوة (الوصول)

        const stepItem = document.createElement('li');
        stepItem.className = 'route-step';

        // اختر أيقونة مناسبة بناءً على نوع التعليمات
        let icon = '➡️';
        if (step.text.includes('انعطف')) {
          icon = step.text.includes('اليمين') ? '↪️' : '↩️';
        } else if (step.text.includes('استمر')) {
          icon = '⬆️';
        }

        stepItem.innerHTML = `
          <div class="step-icon">${icon}</div>
          <div class="step-text">${step.text}</div>
          <div class="step-distance">${step.distance.toFixed(1)} كم</div>
        `;

        stepsList.appendChild(stepItem);
      });

      // إضافة الخطوة الأخيرة (الوصول)
      const finalStep = document.createElement('li');
      finalStep.className = 'route-step final-step';
      finalStep.innerHTML = `
        <div class="step-icon">🏁</div>
        <div class="step-text">الوصول إلى الوجهة</div>
        <div class="step-distance"></div>
      `;
      stepsList.appendChild(finalStep);

      this.routeControls.appendChild(stepsList);
    },

    /**
     * إزالة لوحة معلومات المسار
     */
    removeRouteInfoPanel: function() {
      if (this.routeInfoPanel) {
        document.body.removeChild(this.routeInfoPanel);
        this.routeInfoPanel = null;
        this.routeOverview = null;
        this.routeControls = null;
      }
    },

    /**
     * دالة لطلب المسار باستخدام خدمة التوجيه المحلية
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @param {Function} callback دالة ترجع عند اكتمال العملية
     */
    mockRoutingRequest: function(startLatLng, endLatLng, mode, callback) {
      console.log('طلب مسار محلي من', startLatLng, 'إلى', endLatLng, 'بوضع', mode);

      // إظهار مؤشر التحميل
      this.showLoadingIndicator();

      // التحقق من وجود بيانات محلية للمسارات
      if (window.localRoutingData && typeof window.localRoutingData.getRoutes === 'function') {
        try {
          // استخدام بيانات المسارات المحلية
          const localRoutes = window.localRoutingData.getRoutes(startLatLng, endLatLng, mode);

          if (localRoutes && localRoutes.length > 0) {
            console.log('تم العثور على مسارات محلية:', localRoutes);

            // إخفاء مؤشر التحميل
            this.hideLoadingIndicator();

            // استدعاء دالة الاستجابة مع المسارات المحلية
            callback(localRoutes);
            return;
          }
        } catch (error) {
          console.warn('فشل في استخدام بيانات المسارات المحلية:', error);
        }
      }

      // إذا لم تكن هناك بيانات محلية أو فشل استخدامها، استخدم OSRM إذا كان متاحاً
      if (navigator.onLine && window.useOnlineRouting !== false) {
        // تحويل وضع التنقل إلى صيغة مناسبة لـ OSRM
        const osrmMode = mode === 'car' ? 'driving' : (mode === 'bike' ? 'cycling' : 'walking');

        // تحضير نقاط البداية والنهاية بالصيغة المطلوبة [lng, lat]
        const startCoord = startLatLng.lng + ',' + startLatLng.lat;
        const endCoord = endLatLng.lng + ',' + endLatLng.lat;

        // بناء عنوان URL للطلب
        const url = `https://router.project-osrm.org/route/v1/${osrmMode}/${startCoord};${endCoord}?overview=full&geometries=geojson&steps=true&annotations=true`;

        console.log('إرسال طلب إلى OSRM:', url);

        // استدعاء خدمة OSRM
        fetch(url)
          .then(response => {
            if (!response.ok) {
              console.error('خطأ في استجابة OSRM:', response.status, response.statusText);
              throw new Error(`فشل الاتصال بخدمة OSRM: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log('تم استلام بيانات المسار من OSRM:', data);

            // التحقق من صحة البيانات
            if (!data.routes || data.routes.length === 0) {
              throw new Error('لم يتم العثور على مسارات في استجابة OSRM');
            }

            // معالجة البيانات من OSRM
            const routes = this.processOsrmResponse(data, startLatLng, endLatLng, mode);

            // حفظ المسارات محلياً للاستخدام في المستقبل
            if (window.localRoutingData && typeof window.localRoutingData.saveRoutes === 'function') {
              window.localRoutingData.saveRoutes(startLatLng, endLatLng, mode, routes);
            }

            // إخفاء مؤشر التحميل
            this.hideLoadingIndicator();

            // استدعاء دالة الاستجابة مع المسارات
            callback(routes);
          })
          .catch(error => {
            console.error('خطأ في الحصول على المسارات من OSRM:', error);

            // في حالة الفشل، استخدم المحاكاة البسيطة كاحتياطي
            console.warn('استخدام المسارات المحاكاة كاحتياطي');
            this.fallbackToMockRoutes(startLatLng, endLatLng, mode, callback);

            // إخفاء مؤشر التحميل
            this.hideLoadingIndicator();
          });
      } else {
        // إذا كان المستخدم غير متصل بالإنترنت أو تم تعطيل التوجيه عبر الإنترنت، استخدم المحاكاة البسيطة
        console.warn('استخدام المسارات المحاكاة (غير متصل بالإنترنت أو تم تعطيل التوجيه عبر الإنترنت)');
        this.fallbackToMockRoutes(startLatLng, endLatLng, mode, callback);

        // إخفاء مؤشر التحميل
        this.hideLoadingIndicator();
      }
    },

    /**
     * معالجة استجابة OSRM
     * @param {Object} data استجابة OSRM
     * @param {Object} startLatLng إحداثيات نقطة البداية
     * @param {Object} endLatLng إحداثيات نقطة النهاية
     * @param {String} mode وضع التنقل
     * @return {Array} مصفوفة من كائنات المسارات
     */
    processOsrmResponse: function(data, startLatLng, endLatLng, mode) {
      const routes = [];

      try {
        // استخراج المسار الرئيسي
        const mainRoute = data.routes[0];
        console.log('معالجة مسار OSRM:', mainRoute);

        if (!mainRoute || !mainRoute.geometry || !mainRoute.geometry.coordinates) {
          throw new Error('بنية مسار OSRM غير صالحة');
        }

        // استخراج الإحداثيات
        const coordinates = mainRoute.geometry.coordinates;
        console.log('عدد نقاط المسار:', coordinates.length);

        // تحويل الإحداثيات من [lng, lat] إلى [lat, lng] لتناسب Leaflet
        const geometry = coordinates.map(coord => [coord[1], coord[0]]);

        // استخراج التعليمات
        const instructions = [];
        if (mainRoute.legs) {
          mainRoute.legs.forEach(leg => {
            if (leg.steps) {
              leg.steps.forEach(step => {
                // تحويل تعليمات OSRM إلى اللغة العربية
                const arabicInstruction = this.translateOsrmInstruction(step.maneuver.type, step.maneuver.modifier);

                instructions.push({
                  text: arabicInstruction,
                  distance: step.distance / 1000, // تحويل من متر إلى كيلومتر
                  duration: step.duration / 60 // تحويل من ثانية إلى دقيقة
                });
              });
            }
          });
        }

        // إنشاء كائن المسار
        routes.push({
          geometry: geometry,
          distance: mainRoute.distance / 1000, // تحويل من متر إلى كيلومتر
          duration: mainRoute.duration / 60, // تحويل من ثانية إلى دقيقة
          instructions: instructions
        });

        // إذا كان هناك مسارات بديلة
        if (data.routes.length > 1) {
          const altRoute = data.routes[1];

          if (altRoute && altRoute.geometry && altRoute.geometry.coordinates) {
            // استخراج الإحداثيات
            const altCoordinates = altRoute.geometry.coordinates;

            // تحويل الإحداثيات من [lng, lat] إلى [lat, lng] لتناسب Leaflet
            const altGeometry = altCoordinates.map(coord => [coord[1], coord[0]]);

            // استخراج التعليمات
            const altInstructions = [];
            if (altRoute.legs) {
              altRoute.legs.forEach(leg => {
                if (leg.steps) {
                  leg.steps.forEach(step => {
                    // تحويل تعليمات OSRM إلى اللغة العربية
                    const arabicInstruction = this.translateOsrmInstruction(step.maneuver.type, step.maneuver.modifier);

                    altInstructions.push({
                      text: arabicInstruction,
                      distance: step.distance / 1000, // تحويل من متر إلى كيلومتر
                      duration: step.duration / 60 // تحويل من ثانية إلى دقيقة
                    });
                  });
                }
              });
            }

            // إضافة المسار البديل
            routes.push({
              geometry: altGeometry,
              distance: altRoute.distance / 1000, // تحويل من متر إلى كيلومتر
              duration: altRoute.duration / 60, // تحويل من ثانية إلى دقيقة
              instructions: altInstructions
            });
          }
        }

        console.log('تم معالجة المسارات بنجاح:', routes);
      } catch (error) {
        console.error('خطأ في معالجة استجابة OSRM:', error);
      }

      // إذا لم يتم العثور على مسارات، استخدم المحاكاة البسيطة
      if (routes.length === 0) {
        console.warn('لم يتم العثور على مسارات من OSRM، استخدام المسارات المحاكاة');
        return this.generateMockRoutesSync(startLatLng, endLatLng, mode);
      }

      return routes;
    },

    /**
     * ترجمة تعليمات OSRM إلى اللغة العربية
     * @param {String} type نوع المناورة
     * @param {String} modifier معدل المناورة
     * @return {String} التعليمات بالعربية
     */
    translateOsrmInstruction: function(type, modifier) {
      // ترجمة نوع المناورة
      let instruction = '';

      switch (type) {
        case 'depart':
          instruction = 'ابدأ ';
          break;
        case 'arrive':
          instruction = 'وصلت إلى الوجهة';
          break;
        case 'turn':
          instruction = 'انعطف ';
          break;
        case 'continue':
          instruction = 'استمر ';
          break;
        case 'merge':
          instruction = 'اندمج ';
          break;
        case 'on ramp':
          instruction = 'اسلك المنحدر ';
          break;
        case 'off ramp':
          instruction = 'اخرج من المنحدر ';
          break;
        case 'fork':
          instruction = 'خذ التفرع ';
          break;
        case 'end of road':
          instruction = 'نهاية الطريق، ';
          break;
        case 'roundabout':
          instruction = 'ادخل الدوار واخرج ';
          break;
        case 'rotary':
          instruction = 'ادخل الدوار واخرج ';
          break;
        case 'roundabout turn':
          instruction = 'في الدوار، انعطف ';
          break;
        case 'notification':
          instruction = '';
          break;
        case 'new name':
          instruction = 'استمر على ';
          break;
        default:
          instruction = '';
      }

      // إضافة الاتجاه
      if (modifier) {
        switch (modifier) {
          case 'left':
            instruction += 'يساراً';
            break;
          case 'right':
            instruction += 'يميناً';
            break;
          case 'sharp left':
            instruction += 'يساراً بحدة';
            break;
          case 'sharp right':
            instruction += 'يميناً بحدة';
            break;
          case 'slight left':
            instruction += 'يساراً قليلاً';
            break;
          case 'slight right':
            instruction += 'يميناً قليلاً';
            break;
          case 'straight':
            instruction += 'مباشرة';
            break;
          case 'uturn':
            instruction += 'دوران للخلف';
            break;
        }
      }

      return instruction || 'استمر على الطريق';
    },

    /**
     * استخدام المسارات المحاكاة في حالة فشل الاتصال بالخدمة
     */
    fallbackToMockRoutes: function(startLatLng, endLatLng, mode, callback) {
      console.log('استخدام المسارات المحاكاة كاحتياطي');
      // تقليد تأخير الشبكة
      setTimeout(() => {
        const speedFactor = mode === 'car' ? 60 : (mode === 'bike' ? 15 : 5); // كم/ساعة

        // المسار المباشر مع انحراف بسيط
        const mainRoute = this.generateDirectRoute(
          [startLatLng.lng, startLatLng.lat],
          [endLatLng.lng, endLatLng.lat],
          0, 0
        );

        // مسارات بديلة
        const alternativeRoutes = [
          this.generateDirectRoute(
            [startLatLng.lng, startLatLng.lat],
            [endLatLng.lng, endLatLng.lat],
            0.002, 0.001
          ),
          this.generateDirectRoute(
            [startLatLng.lng, startLatLng.lat],
            [endLatLng.lng, endLatLng.lat],
            -0.004, 0.003
          )
        ];

        // جمع كل المسارات
        const routes = [mainRoute, ...alternativeRoutes];

        // حساب المسافة والوقت لكل مسار
        routes.forEach(route => {
          let totalDistance = 0;
          let prevPoint = null;

          // تحويل الاحداثيات من [lng, lat] إلى [lat, lng] لتناسب Leaflet
          route.geometry = route.geometry.map(coords => [coords[1], coords[0]]);

          route.geometry.forEach(point => {
            if (prevPoint) {
              const segDistance = this.calculateDistance(
                prevPoint[0], prevPoint[1],
                point[0], point[1]
              );
              totalDistance += segDistance;
            }
            prevPoint = point;
          });

          route.distance = totalDistance;
          route.duration = (totalDistance / speedFactor) * 60; // تحويل من ساعات إلى دقائق

          // تعيين التعليمات على الإحداثيات
          route.instructions = this.generateInstructions(route.geometry, totalDistance);
        });

        callback(routes);
      }, 1000);
    },

    /**
     * توليد مسار وهمي بين نقطتين مع انحراف
     * @param {Array} start نقطة البداية [خط الطول، خط العرض]
     * @param {Array} end نقطة النهاية [خط الطول، خط العرض]
     * @param {Number} offsetX انحراف في خط الطول
     * @param {Number} offsetY انحراف في خط العرض
     */
    generateDirectRoute: function(start, end, offsetX, offsetY) {
      const numPoints = 10; // عدد النقاط الوسيطة
      const points = [];

      points.push(start);

      for (let i = 1; i < numPoints; i++) {
        const ratio = i / numPoints;
        const lng = start[0] + (end[0] - start[0]) * ratio;
        const lat = start[1] + (end[1] - start[1]) * ratio;

        // إضافة انحراف عشوائي لمحاكاة طريق حقيقي
        const deviation = Math.sin(ratio * Math.PI);
        const noiseX = (Math.random() - 0.5) * 0.0005;
        const noiseY = (Math.random() - 0.5) * 0.0005;

        points.push([
          lng + offsetX * deviation + noiseX,
          lat + offsetY * deviation + noiseY
        ]);
      }

      points.push(end);

      return {
        geometry: points
      };
    },

    /**
     * توليد تعليمات وهمية للمسار
     * @param {Array} geometry هندسة المسار
     * @param {Number} totalDistance إجمالي المسافة
     */
    generateInstructions: function(geometry, totalDistance) {
      // محاكاة التعليمات
      const segments = geometry.length - 1;
      const instructions = [];
      let distanceCovered = 0;

      // توليد التعليمات
      for (let i = 0; i < segments; i++) {
        const start = geometry[i];
        const end = geometry[i + 1];
        const distance = this.calculateDistance(start[0], start[1], end[0], end[1]);
        distanceCovered += distance;

        let text = '';
        if (i === 0) {
          text = 'انطلق في الاتجاه';
        } else if (i < segments / 3) {
          text = 'استمر على الطريق';
        } else if (i < segments / 2) {
          text = 'انعطف إلى اليمين';
        } else if (i < segments - 1) {
          text = 'انعطف إلى اليسار';
        } else {
          text = 'اتبع الطريق حتى الوصول';
        }

        // إضافة أسماء شوارع عشوائية
        const streets = ['شارع الستين', 'شارع الزبيري', 'شارع حدة', 'شارع التحرير', 'شارع الدائري'];
        if (Math.random() > 0.5) {
          text += ' على ' + streets[Math.floor(Math.random() * streets.length)];
        }

        instructions.push({
          text: text,
          distance: distance,
          duration: (distance / 40) * 60 // محاكاة: 40 كم/ساعة
        });
      }

      // إضافة تعليمة الوصول
      instructions.push({
        text: 'لقد وصلت إلى وجهتك',
        distance: 0,
        duration: 0
      });

      return instructions;
    },

    /**
     * حساب المسافة بين نقطتين بالكيلومتر
     * @param {Number} lat1 خط عرض النقطة الأولى
     * @param {Number} lon1 خط طول النقطة الأولى
     * @param {Number} lat2 خط عرض النقطة الثانية
     * @param {Number} lon2 خط طول النقطة الثانية
     */
    calculateDistance: function(lat1, lon1, lat2, lon2) {
      const R = 6371; // نصف قطر الأرض بالكيلومتر
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    },

    /**
     * تحويل من درجات إلى راديان
     * @param {Number} deg زاوية بالدرجات
     */
    deg2rad: function(deg) {
      return deg * (Math.PI/180);
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoadingIndicator: function() {
      // إزالة أي مؤشر تحميل سابق
      this.hideLoadingIndicator();

      // إنشاء مؤشر التحميل
      const loadingIndicator = document.createElement('div');
      loadingIndicator.id = 'routing-loading-indicator';
      loadingIndicator.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(255, 255, 255, 0.9);
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        text-align: center;
        font-weight: bold;
      `;
      loadingIndicator.innerHTML = `
        <div style="margin-bottom: 10px;">جاري حساب أفضل مسار...</div>
        <div class="spinner" style="
          width: 30px;
          height: 30px;
          border: 3px solid #3388ff;
          border-top-color: transparent;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        "></div>
        <style>
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      `;

      document.body.appendChild(loadingIndicator);
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoadingIndicator: function() {
      const loadingIndicator = document.getElementById('routing-loading-indicator');
      if (loadingIndicator) {
        document.body.removeChild(loadingIndicator);
      }
    }
  };

  // تصدير الكائن للاستخدام العام
  window.GoogleStyleRouting = GoogleStyleRouting;

})();

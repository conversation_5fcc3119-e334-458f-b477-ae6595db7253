{"name": "yemen-gps-production", "version": "2.0.0", "description": "نظام يمن GPS - خادم الإنتاج للعمل عبر الإنترنت", "main": "production-server.js", "scripts": {"start": "node production-server.js", "dev": "nodemon production-server.js", "test": "node simple-test-server.js", "production": "NODE_ENV=production node production-server.js"}, "keywords": ["yemen", "gps", "maps", "navigation", "google-maps", "places", "production"], "author": "Yemen GPS Team", "license": "MIT", "dependencies": {"express": "^4.21.2", "compression": "^1.8.0", "helmet": "^8.1.0", "cors": "^2.8.5", "pg": "^8.16.0", "dotenv": "^16.5.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yemen-gps/yemen-gps.git"}, "bugs": {"url": "https://github.com/yemen-gps/yemen-gps/issues"}, "homepage": "http://***********:5000"}
// إعدادات التطبيق
window.appConfig = {
    defaultCenter: [15.3694, 44.1910], // إحداثيات صنعاء
    defaultZoom: 12,
    minZoom: 5,
    maxZoom: 18,
    useLocalMaps: true, // استخدام الخرائط المحلية
    // نسخة احتياطية من إعدادات الخرائط في حالة عدم توفر المزود المحلي
    mapLayers: {
        streets: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
        terrain: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}',
        transport: 'https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey=6170aad10dfd42a38d4d8c709a536f38'
    },
    attribution: {
        osm: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',
        esri: '&copy; <a href="https://www.esri.com/">Esri</a>',
        thunderforest: '&copy; <a href="https://www.thunderforest.com/">Thunderforest</a>',
        local: '&copy; Yemen Nav'
    }
};

// حدود اليمن للتحقق من الموقع
const yemenBounds = L.latLngBounds(
    L.latLng(12.1, 41.6),  // الزاوية الجنوبية الغربية
    L.latLng(19.0, 54.5)   // الزاوية الشمالية الشرقية
);

// تهيئة التطبيق عند تحميل الصفحة
// تم تعطيل هذا الحدث لأننا نستدعي الوظائف مباشرة
// document.addEventListener('DOMContentLoaded', function() {
//    initMap();
//    initUI();
//    checkSharedLocation();
// });

// شيفرة للتأكد من أن الخريطة تعمل بشكل صحيح
console.log('تم تحميل ملف الخريطة الرئيسي');

// هذه الوظيفة سيتم استدعاؤها مباشرة من الصفحة الرئيسية
window.startYemenNav = function() {
    try {
        console.log('بدء تشغيل يمن ناف');
        // تهيئة الخريطة
        initMap();
        
        // تهيئة واجهة المستخدم
        initUI();
        
        // تحقق من URL للبحث عن مواقع مشاركة
        checkSharedLocation();
        return true;
    } catch (error) {
        console.error('حدث خطأ أثناء بدء يمن ناف:', error);
        return false;
    }
};

// تهيئة الخريطة
function initMap() {
    // إنشاء كائن الخريطة
    window.map = L.map('map', {
        center: window.appConfig.defaultCenter,
        zoom: window.appConfig.defaultZoom,
        minZoom: window.appConfig.minZoom,
        maxZoom: window.appConfig.maxZoom,
        zoomControl: false, // تعطيل أزرار التكبير/التصغير الافتراضية
        attributionControl: true
    });
    
    // إنشاء طبقات الخريطة
    // التحقق مما إذا كان يجب استخدام المزود المحلي
    if (window.appConfig.useLocalMaps && window.localMapsProvider) {
        console.log('استخدام مزود الخرائط المحلي');
        // استخدام مزود الخرائط المحلي
        window.baseMaps = window.localMapsProvider.createMapLayers();
    } else {
        console.log('استخدام مزود الخرائط عبر الإنترنت');
        // استخدام المزود عبر الإنترنت
        window.baseMaps = {
            streets: L.tileLayer(window.appConfig.mapLayers.transport, {
                attribution: window.appConfig.attribution.thunderforest,
                maxZoom: window.appConfig.maxZoom + 3
            }),
            satellite: L.tileLayer(window.appConfig.mapLayers.satellite, {
                attribution: window.appConfig.attribution.esri,
                maxZoom: window.appConfig.maxZoom
            }),
            terrain: L.tileLayer(window.appConfig.mapLayers.terrain, {
                attribution: window.appConfig.attribution.esri,
                maxZoom: window.appConfig.maxZoom
            })
        };
    }
    
    // إضافة الطبقة الافتراضية
    window.baseMaps.streets.addTo(window.map);
    
    // إضافة حدث النقر على الخريطة
    window.map.on('click', handleMapClick);
}

// تهيئة واجهة المستخدم وإضافة أحداث للعناصر
function initUI() {
    // أزرار التكبير والتصغير
    document.getElementById('zoom-in').addEventListener('click', function() {
        window.map.zoomIn(1);
    });
    
    document.getElementById('zoom-out').addEventListener('click', function() {
        window.map.zoomOut(1);
    });
    
    // زر الموقع الحالي
    document.getElementById('my-location').addEventListener('click', locateUser);
    
    // زر طبقات الخريطة
    document.getElementById('map-layers-button').addEventListener('click', function(e) {
        e.stopPropagation();
        const mapLayersMenu = document.getElementById('map-layers-menu');
        mapLayersMenu.classList.toggle('active');
    });
    
    // خيارات طبقات الخريطة
    document.querySelectorAll('.map-layer-option').forEach(option => {
        option.addEventListener('click', function() {
            const layerId = this.id;
            
            // إزالة التنشيط من جميع الطبقات
            document.querySelectorAll('.map-layer-option').forEach(layer => {
                layer.classList.remove('active');
            });
            
            // تنشيط الطبقة المحددة
            this.classList.add('active');
            
            // تطبيق الطبقة المحددة
            if (layerId === 'streets-layer') {
                switchMapLayer('streets');
                showNotification('تم تفعيل خريطة الشوارع فائقة الدقة', 'success');
            } else if (layerId === 'satellite-layer') {
                switchMapLayer('satellite');
                showNotification('تم تفعيل طبقة القمر الصناعي', 'success');
            } else if (layerId === 'terrain-layer') {
                switchMapLayer('terrain');
                showNotification('تم تفعيل طبقة التضاريس', 'success');
            }
            
            // إغلاق القائمة
            document.getElementById('map-layers-menu').classList.remove('active');
        });
    });
    
    // زر القائمة الجانبية
    document.getElementById('menu-button').addEventListener('click', function() {
        document.getElementById('sidebar').classList.add('active');
    });
    
    // زر إغلاق القائمة الجانبية
    document.getElementById('sidebar-close').addEventListener('click', function() {
        document.getElementById('sidebar').classList.remove('active');
    });
    
    // خيارات القائمة الجانبية
    document.querySelectorAll('.sidebar-menu-item').forEach(item => {
        item.addEventListener('click', function() {
            // يمكن إضافة وظائف لكل عنصر قائمة هنا
            const itemText = this.querySelector('span').innerText;
            showNotification(`تم النقر على: ${itemText}`, 'info');
        });
    });
    
    // حقل البحث
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const searchTerm = this.value.trim();
            if (searchTerm) {
                searchLocation(searchTerm);
            }
        }
    });
    
    // زر مسح البحث
    document.getElementById('clear-search').addEventListener('click', function() {
        document.getElementById('search-input').value = '';
    });
    
    // زر البحث الصوتي
    document.getElementById('voice-search').addEventListener('click', function() {
        showNotification('ميزة البحث الصوتي غير متاحة حالياً', 'info');
    });
    
    // أزرار معلومات الموقع
    document.getElementById('set-destination').addEventListener('click', setDestination);
    document.getElementById('share-location').addEventListener('click', shareLocation);
    document.getElementById('save-location').addEventListener('click', saveLocation);
    
    // زر إغلاق نافذة المعلومات
    document.getElementById('close-location-info').addEventListener('click', closeLocationInfoCard);
    
    // إضافة حدث لإغلاق نافذة المعلومات عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLocationInfoCard();
        }
    });
    
    // إغلاق القوائم عند النقر في أي مكان آخر
    document.addEventListener('click', function(e) {
        const mapLayersMenu = document.getElementById('map-layers-menu');
        if (mapLayersMenu.classList.contains('active') && 
            !mapLayersMenu.contains(e.target) && 
            e.target !== document.getElementById('map-layers-button')) {
            mapLayersMenu.classList.remove('active');
        }
        
        const sidebar = document.getElementById('sidebar');
        if (sidebar.classList.contains('active') && 
            !sidebar.contains(e.target) && 
            e.target !== document.getElementById('menu-button')) {
            sidebar.classList.remove('active');
        }
    });
}

// معالجة النقر على الخريطة
function handleMapClick(e) {
    // التحقق من أن الموقع داخل حدود اليمن
    if (!isLocationInYemen(e.latlng.lat, e.latlng.lng)) {
        showNotification('هذا الموقع خارج حدود اليمن', 'error');
        return;
    }
    
    // الحصول على معلومات الموقع
    reverseGeocode(e.latlng.lat, e.latlng.lng)
        .then(locationData => {
            displayLocationInfo(locationData);
        })
        .catch(error => {
            console.error('Error getting location info:', error);
            showNotification('حدث خطأ أثناء الحصول على معلومات الموقع', 'error');
        });
}

// التحقق من أن الموقع داخل حدود اليمن
function isLocationInYemen(lat, lng) {
    return yemenBounds.contains(L.latLng(lat, lng));
}

// تحديد موقع المستخدم
function locateUser() {
    showLoadingIndicator('جاري تحديد موقعك...');
    
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                hideLoadingIndicator();
                
                // إضافة علامة لموقع المستخدم
                if (window.userLocationMarker) {
                    map.removeLayer(window.userLocationMarker);
                }
                
                if (window.accuracyCircle) {
                    map.removeLayer(window.accuracyCircle);
                }
                
                // إضافة دائرة لتوضيح دقة تحديد الموقع
                window.accuracyCircle = L.circle([lat, lng], {
                    radius: position.coords.accuracy,
                    color: '#4285F4',
                    fillColor: '#4285F4',
                    fillOpacity: 0.15
                }).addTo(map);
                
                // إضافة علامة الموقع
                window.userLocationMarker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        html: '<div class="pulse-animation"><i class="fas fa-location-arrow" style="color: #4285F4;"></i></div>',
                        className: 'user-location-marker',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    })
                }).addTo(map);
                
                // تحريك الخريطة إلى موقع المستخدم
                map.setView([lat, lng], 16);
                
                // إظهار إشعار للمستخدم
                showNotification('تم تحديد موقعك بنجاح', 'success');
            },
            function(error) {
                hideLoadingIndicator();
                
                let errorMessage;
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = 'تم رفض الوصول إلى الموقع الجغرافي';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = 'معلومات الموقع غير متاحة';
                        break;
                    case error.TIMEOUT:
                        errorMessage = 'انتهت مهلة طلب الموقع';
                        break;
                    case error.UNKNOWN_ERROR:
                        errorMessage = 'حدث خطأ غير معروف';
                        break;
                }
                
                showNotification(errorMessage, 'error');
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
            }
        );
    } else {
        hideLoadingIndicator();
        showNotification('متصفحك لا يدعم تحديد الموقع الجغرافي', 'error');
    }
}

// البحث عن موقع باستخدام OpenStreetMap Nominatim
function searchLocation(searchTerm) {
    showLoadingIndicator('جاري البحث...');
    
    // استخدام Nominatim API للبحث
    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchTerm)}+Yemen&limit=1&accept-language=ar`)
        .then(response => response.json())
        .then(data => {
            hideLoadingIndicator();
            
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lng = parseFloat(result.lon);
                
                // التحقق من أن الموقع داخل حدود اليمن
                if (!isLocationInYemen(lat, lng)) {
                    showNotification('الموقع الذي تبحث عنه خارج حدود اليمن', 'error');
                    return;
                }
                
                // تحريك الخريطة إلى الموقع
                map.setView([lat, lng], 16);
                
                // إضافة علامة للموقع
                if (window.searchMarker) {
                    map.removeLayer(window.searchMarker);
                }
                
                window.searchMarker = L.marker([lat, lng]).addTo(map);
                
                // عرض معلومات الموقع
                const locationData = {
                    name: result.display_name,
                    type: result.type,
                    lat: lat,
                    lng: lng,
                    address: result.display_name
                };
                
                displayLocationInfo(locationData);
                
                showNotification('تم العثور على الموقع', 'success');
            } else {
                showNotification('لم يتم العثور على نتائج للبحث', 'error');
            }
        })
        .catch(error => {
            hideLoadingIndicator();
            console.error('Error searching for location:', error);
            showNotification('حدث خطأ أثناء البحث', 'error');
        });
}

// البحث العكسي للحصول على معلومات الموقع من الإحداثيات
function reverseGeocode(lat, lng) {
    return new Promise((resolve, reject) => {
        fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&accept-language=ar`)
            .then(response => response.json())
            .then(data => {
                if (data && data.display_name) {
                    const locationData = {
                        name: data.address.road || data.address.suburb || data.address.city || data.address.town || data.address.village || 'موقع غير معروف',
                        type: data.type || 'موقع',
                        lat: lat,
                        lng: lng,
                        address: data.display_name,
                        details: Object.values(data.address).join(', ')
                    };
                    
                    resolve(locationData);
                } else {
                    reject('لم يتم العثور على معلومات للموقع');
                }
            })
            .catch(error => {
                reject(error);
            });
    });
}

// عرض معلومات الموقع
function displayLocationInfo(locationData) {
    // تحديث عناصر نافذة المعلومات
    document.getElementById('location-info-title').textContent = locationData.name;
    document.getElementById('location-info-subtitle').textContent = locationData.type;
    document.getElementById('location-address').textContent = locationData.address;
    document.getElementById('location-details').textContent = locationData.details || 'لا توجد تفاصيل إضافية';
    document.getElementById('location-coordinates').textContent = `الإحداثيات: ${locationData.lat.toFixed(6)}, ${locationData.lng.toFixed(6)}`;
    
    // تخزين بيانات الموقع الحالي
    window.currentLocation = locationData;
    
    // إظهار نافذة المعلومات
    document.getElementById('location-info-card').classList.add('active');
    
    // إضافة علامة على الخريطة
    if (window.locationMarker) {
        map.removeLayer(window.locationMarker);
    }
    
    window.locationMarker = L.marker([locationData.lat, locationData.lng]).addTo(map);
}

// تعيين الموقع كوجهة
function setDestination() {
    if (!window.currentLocation) {
        showNotification('الرجاء تحديد موقع أولاً', 'error');
        return;
    }
    
    showLoadingIndicator('جاري تحديد المسار...');
    
    // الحصول على موقع المستخدم الحالي
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;
                const destLat = window.currentLocation.lat;
                const destLng = window.currentLocation.lng;
                
                // إنشاء مسار بين الموقع الحالي والوجهة
                if (window.routingControl) {
                    map.removeControl(window.routingControl);
                }
                
                window.routingControl = L.Routing.control({
                    waypoints: [
                        L.latLng(userLat, userLng),
                        L.latLng(destLat, destLng)
                    ],
                    routeWhileDragging: true,
                    showAlternatives: true,
                    fitSelectedRoutes: true,
                    altLineOptions: {
                        styles: [
                            {color: 'black', opacity: 0.15, weight: 9},
                            {color: 'white', opacity: 0.8, weight: 6},
                            {color: 'blue', opacity: 0.5, weight: 2}
                        ]
                    },
                    lineOptions: {
                        styles: [
                            {color: 'black', opacity: 0.15, weight: 9},
                            {color: 'white', opacity: 0.8, weight: 6},
                            {color: '#4285F4', opacity: 1, weight: 4}
                        ]
                    },
                    router: L.Routing.osrmv1({
                        serviceUrl: 'https://router.project-osrm.org/route/v1',
                        profile: 'driving'
                    })
                }).addTo(map);
                
                hideLoadingIndicator();
                showNotification('تم تحديد المسار إلى الوجهة', 'success');
                
                // إغلاق نافذة معلومات الموقع
                document.getElementById('location-info-card').classList.remove('active');
            },
            function(error) {
                hideLoadingIndicator();
                
                let errorMessage;
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = 'تم رفض الوصول إلى الموقع الجغرافي';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = 'معلومات الموقع غير متاحة';
                        break;
                    case error.TIMEOUT:
                        errorMessage = 'انتهت مهلة طلب الموقع';
                        break;
                    case error.UNKNOWN_ERROR:
                        errorMessage = 'حدث خطأ غير معروف';
                        break;
                }
                
                showNotification(errorMessage, 'error');
            }
        );
    } else {
        hideLoadingIndicator();
        showNotification('متصفحك لا يدعم تحديد الموقع الجغرافي', 'error');
    }
}

// إغلاق نافذة معلومات الموقع
function closeLocationInfoCard() {
    document.getElementById('location-info-card').classList.remove('active');
}

// مشاركة الموقع
function shareLocation() {
    if (!window.currentLocation) {
        showNotification('الرجاء تحديد موقع أولاً', 'error');
        return;
    }
    
    const lat = window.currentLocation.lat;
    const lng = window.currentLocation.lng;
    const name = encodeURIComponent(window.currentLocation.name);
    
    // إنشاء رابط مشاركة
    const shareURL = `${window.location.origin}${window.location.pathname}?lat=${lat}&lng=${lng}&name=${name}`;
    
    // نسخ الرابط إلى الحافظة
    navigator.clipboard.writeText(shareURL)
        .then(() => {
            showNotification('تم نسخ رابط الموقع إلى الحافظة', 'success');
        })
        .catch(() => {
            // إذا فشل النسخ، عرض الرابط للمستخدم
            const textArea = document.createElement('textarea');
            textArea.value = shareURL;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('تم نسخ رابط الموقع إلى الحافظة', 'success');
        });
}

// حفظ الموقع
function saveLocation() {
    if (!window.currentLocation) {
        showNotification('الرجاء تحديد موقع أولاً', 'error');
        return;
    }
    
    // التحقق من وجود مفضلة سابقة
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    
    // التحقق من وجود الموقع في المفضلة
    const existingIndex = favorites.findIndex(fav => 
        fav.lat === window.currentLocation.lat && 
        fav.lng === window.currentLocation.lng
    );
    
    if (existingIndex !== -1) {
        showNotification('هذا الموقع موجود بالفعل في المفضلة', 'info');
        return;
    }
    
    // إضافة الموقع إلى المفضلة
    favorites.push({
        name: window.currentLocation.name,
        type: window.currentLocation.type,
        lat: window.currentLocation.lat,
        lng: window.currentLocation.lng,
        address: window.currentLocation.address,
        savedAt: new Date().toISOString()
    });
    
    // حفظ المفضلة في التخزين المحلي
    localStorage.setItem('favorites', JSON.stringify(favorites));
    
    showNotification('تم حفظ الموقع في المفضلة', 'success');
}

// تبديل طبقة الخريطة
function switchMapLayer(layerName) {
    if (!window.baseMaps[layerName]) {
        showNotification('طبقة الخريطة غير متوفرة', 'error');
        return;
    }
    
    // إزالة جميع الطبقات الحالية
    Object.values(window.baseMaps).forEach(layer => {
        if (map.hasLayer(layer)) {
            map.removeLayer(layer);
        }
    });
    
    // إضافة الطبقة المحددة
    window.baseMaps[layerName].addTo(map);
}

// التحقق من رابط مشاركة في URL
function checkSharedLocation() {
    const urlParams = new URLSearchParams(window.location.search);
    const lat = parseFloat(urlParams.get('lat'));
    const lng = parseFloat(urlParams.get('lng'));
    const name = urlParams.get('name');
    
    if (lat && lng && !isNaN(lat) && !isNaN(lng)) {
        // التحقق من أن الموقع داخل حدود اليمن
        if (!isLocationInYemen(lat, lng)) {
            showNotification('الموقع المشارك خارج حدود اليمن', 'error');
            return;
        }
        
        // تحريك الخريطة إلى الموقع المشارك
        map.setView([lat, lng], 16);
        
        // إضافة علامة للموقع
        if (window.sharedMarker) {
            map.removeLayer(window.sharedMarker);
        }
        
        window.sharedMarker = L.marker([lat, lng]).addTo(map);
        
        // عرض معلومات الموقع
        reverseGeocode(lat, lng)
            .then(locationData => {
                // تحديث الاسم إذا كان متوفراً في الرابط
                if (name) {
                    locationData.name = decodeURIComponent(name);
                }
                
                displayLocationInfo(locationData);
                showNotification('تم فتح الموقع المشارك', 'success');
            })
            .catch(error => {
                console.error('Error getting shared location info:', error);
                
                // إنشاء معلومات أساسية إذا فشل البحث العكسي
                const basicLocationData = {
                    name: name ? decodeURIComponent(name) : 'موقع مشارك',
                    type: 'موقع مشارك',
                    lat: lat,
                    lng: lng,
                    address: `${lat.toFixed(6)}, ${lng.toFixed(6)}`
                };
                
                displayLocationInfo(basicLocationData);
                showNotification('تم فتح الموقع المشارك', 'success');
            });
    }
}

// عرض مؤشر التحميل
function showLoadingIndicator(message = 'جاري التحميل...') {
    const loadingIndicator = document.getElementById('loading-indicator');
    document.getElementById('loading-text').textContent = message;
    loadingIndicator.classList.add('active');
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const loadingIndicator = document.getElementById('loading-indicator');
    loadingIndicator.classList.remove('active');
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notification-text');
    const notificationIcon = notification.querySelector('.notification-icon i');
    
    // تحديد نوع الأيقونة حسب نوع الإشعار
    notification.className = 'notification';
    notification.classList.add(`notification-${type}`);
    
    switch (type) {
        case 'success':
            notificationIcon.className = 'fas fa-check-circle';
            break;
        case 'error':
            notificationIcon.className = 'fas fa-exclamation-circle';
            break;
        case 'warning':
            notificationIcon.className = 'fas fa-exclamation-triangle';
            break;
        default:
            notificationIcon.className = 'fas fa-info-circle';
    }
    
    // تحديث نص الإشعار
    notificationText.textContent = message;
    
    // عرض الإشعار
    notification.classList.add('active');
    
    // إخفاء الإشعار بعد 3 ثواني
    setTimeout(() => {
        notification.classList.remove('active');
    }, 3000);
}

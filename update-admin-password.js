// سكريبت لتحديث كلمة المرور للمستخدم admin
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// إعدادات الاتصال بقاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'admin',
  port: 5432
});

async function updateAdminPassword() {
  try {
    // تشفير كلمة المرور الجديدة
    const saltRounds = 10;
    const newPassword = 'yemen123';
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log('تم تشفير كلمة المرور الجديدة');
    
    // تعطيل المشغلات (triggers) مؤقتًا
    await pool.query('ALTER TABLE users DISABLE TRIGGER ALL');
    
    // تحديث كلمة المرور في قاعدة البيانات
    const updateQuery = 'UPDATE users SET password = $1 WHERE username = $2';
    const result = await pool.query(updateQuery, [hashedPassword, 'admin']);
    
    // إعادة تفعيل المشغلات
    await pool.query('ALTER TABLE users ENABLE TRIGGER ALL');
    
    console.log(`تم تحديث كلمة المرور للمستخدم admin بنجاح. عدد الصفوف المتأثرة: ${result.rowCount}`);
  } catch (error) {
    console.error('خطأ في تحديث كلمة المرور:', error);
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    await pool.end();
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
  }
}

// تنفيذ الدالة
updateAdminPassword();

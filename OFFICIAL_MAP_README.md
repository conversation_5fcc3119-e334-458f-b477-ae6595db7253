# 🗺️ الخريطة الرسمية - يمن ناف

## 📋 نظرة عامة

هذا هو الملف الرسمي للخريطة التفاعلية لنظام "يمن ناف" بتصميم Google Maps. تم تطوير هذه الخريطة لتوفير تجربة مستخدم متميزة مع دعم كامل للغة العربية وميزات متقدمة للملاحة في اليمن.

## 📁 الملفات الرسمية

### الملف الرئيسي
- **`index.html`** - الصفحة الرئيسية للخريطة التفاعلية
- **`public/index.html`** - نسخة الإنتاج المنشورة على الخادم

### مجلد الأصول (Assets)
```
assets/
├── css/
│   ├── style.css          # الأنماط الرئيسية للخريطة
│   ├── leaflet.css        # أنماط مكتبة Leaflet
│   └── icons.css          # أنماط الأيقونات
├── js/
│   ├── app.js             # منطق التطبيق الرئيسي
│   └── leaflet.js         # مكتبة الخرائط
└── images/
    └── logo.svg           # شعار التطبيق
```

## 🌟 الميزات المتوفرة

### 🗺️ ميزات الخريطة الأساسية
- ✅ خرائط تفاعلية عالية الجودة
- ✅ تكبير وتصغير سلس
- ✅ طبقات متعددة (شوارع، أقمار صناعية، تضاريس)
- ✅ عرض ملء الشاشة
- ✅ تحديد الموقع الحالي بـ GPS

### 🔍 البحث والملاحة
- ✅ بحث متقدم عن الأماكن
- ✅ اقتراحات البحث التلقائية
- ✅ عرض الإحداثيات الدقيقة
- ✅ معلومات تفصيلية عن المواقع

### 💾 إدارة البيانات
- ✅ حفظ الأماكن المفضلة
- ✅ مشاركة المواقع عبر الروابط
- ✅ استيراد وتصدير البيانات
- ✅ عمل بدون إنترنت للخرائط المحملة

### 🎨 واجهة المستخدم
- ✅ تصميم عصري مطابق لـ Google Maps
- ✅ دعم كامل للغة العربية (RTL)
- ✅ واجهة متجاوبة لجميع الأجهزة
- ✅ أيقونات وعناصر تحكم بديهية

## 🚀 كيفية التشغيل

### التشغيل المحلي
```bash
# تشغيل الخادم المحلي
node simple-server.js

# أو باستخدام Python
python -m http.server 8000

# ثم افتح المتصفح على
http://localhost:8000
```

### التشغيل على الخادم
1. انسخ جميع الملفات إلى مجلد الويب
2. تأكد من وجود مجلد `assets` مع جميع الملفات
3. افتح `index.html` في المتصفح

## 🔧 التخصيص والتطوير

### تعديل الألوان والتصميم
- عدّل ملف `assets/css/style.css`
- غيّر المتغيرات في بداية الملف
- احفظ وأعد تحميل الصفحة

### إضافة ميزات جديدة
- عدّل ملف `assets/js/app.js`
- أضف الوظائف الجديدة
- اختبر التغييرات محلياً

### تحديث الشعار
- استبدل ملف `assets/images/logo.svg`
- تأكد من أن الأبعاد مناسبة (32x32 بكسل)

## 📊 الإحصائيات

- **حجم الملف الرئيسي**: ~250 سطر HTML
- **حجم ملف CSS**: ~1200 سطر
- **حجم ملف JavaScript**: ~800 سطر
- **المكتبات المستخدمة**: Leaflet.js, OpenStreetMap
- **دعم المتصفحات**: جميع المتصفحات الحديثة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### الخريطة لا تظهر
- تحقق من وجود ملف `assets/js/leaflet.js`
- تأكد من اتصال الإنترنت لتحميل البلاطات
- افحص وحدة التحكم في المتصفح (F12)

#### البحث لا يعمل
- تحقق من اتصال الإنترنت
- تأكد من عمل خدمة Nominatim
- جرب البحث بكلمات مختلفة

#### الخريطة تظهر في نصف الصفحة
- تم إصلاح هذه المشكلة في الإصدار الحالي
- تأكد من استخدام أحدث إصدار من `style.css`

## 📝 سجل التحديثات

### الإصدار الحالي (2025-01-26)
- ✅ إصلاح مشكلة عرض الخريطة في كامل الصفحة
- ✅ تحسين الأداء والاستجابة
- ✅ إضافة دعم أفضل للأجهزة المحمولة
- ✅ تحديث التصميم ليطابق Google Maps

### الإصدارات السابقة
- إصدار أولي بميزات أساسية
- إضافة البحث والملاحة
- تحسين واجهة المستخدم

## 🤝 المساهمة

لتحسين الخريطة أو إضافة ميزات جديدة:

1. انسخ الملفات الحالية كنسخة احتياطية
2. قم بالتعديلات المطلوبة
3. اختبر التغييرات محلياً
4. وثّق التغييرات في هذا الملف

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افحص وحدة التحكم في المتصفح أولاً
- تأكد من استخدام أحدث إصدار
- راجع قسم استكشاف الأخطاء أعلاه

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم التحديث**: 26 يناير 2025  
**الإصدار**: 1.0 (الإصدار الرسمي)  
**المطور**: فريق يمن ناف

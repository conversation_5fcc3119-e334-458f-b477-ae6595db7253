# 🚀 دليل تشغيل نظام يمن GPS على الخادم الخارجي

## 📋 المعلومات الأساسية

- **عنوان الخادم:** `http://***********:5000`
- **المنفذ:** `5000`
- **البيئة:** الإنتاج (Production)

## 🛠️ متطلبات التشغيل

### 1. متطلبات النظام
- Node.js (الإصدار 16 أو أحدث)
- npm (الإصدار 8 أو أحدث)
- ذاكرة RAM: 512 MB على الأقل
- مساحة القرص: 100 MB على الأقل

### 2. المتطلبات الاختيارية
- PostgreSQL (لقاعدة البيانات الكاملة)
- PM2 (لإدارة العمليات)

## 🚀 خطوات التشغيل السريع

### الطريقة الأولى: التشغيل المباشر
```bash
# 1. تثبيت المتطلبات الأساسية
npm install express compression helmet

# 2. تشغيل الخادم
node production-server.js
```

### الطريقة الثانية: استخدام سكريبت التشغيل
```bash
# تشغيل السكريبت المخصص
node start-production.js
```

### الطريقة الثالثة: استخدام PM2 (موصى به للإنتاج)
```bash
# تثبيت PM2
npm install -g pm2

# تشغيل التطبيق
pm2 start production-server.js --name "yemen-gps"

# مراقبة التطبيق
pm2 monit

# إعادة تشغيل التطبيق
pm2 restart yemen-gps

# إيقاف التطبيق
pm2 stop yemen-gps
```

## 🌐 الروابط المتاحة

بعد تشغيل الخادم، ستكون الروابط التالية متاحة:

- **🗺️ الخريطة الرئيسية:** http://***********:5000/
- **📍 صفحة الأماكن:** http://***********:5000/places
- **⚙️ لوحة التحكم:** http://***********:5000/admin
- **📡 API الأماكن:** http://***********:5000/api/places

## 🔧 إعدادات متقدمة

### تخصيص المنفذ
```bash
# تشغيل على منفذ مختلف
PORT=8080 node production-server.js
```

### تخصيص المضيف
```bash
# تشغيل على مضيف محدد
HOST=************* node production-server.js
```

### استخدام ملف البيئة
```bash
# نسخ ملف الإعدادات
cp .env.production .env

# تحرير الإعدادات
nano .env

# تشغيل مع الإعدادات
node production-server.js
```

## 📊 مراقبة الأداء

### فحص حالة الخادم
```bash
# فحص المنفذ
netstat -tulpn | grep :5000

# فحص العمليات
ps aux | grep node
```

### فحص السجلات
```bash
# عرض السجلات المباشرة
tail -f /var/log/yemen-gps.log

# أو استخدام PM2
pm2 logs yemen-gps
```

## 🛡️ الأمان

### إعدادات الجدار الناري
```bash
# السماح بالمنفذ 5000
sudo ufw allow 5000

# فحص الحالة
sudo ufw status
```

### إعدادات SSL (اختياري)
لتأمين الاتصال، يمكن استخدام:
- Nginx كوكيل عكسي
- Let's Encrypt للشهادات المجانية
- Cloudflare للحماية الإضافية

## 🔄 التحديث والصيانة

### تحديث التطبيق
```bash
# إيقاف الخادم
pm2 stop yemen-gps

# تحديث الملفات
git pull origin main

# إعادة تشغيل الخادم
pm2 start yemen-gps
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي للملفات
tar -czf yemen-gps-backup-$(date +%Y%m%d).tar.gz public/ server/ *.js

# نسخ احتياطي لقاعدة البيانات (إذا كانت متوفرة)
pg_dump yemen_gps > yemen-gps-db-backup-$(date +%Y%m%d).sql
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **المنفذ مستخدم:**
   ```bash
   # العثور على العملية المستخدمة للمنفذ
   lsof -i :5000
   
   # إيقاف العملية
   kill -9 <PID>
   ```

2. **نفاد الذاكرة:**
   ```bash
   # زيادة حد الذاكرة
   node --max-old-space-size=1024 production-server.js
   ```

3. **مشاكل الشبكة:**
   ```bash
   # فحص الاتصال
   curl http://***********:5000/api/places
   ```

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من السجلات
2. تأكد من تشغيل جميع الخدمات المطلوبة
3. فحص إعدادات الشبكة والجدار الناري
4. إعادة تشغيل الخادم

## 📈 الميزات المتاحة

- ✅ خرائط Google Maps تفاعلية
- ✅ بحث في الأماكن
- ✅ عرض تفاصيل الأماكن
- ✅ دعم اللغة العربية
- ✅ واجهة مستجيبة للجوال
- ✅ API للمطورين
- ✅ بيانات تجريبية مدمجة

---

**ملاحظة:** هذا النظام جاهز للعمل مع أو بدون قاعدة بيانات. في حالة عدم توفر قاعدة البيانات، سيتم استخدام البيانات التجريبية المدمجة.

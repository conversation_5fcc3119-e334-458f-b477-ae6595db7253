/**
 * ملف جافا سكريبت للتعامل مع إدارة المستخدمين بشكل مباشر
 * يتضمن وظائف لعرض وإضافة وتعديل وحذف المستخدمين
 */

// متغيرات عامة
let allUsers = [];
let filteredUsers = [];
const API_BASE_URL = 'http://localhost:3000/api';

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل المستخدمين
    loadUsers();
    
    // إضافة مستمع أحداث للبحث
    document.getElementById('user-search').addEventListener('input', filterUsers);
});

/**
 * تحميل المستخدمين من قاعدة البيانات
 */
function loadUsers() {
    // إظهار مؤشر التحميل
    document.getElementById('users-loading').style.display = 'flex';
    document.getElementById('users-error').style.display = 'none';
    
    // استدعاء API للحصول على المستخدمين
    fetch(`${API_BASE_URL}/admin/users`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // تخزين المستخدمين
            allUsers = data;
            filteredUsers = [...allUsers];
            
            // عرض المستخدمين
            displayUsers(filteredUsers);
            
            // تحديث إحصائيات المستخدمين
            updateUserStats();
            
            // إخفاء مؤشر التحميل
            document.getElementById('users-loading').style.display = 'none';
        })
        .catch(error => {
            console.error('خطأ في تحميل المستخدمين:', error);
            
            // إظهار رسالة الخطأ
            const errorElement = document.getElementById('users-error');
            errorElement.textContent = `فشل في تحميل المستخدمين: ${error.message}`;
            errorElement.style.display = 'block';
            
            // إخفاء مؤشر التحميل
            document.getElementById('users-loading').style.display = 'none';
        });
}

/**
 * عرض المستخدمين في الجدول
 */
function displayUsers(users) {
    const tableBody = document.getElementById('users-table-body');
    tableBody.innerHTML = '';
    
    if (users.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="9" class="text-center">لا يوجد مستخدمين</td>';
        tableBody.appendChild(row);
        return;
    }
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        // تنسيق التاريخ
        const registrationDate = user.registration_date ? new Date(user.registration_date).toLocaleDateString('ar-SA') : '-';
        const lastLogin = user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : '-';
        
        // تحديد الدور
        let roleName = 'مستخدم';
        if (user.role_id === 1 || user.role === 'admin') {
            roleName = 'مدير';
        } else if (user.role_id === 3 || user.role === 'developer') {
            roleName = 'مطور';
        }
        
        // تحديد الحالة
        const isActive = user.is_active === true || user.is_active === 1;
        const statusClass = isActive ? 'status-active' : 'status-inactive';
        const statusText = isActive ? 'نشط' : 'غير نشط';
        
        // إنشاء صفوف الجدول
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.full_name || '-'}</td>
            <td>${user.email || '-'}</td>
            <td>${user.username}</td>
            <td>${user.phone || '-'}</td>
            <td>${roleName}</td>
            <td>${registrationDate}</td>
            <td>${lastLogin}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
        `;
        
        tableBody.appendChild(row);
    });
}

/**
 * تصفية المستخدمين حسب معايير البحث والتصفية
 */
function filterUsers() {
    const searchTerm = document.getElementById('user-search').value.trim().toLowerCase();
    const roleFilter = document.getElementById('user-role-filter').value;
    const statusFilter = document.getElementById('user-status-filter').value;
    
    filteredUsers = allUsers.filter(user => {
        // تصفية حسب البحث
        const searchMatch = 
            (user.username && user.username.toLowerCase().includes(searchTerm)) ||
            (user.full_name && user.full_name.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm)) ||
            (user.phone && user.phone.toLowerCase().includes(searchTerm));
        
        // تصفية حسب الدور
        let roleMatch = true;
        if (roleFilter !== 'all') {
            if (roleFilter === 'admin') {
                roleMatch = user.role_id === 1 || user.role === 'admin';
            } else if (roleFilter === 'developer') {
                roleMatch = user.role_id === 3 || user.role === 'developer';
            } else if (roleFilter === 'user') {
                roleMatch = (user.role_id === 2 || user.role === 'user');
            }
        }
        
        // تصفية حسب الحالة
        let statusMatch = true;
        if (statusFilter !== 'all') {
            const isActive = user.is_active === true || user.is_active === 1;
            statusMatch = (statusFilter === 'active' && isActive) || (statusFilter === 'inactive' && !isActive);
        }
        
        return searchMatch && roleMatch && statusMatch;
    });
    
    // عرض المستخدمين المصفاة
    displayUsers(filteredUsers);
    
    // تحديث إحصائيات المستخدمين
    updateUserStats();
}

/**
 * مسح البحث وإعادة تعيين التصفية
 */
function clearUserSearch() {
    document.getElementById('user-search').value = '';
    document.getElementById('user-role-filter').value = 'all';
    document.getElementById('user-status-filter').value = 'all';
    
    filterUsers();
}

/**
 * تحديث إحصائيات المستخدمين
 */
function updateUserStats() {
    document.getElementById('total-users').textContent = allUsers.length;
    document.getElementById('displayed-users').textContent = filteredUsers.length;
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات مع التعامل مع الأخطاء
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + (sessionStorage.getItem('yemenNavToken') || localStorage.getItem('yemenNavToken'))
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = `${API_BASE_URL}${endpoint}`;
    }
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        });
}

# دليل اختبار نظام "يمن ناف" (Yemen Nav)

هذا الدليل يشرح كيفية اختبار تطبيق "يمن ناف" بشكل شامل، بدءًا من اختبارات التطوير وحتى الاختبارات الميدانية.

## 1. اختبارات التطوير

### اختبارات الوحدة (Unit Tests)

تستهدف هذه الاختبارات وحدات صغيرة من الكود للتأكد من عملها بشكل صحيح بمعزل عن باقي النظام.

#### كيفية تشغيل اختبارات الوحدة:

1. افتح المشروع في Android Studio
2. انتقل إلى مجلد الاختبارات: `android/app/src/test/`
3. اضغط بزر الماوس الأيمن على المجلد واختر "Run Tests"

#### أمثلة على اختبارات الوحدة المنفذة:

- اختبارات `LocationRepositoryTest`: للتأكد من صحة عمليات قراءة وكتابة بيانات المواقع
- اختبارات `OfflineMapManagerTest`: للتأكد من صحة تنزيل وإدارة الخرائط دون اتصال
- اختبارات `LocationTypeConverterTest`: للتأكد من صحة تحويل أنواع المواقع

### اختبارات التكامل (Integration Tests)

تختبر تفاعل مكونات متعددة من النظام معًا.

#### كيفية تشغيل اختبارات التكامل:

1. افتح المشروع في Android Studio
2. انتقل إلى مجلد اختبارات الأندرويد: `android/app/src/androidTest/`
3. اضغط بزر الماوس الأيمن على المجلد واختر "Run Tests"

#### أمثلة على اختبارات التكامل:

- اختبارات `DatabaseIntegrationTest`: للتأكد من تكامل قاعدة البيانات مع المستودعات
- اختبارات `ApiIntegrationTest`: للتأكد من تكامل واجهة برمجة التطبيقات مع المستودعات
- اختبارات `LocationServiceIntegrationTest`: للتأكد من تكامل خدمة الموقع مع باقي النظام

### اختبارات واجهة المستخدم (UI Tests)

تختبر تفاعل المستخدم مع واجهة التطبيق.

#### كيفية تشغيل اختبارات واجهة المستخدم:

1. قم بتوصيل جهاز أندرويد حقيقي أو تشغيل محاكي
2. افتح المشروع في Android Studio
3. انتقل إلى مجلد اختبارات الأندرويد: `android/app/src/androidTest/`
4. اضغط بزر الماوس الأيمن على ملفات اختبارات واجهة المستخدم واختر "Run"

#### أمثلة على اختبارات واجهة المستخدم:

- اختبارات `MainActivityTest`: للتأكد من صحة عرض الخريطة والتفاعل معها
- اختبارات `OfflineMapsActivityTest`: للتأكد من صحة تنزيل وإدارة الخرائط دون اتصال
- اختبارات `AddLocationActivityTest`: للتأكد من صحة إضافة مواقع جديدة

## 2. اختبارات الأداء (Performance Testing)

تقيس هذه الاختبارات أداء التطبيق من حيث السرعة واستهلاك الموارد.

### كيفية إجراء اختبارات الأداء:

1. استخدم أداة Android Profiler في Android Studio:
   - افتح Android Studio
   - شغل التطبيق على جهاز أو محاكي
   - افتح Android Profiler من View > Tool Windows > Profiler

2. قياس استهلاك الموارد:
   - استهلاك وحدة المعالجة المركزية (CPU)
   - استهلاك الذاكرة (Memory)
   - استهلاك البطارية (Battery)
   - استخدام الشبكة (Network)

### سيناريوهات اختبار الأداء:

- **اختبار تحميل الخرائط**: قياس الوقت والموارد المستهلكة عند تحميل مناطق مختلفة من الخريطة
- **اختبار التنبيهات**: قياس تأثير خدمة التنبيهات المستمرة على استهلاك البطارية
- **اختبار التخزين**: قياس حجم البيانات المخزنة محليًا وتأثيرها على أداء الجهاز

## 3. اختبارات الاستخدام (Usability Testing)

تقيم هذه الاختبارات سهولة استخدام التطبيق وتجربة المستخدم.

### كيفية إجراء اختبارات الاستخدام:

1. اختر مجموعة متنوعة من المستخدمين (5-10 أشخاص)
2. حدد مهام محددة للمستخدمين لإكمالها، مثل:
   - تسجيل حساب جديد
   - تنزيل منطقة من الخريطة للاستخدام دون اتصال
   - إضافة موقع جديد
   - البحث عن موقع محدد
   - تعديل إعدادات التنبيهات

3. راقب المستخدمين أثناء تنفيذ المهام وسجل:
   - الوقت المستغرق لإكمال كل مهمة
   - الأخطاء التي ارتكبها المستخدمون
   - تعليقات المستخدمين وملاحظاتهم

4. اجمع ملاحظات المستخدمين من خلال استبيان بعد الاختبار

## 4. اختبارات الميدانية (Field Testing)

تُجرى هذه الاختبارات في بيئة حقيقية لتقييم أداء التطبيق في ظروف واقعية.

### كيفية إجراء الاختبارات الميدانية:

1. **اختيار المواقع**:
   - اختر مناطق متنوعة في اليمن (مدن رئيسية، مناطق ريفية، طرق جبلية)
   - حدد مسارات اختبار تشمل أنواعًا مختلفة من الطرق والتضاريس

2. **تجهيز الأجهزة**:
   - استخدم أجهزة أندرويد متنوعة (إصدارات مختلفة، أحجام شاشات مختلفة)
   - تأكد من تثبيت أحدث نسخة من التطبيق
   - جهز أدوات تسجيل الملاحظات (تطبيق ملاحظات، كاميرا، مسجل صوت)

3. **إجراء الاختبار**:
   - اتبع المسارات المحددة مسبقًا
   - سجل دقة تحديد الموقع GPS في مناطق مختلفة
   - اختبر التنبيهات الصوتية في ظروف مختلفة (ضوضاء، سرعات مختلفة)
   - اختبر وضع عدم الاتصال في مناطق بدون تغطية إنترنت
   - أضف مواقع جديدة أثناء التنقل

4. **توثيق النتائج**:
   - سجل أي مشاكل أو أخطاء تظهر
   - وثق دقة المعلومات المعروضة على الخريطة
   - قيم أداء التنبيهات الصوتية
   - قيم استهلاك البطارية خلال فترات استخدام طويلة

### مناطق اختبار مقترحة في اليمن:

- **صنعاء وضواحيها**: لاختبار الأداء في المناطق الحضرية المزدحمة
- **الطريق بين صنعاء وعدن**: لاختبار الأداء على الطرق الطويلة والمتنوعة
- **المناطق الجبلية في تعز**: لاختبار دقة GPS في التضاريس الصعبة
- **المناطق الساحلية في الحديدة**: لاختبار الأداء في المناطق المفتوحة

## 5. اختبارات الأمان (Security Testing)

تقيم هذه الاختبارات مدى أمان التطبيق وحماية بيانات المستخدمين.

### كيفية إجراء اختبارات الأمان:

1. **اختبار التشفير**:
   - تأكد من تشفير بيانات المستخدمين المخزنة محليًا
   - تأكد من تشفير الاتصالات مع الخادم عبر HTTPS

2. **اختبار التحقق من الهوية**:
   - اختبر آليات تسجيل الدخول والتحقق من الهوية
   - اختبر حماية كلمات المرور وتخزينها

3. **اختبار الصلاحيات**:
   - تأكد من تطبيق صلاحيات المستخدمين المختلفة (مدير، مشرف، مستخدم عادي)
   - اختبر منع الوصول غير المصرح به إلى وظائف الإدارة

## 6. اختبارات التوافق (Compatibility Testing)

تتحقق هذه الاختبارات من توافق التطبيق مع مجموعة متنوعة من الأجهزة وإصدارات نظام التشغيل.

### كيفية إجراء اختبارات التوافق:

1. **اختبار على أجهزة متعددة**:
   - اختبر على أجهزة بأحجام شاشات مختلفة (من 4 بوصة إلى 10 بوصات)
   - اختبر على أجهزة بدقة شاشة مختلفة (من منخفضة إلى عالية)
   - اختبر على أجهزة بمواصفات مختلفة (من منخفضة إلى عالية)

2. **اختبار على إصدارات أندرويد مختلفة**:
   - اختبر على الإصدارات من Android 8.0 إلى أحدث إصدار
   - تأكد من عمل جميع الميزات على جميع الإصدارات المدعومة

3. **اختبار مع أنظمة السيارات**:
   - اختبر التوافق مع Android Auto
   - اختبر على شاشات السيارات المختلفة

## 7. خطة الاختبار المستمر (Continuous Testing Plan)

خطة لضمان استمرار جودة التطبيق مع إضافة ميزات جديدة وتحديثات.

### عناصر خطة الاختبار المستمر:

1. **اختبارات تلقائية**:
   - إعداد اختبارات وحدة وتكامل تلقائية تُشغل مع كل تغيير في الكود
   - استخدام أدوات التكامل المستمر (CI) مثل Jenkins أو GitHub Actions

2. **اختبارات قبل الإصدار**:
   - قائمة تحقق شاملة للاختبار قبل كل إصدار
   - اختبارات انحدار للتأكد من عدم كسر الميزات الموجودة

3. **تحليل تقارير الأعطال**:
   - جمع تقارير الأعطال من المستخدمين
   - تحليل الأعطال وإعطاء الأولوية للإصلاحات

4. **تحديثات دورية للاختبارات**:
   - تحديث حالات الاختبار مع إضافة ميزات جديدة
   - مراجعة وتحسين استراتيجية الاختبار بناءً على التغذية الراجعة

## 8. أدوات الاختبار الموصى بها

### أدوات اختبار أندرويد:

- **JUnit**: لاختبارات الوحدة
- **Espresso**: لاختبارات واجهة المستخدم
- **Mockito**: لمحاكاة المكونات في اختبارات الوحدة
- **Robolectric**: لاختبارات أندرويد بدون جهاز
- **Firebase Test Lab**: لاختبار التطبيق على مجموعة واسعة من الأجهزة

### أدوات مراقبة الأداء:

- **Android Profiler**: لمراقبة استهلاك الموارد
- **Firebase Performance Monitoring**: لمراقبة أداء التطبيق في الإنتاج
- **LeakCanary**: لاكتشاف تسريبات الذاكرة

### أدوات اختبار الأمان:

- **OWASP ZAP**: لاختبار أمان واجهة برمجة التطبيقات
- **MobSF**: لتحليل أمان تطبيقات الجوال
- **Drozer**: لاختبار أمان تطبيقات أندرويد

## 9. نموذج تقرير اختبار

### نموذج تقرير اختبار ميداني:

```
# تقرير اختبار ميداني - نظام "يمن ناف"

## معلومات الاختبار:
- التاريخ: [التاريخ]
- الموقع: [المنطقة/المدينة]
- المسار: [وصف المسار]
- الجهاز المستخدم: [طراز الجهاز، إصدار أندرويد]
- إصدار التطبيق: [رقم الإصدار]
- المختبر: [اسم المختبر]

## نتائج الاختبار:

### دقة تحديد الموقع:
- متوسط دقة GPS: [القيمة] متر
- أقصى انحراف: [القيمة] متر
- ملاحظات: [ملاحظات حول دقة تحديد الموقع]

### التنبيهات الصوتية:
- عدد التنبيهات الصحيحة: [العدد]
- عدد التنبيهات الخاطئة: [العدد]
- متوسط مسافة التنبيه قبل الوصول: [القيمة] متر
- ملاحظات: [ملاحظات حول التنبيهات]

### الخرائط دون اتصال:
- حجم البيانات المحملة: [الحجم] ميجابايت
- دقة الخرائط: [تقييم من 1-10]
- ملاحظات: [ملاحظات حول الخرائط]

### استهلاك البطارية:
- نسبة استهلاك البطارية خلال الاختبار: [النسبة]%
- مدة الاختبار: [المدة] ساعة
- ملاحظات: [ملاحظات حول استهلاك البطارية]

### المشاكل المكتشفة:
1. [وصف المشكلة 1]
2. [وصف المشكلة 2]
3. [...]

### اقتراحات التحسين:
1. [اقتراح 1]
2. [اقتراح 2]
3. [...]

## الخلاصة:
[ملخص عام لنتائج الاختبار]

## المرفقات:
- [صور]
- [مقاطع فيديو]
- [ملفات سجلات]
```

## 10. الخطوات التالية بعد الاختبار

1. **تحليل نتائج الاختبار**:
   - تحديد المشاكل المتكررة
   - تحديد أولويات الإصلاح

2. **تنفيذ التحسينات**:
   - إصلاح الأخطاء المكتشفة
   - تحسين الأداء بناءً على نتائج الاختبار
   - تحسين واجهة المستخدم بناءً على ملاحظات المستخدمين

3. **إعادة الاختبار**:
   - إعادة اختبار المناطق التي تم تحسينها
   - التحقق من عدم ظهور مشاكل جديدة

4. **إصدار تحديث**:
   - إصدار تحديث للتطبيق يتضمن التحسينات
   - توثيق التغييرات في ملاحظات الإصدار

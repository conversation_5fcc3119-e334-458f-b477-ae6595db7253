<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتجاهات - يمن GPS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .place-info {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .coordinates {
            font-family: monospace;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار وظيفة الاتجاهات</h1>
        
        <p>اختبر الانتقال من صفحة الأماكن إلى الخريطة مع الاتجاهات:</p>
        
        <h2>📍 أماكن للاختبار:</h2>
        
        <div class="place-info">
            <h3>فندق موفنبيك صنعاء</h3>
            <p class="coordinates">الإحداثيات: 15.3547, 44.2066</p>
            <a href="http://localhost:8000/?lat=15.3547&lng=44.2066&place=فندق موفنبيك صنعاء&directions=true" 
               class="test-button" target="_blank">
                🧭 اختبار الاتجاهات
            </a>
        </div>
        
        <div class="place-info">
            <h3>المدينة القديمة صنعاء</h3>
            <p class="coordinates">الإحداثيات: 15.3547, 44.2066</p>
            <a href="http://localhost:8000/?lat=15.3547&lng=44.2066&place=المدينة القديمة صنعاء&directions=true" 
               class="test-button" target="_blank">
                🧭 اختبار الاتجاهات
            </a>
        </div>
        
        <div class="place-info">
            <h3>فندق جولد موهور عدن</h3>
            <p class="coordinates">الإحداثيات: 12.7797, 45.0365</p>
            <a href="http://localhost:8000/?lat=12.7797&lng=45.0365&place=فندق جولد موهور عدن&directions=true" 
               class="test-button" target="_blank">
                🧭 اختبار الاتجاهات
            </a>
        </div>
        
        <div class="place-info">
            <h3>Albustan Luxurious Suites (من Google Places)</h3>
            <p class="coordinates">الإحداثيات: 15.30021430, 44.19296050</p>
            <a href="http://localhost:8000/?lat=15.30021430&lng=44.19296050&place=Albustan Luxurious Suites&directions=true" 
               class="test-button" target="_blank">
                🧭 اختبار الاتجاهات
            </a>
        </div>
        
        <h2>🔗 روابط مفيدة:</h2>
        
        <a href="http://localhost:8000/" class="test-button">🗺️ الخريطة الرئيسية</a>
        <a href="http://localhost:8000/places" class="test-button">📍 صفحة الأماكن</a>
        
        <h2>📋 خطوات الاختبار:</h2>
        
        <ol>
            <li>اذهب إلى <a href="http://localhost:8000/places" target="_blank">صفحة الأماكن</a></li>
            <li>اختر أي مكان واضغط على "الاتجاهات"</li>
            <li>يجب أن تنتقل إلى الخريطة الرئيسية مع عرض الاتجاهات</li>
            <li>تحقق من أن عنوان الصفحة يتغير إلى "الاتجاهات إلى [اسم المكان]"</li>
            <li>تحقق من أن مربع البحث يعرض "الاتجاهات إلى [اسم المكان]"</li>
        </ol>
        
        <h2>🐛 استكشاف الأخطاء:</h2>
        
        <ul>
            <li><strong>إذا لم تعمل الخريطة:</strong> تحقق من وحدة التحكم في المتصفح (F12)</li>
            <li><strong>إذا لم تظهر الاتجاهات:</strong> تحقق من أن الإحداثيات صحيحة</li>
            <li><strong>إذا لم يتغير العنوان:</strong> تحقق من معاملات URL</li>
        </ul>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #d4edda; border-radius: 5px;">
            <h3>✅ النتيجة المتوقعة:</h3>
            <p>عند الضغط على "الاتجاهات" في أي مكان، يجب أن:</p>
            <ul>
                <li>تنتقل إلى http://localhost:8000/ مع معاملات الاتجاهات</li>
                <li>تظهر الخريطة مع تركيز على الوجهة المحددة</li>
                <li>يتم حساب وعرض الاتجاهات من موقعك الحالي إلى الوجهة</li>
                <li>يتغير عنوان الصفحة ومربع البحث ليعكس الوجهة</li>
            </ul>
        </div>
    </div>
    
    <script>
        // إضافة معلومات إضافية للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            // عرض معلومات المتصفح
            const info = document.createElement('div');
            info.style.cssText = 'margin-top: 20px; padding: 10px; background: #e9ecef; border-radius: 5px; font-size: 12px;';
            info.innerHTML = `
                <strong>معلومات المتصفح:</strong><br>
                User Agent: ${navigator.userAgent}<br>
                الوقت الحالي: ${new Date().toLocaleString('ar')}<br>
                دعم الموقع الجغرافي: ${navigator.geolocation ? 'متوفر' : 'غير متوفر'}
            `;
            document.querySelector('.test-container').appendChild(info);
        });
    </script>
</body>
</html>

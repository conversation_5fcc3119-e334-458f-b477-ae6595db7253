// سكريبت محدث لعرض بيانات المستخدمين والعملاء والمواقع من قاعدة البيانات
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function showDatabaseData() {
  try {
    console.log('============ استعلام بيانات نظام Yemen GPS ============');

    // استعلام بيانات المستخدمين
    console.log('\n📊 جدول المستخدمين:');
    const usersQuery = `
      SELECT id, username, full_name, email, role_id, is_active, registration_date, last_login, type 
      FROM users 
      ORDER BY id
    `;
    const usersResult = await pool.query(usersQuery);
    
    if (usersResult.rows.length === 0) {
      console.log('  لا توجد بيانات للمستخدمين');
    } else {
      console.log(`  تم العثور على ${usersResult.rows.length} مستخدم:`);
      usersResult.rows.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.username} (${user.full_name || 'بدون اسم'}) - ${user.email || 'بدون بريد إلكتروني'}`);
        console.log(`     الدور: ${user.role_id}, الحالة: ${user.is_active ? 'نشط' : 'غير نشط'}, النوع: ${user.type || 'غير محدد'}`);
        console.log(`     تاريخ التسجيل: ${user.registration_date ? user.registration_date.toLocaleString() : 'غير محدد'}`);
        console.log(`     آخر تسجيل دخول: ${user.last_login ? user.last_login.toLocaleString() : 'غير محدد'}`);
        console.log('    -------');
      });
    }

    // استعلام بيانات العملاء (في جدول clients إذا كان موجودًا)
    console.log('\n📊 جدول العملاء:');
    const clientsQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'clients'
    `;
    
    const clientsTableResult = await pool.query(clientsQuery);
    
    if (clientsTableResult.rows.length === 0) {
      console.log('  ⚠️ جدول العملاء (clients) غير موجود في قاعدة البيانات');
      
      // البحث عن جدول بديل قد يحتوي على بيانات العملاء
      console.log('  البحث عن جدول بديل للعملاء...');
      const alternativeTablesQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%client%' OR table_name LIKE '%customer%'
      `;
      
      const alternativeResult = await pool.query(alternativeTablesQuery);
      
      if (alternativeResult.rows.length > 0) {
        console.log(`  وجدت ${alternativeResult.rows.length} جدول بديل محتمل:`);
        alternativeResult.rows.forEach(table => {
          console.log(`  - ${table.table_name}`);
        });
      } else {
        console.log('  لم يتم العثور على جداول بديلة للعملاء');
      }
    } else {
      // جدول العملاء موجود، استعلم عن البيانات
      const clientsDataQuery = `SELECT * FROM clients ORDER BY id`;
      const clientsDataResult = await pool.query(clientsDataQuery);
      
      if (clientsDataResult.rows.length === 0) {
        console.log('  لا توجد بيانات للعملاء');
      } else {
        console.log(`  تم العثور على ${clientsDataResult.rows.length} عميل:`);
        clientsDataResult.rows.forEach((client, index) => {
          console.log(`  ${index + 1}. ${client.name || 'بدون اسم'}`);
          // عرض الحقول الأخرى إذا كانت موجودة
          Object.keys(client).forEach(key => {
            if (key !== 'id' && key !== 'name' && client[key]) {
              console.log(`     ${key}: ${client[key]}`);
            }
          });
          console.log('    -------');
        });
      }
    }

    // استعلام بيانات المواقع
    console.log('\n📊 جدول المواقع:');
    const locationsQuery = `
      SELECT id, name, description, lat, lng, category_id, address, phone, 
             created_at, status, added_by
      FROM locations 
      ORDER BY id
    `;
    
    const locationsResult = await pool.query(locationsQuery);
    
    if (locationsResult.rows.length === 0) {
      console.log('  لا توجد بيانات للمواقع');
    } else {
      console.log(`  تم العثور على ${locationsResult.rows.length} موقع:`);
      locationsResult.rows.forEach((location, index) => {
        console.log(`  ${index + 1}. ${location.name || 'بدون اسم'}`);
        console.log(`     الإحداثيات: (${location.lat}, ${location.lng})`);
        
        if (location.category_id) console.log(`     فئة التصنيف: ${location.category_id}`);
        if (location.description) console.log(`     الوصف: ${location.description}`);
        if (location.address) console.log(`     العنوان: ${location.address}`);
        if (location.phone) console.log(`     الهاتف: ${location.phone}`);
        if (location.status) console.log(`     الحالة: ${location.status}`);
        if (location.created_at) console.log(`     تاريخ الإنشاء: ${location.created_at.toLocaleString()}`);
        
        console.log('    -------');
      });
    }

    // استعلام بيانات التصنيفات
    console.log('\n📊 جدول التصنيفات:');
    const categoriesQuery = `
      SELECT * FROM categories ORDER BY id
    `;
    
    const categoriesResult = await pool.query(categoriesQuery);
    
    if (categoriesResult.rows.length === 0) {
      console.log('  لا توجد بيانات للتصنيفات');
    } else {
      console.log(`  تم العثور على ${categoriesResult.rows.length} تصنيف:`);
      categoriesResult.rows.forEach((category, index) => {
        console.log(`  ${index + 1}. ${category.name || 'بدون اسم'} (ID: ${category.id})`);
        
        // عرض الحقول الأخرى إذا كانت موجودة
        Object.keys(category).forEach(key => {
          if (!['id', 'name'].includes(key) && category[key]) {
            console.log(`     ${key}: ${category[key]}`);
          }
        });
        
        console.log('    -------');
      });
    }

  } catch (error) {
    console.error('حدث خطأ أثناء استعلام البيانات:', error);
  } finally {
    await pool.end();
  }
}

showDatabaseData();

package com.yemengps.app.data;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * Manager for handling user preferences
 */
public class PreferenceManager {

    private static final String PREF_NAME = "yemen_gps_prefs";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_USER_NAME = "user_name";
    private static final String KEY_USER_EMAIL = "user_email";
    private static final String KEY_AUTH_TOKEN = "auth_token";
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";
    private static final String KEY_IS_ADMIN = "is_admin";
    private static final String KEY_ALERT_DISTANCE = "alert_distance";
    private static final String KEY_VOICE_ALERTS = "voice_alerts";
    private static final String KEY_VIBRATION_ALERTS = "vibration_alerts";
    private static final String KEY_MAP_TYPE = "map_type";
    private static final String KEY_LANGUAGE = "language";
    private static final String KEY_FIRST_RUN = "first_run";

    private final SharedPreferences preferences;

    /**
     * Constructor
     * @param context The application context
     */
    public PreferenceManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Save user login information
     * @param userId The user ID
     * @param userName The user name
     * @param userEmail The user email
     * @param authToken The authentication token
     * @param isAdmin Whether the user is an administrator
     */
    public void saveUserLogin(String userId, String userName, String userEmail, String authToken, boolean isAdmin) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(KEY_USER_ID, userId);
        editor.putString(KEY_USER_NAME, userName);
        editor.putString(KEY_USER_EMAIL, userEmail);
        editor.putString(KEY_AUTH_TOKEN, authToken);
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.putBoolean(KEY_IS_ADMIN, isAdmin);
        editor.apply();
    }

    /**
     * Check if the user is logged in
     * @return True if the user is logged in, false otherwise
     */
    public boolean isLoggedIn() {
        return preferences.getBoolean(KEY_IS_LOGGED_IN, false);
    }

    /**
     * Check if the user is an administrator
     * @return True if the user is an administrator, false otherwise
     */
    public boolean isAdmin() {
        return preferences.getBoolean(KEY_IS_ADMIN, false);
    }

    /**
     * Get the user ID
     * @return The user ID
     */
    public String getUserId() {
        return preferences.getString(KEY_USER_ID, null);
    }

    /**
     * Get the user name
     * @return The user name
     */
    public String getUserName() {
        return preferences.getString(KEY_USER_NAME, null);
    }

    /**
     * Get the user email
     * @return The user email
     */
    public String getUserEmail() {
        return preferences.getString(KEY_USER_EMAIL, null);
    }

    /**
     * Get the authentication token
     * @return The authentication token
     */
    public String getAuthToken() {
        return preferences.getString(KEY_AUTH_TOKEN, null);
    }

    /**
     * Logout the user
     */
    public void logout() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_IS_LOGGED_IN, false);
        editor.putString(KEY_AUTH_TOKEN, null);
        editor.apply();
    }

    /**
     * Get the alert distance in meters
     * @return The alert distance in meters
     */
    public int getAlertDistance() {
        return preferences.getInt(KEY_ALERT_DISTANCE, 500); // Default: 500 meters
    }

    /**
     * Set the alert distance in meters
     * @param distance The alert distance in meters
     */
    public void setAlertDistance(int distance) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(KEY_ALERT_DISTANCE, distance);
        editor.apply();
    }

    /**
     * Check if voice alerts are enabled
     * @return True if voice alerts are enabled, false otherwise
     */
    public boolean isVoiceAlertsEnabled() {
        return preferences.getBoolean(KEY_VOICE_ALERTS, true); // Default: enabled
    }

    /**
     * Set whether voice alerts are enabled
     * @param enabled True to enable voice alerts, false to disable
     */
    public void setVoiceAlertsEnabled(boolean enabled) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_VOICE_ALERTS, enabled);
        editor.apply();
    }

    /**
     * Check if vibration alerts are enabled
     * @return True if vibration alerts are enabled, false otherwise
     */
    public boolean isVibrationAlertsEnabled() {
        return preferences.getBoolean(KEY_VIBRATION_ALERTS, true); // Default: enabled
    }

    /**
     * Set whether vibration alerts are enabled
     * @param enabled True to enable vibration alerts, false to disable
     */
    public void setVibrationAlertsEnabled(boolean enabled) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_VIBRATION_ALERTS, enabled);
        editor.apply();
    }

    /**
     * Get the map type
     * @return The map type (0 = normal, 1 = satellite, 2 = terrain, 3 = hybrid)
     */
    public int getMapType() {
        return preferences.getInt(KEY_MAP_TYPE, 0); // Default: normal
    }

    /**
     * Set the map type
     * @param mapType The map type (0 = normal, 1 = satellite, 2 = terrain, 3 = hybrid)
     */
    public void setMapType(int mapType) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(KEY_MAP_TYPE, mapType);
        editor.apply();
    }

    /**
     * Get the language
     * @return The language code (e.g., "en", "ar")
     */
    public String getLanguage() {
        return preferences.getString(KEY_LANGUAGE, "en"); // Default: English
    }

    /**
     * Set the language
     * @param language The language code (e.g., "en", "ar")
     */
    public void setLanguage(String language) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(KEY_LANGUAGE, language);
        editor.apply();
    }

    /**
     * Check if this is the first run of the app
     * @return True if this is the first run, false otherwise
     */
    public boolean isFirstRun() {
        return preferences.getBoolean(KEY_FIRST_RUN, true); // Default: true
    }

    /**
     * Set whether this is the first run of the app
     * @param firstRun True if this is the first run, false otherwise
     */
    public void setFirstRun(boolean firstRun) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_FIRST_RUN, firstRun);
        editor.apply();
    }

    /**
     * Clear all preferences
     */
    public void clearAll() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.clear();
        editor.apply();
    }
}

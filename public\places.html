<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأماكن اليمنية - يمن GPS</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Ta<PERSON>wal Font -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background-color: #f8f9fa;
        }

        .place-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }

        .place-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .place-image {
            height: 200px;
            object-fit: cover;
            border-radius: 15px 15px 0 0;
        }

        .place-category {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .rating-stars {
            color: #ffc107;
        }

        .price-range {
            color: #28a745;
            font-weight: bold;
        }

        .contact-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .contact-item i {
            width: 20px;
            margin-left: 10px;
            color: #6c757d;
        }

        .search-filters {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .loading {
            text-align: center;
            padding: 50px;
        }

        .no-results {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .place-details-modal .modal-dialog {
            max-width: 800px;
        }

        .amenity-badge {
            background-color: #e9ecef;
            color: #495057;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }

        .opening-hours {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .day-hours {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .day-hours:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-map-marker-alt"></i>
                يمن GPS - الأماكن
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">الخريطة</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/places.html">الأماكن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin.html">لوحة التحكم</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- فلاتر البحث -->
        <div class="search-filters">
            <div class="row">
                <div class="col-md-4">
                    <label for="searchInput" class="form-label">البحث</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن مكان...">
                        <button class="btn btn-primary" type="button" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="categoryFilter" class="form-label">الفئة</label>
                    <select class="form-select" id="categoryFilter">
                        <option value="">جميع الفئات</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="governorateFilter" class="form-label">المحافظة</label>
                    <select class="form-select" id="governorateFilter">
                        <option value="">جميع المحافظات</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-outline-secondary w-100" id="clearFilters">
                        <i class="fas fa-times"></i> مسح
                    </button>
                </div>
            </div>
        </div>

        <!-- نتائج البحث -->
        <div id="resultsContainer">
            <div class="loading" id="loadingIndicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري تحميل الأماكن...</p>
            </div>
        </div>

        <!-- قائمة الأماكن -->
        <div class="row" id="placesContainer">
            <!-- سيتم ملء الأماكن هنا بواسطة JavaScript -->
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div class="no-results d-none" id="noResults">
            <i class="fas fa-search fa-3x mb-3"></i>
            <h4>لا توجد نتائج</h4>
            <p>لم يتم العثور على أماكن تطابق معايير البحث</p>
        </div>
    </div>

    <!-- نافذة تفاصيل المكان -->
    <div class="modal fade place-details-modal" id="placeDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="placeModalTitle">تفاصيل المكان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="placeModalBody">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="getDirectionsBtn">
                        <i class="fas fa-directions"></i> الاتجاهات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغيرات عامة
        let allPlaces = [];
        let categories = [];
        let governorates = [];
        let currentPlace = null;

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
            loadGovernorates();
            loadPlaces();

            // إعداد أحداث البحث والفلاتر
            setupEventListeners();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('searchBtn').addEventListener('click', performSearch);
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            document.getElementById('categoryFilter').addEventListener('change', performSearch);
            document.getElementById('governorateFilter').addEventListener('change', performSearch);
            document.getElementById('clearFilters').addEventListener('click', clearFilters);

            // إضافة مستمع لزر الاتجاهات
            document.getElementById('getDirectionsBtn').addEventListener('click', getDirections);
        }

        // تحميل الفئات
        async function loadCategories() {
            try {
                const response = await fetch('/api/places/categories');
                const result = await response.json();

                if (result.success) {
                    categories = result.data;
                    populateCategoryFilter();
                }
            } catch (error) {
                console.error('خطأ في تحميل الفئات:', error);
            }
        }

        // تحميل المحافظات
        async function loadGovernorates() {
            try {
                const response = await fetch('/api/places/governorates');
                const result = await response.json();

                if (result.success) {
                    governorates = result.data;
                    populateGovernorateFilter();
                }
            } catch (error) {
                console.error('خطأ في تحميل المحافظات:', error);
            }
        }

        // ملء قائمة الفئات
        function populateCategoryFilter() {
            const select = document.getElementById('categoryFilter');
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name_ar;
                select.appendChild(option);
            });
        }

        // ملء قائمة المحافظات
        function populateGovernorateFilter() {
            const select = document.getElementById('governorateFilter');
            governorates.forEach(governorate => {
                const option = document.createElement('option');
                option.value = governorate.id;
                option.textContent = governorate.name_ar;
                select.appendChild(option);
            });
        }

        // تحميل الأماكن
        async function loadPlaces() {
            try {
                showLoading(true);
                const response = await fetch('/api/places/places?limit=50');
                const result = await response.json();

                if (result.success) {
                    allPlaces = result.data;
                    displayPlaces(allPlaces);
                } else {
                    showError('خطأ في تحميل الأماكن');
                }
            } catch (error) {
                console.error('خطأ في تحميل الأماكن:', error);
                showError('خطأ في الاتصال بالخادم');
            } finally {
                showLoading(false);
            }
        }

        // عرض الأماكن
        function displayPlaces(places) {
            const container = document.getElementById('placesContainer');
            const noResults = document.getElementById('noResults');

            container.innerHTML = '';

            if (places.length === 0) {
                noResults.classList.remove('d-none');
                return;
            }

            noResults.classList.add('d-none');

            places.forEach(place => {
                const placeCard = createPlaceCard(place);
                container.appendChild(placeCard);
            });
        }

        // إنشاء بطاقة مكان
        function createPlaceCard(place) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4';

            const imageUrl = place.main_image || 'https://via.placeholder.com/300x200?text=لا+توجد+صورة';
            const rating = place.rating || 0;
            const reviewsCount = place.reviews_count || 0;

            col.innerHTML = `
                <div class="card place-card" onclick="showPlaceDetails(${place.id})">
                    <div class="position-relative">
                        <img src="${imageUrl}" class="card-img-top place-image" alt="${place.name_ar}">
                        <span class="place-category" style="background-color: ${place.category_color || '#007bff'}">
                            <i class="fas fa-${place.category_icon || 'map-marker-alt'}"></i>
                            ${place.category_name_ar || 'غير محدد'}
                        </span>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${place.name_ar}</h5>
                        <p class="card-text text-muted">${place.description_ar ? place.description_ar.substring(0, 100) + '...' : ''}</p>

                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="rating-stars">
                                ${generateStars(rating)}
                                <small class="text-muted">(${reviewsCount})</small>
                            </div>
                            ${place.price_range ? `<span class="price-range">${place.price_range}</span>` : ''}
                        </div>

                        <div class="contact-info">
                            ${place.governorate_name_ar ? `
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>${place.governorate_name_ar}</span>
                                </div>
                            ` : ''}

                            ${place.phone ? `
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:${place.phone}" class="text-decoration-none">${place.phone}</a>
                                </div>
                            ` : ''}

                            ${place.website ? `
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <a href="${place.website}" target="_blank" class="text-decoration-none">الموقع الإلكتروني</a>
                                </div>
                            ` : ''}
                        </div>

                        ${place.latitude && place.longitude ? `
                            <div class="mt-2">
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="event.stopPropagation(); openDirections(${place.latitude}, ${place.longitude}, '${place.name_ar.replace(/'/g, "\\'")}')">
                                    <i class="fas fa-directions"></i> الاتجاهات
                                </button>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            return col;
        }

        // توليد النجوم للتقييم
        function generateStars(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<i class="fas fa-star"></i>';
                } else if (i - 0.5 <= rating) {
                    stars += '<i class="fas fa-star-half-alt"></i>';
                } else {
                    stars += '<i class="far fa-star"></i>';
                }
            }
            return stars;
        }

        // عرض تفاصيل المكان
        async function showPlaceDetails(placeId) {
            try {
                const response = await fetch(`/api/places/places/${placeId}`);
                const result = await response.json();

                if (result.success) {
                    currentPlace = result.data;
                    populatePlaceModal(currentPlace);

                    const modal = new bootstrap.Modal(document.getElementById('placeDetailsModal'));
                    modal.show();
                } else {
                    alert('خطأ في تحميل تفاصيل المكان');
                }
            } catch (error) {
                console.error('خطأ في تحميل تفاصيل المكان:', error);
                alert('خطأ في الاتصال بالخادم');
            }
        }

        // ملء نافذة تفاصيل المكان
        function populatePlaceModal(place) {
            document.getElementById('placeModalTitle').textContent = place.name_ar;

            const modalBody = document.getElementById('placeModalBody');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <img src="${place.images && place.images.length > 0 ? place.images[0].image_url : 'https://via.placeholder.com/400x300'}"
                             class="img-fluid rounded" alt="${place.name_ar}">
                    </div>
                    <div class="col-md-6">
                        <h6>الوصف</h6>
                        <p>${place.description_ar || 'لا يوجد وصف متاح'}</p>

                        <h6>معلومات التواصل</h6>
                        <div class="contact-info">
                            ${place.phone ? `
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:${place.phone}">${place.phone}</a>
                                </div>
                            ` : ''}

                            ${place.email ? `
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <a href="mailto:${place.email}">${place.email}</a>
                                </div>
                            ` : ''}

                            ${place.website ? `
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <a href="${place.website}" target="_blank">الموقع الإلكتروني</a>
                                </div>
                            ` : ''}

                            ${place.whatsapp ? `
                                <div class="contact-item">
                                    <i class="fab fa-whatsapp"></i>
                                    <a href="https://wa.me/${place.whatsapp}" target="_blank">واتساب</a>
                                </div>
                            ` : ''}
                        </div>

                        <h6 class="mt-3">معلومات الموقع</h6>
                        <div class="contact-info">
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${place.governorate_name_ar || 'غير محدد'}</span>
                            </div>
                            ${place.latitude && place.longitude ? `
                                <div class="contact-item">
                                    <i class="fas fa-crosshairs"></i>
                                    <span>الإحداثيات: ${parseFloat(place.latitude).toFixed(6)}, ${parseFloat(place.longitude).toFixed(6)}</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-external-link-alt"></i>
                                    <a href="https://www.google.com/maps?q=${place.latitude},${place.longitude}" target="_blank">عرض في خرائط Google</a>
                                </div>
                            ` : ''}
                        </div>

                        ${place.amenities && place.amenities.length > 0 ? `
                            <h6 class="mt-3">المرافق والخدمات</h6>
                            <div>
                                ${place.amenities.map(amenity => `<span class="amenity-badge">${amenity.name_ar}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>

                ${place.opening_hours && place.opening_hours.length > 0 ? `
                    <div class="mt-4">
                        <h6>أوقات العمل</h6>
                        <div class="opening-hours">
                            ${place.opening_hours.map(hour => `
                                <div class="day-hours">
                                    <span>${getDayName(hour.day_of_week)}</span>
                                    <span>${hour.is_24_hours ? '24 ساعة' : hour.is_closed ? 'مغلق' : `${hour.open_time} - ${hour.close_time}`}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // الحصول على اسم اليوم
        function getDayName(dayNumber) {
            const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
            return days[dayNumber] || 'غير محدد';
        }

        // تنفيذ البحث
        async function performSearch() {
            const searchQuery = document.getElementById('searchInput').value.trim();
            const categoryId = document.getElementById('categoryFilter').value;
            const governorateId = document.getElementById('governorateFilter').value;

            try {
                showLoading(true);

                let url = '/api/places/places?';
                const params = new URLSearchParams();

                if (categoryId) params.append('category', categoryId);
                if (governorateId) params.append('governorate', governorateId);
                params.append('limit', '50');

                if (searchQuery) {
                    url = `/api/places/places/search/${encodeURIComponent(searchQuery)}?`;
                }

                url += params.toString();

                const response = await fetch(url);
                const result = await response.json();

                if (result.success) {
                    displayPlaces(result.data);
                } else {
                    showError('خطأ في البحث');
                }
            } catch (error) {
                console.error('خطأ في البحث:', error);
                showError('خطأ في الاتصال بالخادم');
            } finally {
                showLoading(false);
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('governorateFilter').value = '';
            loadPlaces();
        }

        // عرض/إخفاء مؤشر التحميل
        function showLoading(show) {
            const loading = document.getElementById('loadingIndicator');
            const container = document.getElementById('placesContainer');
            const noResults = document.getElementById('noResults');

            if (show) {
                loading.style.display = 'block';
                container.style.display = 'none';
                noResults.classList.add('d-none');
            } else {
                loading.style.display = 'none';
                container.style.display = 'flex';
            }
        }

        // عرض رسالة خطأ
        function showError(message) {
            alert(message);
        }

        // الحصول على الاتجاهات
        function getDirections() {
            if (!currentPlace) {
                alert('لم يتم تحديد مكان');
                return;
            }

            if (!currentPlace.latitude || !currentPlace.longitude) {
                alert('الإحداثيات غير متوفرة لهذا المكان');
                return;
            }

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('placeDetailsModal'));
            if (modal) {
                modal.hide();
            }

            // الانتقال إلى الخريطة المحلية مع الوجهة
            const mapUrl = `http://localhost:8000/?lat=${currentPlace.latitude}&lng=${currentPlace.longitude}&place=${encodeURIComponent(currentPlace.name_ar)}&directions=true`;
            window.location.href = mapUrl;
        }

        // إضافة زر الاتجاهات في بطاقة المكان
        function addDirectionsButton(place) {
            if (place.latitude && place.longitude) {
                return `
                    <div class="mt-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="openDirections(${place.latitude}, ${place.longitude}, '${place.name_ar}')">
                            <i class="fas fa-directions"></i> الاتجاهات
                        </button>
                    </div>
                `;
            }
            return '';
        }

        // فتح الاتجاهات مباشرة من البطاقة
        function openDirections(lat, lng, placeName) {
            // الانتقال إلى الخريطة المحلية مع الوجهة
            const mapUrl = `http://localhost:8000/?lat=${lat}&lng=${lng}&place=${encodeURIComponent(placeName)}&directions=true`;
            window.location.href = mapUrl;
        }
    </script>
</body>
</html>

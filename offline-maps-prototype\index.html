<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الخرائط المستقل - يمن GPS</title>
    
    <!-- OpenLayers CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/ol@8.2.0/ol.css">
    
    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- الخطوط العربية -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- الأنماط المخصصة -->
    <link rel="stylesheet" href="assets/css/maps-style.css">
</head>
<body>
    <!-- شاشة التحميل -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>جاري تحميل الخرائط...</h2>
            <p>يرجى الانتظار</p>
        </div>
    </div>

    <!-- شريط البحث العلوي -->
    <div class="search-container">
        <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="searchInput" placeholder="البحث في الخرائط..." autocomplete="off">
            <button id="searchBtn" class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
        
        <!-- نتائج البحث -->
        <div id="searchResults" class="search-results hidden">
            <div class="search-suggestions">
                <!-- ستملأ بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <!-- حاوي الخريطة الرئيسي -->
    <div id="map" class="map-container">
        <!-- الخريطة ستُحمل هنا -->
    </div>

    <!-- أزرار التحكم الجانبية -->
    <div class="map-controls">
        <!-- أزرار التكبير والتصغير -->
        <div class="zoom-controls">
            <button id="zoomIn" class="control-btn zoom-btn" title="تكبير">
                <i class="fas fa-plus"></i>
            </button>
            <button id="zoomOut" class="control-btn zoom-btn" title="تصغير">
                <i class="fas fa-minus"></i>
            </button>
        </div>

        <!-- زر الموقع الحالي -->
        <button id="myLocationBtn" class="control-btn location-btn" title="موقعي الحالي">
            <i class="fas fa-crosshairs"></i>
        </button>

        <!-- زر طبقات الخريطة -->
        <button id="layersBtn" class="control-btn layers-btn" title="طبقات الخريطة">
            <i class="fas fa-layers"></i>
        </button>

        <!-- زر ملء الشاشة -->
        <button id="fullscreenBtn" class="control-btn fullscreen-btn" title="ملء الشاشة">
            <i class="fas fa-expand"></i>
        </button>

        <!-- زر الاتجاهات -->
        <button id="directionsBtn" class="control-btn directions-btn" title="الاتجاهات">
            <i class="fas fa-route"></i>
        </button>
    </div>

    <!-- قائمة طبقات الخريطة -->
    <div id="layersPanel" class="layers-panel hidden">
        <div class="panel-header">
            <h3>طبقات الخريطة</h3>
            <button id="closeLayersPanel" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="panel-content">
            <div class="layer-options">
                <label class="layer-option">
                    <input type="radio" name="mapLayer" value="street" checked>
                    <span class="layer-icon">
                        <i class="fas fa-road"></i>
                    </span>
                    <span class="layer-text">خريطة الشوارع</span>
                </label>
                
                <label class="layer-option">
                    <input type="radio" name="mapLayer" value="satellite">
                    <span class="layer-icon">
                        <i class="fas fa-satellite"></i>
                    </span>
                    <span class="layer-text">صور الأقمار الصناعية</span>
                </label>
                
                <label class="layer-option">
                    <input type="radio" name="mapLayer" value="terrain">
                    <span class="layer-icon">
                        <i class="fas fa-mountain"></i>
                    </span>
                    <span class="layer-text">خريطة التضاريس</span>
                </label>
            </div>
            
            <div class="layer-overlays">
                <h4>طبقات إضافية</h4>
                <label class="overlay-option">
                    <input type="checkbox" id="trafficLayer">
                    <span>حركة المرور</span>
                </label>
                <label class="overlay-option">
                    <input type="checkbox" id="transitLayer">
                    <span>وسائل النقل العام</span>
                </label>
                <label class="overlay-option">
                    <input type="checkbox" id="bikingLayer">
                    <span>مسارات الدراجات</span>
                </label>
            </div>
        </div>
    </div>

    <!-- نافذة معلومات النقطة -->
    <div id="pointInfoPopup" class="point-info-popup hidden">
        <div class="popup-header">
            <h3 id="pointTitle">اسم المكان</h3>
            <button id="closePointPopup" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="popup-content">
            <!-- صور المكان -->
            <div id="pointImages" class="point-images">
                <div class="image-slider">
                    <div class="slider-container">
                        <!-- الصور ستُضاف هنا -->
                    </div>
                    <button class="slider-btn prev-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="slider-btn next-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <!-- معلومات المكان -->
            <div class="point-details">
                <div id="pointDescription" class="point-description"></div>
                <div id="pointAddress" class="point-address">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="address-text"></span>
                </div>
                <div id="pointContact" class="point-contact">
                    <div class="contact-item phone">
                        <i class="fas fa-phone"></i>
                        <span class="contact-text"></span>
                    </div>
                    <div class="contact-item website">
                        <i class="fas fa-globe"></i>
                        <a href="#" target="_blank" class="contact-link"></a>
                    </div>
                </div>
                <div id="pointRating" class="point-rating">
                    <div class="rating-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    </div>
                    <span class="rating-text">4.2 (156 تقييم)</span>
                </div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="point-actions">
                <button id="routeToBtn" class="action-btn route-btn">
                    <i class="fas fa-route"></i>
                    <span>المسار</span>
                </button>
                <button id="savePointBtn" class="action-btn save-btn">
                    <i class="fas fa-bookmark"></i>
                    <span>حفظ</span>
                </button>
                <button id="sharePointBtn" class="action-btn share-btn">
                    <i class="fas fa-share-alt"></i>
                    <span>مشاركة</span>
                </button>
                <button id="streetViewBtn" class="action-btn streetview-btn">
                    <i class="fas fa-street-view"></i>
                    <span>عرض الشارع</span>
                </button>
            </div>
        </div>
    </div>

    <!-- لوحة الاتجاهات -->
    <div id="directionsPanel" class="directions-panel hidden">
        <div class="panel-header">
            <h3>الاتجاهات</h3>
            <button id="closeDirectionsPanel" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="panel-content">
            <div class="directions-form">
                <div class="route-inputs">
                    <div class="input-group">
                        <div class="input-icon">
                            <i class="fas fa-circle start-icon"></i>
                        </div>
                        <input type="text" id="fromInput" placeholder="نقطة البداية" class="route-input">
                        <button class="input-action" id="useCurrentLocation" title="استخدام موقعي الحالي">
                            <i class="fas fa-crosshairs"></i>
                        </button>
                    </div>
                    
                    <div class="input-group">
                        <div class="input-icon">
                            <i class="fas fa-map-marker-alt end-icon"></i>
                        </div>
                        <input type="text" id="toInput" placeholder="الوجهة" class="route-input">
                        <button class="input-action" id="swapLocations" title="تبديل المواقع">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                    </div>
                </div>
                
                <!-- خيارات وسائل النقل -->
                <div class="transport-options">
                    <button class="transport-btn active" data-mode="driving" title="بالسيارة">
                        <i class="fas fa-car"></i>
                        <span>سيارة</span>
                    </button>
                    <button class="transport-btn" data-mode="walking" title="مشياً">
                        <i class="fas fa-walking"></i>
                        <span>مشي</span>
                    </button>
                    <button class="transport-btn" data-mode="cycling" title="بالدراجة">
                        <i class="fas fa-bicycle"></i>
                        <span>دراجة</span>
                    </button>
                    <button class="transport-btn" data-mode="transit" title="النقل العام">
                        <i class="fas fa-bus"></i>
                        <span>نقل عام</span>
                    </button>
                </div>
                
                <!-- خيارات المسار -->
                <div class="route-options">
                    <label class="route-option">
                        <input type="radio" name="routeType" value="fastest" checked>
                        <span>أسرع مسار</span>
                    </label>
                    <label class="route-option">
                        <input type="radio" name="routeType" value="shortest">
                        <span>أقصر مسار</span>
                    </label>
                    <label class="route-option">
                        <input type="checkbox" id="avoidTolls">
                        <span>تجنب الرسوم</span>
                    </label>
                    <label class="route-option">
                        <input type="checkbox" id="avoidHighways">
                        <span>تجنب الطرق السريعة</span>
                    </label>
                </div>
                
                <button id="calculateRouteBtn" class="calculate-btn">
                    <i class="fas fa-route"></i>
                    حساب المسار
                </button>
            </div>
            
            <!-- نتائج المسار -->
            <div id="routeResults" class="route-results hidden">
                <div class="route-summary">
                    <div class="route-info">
                        <div class="route-stat">
                            <i class="fas fa-road"></i>
                            <span id="routeDistance">15.2 كم</span>
                        </div>
                        <div class="route-stat">
                            <i class="fas fa-clock"></i>
                            <span id="routeDuration">22 دقيقة</span>
                        </div>
                        <div class="route-stat">
                            <i class="fas fa-gas-pump"></i>
                            <span id="routeFuel">1.2 لتر</span>
                        </div>
                    </div>
                    <div class="route-actions">
                        <button id="startNavigationBtn" class="start-nav-btn">
                            <i class="fas fa-play"></i>
                            بدء الملاحة
                        </button>
                        <button id="previewRouteBtn" class="preview-btn">
                            <i class="fas fa-eye"></i>
                            معاينة
                        </button>
                    </div>
                </div>
                
                <!-- تعليمات المسار -->
                <div id="routeInstructions" class="route-instructions">
                    <h4>تعليمات المسار</h4>
                    <div class="instructions-list">
                        <!-- التعليمات ستُضاف هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط الملاحة السفلي -->
    <div id="navigationBar" class="navigation-bar hidden">
        <div class="nav-content">
            <div class="nav-instruction">
                <div class="instruction-icon">
                    <i class="fas fa-arrow-right"></i>
                </div>
                <div class="instruction-text">
                    <div class="instruction-main">انعطف يميناً في شارع الزبيري</div>
                    <div class="instruction-distance">بعد 500 متر</div>
                </div>
            </div>
            <div class="nav-controls">
                <button id="muteBtn" class="nav-btn">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button id="stopNavigationBtn" class="nav-btn stop-btn">
                    <i class="fas fa-stop"></i>
                </button>
            </div>
        </div>
        <div class="nav-progress">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 35%"></div>
            </div>
            <div class="nav-stats">
                <span class="remaining-time">15 دقيقة متبقية</span>
                <span class="remaining-distance">8.5 كم متبقية</span>
            </div>
        </div>
    </div>

    <!-- مشغل الصوت للتنبيهات -->
    <audio id="audioPlayer" preload="auto">
        <source src="assets/audio/navigation-alert.mp3" type="audio/mpeg">
        <source src="assets/audio/navigation-alert.ogg" type="audio/ogg">
    </audio>

    <!-- رسائل التنبيه -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- السكريبتات -->
    <script src="https://cdn.jsdelivr.net/npm/ol@8.2.0/dist/ol.js"></script>
    <script src="assets/js/maps-core.js"></script>
    <script src="assets/js/routing-engine.js"></script>
    <script src="assets/js/audio-alerts.js"></script>
    <script src="assets/js/search-engine.js"></script>
    <script src="assets/js/offline-manager.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>

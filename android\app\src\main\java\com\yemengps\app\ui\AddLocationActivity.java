package com.yemengps.app.ui;

import android.Manifest;
import android.content.pm.PackageManager;
import android.location.Location;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.material.textfield.TextInputEditText;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.data.LocationRepository;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.model.LocationType;
import com.yemengps.app.network.ApiClient;
import com.yemengps.app.network.ApiResponse;

import java.util.Date;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Activity for adding a new location
 */
public class AddLocationActivity extends AppCompatActivity implements OnMapReadyCallback {

    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    private static final float DEFAULT_ZOOM = 15f;

    private GoogleMap map;
    private FusedLocationProviderClient fusedLocationClient;
    private LocationRepository locationRepository;
    private PreferenceManager preferenceManager;
    private ApiClient apiClient;

    private TextInputEditText editLocationName;
    private TextInputEditText editDescription;
    private RadioGroup radioLocationType;
    private TextView locationCoordinates;
    private Button btnSubmit;

    private LatLng selectedLocation;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_location);

        // Initialize toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(R.string.add_location_title);

        // Initialize components
        editLocationName = findViewById(R.id.edit_location_name);
        editDescription = findViewById(R.id.edit_description);
        radioLocationType = findViewById(R.id.radio_location_type);
        locationCoordinates = findViewById(R.id.location_coordinates);
        btnSubmit = findViewById(R.id.btn_submit);

        // Initialize map
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager()
                .findFragmentById(R.id.map_preview);
        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        }

        // Initialize location client
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);

        // Initialize repositories
        locationRepository = new LocationRepository(YemenGpsApplication.getInstance().getDatabase());
        preferenceManager = YemenGpsApplication.getInstance().getPreferenceManager();
        apiClient = YemenGpsApplication.getInstance().getApiClient();

        // Set submit button click listener
        btnSubmit.setOnClickListener(v -> submitLocation());
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        map = googleMap;

        // Set map settings
        map.setMapType(GoogleMap.MAP_TYPE_NORMAL);
        map.getUiSettings().setZoomControlsEnabled(true);
        map.getUiSettings().setCompassEnabled(true);

        // Move to current location
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED) {
            map.setMyLocationEnabled(true);
            moveToCurrentLocation();
        } else {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
        }

        // Set map click listener
        map.setOnMapClickListener(latLng -> {
            // Update selected location
            selectedLocation = latLng;

            // Update coordinates text
            updateCoordinatesText();

            // Clear previous markers and add new one
            map.clear();
            map.addMarker(new MarkerOptions().position(latLng));
        });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (map != null) {
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                            == PackageManager.PERMISSION_GRANTED) {
                        map.setMyLocationEnabled(true);
                        moveToCurrentLocation();
                    }
                }
            } else {
                Toast.makeText(this, R.string.error_location_permission, Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    /**
     * Move the map to the current location
     */
    private void moveToCurrentLocation() {
        if (map == null) return;

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
            return;
        }

        fusedLocationClient.getLastLocation().addOnSuccessListener(this, location -> {
            if (location != null) {
                LatLng currentLatLng = new LatLng(location.getLatitude(), location.getLongitude());
                selectedLocation = currentLatLng;
                updateCoordinatesText();
                map.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, DEFAULT_ZOOM));
                map.addMarker(new MarkerOptions().position(currentLatLng));
            }
        });
    }

    /**
     * Update the coordinates text
     */
    private void updateCoordinatesText() {
        if (selectedLocation != null) {
            String text = String.format("Latitude: %.6f, Longitude: %.6f",
                    selectedLocation.latitude, selectedLocation.longitude);
            locationCoordinates.setText(text);
        }
    }

    /**
     * Submit the location
     */
    private void submitLocation() {
        // Validate input
        String name = editLocationName.getText().toString().trim();
        if (name.isEmpty()) {
            editLocationName.setError("Please enter a name");
            return;
        }

        if (selectedLocation == null) {
            Toast.makeText(this, "Please select a location on the map", Toast.LENGTH_SHORT).show();
            return;
        }

        int selectedTypeId = radioLocationType.getCheckedRadioButtonId();
        if (selectedTypeId == -1) {
            Toast.makeText(this, "Please select a location type", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get location type
        LocationType locationType;
        if (selectedTypeId == R.id.radio_traffic) {
            locationType = LocationType.TRAFFIC;
        } else if (selectedTypeId == R.id.radio_speedbump) {
            locationType = LocationType.SPEEDBUMP;
        } else if (selectedTypeId == R.id.radio_pothole) {
            locationType = LocationType.POTHOLE;
        } else if (selectedTypeId == R.id.radio_dirt_road) {
            locationType = LocationType.DIRT_ROAD;
        } else if (selectedTypeId == R.id.radio_checkpoint) {
            locationType = LocationType.CHECKPOINT;
        } else {
            locationType = LocationType.TRAFFIC; // Default
        }

        // Get description
        String description = editDescription.getText().toString().trim();

        // Create location
        LocationPoint location = new LocationPoint(
                name,
                selectedLocation.latitude,
                selectedLocation.longitude,
                locationType,
                description,
                preferenceManager.getUserId(),
                preferenceManager.getUserName()
        );

        // Save to local database
        locationRepository.insert(location);

        // Send to server
        apiClient.addLocation(location, new Callback<ApiResponse<LocationPoint>>() {
            @Override
            public void onResponse(Call<ApiResponse<LocationPoint>> call, Response<ApiResponse<LocationPoint>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    runOnUiThread(() -> {
                        Toast.makeText(AddLocationActivity.this, R.string.location_submitted, Toast.LENGTH_LONG).show();
                        finish();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(AddLocationActivity.this, "Error submitting location to server, but saved locally", Toast.LENGTH_LONG).show();
                        finish();
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LocationPoint>> call, Throwable t) {
                runOnUiThread(() -> {
                    Toast.makeText(AddLocationActivity.this, "Error submitting location to server, but saved locally", Toast.LENGTH_LONG).show();
                    finish();
                });
            }
        });
    }
}

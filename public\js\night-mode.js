// public/js/night-mode.js
class NightModeManager {
  constructor() {
    this.isNightMode = false;
    this.autoNightMode = false;
    this.nightModeStartHour = 18; // 6 مساءً
    this.nightModeEndHour = 6; // 6 صباحًا
    
    // تحميل الإعدادات من التخزين المحلي
    this.loadSettings();
    
    // تهيئة الوضع الليلي بناءً على الوقت إذا كان التبديل التلقائي مفعلاً
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
  }
  
  // تحميل الإعدادات
  loadSettings() {
    const settings = JSON.parse(localStorage.getItem('nightModeSettings')) || {};
    this.isNightMode = settings.isNightMode || false;
    this.autoNightMode = settings.autoNightMode || false;
    this.nightModeStartHour = settings.nightModeStartHour || 18;
    this.nightModeEndHour = settings.nightModeEndHour || 6;
    
    // تطبيق الوضع الليلي إذا كان مفعلاً
    if (this.isNightMode) {
      this.enableNightMode();
    }
  }
  
  // حفظ الإعدادات
  saveSettings() {
    const settings = {
      isNightMode: this.isNightMode,
      autoNightMode: this.autoNightMode,
      nightModeStartHour: this.nightModeStartHour,
      nightModeEndHour: this.nightModeEndHour
    };
    
    localStorage.setItem('nightModeSettings', JSON.stringify(settings));
  }
  
  // تفعيل الوضع الليلي
  enableNightMode() {
    document.body.classList.add('night-mode');
    this.isNightMode = true;
    this.saveSettings();
    
    // تغيير نمط الخريطة إلى الوضع الليلي
    if (window.map && window.baseMaps) {
      // إزالة جميع الطبقات
      Object.values(window.baseMaps).forEach(layer => {
        if (window.map.hasLayer(layer)) {
          window.map.removeLayer(layer);
        }
      });
      
      // إضافة طبقة الخريطة الليلية
      if (window.baseMaps['خريطة ليلية']) {
        window.baseMaps['خريطة ليلية'].addTo(window.map);
      } else if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    }
    
    // تحديث أيقونة زر الوضع الليلي
    const nightModeButton = document.getElementById('night-mode-toggle');
    if (nightModeButton) {
      nightModeButton.innerHTML = '&#9788;'; // رمز الشمس
      nightModeButton.title = 'تبديل إلى الوضع العادي';
    }
    
    // إرسال حدث تغيير الوضع الليلي
    document.dispatchEvent(new CustomEvent('nightModeChanged', { detail: { isNightMode: true } }));
  }
  
  // تعطيل الوضع الليلي
  disableNightMode() {
    document.body.classList.remove('night-mode');
    this.isNightMode = false;
    this.saveSettings();
    
    // تغيير نمط الخريطة إلى الوضع العادي
    if (window.map && window.baseMaps) {
      // إزالة جميع الطبقات
      Object.values(window.baseMaps).forEach(layer => {
        if (window.map.hasLayer(layer)) {
          window.map.removeLayer(layer);
        }
      });
      
      // إضافة طبقة الخريطة العادية
      if (window.baseMaps['OpenStreetMap']) {
        window.baseMaps['OpenStreetMap'].addTo(window.map);
      }
    }
    
    // تحديث أيقونة زر الوضع الليلي
    const nightModeButton = document.getElementById('night-mode-toggle');
    if (nightModeButton) {
      nightModeButton.innerHTML = '&#127769;'; // رمز القمر
      nightModeButton.title = 'تبديل إلى وضع القيادة الليلية';
    }
    
    // إرسال حدث تغيير الوضع الليلي
    document.dispatchEvent(new CustomEvent('nightModeChanged', { detail: { isNightMode: false } }));
  }
  
  // تبديل الوضع الليلي
  toggleNightMode() {
    if (this.isNightMode) {
      this.disableNightMode();
    } else {
      this.enableNightMode();
    }
  }
  
  // تفعيل/تعطيل التبديل التلقائي
  toggleAutoNightMode() {
    this.autoNightMode = !this.autoNightMode;
    this.saveSettings();
    
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
    
    return this.autoNightMode;
  }
  
  // التحقق من الوقت وتعيين الوضع المناسب
  checkTimeAndSetMode() {
    const currentHour = new Date().getHours();
    
    // التحقق مما إذا كان الوقت الحالي ضمن ساعات الليل
    const isNightTime = (currentHour >= this.nightModeStartHour || currentHour < this.nightModeEndHour);
    
    if (isNightTime && !this.isNightMode) {
      this.enableNightMode();
    } else if (!isNightTime && this.isNightMode) {
      this.disableNightMode();
    }
  }
  
  // تعيين ساعات الوضع الليلي
  setNightModeHours(startHour, endHour) {
    this.nightModeStartHour = startHour;
    this.nightModeEndHour = endHour;
    this.saveSettings();
    
    if (this.autoNightMode) {
      this.checkTimeAndSetMode();
    }
  }
  
  // الحصول على حالة الوضع الليلي الحالية
  getNightModeStatus() {
    return {
      isNightMode: this.isNightMode,
      autoNightMode: this.autoNightMode,
      nightModeStartHour: this.nightModeStartHour,
      nightModeEndHour: this.nightModeEndHour
    };
  }
  
  // إنشاء واجهة مستخدم لإعدادات الوضع الليلي
  createSettingsUI() {
    // إنشاء عنصر الإعدادات إذا لم يكن موجودًا
    if (!document.getElementById('night-mode-settings')) {
      const settingsDiv = document.createElement('div');
      settingsDiv.id = 'night-mode-settings';
      settingsDiv.className = 'settings-panel';
      settingsDiv.style.display = 'none';
      settingsDiv.style.position = 'absolute';
      settingsDiv.style.top = '50%';
      settingsDiv.style.left = '50%';
      settingsDiv.style.transform = 'translate(-50%, -50%)';
      settingsDiv.style.backgroundColor = 'white';
      settingsDiv.style.padding = '20px';
      settingsDiv.style.borderRadius = '5px';
      settingsDiv.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
      settingsDiv.style.zIndex = '1000';
      settingsDiv.style.minWidth = '300px';
      
      // إضافة عنوان
      const title = document.createElement('h3');
      title.textContent = 'إعدادات وضع القيادة الليلية';
      title.style.textAlign = 'center';
      title.style.margin = '0 0 15px 0';
      settingsDiv.appendChild(title);
      
      // إضافة خيار التبديل التلقائي
      const autoDiv = document.createElement('div');
      autoDiv.style.marginBottom = '15px';
      
      const autoLabel = document.createElement('label');
      autoLabel.textContent = 'تفعيل التبديل التلقائي: ';
      autoLabel.style.display = 'inline-block';
      autoLabel.style.width = '70%';
      
      const autoCheckbox = document.createElement('input');
      autoCheckbox.type = 'checkbox';
      autoCheckbox.id = 'auto-night-mode';
      autoCheckbox.checked = this.autoNightMode;
      autoCheckbox.addEventListener('change', () => {
        const isAuto = this.toggleAutoNightMode();
        autoCheckbox.checked = isAuto;
      });
      
      autoDiv.appendChild(autoLabel);
      autoDiv.appendChild(autoCheckbox);
      settingsDiv.appendChild(autoDiv);
      
      // إضافة إعدادات ساعة البدء
      const startDiv = document.createElement('div');
      startDiv.style.marginBottom = '15px';
      
      const startLabel = document.createElement('label');
      startLabel.textContent = 'ساعة بدء الوضع الليلي: ';
      startLabel.style.display = 'inline-block';
      startLabel.style.width = '70%';
      
      const startSelect = document.createElement('select');
      startSelect.id = 'night-mode-start';
      
      for (let i = 0; i < 24; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i + ':00';
        if (i === this.nightModeStartHour) {
          option.selected = true;
        }
        startSelect.appendChild(option);
      }
      
      startSelect.addEventListener('change', () => {
        this.setNightModeHours(parseInt(startSelect.value), this.nightModeEndHour);
      });
      
      startDiv.appendChild(startLabel);
      startDiv.appendChild(startSelect);
      settingsDiv.appendChild(startDiv);
      
      // إضافة إعدادات ساعة النهاية
      const endDiv = document.createElement('div');
      endDiv.style.marginBottom = '15px';
      
      const endLabel = document.createElement('label');
      endLabel.textContent = 'ساعة انتهاء الوضع الليلي: ';
      endLabel.style.display = 'inline-block';
      endLabel.style.width = '70%';
      
      const endSelect = document.createElement('select');
      endSelect.id = 'night-mode-end';
      
      for (let i = 0; i < 24; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = i + ':00';
        if (i === this.nightModeEndHour) {
          option.selected = true;
        }
        endSelect.appendChild(option);
      }
      
      endSelect.addEventListener('change', () => {
        this.setNightModeHours(this.nightModeStartHour, parseInt(endSelect.value));
      });
      
      endDiv.appendChild(endLabel);
      endDiv.appendChild(endSelect);
      settingsDiv.appendChild(endDiv);
      
      // إضافة أزرار الإغلاق والحفظ
      const buttonsDiv = document.createElement('div');
      buttonsDiv.style.display = 'flex';
      buttonsDiv.style.justifyContent = 'space-between';
      
      const closeButton = document.createElement('button');
      closeButton.textContent = 'إغلاق';
      closeButton.style.padding = '8px 15px';
      closeButton.style.backgroundColor = '#f44336';
      closeButton.style.color = 'white';
      closeButton.style.border = 'none';
      closeButton.style.borderRadius = '3px';
      closeButton.style.cursor = 'pointer';
      closeButton.addEventListener('click', () => {
        document.getElementById('night-mode-settings').style.display = 'none';
      });
      
      const saveButton = document.createElement('button');
      saveButton.textContent = 'حفظ';
      saveButton.style.padding = '8px 15px';
      saveButton.style.backgroundColor = '#4CAF50';
      saveButton.style.color = 'white';
      saveButton.style.border = 'none';
      saveButton.style.borderRadius = '3px';
      saveButton.style.cursor = 'pointer';
      saveButton.addEventListener('click', () => {
        this.saveSettings();
        document.getElementById('night-mode-settings').style.display = 'none';
      });
      
      buttonsDiv.appendChild(closeButton);
      buttonsDiv.appendChild(saveButton);
      settingsDiv.appendChild(buttonsDiv);
      
      // إضافة عنصر الإعدادات إلى الصفحة
      document.body.appendChild(settingsDiv);
    }
  }
  
  // فتح واجهة إعدادات الوضع الليلي
  openSettings() {
    this.createSettingsUI();
    
    // تحديث حالة عناصر الواجهة
    const autoCheckbox = document.getElementById('auto-night-mode');
    const startSelect = document.getElementById('night-mode-start');
    const endSelect = document.getElementById('night-mode-end');
    
    if (autoCheckbox) autoCheckbox.checked = this.autoNightMode;
    if (startSelect) startSelect.value = this.nightModeStartHour;
    if (endSelect) endSelect.value = this.nightModeEndHour;
    
    // عرض واجهة الإعدادات
    document.getElementById('night-mode-settings').style.display = 'block';
  }
}

// تهيئة مدير الوضع الليلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('Initializing night mode manager');
  window.nightModeManager = new NightModeManager();
  
  // إضافة حدث النقر على زر الوضع الليلي
  const nightModeToggle = document.getElementById('night-mode-toggle');
  if (nightModeToggle) {
    nightModeToggle.addEventListener('click', (event) => {
      if (event.ctrlKey || event.metaKey) {
        // إذا تم الضغط على زر Ctrl/Command + النقر، افتح واجهة الإعدادات
        window.nightModeManager.openSettings();
      } else {
        // وإلا، قم بتبديل الوضع الليلي
        window.nightModeManager.toggleNightMode();
      }
    });
    
    // تحديث أيقونة الزر بناءً على الحالة الحالية
    if (window.nightModeManager.isNightMode) {
      nightModeToggle.innerHTML = '&#9788;'; // رمز الشمس
      nightModeToggle.title = 'تبديل إلى الوضع العادي';
    } else {
      nightModeToggle.innerHTML = '&#127769;'; // رمز القمر
      nightModeToggle.title = 'تبديل إلى وضع القيادة الليلية';
    }
  }
  
  // التحقق من الوقت كل ساعة إذا كان التبديل التلقائي مفعلاً
  if (window.nightModeManager.autoNightMode) {
    setInterval(() => {
      window.nightModeManager.checkTimeAndSetMode();
    }, 60 * 60 * 1000); // كل ساعة
  }
});

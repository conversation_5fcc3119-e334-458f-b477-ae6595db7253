# 🇾🇪 Yemen Maps Complete - نظام خرائط اليمن الشامل

## 📋 نظرة عامة

نظام خرائط شامل لليمن يجمع بين خرائط MapTiler عالية الجودة وبيانات Google Places API مع إمكانية العمل بدون إنترنت وملكية كاملة للبيانات.

## ✨ المميزات الرئيسية

- 🗺️ **خرائط عالية الجودة**: MapTiler مع 3 أنماط (شوارع، قمر صناعي، تضاريس)
- 📍 **تغطية شاملة**: 85%+ من اليمن مع 20,000+ مكان
- 🖼️ **صور محلية**: 50,000+ صورة محفوظة محلياً
- 🔒 **عمل بدون إنترنت**: 100% محلي بعد الإعداد
- 💰 **ملكية دائمة**: لا رسوم شهرية
- 🛠️ **قابل للتخصيص**: كود مفتوح المصدر

## 🚀 البدء السريع

```bash
# تشغيل الإعداد التلقائي
quick_start.bat

# أو تشغيل الخادم مباشرة
cd yemen-maps-complete
python server\app.py
```

الخادم سيعمل على: `http://localhost:5000`

## 🏗️ هيكل المشروع

```
yemen-maps-complete\
├── 📁 data/                    # بيانات الخرائط
│   ├── maptiler/              # ملفات MapTiler
│   ├── tiles/                 # بلاطات الخرائط
│   └── backups/               # نسخ احتياطية
├── 📁 database/               # قاعدة البيانات
│   ├── migrations/            # تحديثات قاعدة البيانات
│   └── seeds/                 # بيانات أولية
├── 📁 images/                 # صور الأماكن
│   ├── places/                # صور جديدة
│   ├── imported/              # صور مستوردة
│   └── temp/                  # صور مؤقتة
├── 📁 server/                 # خادم التطبيق
│   ├── api/                   # APIs
│   ├── tiles/                 # خادم البلاطات
│   └── admin/                 # واجهة الإدارة
├── 📁 tools/                  # أدوات مساعدة
│   ├── import/                # استيراد البيانات
│   ├── download/              # تحميل من Google
│   └── backup/                # نسخ احتياطي
├── 📁 templates/              # قوالب HTML
└── 📁 public/                 # ملفات الويب
```

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية

- **Windows Server 2019** ✅
- **Python 3.8+** 
- **PostgreSQL 12+** ✅
- **Node.js 16+** (اختياري)

### الإعداد السريع

1. **تشغيل الإعداد التلقائي:**
   ```bash
   quick_start.bat
   ```

2. **أو الإعداد اليدوي:**
   ```bash
   # إنشاء هيكل المشروع
   setup_project.bat
   
   # إنشاء البيئة الافتراضية
   python -m venv venv
   venv\Scripts\activate
   
   # تثبيت المكتبات
   pip install -r requirements.txt
   
   # إعداد قاعدة البيانات
   psql -U postgres -f database\create_database.sql
   
   # استيراد البيانات الموجودة
   python tools\import\import_existing_data.py
   ```

## 🌐 الواجهات والـ APIs

### الصفحات الرئيسية

- **الصفحة الرئيسية**: `http://localhost:5000`
- **لوحة الإدارة**: `http://localhost:5000/admin`

### APIs المتاحة

#### 📍 الأماكن
```http
GET /api/places
GET /api/place/{place_id}
```

#### 🏛️ المحافظات
```http
GET /api/governorates
```

#### 📊 الإحصائيات
```http
GET /api/stats
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية

- `places` - الأماكن
- `place_photos` - صور الأماكن  
- `locations` - المحافظات والمدن
- `place_categories` - فئات الأماكن

## 🛠️ الأدوات المساعدة

### استيراد البيانات
```bash
python tools\import\import_existing_data.py
```

### تحميل من Google API
```bash
python tools\download\google_downloader.py --governorate="عدن"
```

### النسخ الاحتياطي
```bash
python tools\backup\backup_data.py
```

## 📞 الدعم الفني

### ملفات السجلات
- **خادم التطبيق**: `logs/app.log`
- **قاعدة البيانات**: `logs/db.log`
- **أخطاء التحميل**: `logs/download.log`

---

**🗺️ مرحباً بك في نظام خرائط اليمن الشامل!**

*آخر تحديث: ديسمبر 2024*

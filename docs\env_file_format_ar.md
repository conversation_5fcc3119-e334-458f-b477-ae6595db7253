# شكل ملف الإعدادات البيئية (.env)

## شكل الملف كما يظهر في المحرر

من الصورة التي أرسلتها، يمكننا رؤية الشكل الدقيق لملف `.env` كما يظهر في محرر النصوص:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yemen_nav
DB_USER=yemen
DB_PASSWORD=admin
PORT=3000
NODE_ENV=development
JWT_SECRET=your_jwt_secret_key
STORAGE_PATH=C:/yemen-nav/storage
```

## خصائص الملف

1. **اسم الملف**: `.env` (مع النقطة في البداية)
2. **نوع الملف**: ملف نصي بسيط (plain text)
3. **الترميز**: UTF-8
4. **تنسيق السطور**: كل متغير في سطر منفصل
5. **بنية المتغير**: `اسم_المتغير=القيمة` (بدون مسافات حول علامة =)

## شرح المتغيرات كما تظهر في الملف

1. **DB_HOST=localhost**
   - هذا هو عنوان خادم قاعدة البيانات
   - `localhost` تعني أن قاعدة البيانات موجودة على نفس الجهاز

2. **DB_PORT=5432**
   - هذا هو رقم المنفذ الذي تعمل عليه قاعدة البيانات
   - `5432` هو المنفذ الافتراضي لـ PostgreSQL

3. **DB_NAME=yemen_nav**
   - هذا هو اسم قاعدة البيانات
   - `yemen_nav` هو الاسم الذي اخترناه لقاعدة البيانات

4. **DB_USER=yemen**
   - هذا هو اسم المستخدم للاتصال بقاعدة البيانات
   - `yemen` هو المستخدم الذي تم إنشاؤه أثناء عملية التثبيت

5. **DB_PASSWORD=admin**
   - هذه هي كلمة المرور للاتصال بقاعدة البيانات
   - `admin` هي كلمة المرور التي تم تعيينها للمستخدم

6. **PORT=3000**
   - هذا هو رقم المنفذ الذي سيعمل عليه خادم الويب
   - `3000` هو المنفذ الافتراضي لتطبيقات Node.js

7. **NODE_ENV=development**
   - هذا هو وضع التشغيل للتطبيق
   - `development` تعني أن التطبيق في وضع التطوير (وليس الإنتاج)

8. **JWT_SECRET=your_jwt_secret_key**
   - هذا هو المفتاح السري المستخدم لتشفير وتوقيع رموز المصادقة (JWT)
   - `your_jwt_secret_key` يجب استبدالها بمفتاح عشوائي وآمن

9. **STORAGE_PATH=C:/yemen-nav/storage**
   - هذا هو المسار الذي سيتم تخزين الملفات فيه
   - `C:/yemen-nav/storage` هو المجلد الذي تم إنشاؤه أثناء عملية التثبيت

## ملاحظات مهمة حول شكل الملف

1. **لا توجد علامات تنصيص**: القيم لا تحتاج إلى علامات تنصيص، حتى لو كانت نصية
2. **لا توجد فواصل منقوطة**: لا تضع فواصل منقوطة (;) في نهاية السطور
3. **لا توجد تعليقات**: في الملف الذي تظهره الصورة، لا توجد تعليقات، ولكن يمكن إضافة تعليقات باستخدام علامة # في بداية السطر
4. **لا توجد مسافات**: لا توجد مسافات حول علامة = بين اسم المتغير وقيمته
5. **المسارات**: في المسارات، يتم استخدام الشرطة المائلة للأمام (/) بدلاً من الشرطة المائلة للخلف (\) حتى في نظام ويندوز

## كيفية تعديل الملف

إذا كنت ترغب في تعديل أي من هذه القيم:

1. افتح الملف في محرر نصوص (مثل Notepad أو VS Code)
2. قم بتغيير القيمة التي تريدها
3. احفظ الملف
4. أعد تشغيل الخادم لتطبيق التغييرات

## مثال على تعديل الملف

إذا كنت ترغب في تغيير المنفذ الذي يعمل عليه الخادم من 3000 إلى 8080:

1. افتح الملف `.env`
2. ابحث عن السطر `PORT=3000`
3. غيره إلى `PORT=8080`
4. احفظ الملف
5. أعد تشغيل الخادم

## تنبيه أمني

تذكر أن ملف `.env` يحتوي على معلومات حساسة مثل كلمات المرور ومفاتيح التشفير. لذلك:

1. لا تقم بمشاركة هذا الملف مع أي شخص
2. لا تقم برفعه إلى مستودعات الكود العامة (مثل GitHub)
3. تأكد من استخدام كلمات مرور قوية ومفاتيح تشفير عشوائية

// Test script to check database connection and retrieve users
const db = require('./backend/src/postgres-db');
const usersService = require('./backend/src/postgres-users');

async function testConnection() {
    try {
        console.log('Testing database connection...');
        const result = await db.query('SELECT version()');
        console.log('Database connection successful:', result.rows[0].version);
        return true;
    } catch (err) {
        console.error('Database connection failed:', err.message);
        return false;
    }
}

async function testGetUsers() {
    try {
        console.log('Testing getAllUsers function...');
        const users = await usersService.getAllUsers();
        console.log(`Successfully retrieved ${users.length} users`);
        console.log('First user:', users[0]);
        return users;
    } catch (err) {
        console.error('Failed to get users:', err.message);
        return [];
    }
}

// Run tests
async function runTests() {
    const connected = await testConnection();
    if (connected) {
        await testGetUsers();
    }
    process.exit(0);
}

runTests();

/**
 * واجهة برمجة تطبيقات بسيطة للوحة التحكم
 * تتيح الوصول إلى بيانات قاعدة البيانات بدون تعقيدات المصادقة
 */

const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// إعداد اتصال قاعدة البيانات PostgreSQL
const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'yemen123',
    port: 5432,
});

// طريقة مساعدة للتعامل مع الاستعلامات
const query = async (text, params) => {
    try {
        const result = await pool.query(text, params);
        return result.rows;
    } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error);
        throw error;
    }
};

// ===== نقاط النهاية API =====

// الحصول على جميع المستخدمين
router.get('/admin/users', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع المستخدمين...');
        const users = await query(`
            SELECT 
                u.id, 
                u.username, 
                u.full_name, 
                u.email, 
                u.phone, 
                u.role_id, 
                r.role_name, 
                u.is_active, 
                u.registration_date
            FROM 
                users u
            LEFT JOIN 
                roles r ON u.role_id = r.id
            ORDER BY 
                u.id
        `);
        console.log(`تم العثور على ${users.length} مستخدم`);
        res.json(users);
    } catch (error) {
        console.error('خطأ في الحصول على المستخدمين:', error);
        res.status(500).json({ error: 'خطأ في الحصول على المستخدمين' });
    }
});

// حذف مستخدم
router.delete('/admin/users/:id', async (req, res) => {
    const userId = req.params.id;
    try {
        console.log(`طلب حذف المستخدم رقم ${userId}...`);
        await query('DELETE FROM users WHERE id = $1', [userId]);
        console.log(`تم حذف المستخدم رقم ${userId} بنجاح`);
        res.json({ success: true, message: 'تم حذف المستخدم بنجاح' });
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        res.status(500).json({ error: 'خطأ في حذف المستخدم' });
    }
});

// الحصول على جميع المواقع
router.get('/admin/locations', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع المواقع...');
        const locations = await query(`
            SELECT 
                l.id, 
                l.name, 
                l.description, 
                l.lat, 
                l.lng, 
                c.name as category_name, 
                l.status
            FROM 
                locations l
            LEFT JOIN 
                categories c ON l.category_id = c.id
            ORDER BY 
                l.id
        `);
        console.log(`تم العثور على ${locations.length} موقع`);
        res.json(locations);
    } catch (error) {
        console.error('خطأ في الحصول على المواقع:', error);
        res.status(500).json({ error: 'خطأ في الحصول على المواقع' });
    }
});

// الحصول على جميع التصنيفات
router.get('/admin/categories', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع التصنيفات...');
        const categories = await query(`
            SELECT 
                id, 
                name, 
                icon, 
                color, 
                parent_id
            FROM 
                categories
            ORDER BY 
                id
        `);
        console.log(`تم العثور على ${categories.length} تصنيف`);
        res.json(categories);
    } catch (error) {
        console.error('خطأ في الحصول على التصنيفات:', error);
        res.status(500).json({ error: 'خطأ في الحصول على التصنيفات' });
    }
});

// الحصول على جميع العملاء
router.get('/admin/clients', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع العملاء...');
        const clients = await query(`
            SELECT 
                id, 
                name, 
                email, 
                phone, 
                address, 
                "deviceSN", 
                "licenseN", 
                status
            FROM 
                clients
            ORDER BY 
                id
        `);
        console.log(`تم العثور على ${clients.length} عميل`);
        res.json(clients);
    } catch (error) {
        console.error('خطأ في الحصول على العملاء:', error);
        res.status(500).json({ error: 'خطأ في الحصول على العملاء' });
    }
});

// تصدير الراوتر
module.exports = router;

package com.yemengps.app.ui;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.navigation.NavigationView;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.data.LocationRepository;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.model.LocationPoint;
import com.yemengps.app.model.LocationType;
import com.yemengps.app.services.LocationService;
import com.yemengps.app.ui.auth.LoginActivity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MainActivity extends AppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener, OnMapReadyCallback {

    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    private static final float DEFAULT_ZOOM = 15f;

    private DrawerLayout drawerLayout;
    private GoogleMap map;
    private FusedLocationProviderClient fusedLocationClient;
    private LocationRepository locationRepository;
    private PreferenceManager preferenceManager;
    private ChipGroup filterChipGroup;
    
    private Map<LocationType, Boolean> locationTypeFilters = new HashMap<>();
    private Map<String, Marker> markers = new HashMap<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // Initialize components
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        drawerLayout = findViewById(R.id.drawer_layout);
        NavigationView navigationView = findViewById(R.id.nav_view);
        
        // Setup drawer toggle
        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(
                this, drawerLayout, toolbar, R.string.app_name, R.string.app_name);
        drawerLayout.addDrawerListener(toggle);
        toggle.syncState();
        
        navigationView.setNavigationItemSelectedListener(this);
        
        // Initialize map
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager()
                .findFragmentById(R.id.map);
        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        }
        
        // Initialize location client
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        
        // Initialize repositories
        locationRepository = new LocationRepository(YemenGpsApplication.getInstance().getDatabase());
        preferenceManager = YemenGpsApplication.getInstance().getPreferenceManager();
        
        // Setup filter chips
        filterChipGroup = findViewById(R.id.filter_chip_group);
        setupFilterChips();
        
        // Setup floating action buttons
        FloatingActionButton fabMyLocation = findViewById(R.id.fab_my_location);
        fabMyLocation.setOnClickListener(view -> moveToCurrentLocation());
        
        FloatingActionButton fabAddLocation = findViewById(R.id.fab_add_location);
        fabAddLocation.setOnClickListener(view -> {
            Intent intent = new Intent(MainActivity.this, AddLocationActivity.class);
            startActivity(intent);
        });
        
        // Check if user is logged in
        if (!preferenceManager.isLoggedIn()) {
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish();
            return;
        }
        
        // Start location service
        startLocationService();
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        map = googleMap;
        
        // Set map settings
        map.setMapType(GoogleMap.MAP_TYPE_NORMAL);
        map.getUiSettings().setZoomControlsEnabled(true);
        map.getUiSettings().setCompassEnabled(true);
        map.getUiSettings().setMyLocationButtonEnabled(false);
        
        // Check location permission
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED) {
            map.setMyLocationEnabled(true);
            moveToCurrentLocation();
        } else {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
        }
        
        // Set marker click listener
        map.setOnMarkerClickListener(marker -> {
            LocationPoint location = (LocationPoint) marker.getTag();
            if (location != null) {
                Intent intent = new Intent(MainActivity.this, LocationDetailsActivity.class);
                intent.putExtra("location_id", location.getId());
                startActivity(intent);
                return true;
            }
            return false;
        });
        
        // Load locations
        loadLocations();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (map != null) {
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                            == PackageManager.PERMISSION_GRANTED) {
                        map.setMyLocationEnabled(true);
                        moveToCurrentLocation();
                    }
                }
            } else {
                Toast.makeText(this, R.string.error_location_permission, Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();
        
        if (id == R.id.nav_map) {
            // Already on map screen
        } else if (id == R.id.nav_saved_locations) {
            // Open saved locations
            Intent intent = new Intent(this, SavedLocationsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_my_contributions) {
            // Open my contributions
            Intent intent = new Intent(this, MyContributionsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_offline_maps) {
            // Open offline maps
            Intent intent = new Intent(this, OfflineMapsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_settings) {
            // Open settings
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_help) {
            // Open help
            Intent intent = new Intent(this, HelpActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_about) {
            // Open about
            Intent intent = new Intent(this, AboutActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_profile) {
            // Open profile
            Intent intent = new Intent(this, ProfileActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_logout) {
            // Logout
            preferenceManager.logout();
            Intent intent = new Intent(this, LoginActivity.class);
            startActivity(intent);
            finish();
        }
        
        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    /**
     * Move the map to the current location
     */
    private void moveToCurrentLocation() {
        if (map == null) return;
        
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
            return;
        }
        
        fusedLocationClient.getLastLocation().addOnSuccessListener(this, location -> {
            if (location != null) {
                LatLng currentLatLng = new LatLng(location.getLatitude(), location.getLongitude());
                map.animateCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, DEFAULT_ZOOM));
            }
        });
    }

    /**
     * Setup filter chips for location types
     */
    private void setupFilterChips() {
        // Initialize all filters to true (show all types)
        for (LocationType type : LocationType.values()) {
            locationTypeFilters.put(type, true);
        }
        
        // Set chip click listeners
        for (int i = 0; i < filterChipGroup.getChildCount(); i++) {
            View view = filterChipGroup.getChildAt(i);
            if (view instanceof Chip) {
                Chip chip = (Chip) view;
                chip.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    LocationType type = null;
                    int id = buttonView.getId();
                    
                    if (id == R.id.chip_traffic) {
                        type = LocationType.TRAFFIC;
                    } else if (id == R.id.chip_speedbump) {
                        type = LocationType.SPEEDBUMP;
                    } else if (id == R.id.chip_pothole) {
                        type = LocationType.POTHOLE;
                    } else if (id == R.id.chip_dirt_road) {
                        type = LocationType.DIRT_ROAD;
                    } else if (id == R.id.chip_checkpoint) {
                        type = LocationType.CHECKPOINT;
                    }
                    
                    if (type != null) {
                        locationTypeFilters.put(type, isChecked);
                        updateMarkerVisibility();
                    }
                });
            }
        }
    }

    /**
     * Load locations from the repository
     */
    private void loadLocations() {
        locationRepository.getAllLocations().observe(this, locations -> {
            // Clear existing markers
            for (Marker marker : markers.values()) {
                marker.remove();
            }
            markers.clear();
            
            // Add new markers
            for (LocationPoint location : locations) {
                addMarkerForLocation(location);
            }
            
            // Update visibility based on filters
            updateMarkerVisibility();
        });
    }

    /**
     * Add a marker for a location
     * @param location The location to add a marker for
     */
    private void addMarkerForLocation(LocationPoint location) {
        LatLng position = new LatLng(location.getLatitude(), location.getLongitude());
        
        // Choose marker color based on location type
        float markerColor;
        switch (location.getType()) {
            case TRAFFIC:
                markerColor = BitmapDescriptorFactory.HUE_RED;
                break;
            case SPEEDBUMP:
                markerColor = BitmapDescriptorFactory.HUE_YELLOW;
                break;
            case POTHOLE:
                markerColor = BitmapDescriptorFactory.HUE_ORANGE;
                break;
            case DIRT_ROAD:
                markerColor = BitmapDescriptorFactory.HUE_AZURE;
                break;
            case CHECKPOINT:
                markerColor = BitmapDescriptorFactory.HUE_BLUE;
                break;
            default:
                markerColor = BitmapDescriptorFactory.HUE_RED;
                break;
        }
        
        // Create marker
        MarkerOptions markerOptions = new MarkerOptions()
                .position(position)
                .title(location.getName())
                .icon(BitmapDescriptorFactory.defaultMarker(markerColor));
        
        Marker marker = map.addMarker(markerOptions);
        if (marker != null) {
            marker.setTag(location);
            markers.put(location.getId(), marker);
        }
    }

    /**
     * Update marker visibility based on filters
     */
    private void updateMarkerVisibility() {
        for (Marker marker : markers.values()) {
            LocationPoint location = (LocationPoint) marker.getTag();
            if (location != null) {
                boolean isVisible = locationTypeFilters.getOrDefault(location.getType(), true);
                marker.setVisible(isVisible);
            }
        }
    }

    /**
     * Start the location service
     */
    private void startLocationService() {
        Intent serviceIntent = new Intent(this, LocationService.class);
        ContextCompat.startForegroundService(this, serviceIntent);
    }
}

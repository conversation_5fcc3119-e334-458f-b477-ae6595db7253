/* تعريف الخط المخصص */
@font-face {
    font-family: 'YemenGPSFont';
    src: url('../fonts/Khalid-Art-bold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* تعريف نفس الخط للنص العريض */
@font-face {
    font-family: 'YemenGPSFont';
    src: url('../fonts/Khalid-Art-bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* تطبيق الخط على جميع عناصر الصفحة */
:root {
    --primary-font: 'YemenGPSFont', Arial, sans-serif;
}

body {
    font-family: var(--primary-font);
}

/* تطبيق الخط على العناصر المهمة */
.control-button,
.search-box input,
.search-box button,
.location-info h3,
.location-info p,
.status-bar,
.leaflet-popup-content,
.leaflet-control,
.offline-maps-panel {
    font-family: var(--primary-font);
}

/* تحسين مظهر النصوص العربية */
.arabic-text {
    font-family: var(--primary-font);
    letter-spacing: 0;
    word-spacing: 2px;
    line-height: 1.5;
}

/* ضبط أحجام الخط للعناصر المختلفة */
.location-info h3 {
    font-size: 1.4rem;
    margin-bottom: 8px;
}

.location-info p {
    font-size: 1.1rem;
    line-height: 1.4;
}

.search-box input {
    font-size: 1.1rem;
}

.search-box button {
    font-size: 1.1rem;
    font-weight: bold;
}

/* تحسين مظهر النصوص في الخريطة */
.leaflet-popup-content {
    font-size: 1.1rem;
    line-height: 1.5;
}

/* تحسين مظهر الأزرار */
button {
    font-weight: bold;
}

/* تطبيق الخط على عناصر الخريطة */
.leaflet-container {
    font-family: var(--primary-font) !important;
}

/* تطبيق الخط على طبقات الأسماء في الخريطة */
.leaflet-tile-pane {
    font-family: var(--primary-font) !important;
}

/* تطبيق الخط على النصوص في الخريطة */
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-pane > svg path,
.leaflet-tile-container,
.leaflet-tooltip,
.leaflet-control,
.leaflet-popup-content-wrapper,
.leaflet-popup-tip {
    font-family: var(--primary-font) !important;
}

/* تطبيق الخط على العلامات النصية */
.leaflet-div-icon {
    font-family: var(--primary-font) !important;
}

/* تطبيق الخط على العناصر SVG في الخريطة */
.leaflet-pane > svg text {
    font-family: var(--primary-font) !important;
    font-size: 12px !important;
    font-weight: bold !important;
}

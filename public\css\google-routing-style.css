/* public/css/google-routing-style.css */
/* أنماط لمحاكاة واجهة خرائط جوجل في عرض المسارات */

/* حاوية العلامات */
.google-marker-container {
  background: none;
  border: none;
}

/* نمط علامة البداية */
.google-start-marker {
  background-color: #ffffff;
  color: #3c7d5a;
  border: 2px solid #3c7d5a;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* نمط علامة النهاية */
.google-end-marker {
  background-color: #ffffff;
  color: #c34232;
  border: 2px solid #c34232;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* لوحة معلومات المسار الرئيسية */
.google-route-panel {
  position: absolute;
  top: 10px;
  right: 60px;
  z-index: 1000;
  background-color: white;
  width: 300px;
  max-height: 80vh;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  font-family: 'Tajawal', Arial, sans-serif;
}

/* شريط وسائل التنقل */
.google-travel-modes {
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px;
}

/* زر وسيلة التنقل */
.google-travel-mode-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.3s;
}

.google-travel-mode-btn:hover {
  background-color: #f5f5f5;
}

.google-travel-mode-btn.active {
  background-color: #e8f0fe;
  color: #1a73e8;
}

/* نظرة عامة على المسار */
.google-route-overview {
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.route-overview-header {
  display: flex;
  align-items: center;
}

.route-overview-icon {
  font-size: 24px;
  margin-left: 15px;
}

.route-overview-info {
  flex-grow: 1;
}

.route-duration {
  font-size: 1.2rem;
  font-weight: bold;
  color: #202124;
  margin-bottom: 4px;
}

.route-distance {
  font-size: 0.9rem;
  color: #5f6368;
}

/* المسارات البديلة */
.google-route-controls {
  padding: 15px;
}

.route-controls-header {
  font-size: 1rem;
  font-weight: bold;
  color: #202124;
  margin-bottom: 10px;
}

.route-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
}

.route-btn {
  flex: 1;
  background-color: #ffffff;
  border: 1px solid #dadce0;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: background-color 0.3s;
}

.route-btn:hover {
  background-color: #f5f5f5;
}

.route-btn.active {
  background-color: #e8f0fe;
  border-color: #4285f4;
}

.route-btn-letter {
  font-weight: bold;
  margin-bottom: 5px;
}

.route-btn-time {
  font-size: 0.8rem;
  color: #5f6368;
}

/* قائمة خطوات المسار */
.route-steps {
  list-style: none;
  padding: 0;
  margin: 0;
}

.route-step {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f3f4;
}

.route-step:last-child {
  border-bottom: none;
}

.step-icon {
  margin-left: 15px;
  font-size: 18px;
  flex-shrink: 0;
}

.step-text {
  flex-grow: 1;
  font-size: 0.9rem;
}

.step-distance {
  color: #5f6368;
  font-size: 0.8rem;
  white-space: nowrap;
}

.final-step {
  font-weight: bold;
}

/* زر اغلاق لوحة المسار */
.google-route-close {
  position: absolute;
  top: 10px;
  left: 10px;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #5f6368;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 600px) {
  .google-route-panel {
    width: 90%;
    max-width: 300px;
    right: 50%;
    transform: translateX(50%);
  }
}

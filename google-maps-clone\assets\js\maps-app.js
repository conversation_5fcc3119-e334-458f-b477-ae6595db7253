// نظام الخرائط المحاكي لـ Google Maps
class YemenMapsApp {
    constructor() {
        this.map = null;
        this.view = null;
        this.layers = {};
        this.currentLayer = 'street';
        this.markers = [];
        this.currentLocation = null;
        this.isNavigating = false;
        this.isMuted = false;
        this.places = [];
        
        // إعدادات افتراضية (صنعاء، اليمن)
        this.defaultCenter = [44.2066, 15.3547];
        this.defaultZoom = 12;
        
        this.init();
    }

    // تهيئة التطبيق
    async init() {
        this.createView();
        this.createLayers();
        this.createMap();
        this.setupEventListeners();
        await this.loadPlaces();
        this.addPlacesToMap();
    }

    // إنشاء العرض
    createView() {
        this.view = new ol.View({
            center: ol.proj.fromLonLat(this.defaultCenter),
            zoom: this.defaultZoom,
            minZoom: 3,
            maxZoom: 20
        });
    }

    // إنشاء طبقات الخريطة
    createLayers() {
        // طبقة خرائط الشوارع (OpenStreetMap)
        this.layers.street = new ol.layer.Tile({
            source: new ol.source.OSM({
                url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
            }),
            visible: true
        });

        // طبقة صور الأقمار الصناعية
        this.layers.satellite = new ol.layer.Tile({
            source: new ol.source.XYZ({
                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                attributions: '© Esri'
            }),
            visible: false
        });

        // طبقة خرائط التضاريس
        this.layers.terrain = new ol.layer.Tile({
            source: new ol.source.XYZ({
                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}',
                attributions: '© Esri'
            }),
            visible: false
        });
    }

    // إنشاء الخريطة
    createMap() {
        this.map = new ol.Map({
            target: 'map',
            layers: [
                this.layers.street,
                this.layers.satellite,
                this.layers.terrain
            ],
            view: this.view,
            controls: ol.control.defaults({
                zoom: false,
                attribution: false,
                rotate: false
            })
        });

        // إضافة تحكم الإسناد
        this.map.addControl(new ol.control.Attribution({
            collapsible: true,
            collapsed: true
        }));
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // أزرار التكبير والتصغير
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());

        // زر الموقع الحالي
        document.getElementById('myLocationBtn').addEventListener('click', () => this.goToCurrentLocation());

        // زر طبقات الخريطة
        document.getElementById('layersBtn').addEventListener('click', () => this.toggleLayersPanel());
        document.getElementById('closeLayersPanel').addEventListener('click', () => this.hideLayersPanel());

        // زر ملء الشاشة
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // تغيير طبقات الخريطة
        document.querySelectorAll('input[name="mapLayer"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.switchLayer(e.target.value));
        });

        // البحث
        document.getElementById('searchBtn').addEventListener('click', () => this.performSearch());
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });

        // النقر على الخريطة
        this.map.on('click', (event) => this.handleMapClick(event));

        // أزرار الملاحة
        document.getElementById('muteBtn').addEventListener('click', () => this.toggleMute());
        document.getElementById('stopNavBtn').addEventListener('click', () => this.stopNavigation());

        // أزرار النافذة المنبثقة
        document.getElementById('routeBtn').addEventListener('click', () => this.startRoute());
        document.getElementById('saveBtn').addEventListener('click', () => this.savePlace());
        document.getElementById('shareBtn').addEventListener('click', () => this.sharePlace());
    }

    // تحميل الأماكن من قاعدة البيانات
    async loadPlaces() {
        try {
            const response = await fetch('/api/places');
            const data = await response.json();
            
            if (data.success) {
                this.places = data.data;
                console.log(`تم تحميل ${this.places.length} مكان`);
            }
        } catch (error) {
            console.error('خطأ في تحميل الأماكن:', error);
            // استخدام بيانات تجريبية في حالة الخطأ
            this.places = this.getSamplePlaces();
        }
    }

    // الحصول على بيانات تجريبية
    getSamplePlaces() {
        return [
            {
                id: 1,
                name_ar: 'مسجد الصالح',
                name_en: 'Al-Saleh Mosque',
                latitude: 15.3547,
                longitude: 44.2066,
                category_id: 2,
                description_ar: 'أكبر مسجد في اليمن',
                rating: 4.8,
                photos: ['https://via.placeholder.com/300x200?text=Al-Saleh+Mosque']
            },
            {
                id: 2,
                name_ar: 'سوق الملح',
                name_en: 'Salt Market',
                latitude: 15.3500,
                longitude: 44.2100,
                category_id: 8,
                description_ar: 'سوق تقليدي في صنعاء القديمة',
                rating: 4.5,
                photos: ['https://via.placeholder.com/300x200?text=Salt+Market']
            },
            {
                id: 3,
                name_ar: 'قلعة صيرة',
                name_en: 'Sira Fortress',
                latitude: 12.7855,
                longitude: 45.0187,
                category_id: 1,
                description_ar: 'قلعة تاريخية في عدن',
                rating: 4.3,
                photos: ['https://via.placeholder.com/300x200?text=Sira+Fortress']
            }
        ];
    }

    // إضافة الأماكن للخريطة
    addPlacesToMap() {
        const features = this.places.map(place => {
            const feature = new ol.Feature({
                geometry: new ol.geom.Point(ol.proj.fromLonLat([place.longitude, place.latitude])),
                place: place
            });

            // تحديد أيقونة حسب نوع المكان
            const iconUrl = this.getIconForCategory(place.category_id);
            
            feature.setStyle(new ol.style.Style({
                image: new ol.style.Icon({
                    anchor: [0.5, 1],
                    src: iconUrl,
                    scale: 0.8
                })
            }));

            return feature;
        });

        // إنشاء طبقة للعلامات
        const vectorSource = new ol.source.Vector({
            features: features
        });

        this.markersLayer = new ol.layer.Vector({
            source: vectorSource
        });

        this.map.addLayer(this.markersLayer);
    }

    // الحصول على أيقونة حسب الفئة
    getIconForCategory(categoryId) {
        const icons = {
            1: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',      // سياحي
            2: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',     // ديني
            8: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',    // تجاري
            default: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png'
        };
        
        return icons[categoryId] || icons.default;
    }

    // تكبير الخريطة
    zoomIn() {
        const currentZoom = this.view.getZoom();
        this.view.animate({
            zoom: currentZoom + 1,
            duration: 250
        });
    }

    // تصغير الخريطة
    zoomOut() {
        const currentZoom = this.view.getZoom();
        this.view.animate({
            zoom: currentZoom - 1,
            duration: 250
        });
    }

    // الانتقال للموقع الحالي
    goToCurrentLocation() {
        if (navigator.geolocation) {
            const btn = document.getElementById('myLocationBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const coords = [position.coords.longitude, position.coords.latitude];
                    this.currentLocation = coords;
                    
                    this.view.animate({
                        center: ol.proj.fromLonLat(coords),
                        zoom: 16,
                        duration: 1000
                    });

                    this.addCurrentLocationMarker(coords);
                    btn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                },
                (error) => {
                    btn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    alert('لا يمكن تحديد موقعك الحالي');
                }
            );
        }
    }

    // إضافة علامة الموقع الحالي
    addCurrentLocationMarker(coords) {
        if (this.currentLocationLayer) {
            this.map.removeLayer(this.currentLocationLayer);
        }

        const marker = new ol.Feature({
            geometry: new ol.geom.Point(ol.proj.fromLonLat(coords)),
            type: 'current-location'
        });

        marker.setStyle(new ol.style.Style({
            image: new ol.style.Circle({
                radius: 8,
                fill: new ol.style.Fill({ color: '#1a73e8' }),
                stroke: new ol.style.Stroke({ color: '#ffffff', width: 3 })
            })
        }));

        const vectorSource = new ol.source.Vector({ features: [marker] });
        this.currentLocationLayer = new ol.layer.Vector({ source: vectorSource });
        this.map.addLayer(this.currentLocationLayer);
    }

    // تبديل لوحة الطبقات
    toggleLayersPanel() {
        const panel = document.getElementById('layersPanel');
        panel.classList.toggle('hidden');
    }

    // إخفاء لوحة الطبقات
    hideLayersPanel() {
        document.getElementById('layersPanel').classList.add('hidden');
    }

    // تبديل ملء الشاشة
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-compress"></i>';
        } else {
            document.exitFullscreen();
            document.getElementById('fullscreenBtn').innerHTML = '<i class="fas fa-expand"></i>';
        }
    }

    // تبديل طبقة الخريطة
    switchLayer(layerType) {
        Object.values(this.layers).forEach(layer => layer.setVisible(false));
        
        if (this.layers[layerType]) {
            this.layers[layerType].setVisible(true);
            this.currentLayer = layerType;
        }
    }

    // البحث في الأماكن
    performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (!query) return;

        const results = this.places.filter(place => 
            place.name_ar.includes(query) || 
            place.name_en.toLowerCase().includes(query.toLowerCase()) ||
            place.description_ar.includes(query)
        );

        if (results.length > 0) {
            const place = results[0];
            this.view.animate({
                center: ol.proj.fromLonLat([place.longitude, place.latitude]),
                zoom: 16,
                duration: 1000
            });
            
            // إظهار معلومات المكان
            setTimeout(() => {
                this.showPlaceInfo(place, [400, 300]); // موقع تقريبي
            }, 1000);
        } else {
            alert('لم يتم العثور على نتائج');
        }
    }

    // معالجة النقر على الخريطة
    handleMapClick(event) {
        const features = this.map.getFeaturesAtPixel(event.pixel);
        
        if (features && features.length > 0) {
            const feature = features[0];
            const place = feature.get('place');
            
            if (place) {
                this.showPlaceInfo(place, event.pixel);
            }
        } else {
            this.hidePointPopup();
        }
    }

    // عرض معلومات المكان
    showPlaceInfo(place, pixel) {
        const popup = document.getElementById('pointPopup');
        
        document.getElementById('popupTitle').textContent = place.name_ar;
        document.getElementById('popupSubtitle').textContent = place.name_en || '';
        document.getElementById('popupDescription').textContent = place.description_ar || '';
        
        // عرض الصورة إذا كانت متوفرة
        const imageElement = document.getElementById('popupImage');
        if (place.photos && place.photos.length > 0) {
            const photos = typeof place.photos === 'string' ? JSON.parse(place.photos) : place.photos;
            imageElement.src = photos[0];
            imageElement.style.display = 'block';
        } else {
            imageElement.style.display = 'none';
        }
        
        // تحديد موقع النافذة
        popup.style.left = (pixel[0] + 10) + 'px';
        popup.style.top = (pixel[1] - 10) + 'px';
        popup.classList.remove('hidden');
        
        // حفظ المكان المحدد
        this.selectedPlace = place;
    }

    // إخفاء نافذة المعلومات
    hidePointPopup() {
        document.getElementById('pointPopup').classList.add('hidden');
    }

    // بدء المسار
    startRoute() {
        if (!this.selectedPlace) return;
        
        if (this.currentLocation) {
            this.calculateRoute(this.currentLocation, [this.selectedPlace.longitude, this.selectedPlace.latitude]);
        } else {
            alert('يرجى تحديد موقعك الحالي أولاً');
        }
    }

    // حساب المسار
    async calculateRoute(start, end) {
        // محاكاة حساب المسار
        const distance = this.calculateDistance(start, end);
        const duration = Math.round(distance * 2); // تقدير تقريبي
        
        // إظهار شريط الملاحة
        this.showNavigationBar();
        this.isNavigating = true;
        
        // تحديث معلومات الملاحة
        document.getElementById('remainingDistance').textContent = `${distance.toFixed(1)} كم متبقية`;
        document.getElementById('remainingTime').textContent = `${duration} دقيقة متبقية`;
        
        // بدء التنبيهات الصوتية
        this.startVoiceGuidance();
        
        this.hidePointPopup();
    }

    // حساب المسافة بين نقطتين
    calculateDistance(start, end) {
        const R = 6371; // نصف قطر الأرض بالكيلومتر
        const dLat = this.deg2rad(end[1] - start[1]);
        const dLon = this.deg2rad(end[0] - start[0]);
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(this.deg2rad(start[1])) * Math.cos(this.deg2rad(end[1])) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    // إظهار شريط الملاحة
    showNavigationBar() {
        document.getElementById('navigationBar').classList.remove('hidden');
    }

    // إخفاء شريط الملاحة
    hideNavigationBar() {
        document.getElementById('navigationBar').classList.add('hidden');
    }

    // بدء التوجيه الصوتي
    startVoiceGuidance() {
        if (!this.isMuted) {
            this.speak('تم بدء الملاحة');
            
            // محاكاة تعليمات الملاحة
            setTimeout(() => {
                if (this.isNavigating && !this.isMuted) {
                    this.speak('انعطف يميناً بعد 500 متر');
                }
            }, 3000);
        }
    }

    // تحويل النص إلى كلام
    speak(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.9;
            speechSynthesis.speak(utterance);
        }
    }

    // تبديل كتم الصوت
    toggleMute() {
        this.isMuted = !this.isMuted;
        const btn = document.getElementById('muteBtn');
        btn.innerHTML = this.isMuted ? '<i class="fas fa-volume-mute"></i>' : '<i class="fas fa-volume-up"></i>';
    }

    // إيقاف الملاحة
    stopNavigation() {
        this.isNavigating = false;
        this.hideNavigationBar();
        speechSynthesis.cancel();
    }

    // حفظ المكان
    savePlace() {
        if (this.selectedPlace) {
            // محاكاة حفظ المكان
            alert(`تم حفظ ${this.selectedPlace.name_ar} في المفضلة`);
        }
    }

    // مشاركة المكان
    sharePlace() {
        if (this.selectedPlace) {
            const url = `${window.location.origin}?place=${this.selectedPlace.id}`;
            
            if (navigator.share) {
                navigator.share({
                    title: this.selectedPlace.name_ar,
                    text: this.selectedPlace.description_ar,
                    url: url
                });
            } else {
                // نسخ الرابط للحافظة
                navigator.clipboard.writeText(url).then(() => {
                    alert('تم نسخ رابط المكان');
                });
            }
        }
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.yemenMaps = new YemenMapsApp();
});

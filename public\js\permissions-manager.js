// يمن ناف - ملف إدارة الصلاحيات

// متغيرات عامة
let userPermissions = [];

// تنفيذ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل صلاحيات المستخدم
    loadUserPermissions();
});

// دالة لتحميل صلاحيات المستخدم
function loadUserPermissions() {
    try {
        // وضع التطوير: إضافة صلاحيات افتراضية
        const isDevelopment = true; // تغيير إلى false في الإنتاج

        if (isDevelopment) {
            console.log('وضع التطوير: إضافة صلاحيات افتراضية');

            // إنشاء مستخدم افتراضي للتطوير
            const devUser = {
                id: 1,
                user_id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                full_name: 'مدير النظام',
                email: '<EMAIL>',
                role: 'admin',
                roleId: 1,
                role_id: 1,
                canAccessAdmin: true,
                can_access_admin: true,
                is_active: 1,
                permissions: [
                    'view_dashboard',
                    'manage_users',
                    'manage_clients',
                    'manage_locations',
                    'manage_settings',
                    'manage_categories'
                ]
            };

            // تخزين بيانات المستخدم في التخزين المحلي
            localStorage.setItem('currentUser', JSON.stringify(devUser));

            // تعيين الصلاحيات
            userPermissions = devUser.permissions;

            console.log('تم تحميل صلاحيات المستخدم:', userPermissions);

            // تحديث واجهة المستخدم بناءً على الصلاحيات
            updateUIBasedOnPermissions();

            return;
        }

        // التحقق من وجود بيانات المستخدم في التخزين المحلي
        const currentUser = JSON.parse(localStorage.getItem('currentUser'));

        if (!currentUser) {
            console.log('لم يتم العثور على بيانات المستخدم');
            return;
        }

        // تحميل الصلاحيات من بيانات المستخدم
        if (currentUser.permissions_json && Array.isArray(currentUser.permissions_json)) {
            userPermissions = currentUser.permissions_json.map(p => p.code);
        } else if (currentUser.permissions && Array.isArray(currentUser.permissions)) {
            userPermissions = currentUser.permissions;
        } else {
            userPermissions = [];
        }

        console.log('تم تحميل صلاحيات المستخدم:', userPermissions);

        // تحديث واجهة المستخدم بناءً على الصلاحيات
        updateUIBasedOnPermissions();
    } catch (error) {
        console.error('خطأ في تحميل صلاحيات المستخدم:', error);
    }
}

// دالة للتحقق من وجود صلاحية معينة
function hasPermission(permissionCode) {
    // التحقق من وجود بيانات المستخدم في التخزين المحلي
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));

    if (!currentUser) {
        return false;
    }

    // إذا كان المستخدم مدير، فلديه جميع الصلاحيات
    if (currentUser.role === 1 || currentUser.role_id === 1) {
        return true;
    }

    // التحقق من وجود الصلاحية في قائمة صلاحيات المستخدم
    return userPermissions.includes(permissionCode);
}

// دالة لتحديث واجهة المستخدم بناءً على الصلاحيات
function updateUIBasedOnPermissions() {
    // إخفاء/إظهار عناصر واجهة المستخدم بناءً على الصلاحيات

    // إدارة المستخدمين
    if (hasPermission('manage_users')) {
        showElement('.users-management');
    } else {
        hideElement('.users-management');
    }

    // إدارة العملاء
    if (hasPermission('manage_clients')) {
        showElement('.clients-management');
    } else {
        hideElement('.clients-management');
    }

    // إدارة المواقع
    if (hasPermission('manage_locations')) {
        showElement('.locations-management');
    } else {
        hideElement('.locations-management');
    }

    // إدارة الإعدادات
    if (hasPermission('manage_settings')) {
        showElement('.settings-management');
    } else {
        hideElement('.settings-management');
    }

    // إدارة التصنيفات
    if (hasPermission('manage_categories')) {
        showElement('.categories-management');
    } else {
        hideElement('.categories-management');
    }
}

// دالة لإظهار عنصر
function showElement(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = '';
    });
}

// دالة لإخفاء عنصر
function hideElement(selector) {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        element.style.display = 'none';
    });
}

// دالة للتحقق من إمكانية الوصول إلى صفحة معينة
function canAccessPage(pageName) {
    // التحقق من وجود بيانات المستخدم في التخزين المحلي
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));

    if (!currentUser) {
        return false;
    }

    // إذا كان المستخدم مدير، فيمكنه الوصول إلى جميع الصفحات
    if (currentUser.role === 1 || currentUser.role_id === 1) {
        return true;
    }

    // التحقق من إمكانية الوصول إلى الصفحة بناءً على اسمها
    switch (pageName) {
        case 'admin':
            return currentUser.canAccessAdmin || currentUser.can_access_admin;
        case 'users':
            return hasPermission('manage_users');
        case 'clients':
            return hasPermission('manage_clients');
        case 'locations':
            return hasPermission('manage_locations');
        case 'settings':
            return hasPermission('manage_settings');
        case 'categories':
            return hasPermission('manage_categories');
        default:
            return true; // السماح بالوصول إلى الصفحات الأخرى افتراضيًا
    }
}

// تصدير الدوال العامة
window.permissionsManager = {
    hasPermission,
    canAccessPage,
    loadUserPermissions,
    updateUIBasedOnPermissions
};

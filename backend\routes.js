// backend/src/routes.js
const axios = require('axios');
const db = require('./database');

// عنوان خادم OSRM
const OSRM_SERVER = process.env.OSRM_SERVER || 'https://router.project-osrm.org';

// دالة للحصول على مسار بين نقطتين
async function getRoute(startLat, startLng, endLat, endLng, options = {}) {
  try {
    const { profile = 'driving', alternatives = false, steps = true, annotations = true } = options;
    
    const url = `${OSRM_SERVER}/route/v1/${profile}/${startLng},${startLat};${endLng},${endLat}`;
    const params = {
      alternatives,
      steps,
      annotations,
      geometries: 'geojson',
      overview: 'full',
      continue_straight: true
    };
    
    const response = await axios.get(url, { params });
    
    if (response.data.code !== 'Ok') {
      throw new Error('فشل في الحصول على المسار');
    }
    
    // ترجمة التعليمات إلى العربية
    response.data.routes.forEach(route => {
      if (route.legs) {
        route.legs.forEach(leg => {
          if (leg.steps) {
            leg.steps.forEach(step => {
              step.maneuver.instruction_ar = translateInstruction(step.maneuver.type, step.maneuver.modifier);
            });
          }
        });
      }
    });
    
    return response.data;
  } catch (err) {
    console.error('خطأ في الحصول على المسار:', err.message);
    
    // استخدام مسار وهمي في حالة فشل الاتصال بالخادم
    return generateMockRoute(startLat, startLng, endLat, endLng);
  }
}

// دالة لترجمة تعليمات المسار إلى العربية
function translateInstruction(type, modifier) {
  const translations = {
    turn: {
      left: 'انعطف يسارًا',
      right: 'انعطف يمينًا',
      slight_left: 'انعطف قليلًا إلى اليسار',
      slight_right: 'انعطف قليلًا إلى اليمين',
      sharp_left: 'انعطف بحدة إلى اليسار',
      sharp_right: 'انعطف بحدة إلى اليمين',
      uturn: 'استدر للخلف'
    },
    new_name: 'استمر على',
    depart: 'ابدأ السير',
    arrive: 'لقد وصلت إلى وجهتك',
    merge: 'اندمج مع الطريق',
    'on ramp': 'اسلك المنحدر',
    'off ramp': 'اخرج من المنحدر',
    fork: {
      left: 'اسلك التفرع إلى اليسار',
      right: 'اسلك التفرع إلى اليمين',
      slight_left: 'اسلك التفرع قليلًا إلى اليسار',
      slight_right: 'اسلك التفرع قليلًا إلى اليمين'
    },
    end_of_road: {
      left: 'اتجه يسارًا في نهاية الطريق',
      right: 'اتجه يمينًا في نهاية الطريق'
    },
    continue: 'استمر',
    roundabout: 'ادخل الدوار',
    rotary: 'ادخل الدوار',
    roundabout_exit: 'اخرج من الدوار',
    rotary_exit: 'اخرج من الدوار',
    straight: 'استمر للأمام'
  };
  
  if (type === 'turn' || type === 'fork' || type === 'end_of_road') {
    return translations[type][modifier] || 'استمر';
  }
  
  return translations[type] || 'استمر';
}

// دالة لإنشاء مسار وهمي في حالة فشل الاتصال بالخادم
function generateMockRoute(startLat, startLng, endLat, endLng) {
  // حساب المسافة والمدة التقريبية
  const R = 6371; // نصف قطر الأرض بالكيلومتر
  const dLat = (endLat - startLat) * Math.PI / 180;
  const dLon = (endLng - startLng) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(startLat * Math.PI / 180) * Math.cos(endLat * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;
  const duration = distance / 40 * 3600; // بمعدل 40 كم/ساعة
  
  // إنشاء خط مستقيم بين النقطتين
  const coordinates = [
    [startLng, startLat],
    [endLng, endLat]
  ];
  
  return {
    code: 'Ok',
    routes: [
      {
        distance: distance * 1000, // بالمتر
        duration: duration, // بالثانية
        geometry: {
          coordinates: coordinates,
          type: 'LineString'
        },
        legs: [
          {
            steps: [
              {
                distance: distance * 1000,
                duration: duration,
                geometry: {
                  coordinates: coordinates,
                  type: 'LineString'
                },
                maneuver: {
                  location: [startLng, startLat],
                  type: 'depart',
                  instruction: 'ابدأ السير',
                  instruction_ar: 'ابدأ السير'
                },
                mode: 'driving',
                name: ''
              },
              {
                distance: 0,
                duration: 0,
                geometry: {
                  coordinates: [[endLng, endLat]],
                  type: 'Point'
                },
                maneuver: {
                  location: [endLng, endLat],
                  type: 'arrive',
                  instruction: 'لقد وصلت إلى وجهتك',
                  instruction_ar: 'لقد وصلت إلى وجهتك'
                },
                mode: 'driving',
                name: ''
              }
            ],
            distance: distance * 1000,
            duration: duration,
            summary: 'مسار مباشر'
          }
        ],
        weight_name: 'routability',
        weight: distance * 1000
      }
    ],
    waypoints: [
      {
        name: '',
        location: [startLng, startLat]
      },
      {
        name: '',
        location: [endLng, endLat]
      }
    ]
  };
}

// دالة لحفظ مسار
async function saveRoute(userId, routeData) {
  const client = await db.getClient();
  
  try {
    await client.query('BEGIN');
    
    const { name, startPoint, startName, endPoint, endName, distance, duration, routeGeometry, waypoints, routeOptions } = routeData;
    
    const result = await client.query(
      `INSERT INTO saved_routes (user_id, name, start_point, start_name, end_point, end_name, distance_km, duration_minutes, route_geometry, waypoints, route_options)
       VALUES ($1, $2, ST_SetSRID(ST_MakePoint($3, $4), 4326), $5, ST_SetSRID(ST_MakePoint($6, $7), 4326), $8, $9, $10, ST_GeomFromGeoJSON($11), $12, $13)
       RETURNING *`,
      [
        userId, name, 
        startPoint.lng, startPoint.lat, startName,
        endPoint.lng, endPoint.lat, endName,
        distance / 1000, // تحويل من متر إلى كيلومتر
        duration / 60, // تحويل من ثانية إلى دقيقة
        JSON.stringify(routeGeometry),
        JSON.stringify(waypoints),
        JSON.stringify(routeOptions)
      ]
    );
    
    await client.query('COMMIT');
    
    return result.rows[0];
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('خطأ في حفظ المسار:', err.message);
    throw err;
  } finally {
    client.release();
  }
}

// دالة للحصول على المسارات المحفوظة للمستخدم
async function getUserRoutes(userId) {
  try {
    const result = await db.query(
      `SELECT route_id, name, start_name, end_name, distance_km, duration_minutes,
       ST_AsGeoJSON(start_point) as start_point, ST_AsGeoJSON(end_point) as end_point,
       created_at, last_used, is_favorite
       FROM saved_routes
       WHERE user_id = $1
       ORDER BY last_used DESC NULLS LAST, created_at DESC`,
      [userId]
    );
    
    // تحويل النقاط الجغرافية إلى كائنات JSON
    return result.rows.map(route => ({
      ...route,
      start_point: JSON.parse(route.start_point),
      end_point: JSON.parse(route.end_point)
    }));
  } catch (err) {
    console.error('خطأ في الحصول على المسارات المحفوظة:', err.message);
    throw err;
  }
}

// دالة للحصول على تفاصيل مسار محفوظ
async function getRouteById(routeId, userId) {
  try {
    const result = await db.query(
      `SELECT *, ST_AsGeoJSON(start_point) as start_point, ST_AsGeoJSON(end_point) as end_point,
       ST_AsGeoJSON(route_geometry) as route_geometry
       FROM saved_routes
       WHERE route_id = $1 AND user_id = $2`,
      [routeId, userId]
    );
    
    if (result.rows.length === 0) {
      throw new Error('لم يتم العثور على المسار');
    }
    
    const route = result.rows[0];
    
    // تحويل البيانات الجغرافية إلى كائنات JSON
    route.start_point = JSON.parse(route.start_point);
    route.end_point = JSON.parse(route.end_point);
    route.route_geometry = JSON.parse(route.route_geometry);
    
    // تحديث وقت آخر استخدام
    await db.query(
      'UPDATE saved_routes SET last_used = CURRENT_TIMESTAMP WHERE route_id = $1',
      [routeId]
    );
    
    return route;
  } catch (err) {
    console.error('خطأ في الحصول على تفاصيل المسار:', err.message);
    throw err;
  }
}

module.exports = {
  getRoute,
  saveRoute,
  getUserRoutes,
  getRouteById
};
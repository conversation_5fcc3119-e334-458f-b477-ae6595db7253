// يمن ناف - ملف لإنشاء مستخدم مدير
const bcrypt = require('bcrypt');
const db = require('./postgres-db');

// دالة لإنشاء مستخدم مدير
async function createAdminUser() {
    try {
        console.log('بدء إنشاء مستخدم مدير...');
        
        // التحقق من اتصال قاعدة البيانات
        const connected = await db.checkConnection();
        if (!connected) {
            console.error('فشل الاتصال بقاعدة البيانات');
            return;
        }
        
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        
        // التحقق من وجود دور المدير
        const roleResult = await db.query('SELECT * FROM roles WHERE name = $1', ['admin']);
        if (roleResult.rows.length === 0) {
            console.log('إنشاء دور المدير...');
            await db.query(`
                INSERT INTO roles (name, description)
                VALUES ('admin', 'مدير النظام')
                ON CONFLICT (name) DO NOTHING
            `);
        }
        
        // الحصول على معرف دور المدير
        const adminRoleResult = await db.query('SELECT id FROM roles WHERE name = $1', ['admin']);
        if (adminRoleResult.rows.length === 0) {
            console.error('لم يتم العثور على دور المدير');
            return;
        }
        
        const adminRoleId = adminRoleResult.rows[0].id;
        console.log(`معرف دور المدير: ${adminRoleId}`);
        
        // تشفير كلمة المرور
        const password = 'yemen123';
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        
        // إنشاء المستخدم المدير
        console.log('إنشاء المستخدم المدير...');
        await db.query(`
            INSERT INTO users (username, email, password, full_name, role_id, is_active, is_verified)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (username) 
            DO UPDATE SET 
                email = $2,
                password = $3,
                full_name = $4,
                role_id = $5,
                is_active = $6,
                is_verified = $7
        `, [
            'admin',
            '<EMAIL>',
            hashedPassword,
            'مدير النظام',
            adminRoleId,
            true,
            true
        ]);
        
        // التحقق من وجود المستخدم المدير
        const userResult = await db.query('SELECT * FROM users WHERE username = $1', ['admin']);
        if (userResult.rows.length === 0) {
            console.error('فشل إنشاء المستخدم المدير');
            return;
        }
        
        console.log('تم إنشاء المستخدم المدير بنجاح');
        console.log('معلومات المستخدم:');
        console.log(`- اسم المستخدم: ${userResult.rows[0].username}`);
        console.log(`- البريد الإلكتروني: ${userResult.rows[0].email}`);
        console.log(`- الاسم الكامل: ${userResult.rows[0].full_name}`);
        console.log(`- معرف الدور: ${userResult.rows[0].role_id}`);
        console.log(`- نشط: ${userResult.rows[0].is_active}`);
        console.log(`- تم التحقق: ${userResult.rows[0].is_verified}`);
        
        console.log('تم إنشاء المستخدم المدير بنجاح');
    } catch (error) {
        console.error('حدث خطأ أثناء إنشاء المستخدم المدير:', error);
    }
}

// تنفيذ الدالة
createAdminUser()
    .then(() => {
        console.log('تم الانتهاء من العملية');
        process.exit(0);
    })
    .catch(error => {
        console.error('حدث خطأ غير متوقع:', error);
        process.exit(1);
    });

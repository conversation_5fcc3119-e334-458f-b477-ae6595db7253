/**
 * مدير المصادقة
 * يتعامل مع تسجيل الدخول وإدارة الجلسات وصلاحيات المستخدم
 */

const AuthManager = (function() {
    // المتغيرات الخاصة
    let currentUser = null;
    let authToken = null;
    
    // تهيئة مدير المصادقة
    function init() {
        // التحقق من وجود توكن مخزن
        authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
        
        if (authToken) {
            // التحقق من صلاحية التوكن
            validateToken(authToken);
        }
        
        // إعداد مستمعات الأحداث
        setupEventListeners();
        
        return {
            login,
            logout,
            isAuthenticated,
            getCurrentUser,
            getAuthToken
        };
    }
    
    // إعداد مستمعات الأحداث
    function setupEventListeners() {
        // التقاط نموذج تسجيل الدخول إذا كان موجوداً
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('remember-me')?.checked || false;
                
                login(username, password, rememberMe);
            });
        }
        
        // زر تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                logout();
            });
        }
    }
    
    // تسجيل الدخول
    function login(username, password, rememberMe = false) {
        // إرسال طلب تسجيل الدخول إلى الخادم
        fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل تسجيل الدخول');
            }
            return response.json();
        })
        .then(data => {
            // تخزين التوكن والمستخدم
            authToken = data.token;
            currentUser = data.user;
            
            // تخزين التوكن في التخزين المحلي أو تخزين الجلسة
            if (rememberMe) {
                localStorage.setItem('authToken', authToken);
            } else {
                sessionStorage.setItem('authToken', authToken);
            }
            
            // تحديث واجهة المستخدم
            updateUI();
            
            // توجيه المستخدم إلى الصفحة المناسبة
            if (data.user.role === 'admin') {
                window.location.href = '/admin.html';
            } else {
                window.location.href = '/index.html';
            }
        })
        .catch(error => {
            console.error('خطأ في تسجيل الدخول:', error);
            showLoginError('اسم المستخدم أو كلمة المرور غير صحيحة');
        });
    }
    
    // تسجيل الخروج
    function logout() {
        // مسح التوكن والمستخدم
        authToken = null;
        currentUser = null;
        
        // مسح التوكن من التخزين
        localStorage.removeItem('authToken');
        sessionStorage.removeItem('authToken');
        
        // تحديث واجهة المستخدم
        updateUI();
        
        // توجيه المستخدم إلى صفحة تسجيل الدخول
        window.location.href = '/admin-login.html';
    }
    
    // التحقق من صلاحية التوكن
    function validateToken(token) {
        fetch('/api/validate-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('توكن غير صالح');
            }
            return response.json();
        })
        .then(data => {
            // تخزين معلومات المستخدم
            currentUser = data.user;
            
            // تحديث واجهة المستخدم
            updateUI();
        })
        .catch(error => {
            console.error('خطأ في التحقق من التوكن:', error);
            
            // مسح التوكن والمستخدم
            authToken = null;
            currentUser = null;
            
            // مسح التوكن من التخزين
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('authToken');
            
            // تحديث واجهة المستخدم
            updateUI();
        });
    }
    
    // تحديث واجهة المستخدم
    function updateUI() {
        // إخفاء/إظهار عناصر واجهة المستخدم بناءً على حالة تسجيل الدخول
        const authenticatedElements = document.querySelectorAll('.authenticated-only');
        const unauthenticatedElements = document.querySelectorAll('.unauthenticated-only');
        
        if (isAuthenticated()) {
            // إظهار العناصر المخصصة للمستخدمين المسجلين
            authenticatedElements.forEach(element => {
                element.style.display = '';
            });
            
            // إخفاء العناصر المخصصة للمستخدمين غير المسجلين
            unauthenticatedElements.forEach(element => {
                element.style.display = 'none';
            });
            
            // عرض اسم المستخدم إذا كان هناك عنصر مخصص لذلك
            const userNameElement = document.getElementById('user-name');
            if (userNameElement && currentUser) {
                userNameElement.textContent = currentUser.name || currentUser.username;
            }
        } else {
            // إخفاء العناصر المخصصة للمستخدمين المسجلين
            authenticatedElements.forEach(element => {
                element.style.display = 'none';
            });
            
            // إظهار العناصر المخصصة للمستخدمين غير المسجلين
            unauthenticatedElements.forEach(element => {
                element.style.display = '';
            });
        }
    }
    
    // عرض خطأ تسجيل الدخول
    function showLoginError(message) {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }
    
    // التحقق مما إذا كان المستخدم مسجل الدخول
    function isAuthenticated() {
        return authToken !== null && currentUser !== null;
    }
    
    // الحصول على المستخدم الحالي
    function getCurrentUser() {
        return currentUser;
    }
    
    // الحصول على توكن المصادقة
    function getAuthToken() {
        return authToken;
    }
    
    // تصدير الواجهة العامة
    return {
        init
    };
})();

// تهيئة مدير المصادقة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.authManager = AuthManager.init();
});

/**
 * وحدة تفاصيل المواقع
 * تعرض معلومات مفصلة عن المواقع مع خيارات التنقل والمشاركة
 */

const LocationDetails = (function() {
    // المتغيرات الخاصة
    let map = null;
    let currentLocation = null;
    let locationInfoPanel = null;
    
    // تهيئة وحدة تفاصيل المواقع
    function init(mapInstance) {
        map = mapInstance;
        
        // إنشاء لوحة معلومات الموقع
        createLocationInfoPanel();
        
        return {
            showLocationDetails,
            hideLocationDetails,
            shareLocation,
            showDirections,
            saveLocation
        };
    }
    
    // إنشاء لوحة معلومات الموقع
    function createLocationInfoPanel() {
        // التحقق من وجود اللوحة
        if (document.getElementById('location-info-panel')) {
            locationInfoPanel = document.getElementById('location-info-panel');
            return;
        }
        
        // إنشاء لوحة معلومات الموقع
        locationInfoPanel = document.createElement('div');
        locationInfoPanel.id = 'location-info-panel';
        locationInfoPanel.className = 'location-info-panel';
        
        locationInfoPanel.innerHTML = `
            <div class="location-header">
                <h3 id="location-name">اسم الموقع</h3>
                <span id="location-category" class="location-category">التصنيف</span>
                <button id="close-location-info" class="close-btn">&times;</button>
            </div>
            
            <div class="location-image">
                <img id="location-image" src="" alt="">
                <div class="image-nav">
                    <button id="prev-image" class="nav-btn">&lt;</button>
                    <button id="next-image" class="nav-btn">&gt;</button>
                </div>
            </div>
            
            <div class="location-details">
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span id="location-address">العنوان</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-phone"></i>
                    <span id="location-phone">الهاتف</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-info-circle"></i>
                    <span id="location-description">الوصف</span>
                </div>
            </div>
            
            <div class="weather-info">
                <!-- سيتم ملء هذا القسم ديناميكياً -->
            </div>
            
            <div class="nearby-places">
                <!-- سيتم ملء هذا القسم ديناميكياً -->
            </div>
            
            <div class="location-actions">
                <button id="directions-btn" class="action-btn">
                    <i class="fas fa-directions"></i>
                    <span>الاتجاهات</span>
                </button>
                <button id="share-btn" class="action-btn">
                    <i class="fas fa-share-alt"></i>
                    <span>مشاركة</span>
                </button>
                <button id="save-btn" class="action-btn">
                    <i class="fas fa-bookmark"></i>
                    <span>حفظ</span>
                </button>
            </div>
        `;
        
        // إضافة اللوحة إلى المستند
        document.body.appendChild(locationInfoPanel);
        
        // إضافة مستمعات الأحداث
        setupEventListeners();
    }
    
    // إعداد مستمعات الأحداث
    function setupEventListeners() {
        // مستمع لزر الإغلاق
        document.getElementById('close-location-info').addEventListener('click', function() {
            hideLocationDetails();
        });
        
        // مستمع لزر الاتجاهات
        document.getElementById('directions-btn').addEventListener('click', function() {
            showDirections();
        });
        
        // مستمع لزر المشاركة
        document.getElementById('share-btn').addEventListener('click', function() {
            shareLocation();
        });
        
        // مستمع لزر الحفظ
        document.getElementById('save-btn').addEventListener('click', function() {
            saveLocation();
        });
        
        // مستمعات لأزرار التنقل بين الصور
        document.getElementById('prev-image').addEventListener('click', function() {
            showPreviousImage();
        });
        
        document.getElementById('next-image').addEventListener('click', function() {
            showNextImage();
        });
    }
    
    // عرض تفاصيل الموقع
    function showLocationDetails(location) {
        // التحقق من وجود بيانات الموقع
        if (!location) {
            console.log('لا توجد بيانات موقع لعرضها');
            return;
        }
        
        // التحقق من أن الموقع تم تحديده بواسطة المستخدم (نقرة على الخريطة أو بحث)
        // وليس تلقائياً عند تحميل الصفحة
        if (window.locationSelectedByUser !== true && !window.isSharedLocation) {
            console.log('تم تجاهل عرض معلومات الموقع لأنه لم يتم تحديده بواسطة المستخدم');
            return;
        }
        
        currentLocation = location;
        
        // تحديث محتوى لوحة المعلومات
        updateLocationInfoContent(location);
        
        // عرض لوحة المعلومات
        locationInfoPanel.style.display = 'block';
        
        // تحميل معلومات إضافية
        loadAdditionalInfo(location);
    }
    
    // تحديث محتوى لوحة معلومات الموقع
    function updateLocationInfoContent(location) {
        // تحديث العنوان والتصنيف
        document.getElementById('location-name').textContent = location.name || 'موقع غير معروف';
        document.getElementById('location-category').textContent = getCategoryName(location.category) || '';
        
        // تحديث الصورة
        const imageElement = document.getElementById('location-image');
        if (location.image) {
            imageElement.src = location.image;
            imageElement.alt = location.name;
            document.querySelector('.location-image').style.display = 'block';
        } else {
            document.querySelector('.location-image').style.display = 'none';
        }
        
        // تحديث التفاصيل
        document.getElementById('location-address').textContent = location.address || 'العنوان غير متوفر';
        document.getElementById('location-phone').textContent = location.phone || 'الهاتف غير متوفر';
        document.getElementById('location-description').textContent = location.description || 'لا يوجد وصف';
        
        // تحديث أزرار الإجراءات
        updateActionButtons(location);
    }
    
    // تحديث أزرار الإجراءات
    function updateActionButtons(location) {
        // التحقق من وجود الموقع في المفضلة
        const favorites = JSON.parse(localStorage.getItem('favoriteLocations') || '[]');
        
        const existingIndex = favorites.findIndex(loc => 
            loc.lat === location.lat && 
            loc.lng === location.lng
        );
        
        // تحديث زر الحفظ
        const saveButton = document.getElementById('save-btn');
        if (existingIndex !== -1) {
            saveButton.innerHTML = '<i class="fas fa-bookmark"></i><span>إزالة</span>';
            saveButton.classList.add('active');
        } else {
            saveButton.innerHTML = '<i class="fas fa-bookmark"></i><span>حفظ</span>';
            saveButton.classList.remove('active');
        }
    }
    
    // تحميل معلومات إضافية
    function loadAdditionalInfo(location) {
        // تحميل معلومات الطقس
        fetchWeatherInfo(location.lat, location.lng);
        
        // تحميل الأماكن القريبة
        fetchNearbyPlaces(location.lat, location.lng);
    }
    
    // الحصول على معلومات الطقس
    function fetchWeatherInfo(lat, lng) {
        fetch(`/api/weather?lat=${lat}&lng=${lng}`)
            .then(response => response.json())
            .then(data => {
                updateWeatherInfo(data);
            })
            .catch(error => {
                console.error('خطأ في تحميل معلومات الطقس:', error);
                document.querySelector('.weather-info').innerHTML = '<p>معلومات الطقس غير متوفرة</p>';
            });
    }
    
    // تحديث معلومات الطقس
    function updateWeatherInfo(weatherData) {
        const weatherContainer = document.querySelector('.weather-info');
        if (!weatherContainer) return;
        
        weatherContainer.innerHTML = `
            <div class="weather-icon">
                <img src="${weatherData.icon}" alt="${weatherData.description}">
            </div>
            <div class="weather-details">
                <div class="temperature">${weatherData.temperature}°C</div>
                <div class="description">${weatherData.description}</div>
            </div>
        `;
    }
    
    // الحصول على الأماكن القريبة
    function fetchNearbyPlaces(lat, lng) {
        fetch(`/api/nearby?lat=${lat}&lng=${lng}&radius=500`)
            .then(response => response.json())
            .then(data => {
                updateNearbyPlaces(data);
            })
            .catch(error => {
                console.error('خطأ في تحميل الأماكن القريبة:', error);
                document.querySelector('.nearby-places').innerHTML = '<p>الأماكن القريبة غير متوفرة</p>';
            });
    }
    
    // تحديث الأماكن القريبة
    function updateNearbyPlaces(places) {
        const nearbyContainer = document.querySelector('.nearby-places');
        if (!nearbyContainer) return;
        
        if (places.length === 0) {
            nearbyContainer.innerHTML = '<p>لا توجد أماكن قريبة</p>';
            return;
        }
        
        let placesHtml = '<h4>أماكن قريبة</h4>';
        
        places.slice(0, 5).forEach(place => {
            placesHtml += `
                <div class="nearby-place" data-id="${place.id}">
                    <div class="place-icon">
                        <i class="fas fa-${getCategoryIcon(place.category)}"></i>
                    </div>
                    <div class="place-details">
                        <div class="place-name">${place.name}</div>
                        <div class="place-distance">${place.distance}</div>
                    </div>
                </div>
            `;
        });
        
        nearbyContainer.innerHTML = placesHtml;
        
        // إضافة مستمعات الأحداث للأماكن القريبة
        document.querySelectorAll('.nearby-place').forEach(placeElement => {
            placeElement.addEventListener('click', function() {
                const placeId = this.getAttribute('data-id');
                const place = places.find(p => p.id === placeId);
                
                if (place) {
                    showLocationDetails(place);
                }
            });
        });
    }
    
    // الحصول على أيقونة التصنيف
    function getCategoryIcon(category) {
        const icons = {
            'restaurant': 'utensils',
            'hotel': 'bed',
            'hospital': 'hospital',
            'school': 'school',
            'mosque': 'mosque',
            'shop': 'shopping-cart',
            'cafe': 'coffee',
            'bank': 'university',
            'gas_station': 'gas-pump',
            'pharmacy': 'prescription-bottle-alt'
        };
        
        return icons[category] || 'map-marker-alt';
    }
    
    // الحصول على اسم التصنيف
    function getCategoryName(category) {
        const names = {
            'restaurant': 'مطعم',
            'hotel': 'فندق',
            'hospital': 'مستشفى',
            'school': 'مدرسة',
            'mosque': 'مسجد',
            'shop': 'متجر',
            'cafe': 'مقهى',
            'bank': 'بنك',
            'gas_station': 'محطة وقود',
            'pharmacy': 'صيدلية'
        };
        
        return names[category] || category;
    }
    
    // عرض الصورة السابقة
    function showPreviousImage() {
        // يمكن تنفيذ هذه الوظيفة عند وجود معرض صور للموقع
        console.log('عرض الصورة السابقة');
    }
    
    // عرض الصورة التالية
    function showNextImage() {
        // يمكن تنفيذ هذه الوظيفة عند وجود معرض صور للموقع
        console.log('عرض الصورة التالية');
    }
    
    // إخفاء تفاصيل الموقع
    function hideLocationDetails() {
        locationInfoPanel.style.display = 'none';
        currentLocation = null;
    }
    
    // عرض الاتجاهات إلى الموقع
    function showDirections() {
        if (!currentLocation) return;
        
        // إخفاء لوحة المعلومات
        hideLocationDetails();
        
        // الحصول على موقع المستخدم الحالي
        if (navigator.geolocation) {
            showLoadingIndicator('جاري تحديد موقعك الحالي...');
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    hideLoadingIndicator();
                    
                    const userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    
                    // حساب المسار
                    calculateRoute(userLocation, {
                        lat: currentLocation.lat,
                        lng: currentLocation.lng
                    });
                },
                function(error) {
                    hideLoadingIndicator();
                    showNotification('تعذر تحديد موقعك الحالي. يرجى تحديد نقطة البداية يدوياً.', 'error');
                    
                    // تنفيذ منطق بديل لتحديد نقطة البداية
                    promptForStartingPoint();
                }
            );
        } else {
            showNotification('متصفحك لا يدعم تحديد الموقع الجغرافي', 'error');
            
            // تنفيذ منطق بديل لتحديد نقطة البداية
            promptForStartingPoint();
        }
    }
    
    // طلب تحديد نقطة البداية
    function promptForStartingPoint() {
        showNotification('انقر على الخريطة لتحديد نقطة البداية', 'info');
        
        // تغيير مؤشر الفأرة
        document.getElementById('map').style.cursor = 'crosshair';
        
        // إضافة مستمع للنقر على الخريطة
        map.once('click', function(e) {
            // إعادة مؤشر الفأرة إلى الوضع الطبيعي
            document.getElementById('map').style.cursor = '';
            
            // حساب المسار
            calculateRoute({
                lat: e.latlng.lat,
                lng: e.latlng.lng
            }, {
                lat: currentLocation.lat,
                lng: currentLocation.lng
            });
        });
    }
    
    // حساب المسار
    function calculateRoute(start, end) {
        showLoadingIndicator('جاري حساب المسار...');
        
        // إنشاء نقاط المسار
        const waypoints = [
            L.latLng(start.lat, start.lng),
            L.latLng(end.lat, end.lng)
        ];
        
        // إزالة المسار السابق إن وجد
        if (window.routingControl) {
            map.removeControl(window.routingControl);
        }
        
        // إنشاء مسار جديد
        window.routingControl = L.Routing.control({
            waypoints: waypoints,
            routeWhileDragging: true,
            showAlternatives: true,
            fitSelectedRoutes: true,
            language: 'ar',
            lineOptions: {
                styles: [
                    { color: '#1a73e8', opacity: 0.8, weight: 6 },
                    { color: 'white', opacity: 0.3, weight: 2 }
                ]
            },
            createMarker: function(i, waypoint, n) {
                const marker = L.marker(waypoint.latLng, {
                    draggable: true,
                    icon: L.divIcon({
                        className: i === 0 ? 'start-marker' : 'end-marker',
                        html: i === 0 ? '<div class="marker-icon start-icon"></div>' : '<div class="marker-icon end-icon"></div>',
                        iconSize: [30, 30],
                        iconAnchor: [15, 30]
                    })
                });
                
                return marker;
            }
        }).addTo(map);
        
        // إضافة مستمع لحدث اكتمال حساب المسار
        window.routingControl.on('routesfound', function(e) {
            hideLoadingIndicator();
            
            // عرض معلومات المسار
            displayRouteInfo(e.routes[0]);
        });
        
        // إضافة مستمع لحدث فشل حساب المسار
        window.routingControl.on('routingerror', function(e) {
            hideLoadingIndicator();
            showNotification('تعذر حساب المسار. يرجى المحاولة مرة أخرى.', 'error');
        });
    }
    
    // عرض معلومات المسار
    function displayRouteInfo(route) {
        // التحقق من وجود لوحة معلومات المسار
        let routeInfoPanel = document.getElementById('route-info-panel');
        
        if (!routeInfoPanel) {
            // إنشاء لوحة معلومات المسار
            routeInfoPanel = document.createElement('div');
            routeInfoPanel.id = 'route-info-panel';
            routeInfoPanel.className = 'route-info-panel';
            
            routeInfoPanel.innerHTML = `
                <div class="route-header">
                    <h3>معلومات المسار</h3>
                    <button id="close-route-info" class="close-btn">&times;</button>
                </div>
                
                <div class="route-summary">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
                
                <div class="route-steps">
                    <!-- سيتم ملء هذا القسم ديناميكياً -->
                </div>
                
                <div class="route-actions">
                    <button id="clear-route" class="action-btn">
                        <i class="fas fa-times"></i>
                        <span>مسح المسار</span>
                    </button>
                    <button id="share-route" class="action-btn">
                        <i class="fas fa-share-alt"></i>
                        <span>مشاركة المسار</span>
                    </button>
                </div>
            `;
            
            // إضافة اللوحة إلى المستند
            document.body.appendChild(routeInfoPanel);
            
            // إضافة مستمعات الأحداث
            document.getElementById('close-route-info').addEventListener('click', function() {
                routeInfoPanel.style.display = 'none';
            });
            
            document.getElementById('clear-route').addEventListener('click', function() {
                clearRoute();
            });
            
            document.getElementById('share-route').addEventListener('click', function() {
                shareRoute();
            });
        }
        
        // تحديث معلومات المسار
        const summary = route.summary;
        const instructions = route.instructions;
        
        // تحديث ملخص المسار
        const routeSummaryElement = routeInfoPanel.querySelector('.route-summary');
        routeSummaryElement.innerHTML = `
            <div class="route-distance">
                <i class="fas fa-road"></i>
                <span>${formatDistance(summary.totalDistance)}</span>
            </div>
            <div class="route-duration">
                <i class="fas fa-clock"></i>
                <span>${formatDuration(summary.totalTime)}</span>
            </div>
        `;
        
        // تحديث خطوات المسار
        const routeStepsElement = routeInfoPanel.querySelector('.route-steps');
        let stepsHtml = '<h4>خطوات المسار:</h4><ol>';
        
        instructions.forEach(instruction => {
            stepsHtml += `<li>${instruction.text}</li>`;
        });
        
        stepsHtml += '</ol>';
        routeStepsElement.innerHTML = stepsHtml;
        
        // عرض لوحة معلومات المسار
        routeInfoPanel.style.display = 'block';
    }
    
    // تنسيق المسافة
    function formatDistance(distance) {
        if (distance < 1000) {
            return `${Math.round(distance)} متر`;
        } else {
            return `${(distance / 1000).toFixed(1)} كم`;
        }
    }
    
    // تنسيق المدة
    function formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours} ساعة ${minutes} دقيقة`;
        } else {
            return `${minutes} دقيقة`;
        }
    }
    
    // مسح المسار
    function clearRoute() {
        // إزالة المسار
        if (window.routingControl) {
            map.removeControl(window.routingControl);
            window.routingControl = null;
        }
        
        // إخفاء لوحة معلومات المسار
        const routeInfoPanel = document.getElementById('route-info-panel');
        if (routeInfoPanel) {
            routeInfoPanel.style.display = 'none';
        }
    }
    
    // مشاركة المسار
    function shareRoute() {
        if (!window.routingControl) return;
        
        const waypoints = window.routingControl.getWaypoints();
        if (waypoints.length < 2) return;
        
        const start = waypoints[0].latLng;
        const end = waypoints[1].latLng;
        
        // إنشاء رابط المشاركة
        const shareUrl = `${window.location.origin}${window.location.pathname}?route=true&startLat=${start.lat}&startLng=${start.lng}&endLat=${end.lat}&endLng=${end.lng}`;
        
        // مشاركة الرابط
        shareUrl(shareUrl, 'مسار على يمن GPS');
    }
    
    // مشاركة الموقع
    function shareLocation() {
        if (!currentLocation) return;
        
        // إنشاء رابط المشاركة
        const shareUrl = `${window.location.origin}${window.location.pathname}?lat=${currentLocation.lat}&lng=${currentLocation.lng}&name=${encodeURIComponent(currentLocation.name || 'موقع مشترك')}`;
        
        // مشاركة الرابط
        shareUrl(shareUrl, currentLocation.name || 'موقع مشترك');
    }
    
    // مشاركة رابط
    function shareUrl(url, title) {
        // التحقق من دعم واجهة مشاركة الويب
        if (navigator.share) {
            navigator.share({
                title: title,
                text: `شاهد هذا على يمن GPS: ${title}`,
                url: url
            })
            .catch(error => {
                console.error('خطأ في مشاركة الرابط:', error);
                fallbackShare(url);
            });
        } else {
            fallbackShare(url);
        }
    }
    
    // مشاركة احتياطية
    function fallbackShare(url) {
        // إنشاء حقل نص مؤقت
        const textarea = document.createElement('textarea');
        textarea.value = url;
        textarea.style.position = 'fixed';
        textarea.style.opacity = 0;
        
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            // نسخ الرابط إلى الحافظة
            const successful = document.execCommand('copy');
            
            if (successful) {
                showNotification('تم نسخ رابط المشاركة إلى الحافظة', 'success');
            } else {
                showNotification('تعذر نسخ الرابط', 'error');
            }
        } catch (err) {
            showNotification('تعذر نسخ الرابط', 'error');
        }
        
        document.body.removeChild(textarea);
    }
    
    // حفظ الموقع
    function saveLocation() {
        if (!currentLocation) return;
        
        // التحقق من وجود الموقع في المفضلة
        const favorites = JSON.parse(localStorage.getItem('favoriteLocations') || '[]');
        
        const existingIndex = favorites.findIndex(loc => 
            loc.lat === currentLocation.lat && 
            loc.lng === currentLocation.lng
        );
        
        if (existingIndex !== -1) {
            // إزالة الموقع من المفضلة
            favorites.splice(existingIndex, 1);
            localStorage.setItem('favoriteLocations', JSON.stringify(favorites));
            
            showNotification('تمت إزالة الموقع من المفضلة', 'info');
        } else {
            // إضافة الموقع إلى المفضلة
            favorites.push({
                id: Date.now(),
                name: currentLocation.name,
                lat: currentLocation.lat,
                lng: currentLocation.lng,
                address: currentLocation.address,
                category: currentLocation.category,
                savedAt: new Date().toISOString()
            });
            
            localStorage.setItem('favoriteLocations', JSON.stringify(favorites));
            
            showNotification('تمت إضافة الموقع إلى المفضلة', 'success');
        }
        
        // تحديث أزرار الإجراءات
        updateActionButtons(currentLocation);
    }
    
    // تصدير الواجهة العامة
    return {
        init,
        showLocationDetails,
        hideLocationDetails,
        showDirections,
        shareLocation,
        saveLocation
    };
})();

// Simple test script to check database connection
const { Pool } = require('pg');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Print connection info for debugging
console.log('Database connection info:');
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_PORT:', process.env.DB_PORT);

// Database connection settings
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'yemen123',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps'
};

console.log('Connecting to database with config:', dbConfig);

// Create database connection pool
const pool = new Pool(dbConfig);

// Test connection
async function testConnection() {
    let client;
    try {
        console.log('Attempting to connect to database...');
        client = await pool.connect();
        console.log('Successfully connected to database');

        // Test query
        const result = await client.query('SELECT version()');
        console.log('Database version:', result.rows[0].version);
        
        return true;
    } catch (error) {
        console.error('Error connecting to database:', error.message);
        return false;
    } finally {
        if (client) {
            client.release();
        }
        await pool.end();
    }
}

// Run test
testConnection().then(success => {
    console.log('Test completed, success:', success);
    process.exit(0);
}).catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
});

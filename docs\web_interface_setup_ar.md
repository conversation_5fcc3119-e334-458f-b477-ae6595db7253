# دليل تشغيل واجهة الويب لنظام "يمن ناف" (Yemen Nav)

## مقدمة

هذا الدليل يشرح كيفية إعداد وتشغيل واجهة الويب لنظام "يمن ناف" على الخادم وكيفية الوصول إليها من خلال المتصفح. واجهة الويب تتيح للمستخدمين عرض الخرائط، وإضافة نقاط وعلامات جديدة، ومزامنة البيانات مع تطبيق الهاتف المحمول.

## متطلبات النظام

### متطلبات الخادم

- نظام تشغيل: Linux (Ubuntu 20.04 أو أحدث مفضل) أو Windows Server
- وحدة المعالجة المركزية: 4 أنوية على الأقل
- الذاكرة: 8 جيجابايت على الأقل
- مساحة التخزين: 100 جيجابايت على الأقل (للخرائط والبيانات)
- اتصال إنترنت: 10 ميجابت/ثانية على الأقل

### البرامج المطلوبة

- Node.js (الإصدار 16.x أو أحدث)
- PostgreSQL (الإصدار 13 أو أحدث) مع امتداد PostGIS
- Nginx أو Apache كخادم ويب
- PM2 لإدارة عمليات Node.js
- Docker (اختياري، للنشر باستخدام الحاويات)

## خطوات الإعداد

### 1. إعداد قاعدة البيانات

#### تثبيت PostgreSQL وPostGIS

على نظام Ubuntu:

```bash
# تثبيت PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# تثبيت PostGIS
sudo apt install postgis postgresql-13-postgis-3

# بدء تشغيل الخدمة
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### إنشاء قاعدة البيانات

```bash
# الدخول إلى PostgreSQL
sudo -u postgres psql

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE yemen_nav;
CREATE USER yemen_nav_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE yemen_nav TO yemen_nav_user;

# الخروج من PostgreSQL
\q

# الدخول إلى قاعدة البيانات وتفعيل PostGIS
sudo -u postgres psql -d yemen_nav
CREATE EXTENSION postgis;
CREATE EXTENSION postgis_topology;
\q
```

### 2. إعداد الواجهة الخلفية (Backend)

#### تنزيل الكود المصدري

```bash
# تنزيل الكود من مستودع Git
git clone https://github.com/your-organization/yemen-nav-backend.git
cd yemen-nav-backend

# تثبيت الاعتماديات
npm install
```

#### تكوين ملف البيئة

قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع:

```
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yemen_nav
DB_USER=yemen_nav_user
DB_PASSWORD=your_secure_password

# إعدادات الخادم
PORT=3000
NODE_ENV=production
JWT_SECRET=your_jwt_secret_key

# إعدادات API
API_VERSION=v1
CORS_ORIGIN=https://yemen-nav.com

# إعدادات التخزين
STORAGE_PATH=/var/yemen-nav/storage
```

#### بناء وتشغيل الواجهة الخلفية

```bash
# بناء المشروع
npm run build

# تثبيت PM2 عالميًا
npm install -g pm2

# تشغيل الخادم باستخدام PM2
pm2 start dist/server.js --name yemen-nav-backend

# تكوين PM2 للبدء تلقائيًا عند إعادة تشغيل النظام
pm2 startup
pm2 save
```

### 3. إعداد الواجهة الأمامية (Frontend)

#### تنزيل الكود المصدري

```bash
# تنزيل الكود من مستودع Git
git clone https://github.com/your-organization/yemen-nav-frontend.git
cd yemen-nav-frontend

# تثبيت الاعتماديات
npm install
```

#### تكوين ملف البيئة

قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع:

```
REACT_APP_API_URL=https://api.yemen-nav.com/api/v1
REACT_APP_MAPBOX_TOKEN=your_mapbox_token
```

#### بناء الواجهة الأمامية

```bash
# بناء المشروع للإنتاج
npm run build
```

### 4. إعداد خادم الويب (Nginx)

#### تثبيت Nginx

```bash
sudo apt update
sudo apt install nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### تكوين Nginx

قم بإنشاء ملف تكوين جديد:

```bash
sudo nano /etc/nginx/sites-available/yemen-nav
```

أضف التكوين التالي:

```nginx
server {
    listen 80;
    server_name yemen-nav.com www.yemen-nav.com;

    # توجيه HTTP إلى HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name yemen-nav.com www.yemen-nav.com;

    # إعدادات SSL
    ssl_certificate /etc/letsencrypt/live/yemen-nav.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yemen-nav.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # الواجهة الأمامية
    root /var/www/yemen-nav/frontend;
    index index.html;

    # خدمة الملفات الثابتة
    location / {
        try_files $uri $uri/ /index.html;
    }

    # توجيه طلبات API إلى الواجهة الخلفية
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # تكوين Gzip
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

تفعيل التكوين:

```bash
sudo ln -s /etc/nginx/sites-available/yemen-nav /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. إعداد SSL (Let's Encrypt)

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d yemen-nav.com -d www.yemen-nav.com

# تكوين التجديد التلقائي
sudo systemctl status certbot.timer
```

### 6. نقل ملفات الواجهة الأمامية

```bash
# إنشاء مجلد للواجهة الأمامية
sudo mkdir -p /var/www/yemen-nav/frontend

# نسخ ملفات البناء
sudo cp -r yemen-nav-frontend/build/* /var/www/yemen-nav/frontend/

# ضبط الصلاحيات
sudo chown -R www-data:www-data /var/www/yemen-nav
```

## تشغيل النظام عبر المتصفح

بعد إكمال الإعداد، يمكن الوصول إلى واجهة الويب لنظام "يمن ناف" من خلال المتصفح باستخدام الخطوات التالية:

### 1. الوصول إلى الموقع

افتح متصفح الويب (Chrome، Firefox، Safari، إلخ) وانتقل إلى:

```
https://yemen-nav.com
```

أو في بيئة التطوير المحلية:

```
http://localhost:8080
```

### 2. تسجيل الدخول

- انقر على زر "تسجيل الدخول" في الزاوية العلوية اليمنى
- أدخل بريدك الإلكتروني وكلمة المرور
- إذا لم يكن لديك حساب، انقر على "إنشاء حساب جديد"

### 3. استخدام الخريطة

بعد تسجيل الدخول، ستظهر الخريطة التفاعلية مع:

- أزرار التحكم في الخريطة (تكبير، تصغير، تغيير النمط)
- قائمة جانبية للوصول إلى الميزات المختلفة
- طبقات يمكن تفعيلها أو إلغاؤها (مثل طبقة الازدحام، طبقة المطبات)

### 4. إضافة نقطة جديدة

لإضافة نقطة جديدة على الخريطة:

1. انقر على زر "إضافة نقطة" في شريط الأدوات
2. انقر على الموقع المطلوب على الخريطة
3. اختر نوع النقطة من القائمة المنسدلة (ازدحام، مطب، حفرة، إلخ)
4. أدخل وصفًا للنقطة
5. انقر على "حفظ"

### 5. إنشاء مسار

لإنشاء مسار بين نقطتين:

1. انقر على زر "إنشاء مسار" في شريط الأدوات
2. حدد نقطة البداية على الخريطة
3. حدد نقطة النهاية
4. اختر خيارات التوجيه (أقصر مسار، تجنب الطرق الترابية)
5. انقر على "حساب المسار"

### 6. مزامنة البيانات مع التطبيق

لمزامنة البيانات مع تطبيق الهاتف المحمول:

1. تأكد من تسجيل الدخول بنفس الحساب على كلا المنصتين
2. انقر على زر "المزامنة" في الإعدادات
3. اختر البيانات التي تريد مزامنتها (النقاط، المسارات، الإعدادات)
4. انقر على "بدء المزامنة"

## تشغيل النظام محليًا للتطوير

للمطورين الذين يرغبون في تشغيل النظام محليًا للتطوير، اتبع الخطوات التالية:

### 1. تشغيل الواجهة الخلفية

```bash
cd yemen-nav-backend
npm install
npm run dev
```

الواجهة الخلفية ستعمل على المنفذ 3000: http://localhost:3000

### 2. تشغيل الواجهة الأمامية

```bash
cd yemen-nav-frontend
npm install
npm start
```

الواجهة الأمامية ستعمل على المنفذ 8080: http://localhost:8080

### 3. تكوين قاعدة البيانات المحلية

```bash
# إنشاء قاعدة بيانات للتطوير
createdb -U postgres yemen_nav_dev

# تفعيل PostGIS
psql -U postgres -d yemen_nav_dev -c "CREATE EXTENSION postgis; CREATE EXTENSION postgis_topology;"

# تشغيل الهجرات
cd yemen-nav-backend
npm run migrate
npm run seed
```

## استكشاف الأخطاء وإصلاحها

### مشاكل الاتصال بالخادم

إذا لم تتمكن من الوصول إلى واجهة الويب:

1. تحقق من أن الخادم يعمل:
   ```bash
   sudo systemctl status nginx
   pm2 status
   ```

2. تحقق من سجلات الخطأ:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   pm2 logs yemen-nav-backend
   ```

3. تحقق من جدار الحماية:
   ```bash
   sudo ufw status
   ```

### مشاكل قاعدة البيانات

إذا واجهت مشاكل في قاعدة البيانات:

1. تحقق من حالة PostgreSQL:
   ```bash
   sudo systemctl status postgresql
   ```

2. تحقق من الاتصال بقاعدة البيانات:
   ```bash
   psql -U yemen_nav_user -h localhost -d yemen_nav
   ```

3. تحقق من سجلات PostgreSQL:
   ```bash
   sudo tail -f /var/log/postgresql/postgresql-13-main.log
   ```

### مشاكل الواجهة الأمامية

إذا واجهت مشاكل في الواجهة الأمامية:

1. افتح أدوات المطور في المتصفح (F12)
2. تحقق من وحدة التحكم للأخطاء
3. تحقق من علامة التبويب "Network" للتأكد من الاتصال بالواجهة الخلفية

## الخلاصة

باتباع هذا الدليل، يمكنك إعداد وتشغيل واجهة الويب لنظام "يمن ناف" على الخادم والوصول إليها من خلال المتصفح. يوفر النظام واجهة سهلة الاستخدام لعرض الخرائط، وإضافة نقاط وعلامات جديدة، ومزامنة البيانات مع تطبيق الهاتف المحمول.

للحصول على مساعدة إضافية، يرجى الاتصال بفريق الدعم الفني على <EMAIL> أو زيارة موقع الدعم الفني على https://support.yemen-nav.com.

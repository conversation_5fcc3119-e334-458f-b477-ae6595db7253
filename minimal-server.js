// Yemen Nav Backend Server - نسخة مبسطة جدًا
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();

// CORS
app.use(cors());

// JSON parsing
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Set up a logger middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// API Routes
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to Yemen Nav API' });
});

// مسار اختبار للمستخدمين
app.get('/api/test/users', (req, res) => {
  // بيانات مستخدمين وهمية للاختبار
  const users = [
    {
      user_id: 1,
      username: 'admin',
      email: '<EMAIL>',
      full_name: 'المسؤول الرئيسي',
      role_name: 'admin',
      registration_date: '2025-01-01',
      is_active: true
    },
    {
      user_id: 2,
      username: 'ahmed2025',
      email: '<EMAIL>',
      full_name: 'أحمد محمد',
      role_name: 'user',
      registration_date: '2025-01-15',
      is_active: true
    },
    {
      user_id: 3,
      username: 'developer',
      email: '<EMAIL>',
      full_name: 'مطور النظام',
      role_name: 'developer',
      registration_date: '2025-01-01',
      is_active: true
    }
  ];
  
  res.json(users);
});

// مسار اختبار للأدوار
app.get('/api/test/roles', (req, res) => {
  // بيانات أدوار وهمية للاختبار
  const roles = [
    { role_id: 1, role_name: 'admin', role_description: 'مدير النظام' },
    { role_id: 2, role_name: 'user', role_description: 'مستخدم عادي' },
    { role_id: 3, role_name: 'developer', role_description: 'مطور النظام' }
  ];
  
  res.json(roles);
});

// Serve the main HTML file for any other route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تشغيل الخادم
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
});

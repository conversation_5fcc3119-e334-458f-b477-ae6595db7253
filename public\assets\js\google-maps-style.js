class GoogleMapsStyle {
    constructor() {
        this.map = null;
        this.currentLayer = 'streets';
        this.markers = [];
        this.currentLocation = null;
        this.sidebar = document.getElementById('sidebar');
        this.loading = document.getElementById('loading');
        this.permissionRequestShown = false; // لمنع إظهار طلب الإذن مرات متعددة

        // Google Maps Style Layers
        this.layers = {
            streets: {
                name: 'خريطة',
                layer: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19
                })
            },
            satellite: {
                name: 'القمر الصناعي',
                layer: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '© E<PERSON>ri',
                    maxZoom: 19
                })
            },
            terrain: {
                name: 'التضاريس',
                layer: <PERSON><PERSON>tileLay<PERSON>('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenTopoMap contributors',
                    maxZoom: 17
                })
            }
        };

        this.init();
    }

    init() {
        this.initMap();
        this.bindEvents();
        this.loadGoogleAssets();
        this.loadYemenPlaces();
        this.getCurrentLocation();

        // Hide loading after initialization
        setTimeout(() => {
            this.loading.style.display = 'none';
        }, 2000);
    }

    initMap() {
        // Initialize map centered on Yemen
        this.map = L.map('map', {
            center: [15.3694, 44.1910], // Sana'a
            zoom: 7,
            zoomControl: false,
            attributionControl: false
        });

        // Add default layer
        this.layers[this.currentLayer].layer.addTo(this.map);

        // Custom attribution
        L.control.attribution({
            position: 'bottomleft',
            prefix: false
        }).addAttribution('خرائط اليمن - مدعوم بـ OpenStreetMap').addTo(this.map);
    }

    bindEvents() {
        // Zoom controls
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.map.zoomIn();
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.map.zoomOut();
        });

        // My location
        document.getElementById('myLocation').addEventListener('click', () => {
            this.getCurrentLocation(true);
        });

        // Fullscreen
        document.getElementById('fullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Layer switcher
        document.getElementById('layerToggle').addEventListener('click', () => {
            const menu = document.getElementById('layerMenu');
            menu.classList.toggle('show');
        });

        // Layer options
        document.querySelectorAll('.layer-option').forEach(option => {
            option.addEventListener('click', () => {
                const layerType = option.dataset.layer;
                this.switchLayer(layerType);
            });
        });

        // Search
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.search(e.target.value);
            }
        });

        // Sidebar
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.sidebar.classList.remove('show');
        });

        // Click outside to close layer menu
        document.addEventListener('click', (e) => {
            const layerMenu = document.getElementById('layerMenu');
            const layerToggle = document.getElementById('layerToggle');

            if (!layerToggle.contains(e.target) && !layerMenu.contains(e.target)) {
                layerMenu.classList.remove('show');
            }
        });
    }

    switchLayer(layerType) {
        if (this.layers[layerType] && layerType !== this.currentLayer) {
            // Remove current layer
            this.map.removeLayer(this.layers[this.currentLayer].layer);

            // Add new layer
            this.layers[layerType].layer.addTo(this.map);

            // Update current layer
            this.currentLayer = layerType;

            // Update UI
            document.getElementById('currentLayerName').textContent = this.layers[layerType].name;

            // Update active state
            document.querySelectorAll('.layer-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[data-layer="${layerType}"]`).classList.add('active');

            // Close menu
            document.getElementById('layerMenu').classList.remove('show');

            console.log(`تم تغيير الطبقة إلى: ${this.layers[layerType].name}`);
        }
    }

    getCurrentLocation(centerMap = false) {
        console.log('محاولة تحديد الموقع الجغرافي...');

        // التحقق من دعم الجهاز لتحديد الموقع
        if (!navigator.geolocation) {
            this.showNotification('جهازك لا يدعم تحديد الموقع الجغرافي', 'error');
            this.useAlternativeLocation(centerMap);
            return;
        }

        // التحقق من البروتوكول والأمان
        const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
        const isExternalAccess = location.hostname === '***********';

        console.log(`البروتوكول: ${location.protocol}, آمن: ${isSecure}, وصول خارجي: ${isExternalAccess}`);

        // للوصول الخارجي مع HTTP، نظهر نافذة طلب إذن أولاً (فقط إذا لم تظهر من قبل)
        if (isExternalAccess && !isSecure && centerMap && !this.permissionRequestShown) {
            this.permissionRequestShown = true; // منع إظهار النافذة مرة أخرى
            this.showLocationPermissionRequest();
            return;
        }

        // إذا كان وصول خارجي بدون HTTPS ولم يطلب المستخدم الإذن، استخدم البدائل مباشرة
        if (isExternalAccess && !isSecure && !centerMap) {
            console.log('وصول خارجي بدون HTTPS - استخدام البدائل مباشرة');
            this.showLocationAlternatives();
            return;
        }

        // محاولة تحديد الموقع مباشرة
        this.attemptLocationDetection(centerMap, isExternalAccess);
    }

    showLocationPermissionRequest() {
        const permissionDialog = document.createElement('div');
        permissionDialog.className = 'location-permission-request';
        permissionDialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>طلب الوصول للموقع الجغرافي</h3>
                        <button class="close-dialog" onclick="this.closest('.location-permission-request').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div class="permission-icon">
                            <i class="fas fa-map-marker-alt" style="color: #1a73e8; font-size: 48px;"></i>
                        </div>
                        <h4 style="margin: 16px 0; color: #333;">تحديد موقعك الحالي</h4>
                        <p style="color: #666; line-height: 1.5; margin-bottom: 20px;">
                            للحصول على موقعك الحقيقي على الخريطة، نحتاج للوصول لموقعك الجغرافي.
                            سيظهر طلب من المتصفح للسماح بالوصول.
                        </p>

                        <div class="permission-steps">
                            <div class="step">
                                <span class="step-number">1</span>
                                <span>انقر "السماح بتحديد الموقع"</span>
                            </div>
                            <div class="step">
                                <span class="step-number">2</span>
                                <span>اختر "السماح" عند ظهور طلب المتصفح</span>
                            </div>
                            <div class="step">
                                <span class="step-number">3</span>
                                <span>سيتم تحديد موقعك على الخريطة</span>
                            </div>
                        </div>

                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="googleMapsStyle.requestLocationPermission()">
                                <i class="fas fa-crosshairs"></i> السماح بتحديد الموقع
                            </button>
                            <button class="action-btn" onclick="googleMapsStyle.showLocationAlternatives(); this.closest('.location-permission-request').remove();">
                                <i class="fas fa-map"></i> استخدام البدائل
                            </button>
                        </div>

                        <div class="permission-note">
                            <small style="color: #999;">
                                💡 إذا لم يعمل تحديد الموقع، قد يكون بسبب عدم توفر HTTPS. يمكنك استخدام البدائل المتاحة.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(permissionDialog);

        // Add styles for the permission dialog
        const style = document.createElement('style');
        style.textContent = `
            .permission-icon {
                text-align: center;
                margin-bottom: 16px;
            }

            .permission-steps {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 16px;
                margin: 16px 0;
            }

            .step {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;
                font-size: 14px;
                color: #333;
            }

            .step:last-child {
                margin-bottom: 0;
            }

            .step-number {
                background: #1a73e8;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                flex-shrink: 0;
            }

            .permission-note {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                padding: 12px;
                margin-top: 16px;
                text-align: center;
            }
        `;
        document.head.appendChild(style);
    }

    requestLocationPermission() {
        // إغلاق نافذة الطلب
        document.querySelector('.location-permission-request')?.remove();

        // التحقق من البروتوكول مرة أخرى
        const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
        const isExternalAccess = location.hostname === '***********';

        if (isExternalAccess && !isSecure) {
            // للوصول الخارجي مع HTTP، نعلم أن GPS لن يعمل
            console.log('وصول خارجي مع HTTP - GPS لن يعمل، إظهار البدائل مباشرة');
            this.showNotification('تحديد الموقع يتطلب HTTPS. استخدم البدائل المتاحة.', 'warning');
            setTimeout(() => {
                this.showLocationAlternatives();
            }, 1500);
            return;
        }

        // محاولة تحديد الموقع مع رسالة واضحة (للوصول المحلي أو HTTPS)
        this.showNotification('انتظر طلب المتصفح للسماح بالوصول للموقع...', 'info');

        // محاولة فورية لتحديد الموقع
        this.attemptLocationDetection(true, isExternalAccess);
    }

    attemptLocationDetection(centerMap, isExternalAccess) {
        const options = {
            enableHighAccuracy: true,
            timeout: 20000, // مهلة أطول للوصول الخارجي
            maximumAge: 0 // لا نستخدم موقع محفوظ، نريد موقع جديد
        };

        // إظهار رسالة تحميل
        if (centerMap) {
            this.showNotification('جاري تحديد موقعك... يرجى السماح عند ظهور الطلب', 'info');
        }

        console.log('محاولة الحصول على الموقع مع الخيارات:', options);

        navigator.geolocation.getCurrentPosition(
            (position) => {
                console.log('نجح تحديد الموقع!', position);

                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;

                this.currentLocation = { lat, lng, accuracy };

                // Add current location marker
                this.addCurrentLocationMarker(lat, lng);

                if (centerMap) {
                    this.map.setView([lat, lng], 15);
                    this.showNotification(`✅ تم تحديد موقعك الحالي بنجاح! (دقة: ${Math.round(accuracy)}م)`, 'success');
                }

                console.log(`تم تحديد الموقع: ${lat}, ${lng} (دقة: ${accuracy}م)`);
            },
            (error) => {
                console.warn('فشل تحديد الموقع:', error.code, error.message);
                this.handleLocationError(error, centerMap, isExternalAccess);
            },
            options
        );
    }

    handleLocationError(error, centerMap, isExternalAccess) {
        let errorMessage = '';
        let showAlternatives = false;

        console.log(`خطأ GPS - الكود: ${error.code}, الرسالة: ${error.message}`);

        switch (error.code) {
            case error.PERMISSION_DENIED:
                if (isExternalAccess) {
                    errorMessage = '🔒 تحديد الموقع يتطلب HTTPS للوصول الخارجي. استخدم البدائل المتاحة.';
                    showAlternatives = true;
                } else {
                    errorMessage = '❌ تم رفض طلب تحديد الموقع. يمكنك المحاولة مرة أخرى أو استخدام البدائل.';
                    showAlternatives = true;
                    // إظهار نصائح إضافية للمستخدمين المحليين فقط
                    setTimeout(() => {
                        this.showPermissionDeniedHelp();
                    }, 2000);
                }
                break;

            case error.POSITION_UNAVAILABLE:
                errorMessage = '📍 معلومات الموقع غير متوفرة حالياً. جرب البدائل المتاحة.';
                showAlternatives = true;
                break;

            case error.TIMEOUT:
                errorMessage = '⏱️ انتهت مهلة تحديد الموقع. جرب مرة أخرى أو استخدم البدائل.';
                showAlternatives = true;
                break;

            default:
                if (isExternalAccess) {
                    errorMessage = '🔒 تحديد الموقع غير متاح مع HTTP. استخدم البدائل المتاحة.';
                    showAlternatives = true;
                } else {
                    errorMessage = 'فشل تحديد الموقع.';
                    showAlternatives = true;
                }
        }

        if (centerMap) {
            this.showNotification(errorMessage, 'warning');

            if (showAlternatives) {
                setTimeout(() => {
                    this.showLocationAlternatives();
                }, 1500);
            } else {
                this.useAlternativeLocation(centerMap);
            }
        }
    }

    showPermissionDeniedHelp() {
        const helpDialog = document.createElement('div');
        helpDialog.className = 'permission-denied-help';
        helpDialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>كيفية السماح بتحديد الموقع</h3>
                        <button class="close-dialog" onclick="this.closest('.permission-denied-help').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <i class="fas fa-info-circle" style="color: #1a73e8; font-size: 48px;"></i>
                        </div>

                        <h4 style="color: #333; margin-bottom: 16px;">لتفعيل تحديد الموقع:</h4>

                        <div class="help-steps">
                            <div class="help-step">
                                <div class="step-icon">🔒</div>
                                <div class="step-content">
                                    <strong>انقر على أيقونة القفل</strong> في شريط العنوان
                                </div>
                            </div>

                            <div class="help-step">
                                <div class="step-icon">📍</div>
                                <div class="step-content">
                                    <strong>اختر "الموقع"</strong> من القائمة
                                </div>
                            </div>

                            <div class="help-step">
                                <div class="step-icon">✅</div>
                                <div class="step-content">
                                    <strong>اختر "السماح"</strong> ثم أعد تحميل الصفحة
                                </div>
                            </div>
                        </div>

                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="googleMapsStyle.tryLocationAgain(); this.closest('.permission-denied-help').remove();">
                                <i class="fas fa-redo"></i> المحاولة مرة أخرى
                            </button>
                            <button class="action-btn" onclick="googleMapsStyle.showLocationAlternatives(); this.closest('.permission-denied-help').remove();">
                                <i class="fas fa-map"></i> استخدام البدائل
                            </button>
                        </div>

                        <div style="background: #e3f2fd; border-radius: 8px; padding: 12px; margin-top: 16px;">
                            <small style="color: #1976d2;">
                                💡 <strong>نصيحة:</strong> إذا لم تظهر أيقونة القفل، جرب إعادة تحميل الصفحة والنقر على "تحديد موقعي" مرة أخرى
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpDialog);

        // Add styles for help dialog
        const style = document.createElement('style');
        style.textContent = `
            .help-steps {
                margin: 16px 0;
            }

            .help-step {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 16px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 8px;
            }

            .help-step:last-child {
                margin-bottom: 0;
            }

            .step-icon {
                font-size: 24px;
                width: 40px;
                text-align: center;
                flex-shrink: 0;
            }

            .step-content {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
            }
        `;
        document.head.appendChild(style);
    }

    tryLocationAgain() {
        // التحقق من البروتوكول قبل المحاولة
        const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
        const isExternalAccess = location.hostname === '***********';

        if (isExternalAccess && !isSecure) {
            // للوصول الخارجي مع HTTP، لا نحاول مرة أخرى
            this.showNotification('تحديد الموقع يتطلب HTTPS. استخدم البدائل المتاحة.', 'warning');
            setTimeout(() => {
                this.showLocationAlternatives();
            }, 1500);
            return;
        }

        // محاولة جديدة لتحديد الموقع (للوصول المحلي أو HTTPS فقط)
        this.showNotification('جاري المحاولة مرة أخرى...', 'info');
        this.attemptLocationDetection(true, isExternalAccess);
    }

    showLocationPermissionDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'location-permission-dialog';
        dialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>تحديد الموقع الجغرافي</h3>
                        <button class="close-dialog" onclick="this.closest('.location-permission-dialog').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div class="location-icon">
                            <i class="fas fa-exclamation-triangle" style="color: #ff9800;"></i>
                        </div>
                        <p><strong>تحديد الموقع غير متاح حالياً</strong></p>
                        <p style="font-size: 14px; color: #666;">
                            قد يكون السبب عدم السماح بالوصول للموقع أو عدم توفر HTTPS.
                            يمكنك استخدام البدائل التالية:
                        </p>
                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="googleMapsStyle.tryLocationAgain()">
                                <i class="fas fa-crosshairs"></i> المحاولة مرة أخرى
                            </button>
                            <button class="action-btn" onclick="googleMapsStyle.showLocationAlternatives()">
                                <i class="fas fa-map"></i> البدائل المتاحة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .location-permission-dialog {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .dialog-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .dialog-content {
                background: white;
                border-radius: 12px;
                max-width: 400px;
                margin: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            }

            .dialog-header {
                padding: 20px 20px 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .dialog-header h3 {
                margin: 0;
                color: #333;
                font-size: 18px;
            }

            .close-dialog {
                background: none;
                border: none;
                font-size: 18px;
                color: #666;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
            }

            .close-dialog:hover {
                background: #f5f5f5;
            }

            .dialog-body {
                padding: 20px;
                text-align: center;
            }

            .location-icon {
                font-size: 48px;
                color: #1a73e8;
                margin-bottom: 16px;
            }

            .dialog-body p {
                margin-bottom: 16px;
                color: #666;
                line-height: 1.5;
            }

            .dialog-body ol {
                text-align: right;
                margin-bottom: 20px;
                color: #666;
            }

            .dialog-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
            }

            @keyframes slideIn {
                from { transform: scale(0.9); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    tryLocationAgain() {
        document.querySelector('.location-permission-dialog')?.remove();
        this.getCurrentLocation(true);
    }

    showLocationAlternatives() {
        document.querySelector('.location-permission-dialog')?.remove();

        const alternatives = document.createElement('div');
        alternatives.className = 'location-alternatives';
        alternatives.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>بدائل تحديد الموقع</h3>
                        <button class="close-dialog" onclick="this.closest('.location-alternatives').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 48px; margin-bottom: 12px;">🗺️</div>
                            <h4 style="color: #333; margin-bottom: 8px;">بدائل تحديد الموقع</h4>
                            <p style="color: #666; font-size: 14px; line-height: 1.4;">
                                تحديد الموقع الحقيقي يتطلب HTTPS. اختر إحدى البدائل التالية:
                            </p>
                        </div>

                        <div class="alternatives-grid">
                            <button class="alternative-btn" onclick="googleMapsStyle.useAlternativeLocation(true); document.querySelector('.location-alternatives').remove();">
                                <i class="fas fa-map-marker-alt" style="color: #1a73e8;"></i>
                                <span>موقع افتراضي</span>
                                <small>صنعاء، اليمن</small>
                            </button>

                            <button class="alternative-btn" onclick="googleMapsStyle.showManualLocationInput(); document.querySelector('.location-alternatives').remove();">
                                <i class="fas fa-edit" style="color: #34a853;"></i>
                                <span>إدخال يدوي</span>
                                <small>أدخل الإحداثيات</small>
                            </button>

                            <button class="alternative-btn" onclick="googleMapsStyle.useIPLocation(); document.querySelector('.location-alternatives').remove();">
                                <i class="fas fa-globe" style="color: #ea4335;"></i>
                                <span>موقع تقريبي</span>
                                <small>حسب عنوان IP</small>
                            </button>

                            <button class="alternative-btn" onclick="googleMapsStyle.showPopularPlaces(); document.querySelector('.location-alternatives').remove();">
                                <i class="fas fa-star" style="color: #fbbc04;"></i>
                                <span>مواقع شائعة</span>
                                <small>اختر من القائمة</small>
                            </button>
                        </div>

                        <div style="text-align: center; margin-top: 16px;">
                            <button class="action-btn" onclick="document.querySelector('.location-alternatives').remove();" style="background: #f5f5f5; color: #666;">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>

                        <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-top: 16px; text-align: center;">
                            <small style="color: #666;">
                                💡 <strong>نصيحة:</strong> للحصول على موقعك الحقيقي، استخدم رابط HTTPS
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(alternatives);

        // Add styles for alternatives
        const style = document.createElement('style');
        style.textContent = `
            .alternatives-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin-top: 16px;
            }

            .alternative-btn {
                background: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 16px;
                cursor: pointer;
                transition: all 0.2s ease;
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }

            .alternative-btn:hover {
                border-color: #1a73e8;
                background: #f8f9ff;
            }

            .alternative-btn i {
                font-size: 24px;
                color: #1a73e8;
            }

            .alternative-btn span {
                font-weight: 500;
                color: #333;
            }

            .alternative-btn small {
                color: #666;
                font-size: 12px;
            }

            @media (max-width: 480px) {
                .alternatives-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;
        document.head.appendChild(style);
    }

    useAlternativeLocation(centerMap) {
        // استخدام موقع افتراضي في صنعاء
        const defaultLocation = { lat: 15.3694, lng: 44.1910 };
        this.currentLocation = defaultLocation;

        if (centerMap) {
            this.map.setView([defaultLocation.lat, defaultLocation.lng], 12);
            this.addCurrentLocationMarker(defaultLocation.lat, defaultLocation.lng, true);
            this.showNotification('تم استخدام موقع افتراضي في صنعاء', 'info');
        }

        document.querySelector('.location-alternatives')?.remove();
    }

    async useIPLocation() {
        try {
            this.showNotification('جاري تحديد الموقع التقريبي...', 'info');

            // محاولة عدة خدمات لتحديد الموقع بناءً على IP
            const services = [
                'https://ipapi.co/json/',
                'https://ip-api.com/json/',
                'https://ipinfo.io/json'
            ];

            for (const service of services) {
                try {
                    const response = await fetch(service);
                    const data = await response.json();

                    let lat, lng, city, country;

                    // معالجة البيانات حسب نوع الخدمة
                    if (service.includes('ipapi.co')) {
                        lat = data.latitude;
                        lng = data.longitude;
                        city = data.city;
                        country = data.country_name;
                    } else if (service.includes('ip-api.com')) {
                        lat = data.lat;
                        lng = data.lon;
                        city = data.city;
                        country = data.country;
                    } else if (service.includes('ipinfo.io')) {
                        const coords = data.loc?.split(',');
                        lat = coords?.[0];
                        lng = coords?.[1];
                        city = data.city;
                        country = data.country;
                    }

                    if (lat && lng) {
                        const latitude = parseFloat(lat);
                        const longitude = parseFloat(lng);

                        this.currentLocation = { lat: latitude, lng: longitude };
                        this.map.setView([latitude, longitude], 10);
                        this.addCurrentLocationMarker(latitude, longitude);

                        const locationName = city && country ? `${city}, ${country}` : 'موقع تقريبي';
                        this.showNotification(`موقع تقريبي: ${locationName}`, 'success');
                        return;
                    }
                } catch (serviceError) {
                    console.warn(`فشل في خدمة ${service}:`, serviceError);
                    continue;
                }
            }

            throw new Error('فشل في جميع خدمات تحديد الموقع');

        } catch (error) {
            console.warn('فشل تحديد الموقع بناءً على IP:', error);
            this.showNotification('فشل تحديد الموقع التقريبي، استخدام الموقع الافتراضي', 'warning');
            this.useAlternativeLocation(true);
        }

        document.querySelector('.location-alternatives')?.remove();
    }

    showManualLocationInput() {
        document.querySelector('.location-alternatives')?.remove();

        const inputDialog = document.createElement('div');
        inputDialog.className = 'manual-location-dialog';
        inputDialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>إدخال الموقع يدوياً</h3>
                        <button class="close-dialog" onclick="this.closest('.manual-location-dialog').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div class="input-group">
                            <label>خط العرض (Latitude):</label>
                            <input type="number" id="manualLat" placeholder="15.3694" step="any" min="-90" max="90">
                        </div>
                        <div class="input-group">
                            <label>خط الطول (Longitude):</label>
                            <input type="number" id="manualLng" placeholder="44.1910" step="any" min="-180" max="180">
                        </div>
                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="googleMapsStyle.setManualLocation()">
                                <i class="fas fa-check"></i> تأكيد الموقع
                            </button>
                            <button class="action-btn" onclick="this.closest('.manual-location-dialog').remove()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(inputDialog);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .input-group {
                margin-bottom: 16px;
                text-align: right;
            }

            .input-group label {
                display: block;
                margin-bottom: 8px;
                color: #333;
                font-weight: 500;
            }

            .input-group input {
                width: 100%;
                padding: 12px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                font-size: 16px;
                outline: none;
                transition: border-color 0.2s ease;
            }

            .input-group input:focus {
                border-color: #1a73e8;
            }
        `;
        document.head.appendChild(style);
    }

    setManualLocation() {
        const lat = parseFloat(document.getElementById('manualLat').value);
        const lng = parseFloat(document.getElementById('manualLng').value);

        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            this.showNotification('يرجى إدخال إحداثيات صحيحة', 'error');
            return;
        }

        this.currentLocation = { lat, lng };
        this.map.setView([lat, lng], 15);
        this.addCurrentLocationMarker(lat, lng);
        this.showNotification('تم تحديد الموقع يدوياً', 'success');

        document.querySelector('.manual-location-dialog')?.remove();
    }

    showPopularPlaces() {
        document.querySelector('.location-alternatives')?.remove();

        const popularPlaces = [
            { name: 'صنعاء القديمة', lat: 15.3547, lng: 44.2066 },
            { name: 'جامع الصالح', lat: 15.3729, lng: 44.1901 },
            { name: 'مطار صنعاء الدولي', lat: 15.4762, lng: 44.2193 },
            { name: 'جامعة صنعاء', lat: 15.3569, lng: 44.2081 },
            { name: 'عدن', lat: 12.7797, lng: 45.0365 },
            { name: 'تعز', lat: 13.5795, lng: 44.0207 },
            { name: 'الحديدة', lat: 14.7978, lng: 42.9545 },
            { name: 'إب', lat: 13.9667, lng: 44.1833 }
        ];

        const placesDialog = document.createElement('div');
        placesDialog.className = 'popular-places-dialog';
        placesDialog.innerHTML = `
            <div class="dialog-overlay">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>مواقع شائعة</h3>
                        <button class="close-dialog" onclick="this.closest('.popular-places-dialog').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-body">
                        <div class="places-list">
                            ${popularPlaces.map(place => `
                                <button class="place-item" onclick="googleMapsStyle.goToPlace(${place.lat}, ${place.lng}, '${place.name}')">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>${place.name}</span>
                                </button>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(placesDialog);

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .places-list {
                max-height: 300px;
                overflow-y: auto;
            }

            .place-item {
                width: 100%;
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px 16px;
                margin-bottom: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 12px;
                text-align: right;
            }

            .place-item:hover {
                background: #f8f9ff;
                border-color: #1a73e8;
            }

            .place-item i {
                color: #1a73e8;
                font-size: 16px;
            }

            .place-item span {
                color: #333;
                font-weight: 500;
            }
        `;
        document.head.appendChild(style);
    }

    goToPlace(lat, lng, name) {
        this.currentLocation = { lat, lng };
        this.map.setView([lat, lng], 12);
        this.addCurrentLocationMarker(lat, lng);
        this.showNotification(`تم الانتقال إلى: ${name}`, 'success');

        document.querySelector('.popular-places-dialog')?.remove();
    }

    addCurrentLocationMarker(lat, lng, isDefault = false) {
        // Remove existing current location marker
        if (this.currentLocationMarker) {
            this.map.removeLayer(this.currentLocationMarker);
        }

        // Create custom marker icon
        const markerIcon = L.divIcon({
            className: 'custom-marker',
            html: '',
            iconSize: [24, 24],
            iconAnchor: [12, 24]
        });

        this.currentLocationMarker = L.marker([lat, lng], { icon: markerIcon }).addTo(this.map);

        const popupContent = `
            <div style="text-align: center; direction: rtl; min-width: 200px;">
                <h4>${isDefault ? 'موقع افتراضي' : 'موقعك الحالي'}</h4>
                <p>خط العرض: ${lat.toFixed(6)}</p>
                <p>خط الطول: ${lng.toFixed(6)}</p>
                ${isDefault ? '<p style="color: #ff9800;">تم استخدام موقع افتراضي في صنعاء</p>' : ''}
            </div>
        `;

        this.currentLocationMarker.bindPopup(popupContent);
    }

    async loadGoogleAssets() {
        try {
            // Try to load cached Google Maps assets
            const googlePath = 'E:/google';
            console.log('محاولة تحميل موارد Google Maps المحفوظة...');

            // Load Google Maps icons and styles
            await this.loadGoogleIcons();
            await this.loadGoogleStyles();

        } catch (error) {
            console.log('لم يتم العثور على موارد Google Maps، استخدام الموارد الافتراضية');
        }
    }

    async loadGoogleIcons() {
        // Load Google Maps style icons for different POI types
        this.googleIcons = {
            restaurant: '🍽️',
            hospital: '🏥',
            school: '🏫',
            mosque: '🕌',
            gas: '⛽',
            hotel: '🏨',
            bank: '🏦',
            airport: '✈️',
            default: '📍'
        };
    }

    async loadGoogleStyles() {
        // Apply Google Maps style to the map
        const googleStyle = {
            filter: 'hue-rotate(0deg) saturate(1.1) brightness(1.05)',
            transition: 'filter 0.3s ease'
        };

        // Apply style to map container
        document.getElementById('map').style.filter = googleStyle.filter;
    }

    async loadYemenPlaces() {
        try {
            // Load places from JSON file
            const response = await fetch('api/places.json');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    this.addPlaceMarkers(data.data);
                }
            }
        } catch (error) {
            console.log('فشل تحميل المواقع:', error);
        }
    }

    addPlaceMarkers(places) {
        places.forEach(place => {
            if (place.latitude && place.longitude) {
                // Create custom marker based on place type
                const icon = this.getPlaceIcon(place.type);

                const marker = L.marker([place.latitude, place.longitude], {
                    icon: L.divIcon({
                        className: 'place-marker',
                        html: `<div style="background: #ea4335; color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">${icon}</div>`,
                        iconSize: [32, 32],
                        iconAnchor: [16, 16]
                    })
                }).addTo(this.map);

                // Add click event to show place details
                marker.on('click', () => {
                    this.showPlaceDetails(place);
                });

                this.markers.push(marker);
            }
        });
    }

    getPlaceIcon(type) {
        const iconMap = {
            restaurant: '🍽️',
            hospital: '🏥',
            school: '🏫',
            mosque: '🕌',
            heritage: '🏛️',
            airport: '✈️',
            university: '🎓',
            market: '🛒',
            palace: '🏰',
            museum: '🏛️',
            gate: '🚪',
            park: '🌳',
            default: '📍'
        };

        return iconMap[type] || iconMap.default;
    }

    showPlaceDetails(place) {
        const content = `
            <div class="location-card">
                <div class="location-image" style="background-image: url('${place.image || 'assets/images/default-place.jpg'}')"></div>
                <div class="location-info">
                    <h3 class="location-title">${place.name_ar}</h3>
                    <p class="location-description">${place.description_ar}</p>
                    <div class="location-actions">
                        <button class="action-btn primary" onclick="googleMapsStyle.getDirections(${place.latitude}, ${place.longitude})">
                            <i class="fas fa-route"></i> المسار
                        </button>
                        <button class="action-btn" onclick="googleMapsStyle.shareLocation(${place.latitude}, ${place.longitude}, '${place.name_ar}')">
                            <i class="fas fa-share"></i> مشاركة
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('sidebarContent').innerHTML = content;
        this.sidebar.classList.add('show');
    }

    search(query) {
        if (!query.trim()) return;

        console.log(`البحث عن: ${query}`);

        // Simple search in loaded places
        const results = this.markers.filter(marker => {
            // This is a simplified search - in a real app, you'd search through place data
            return true; // For now, show all markers
        });

        if (results.length > 0) {
            // Focus on first result
            const firstResult = results[0];
            this.map.setView(firstResult.getLatLng(), 15);
            firstResult.openPopup();
        }

        this.showNotification(`تم العثور على ${results.length} نتيجة`, 'success');
    }

    getDirections(lat, lng) {
        if (!this.currentLocation) {
            this.showNotification('يرجى تحديد موقعك الحالي أولاً', 'warning');
            return;
        }

        // Simple route line
        const route = L.polyline([
            [this.currentLocation.lat, this.currentLocation.lng],
            [lat, lng]
        ], {
            color: '#1a73e8',
            weight: 4,
            opacity: 0.8
        }).addTo(this.map);

        // Calculate distance
        const distance = this.calculateDistance(
            this.currentLocation.lat, this.currentLocation.lng,
            lat, lng
        );

        this.showNotification(`المسافة التقريبية: ${distance.toFixed(2)} كم`, 'info');

        // Fit map to show route
        this.map.fitBounds(route.getBounds(), { padding: [20, 20] });
    }

    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Earth's radius in km
        const dLat = this.deg2rad(lat2 - lat1);
        const dLng = this.deg2rad(lng2 - lng1);
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    shareLocation(lat, lng, name) {
        const url = `${window.location.origin}/maps.html?lat=${lat}&lng=${lng}&name=${encodeURIComponent(name)}`;

        if (navigator.share) {
            navigator.share({
                title: `موقع: ${name}`,
                text: `شاهد هذا الموقع على خرائط اليمن`,
                url: url
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('تم نسخ رابط الموقع', 'success');
            });
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            document.getElementById('fullscreen').innerHTML = '<i class="fas fa-compress"></i>';
        } else {
            document.exitFullscreen();
            document.getElementById('fullscreen').innerHTML = '<i class="fas fa-expand"></i>';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196f3'};
            color: white;
            padding: 12px 24px;
            border-radius: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            animation: slideDown 0.3s ease;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateX(-50%) translateY(0); opacity: 1; }
        to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.googleMapsStyle = new GoogleMapsStyle();
});

// Handle shared locations from URL
window.addEventListener('load', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    const name = urlParams.get('name');

    if (lat && lng) {
        setTimeout(() => {
            window.googleMapsStyle.map.setView([parseFloat(lat), parseFloat(lng)], 15);

            const marker = L.marker([parseFloat(lat), parseFloat(lng)]).addTo(window.googleMapsStyle.map);
            marker.bindPopup(`<h4>${decodeURIComponent(name || 'موقع مشارك')}</h4>`).openPopup();

            window.googleMapsStyle.showNotification(`تم فتح الموقع المشارك: ${decodeURIComponent(name || 'موقع')}`, 'success');
        }, 1000);
    }
});

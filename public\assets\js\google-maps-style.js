class GoogleMapsStyle {
    constructor() {
        this.map = null;
        this.currentLayer = 'streets';
        this.markers = [];
        this.currentLocation = null;
        this.sidebar = document.getElementById('sidebar');
        this.loading = document.getElementById('loading');
        
        // Google Maps Style Layers
        this.layers = {
            streets: {
                name: 'خريطة',
                layer: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19
                })
            },
            satellite: {
                name: 'القمر الصناعي',
                layer: <PERSON><PERSON>tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: '© Esri',
                    maxZoom: 19
                })
            },
            terrain: {
                name: 'التضاريس',
                layer: <PERSON><PERSON>tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenTopoMap contributors',
                    maxZoom: 17
                })
            }
        };
        
        this.init();
    }

    init() {
        this.initMap();
        this.bindEvents();
        this.loadGoogleAssets();
        this.loadYemenPlaces();
        this.getCurrentLocation();
        
        // Hide loading after initialization
        setTimeout(() => {
            this.loading.style.display = 'none';
        }, 2000);
    }

    initMap() {
        // Initialize map centered on Yemen
        this.map = L.map('map', {
            center: [15.3694, 44.1910], // Sana'a
            zoom: 7,
            zoomControl: false,
            attributionControl: false
        });

        // Add default layer
        this.layers[this.currentLayer].layer.addTo(this.map);

        // Custom attribution
        L.control.attribution({
            position: 'bottomleft',
            prefix: false
        }).addAttribution('خرائط اليمن - مدعوم بـ OpenStreetMap').addTo(this.map);
    }

    bindEvents() {
        // Zoom controls
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.map.zoomIn();
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.map.zoomOut();
        });

        // My location
        document.getElementById('myLocation').addEventListener('click', () => {
            this.getCurrentLocation(true);
        });

        // Fullscreen
        document.getElementById('fullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Layer switcher
        document.getElementById('layerToggle').addEventListener('click', () => {
            const menu = document.getElementById('layerMenu');
            menu.classList.toggle('show');
        });

        // Layer options
        document.querySelectorAll('.layer-option').forEach(option => {
            option.addEventListener('click', () => {
                const layerType = option.dataset.layer;
                this.switchLayer(layerType);
            });
        });

        // Search
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.search(e.target.value);
            }
        });

        // Sidebar
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.sidebar.classList.remove('show');
        });

        // Click outside to close layer menu
        document.addEventListener('click', (e) => {
            const layerMenu = document.getElementById('layerMenu');
            const layerToggle = document.getElementById('layerToggle');
            
            if (!layerToggle.contains(e.target) && !layerMenu.contains(e.target)) {
                layerMenu.classList.remove('show');
            }
        });
    }

    switchLayer(layerType) {
        if (this.layers[layerType] && layerType !== this.currentLayer) {
            // Remove current layer
            this.map.removeLayer(this.layers[this.currentLayer].layer);
            
            // Add new layer
            this.layers[layerType].layer.addTo(this.map);
            
            // Update current layer
            this.currentLayer = layerType;
            
            // Update UI
            document.getElementById('currentLayerName').textContent = this.layers[layerType].name;
            
            // Update active state
            document.querySelectorAll('.layer-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[data-layer="${layerType}"]`).classList.add('active');
            
            // Close menu
            document.getElementById('layerMenu').classList.remove('show');
            
            console.log(`تم تغيير الطبقة إلى: ${this.layers[layerType].name}`);
        }
    }

    getCurrentLocation(centerMap = false) {
        if (!navigator.geolocation) {
            this.showNotification('جهازك لا يدعم تحديد الموقع الجغرافي', 'error');
            return;
        }

        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
        };

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                
                this.currentLocation = { lat, lng };
                
                // Add current location marker
                this.addCurrentLocationMarker(lat, lng);
                
                if (centerMap) {
                    this.map.setView([lat, lng], 15);
                    this.showNotification('تم تحديد موقعك الحالي', 'success');
                }
                
                console.log(`تم تحديد الموقع: ${lat}, ${lng}`);
            },
            (error) => {
                console.warn('فشل تحديد الموقع:', error);
                
                // Use default location (Sana'a) for server environment
                if (centerMap) {
                    const defaultLocation = { lat: 15.3694, lng: 44.1910 };
                    this.map.setView([defaultLocation.lat, defaultLocation.lng], 12);
                    this.addCurrentLocationMarker(defaultLocation.lat, defaultLocation.lng, true);
                    this.showNotification('تم استخدام موقع افتراضي في صنعاء', 'info');
                }
            },
            options
        );
    }

    addCurrentLocationMarker(lat, lng, isDefault = false) {
        // Remove existing current location marker
        if (this.currentLocationMarker) {
            this.map.removeLayer(this.currentLocationMarker);
        }

        // Create custom marker icon
        const markerIcon = L.divIcon({
            className: 'custom-marker',
            html: '',
            iconSize: [24, 24],
            iconAnchor: [12, 24]
        });

        this.currentLocationMarker = L.marker([lat, lng], { icon: markerIcon }).addTo(this.map);
        
        const popupContent = `
            <div style="text-align: center; direction: rtl; min-width: 200px;">
                <h4>${isDefault ? 'موقع افتراضي' : 'موقعك الحالي'}</h4>
                <p>خط العرض: ${lat.toFixed(6)}</p>
                <p>خط الطول: ${lng.toFixed(6)}</p>
                ${isDefault ? '<p style="color: #ff9800;">تم استخدام موقع افتراضي في صنعاء</p>' : ''}
            </div>
        `;
        
        this.currentLocationMarker.bindPopup(popupContent);
    }

    async loadGoogleAssets() {
        try {
            // Try to load cached Google Maps assets
            const googlePath = 'E:/google';
            console.log('محاولة تحميل موارد Google Maps المحفوظة...');
            
            // Load Google Maps icons and styles
            await this.loadGoogleIcons();
            await this.loadGoogleStyles();
            
        } catch (error) {
            console.log('لم يتم العثور على موارد Google Maps، استخدام الموارد الافتراضية');
        }
    }

    async loadGoogleIcons() {
        // Load Google Maps style icons for different POI types
        this.googleIcons = {
            restaurant: '🍽️',
            hospital: '🏥',
            school: '🏫',
            mosque: '🕌',
            gas: '⛽',
            hotel: '🏨',
            bank: '🏦',
            airport: '✈️',
            default: '📍'
        };
    }

    async loadGoogleStyles() {
        // Apply Google Maps style to the map
        const googleStyle = {
            filter: 'hue-rotate(0deg) saturate(1.1) brightness(1.05)',
            transition: 'filter 0.3s ease'
        };
        
        // Apply style to map container
        document.getElementById('map').style.filter = googleStyle.filter;
    }

    async loadYemenPlaces() {
        try {
            // Load places from JSON file
            const response = await fetch('api/places.json');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    this.addPlaceMarkers(data.data);
                }
            }
        } catch (error) {
            console.log('فشل تحميل المواقع:', error);
        }
    }

    addPlaceMarkers(places) {
        places.forEach(place => {
            if (place.latitude && place.longitude) {
                // Create custom marker based on place type
                const icon = this.getPlaceIcon(place.type);
                
                const marker = L.marker([place.latitude, place.longitude], {
                    icon: L.divIcon({
                        className: 'place-marker',
                        html: `<div style="background: #ea4335; color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">${icon}</div>`,
                        iconSize: [32, 32],
                        iconAnchor: [16, 16]
                    })
                }).addTo(this.map);

                // Add click event to show place details
                marker.on('click', () => {
                    this.showPlaceDetails(place);
                });

                this.markers.push(marker);
            }
        });
    }

    getPlaceIcon(type) {
        const iconMap = {
            restaurant: '🍽️',
            hospital: '🏥',
            school: '🏫',
            mosque: '🕌',
            heritage: '🏛️',
            airport: '✈️',
            university: '🎓',
            market: '🛒',
            palace: '🏰',
            museum: '🏛️',
            gate: '🚪',
            park: '🌳',
            default: '📍'
        };
        
        return iconMap[type] || iconMap.default;
    }

    showPlaceDetails(place) {
        const content = `
            <div class="location-card">
                <div class="location-image" style="background-image: url('${place.image || 'assets/images/default-place.jpg'}')"></div>
                <div class="location-info">
                    <h3 class="location-title">${place.name_ar}</h3>
                    <p class="location-description">${place.description_ar}</p>
                    <div class="location-actions">
                        <button class="action-btn primary" onclick="googleMapsStyle.getDirections(${place.latitude}, ${place.longitude})">
                            <i class="fas fa-route"></i> المسار
                        </button>
                        <button class="action-btn" onclick="googleMapsStyle.shareLocation(${place.latitude}, ${place.longitude}, '${place.name_ar}')">
                            <i class="fas fa-share"></i> مشاركة
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('sidebarContent').innerHTML = content;
        this.sidebar.classList.add('show');
    }

    search(query) {
        if (!query.trim()) return;
        
        console.log(`البحث عن: ${query}`);
        
        // Simple search in loaded places
        const results = this.markers.filter(marker => {
            // This is a simplified search - in a real app, you'd search through place data
            return true; // For now, show all markers
        });
        
        if (results.length > 0) {
            // Focus on first result
            const firstResult = results[0];
            this.map.setView(firstResult.getLatLng(), 15);
            firstResult.openPopup();
        }
        
        this.showNotification(`تم العثور على ${results.length} نتيجة`, 'success');
    }

    getDirections(lat, lng) {
        if (!this.currentLocation) {
            this.showNotification('يرجى تحديد موقعك الحالي أولاً', 'warning');
            return;
        }
        
        // Simple route line
        const route = L.polyline([
            [this.currentLocation.lat, this.currentLocation.lng],
            [lat, lng]
        ], {
            color: '#1a73e8',
            weight: 4,
            opacity: 0.8
        }).addTo(this.map);
        
        // Calculate distance
        const distance = this.calculateDistance(
            this.currentLocation.lat, this.currentLocation.lng,
            lat, lng
        );
        
        this.showNotification(`المسافة التقريبية: ${distance.toFixed(2)} كم`, 'info');
        
        // Fit map to show route
        this.map.fitBounds(route.getBounds(), { padding: [20, 20] });
    }

    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Earth's radius in km
        const dLat = this.deg2rad(lat2 - lat1);
        const dLng = this.deg2rad(lng2 - lng1);
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    deg2rad(deg) {
        return deg * (Math.PI/180);
    }

    shareLocation(lat, lng, name) {
        const url = `${window.location.origin}/maps.html?lat=${lat}&lng=${lng}&name=${encodeURIComponent(name)}`;
        
        if (navigator.share) {
            navigator.share({
                title: `موقع: ${name}`,
                text: `شاهد هذا الموقع على خرائط اليمن`,
                url: url
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('تم نسخ رابط الموقع', 'success');
            });
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            document.getElementById('fullscreen').innerHTML = '<i class="fas fa-compress"></i>';
        } else {
            document.exitFullscreen();
            document.getElementById('fullscreen').innerHTML = '<i class="fas fa-expand"></i>';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#2196f3'};
            color: white;
            padding: 12px 24px;
            border-radius: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            animation: slideDown 0.3s ease;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideUp 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
    }
    
    @keyframes slideUp {
        from { transform: translateX(-50%) translateY(0); opacity: 1; }
        to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.googleMapsStyle = new GoogleMapsStyle();
});

// Handle shared locations from URL
window.addEventListener('load', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    const name = urlParams.get('name');
    
    if (lat && lng) {
        setTimeout(() => {
            window.googleMapsStyle.map.setView([parseFloat(lat), parseFloat(lng)], 15);
            
            const marker = L.marker([parseFloat(lat), parseFloat(lng)]).addTo(window.googleMapsStyle.map);
            marker.bindPopup(`<h4>${decodeURIComponent(name || 'موقع مشارك')}</h4>`).openPopup();
            
            window.googleMapsStyle.showNotification(`تم فتح الموقع المشارك: ${decodeURIComponent(name || 'موقع')}`, 'success');
        }, 1000);
    }
});

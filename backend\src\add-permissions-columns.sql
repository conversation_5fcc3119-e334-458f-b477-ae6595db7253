-- يمن ناف - <PERSON>ل<PERSON> SQL لإضافة أعمدة الصلاحيات في جدول المستخدمين

-- إضافة عمود permissions_json لتخزين الصلاحيات كـ JSON
ALTER TABLE users
ADD COLUMN IF NOT EXISTS permissions_json JSONB DEFAULT '[]';

-- إضافة عمود can_access_admin لتحديد ما إذا كان المستخدم يمكنه الوصول إلى صفحة الإدارة
ALTER TABLE users
ADD COLUMN IF NOT EXISTS can_access_admin BOOLEAN DEFAULT FALSE;

-- تحديث المستخدم admin لمنحه صلاحية الوصول إلى صفحة الإدارة
UPDATE users
SET can_access_admin = TRUE
WHERE username = 'admin';

-- إضا<PERSON>ة صلاحيات افتراضية للمستخدم admin
UPDATE users
SET permissions_json = '[
    {"code": "view_dashboard", "name": "عرض لوحة التحكم"},
    {"code": "manage_users", "name": "إدارة المستخدمين"},
    {"code": "manage_clients", "name": "إدارة العملاء"},
    {"code": "manage_locations", "name": "إدارة المواقع"},
    {"code": "manage_settings", "name": "إدارة الإعدادات"},
    {"code": "manage_backups", "name": "إدارة النسخ الاحتياطي"},
    {"code": "manage_categories", "name": "إدارة التصنيفات"},
    {"code": "manage_routes", "name": "إدارة المسارات"},
    {"code": "manage_reports", "name": "إدارة التقارير"},
    {"code": "manage_roles", "name": "إدارة الأدوار"}
]'::jsonb
WHERE username = 'admin';

-- إضافة دالة للتحقق من صلاحيات المستخدم
CREATE OR REPLACE FUNCTION user_has_permission(user_id INTEGER, permission_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM users
        WHERE id = user_id
        AND (
            -- التحقق من وجود الصلاحية في permissions_json
            permissions_json @> format('[{"code": "%s"}]', permission_code)::jsonb
            OR
            -- التحقق من أن المستخدم مدير (role_id = 1)
            role_id = 1
        )
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;

-- إضافة دالة للحصول على صلاحيات المستخدم
CREATE OR REPLACE FUNCTION get_user_permissions(user_id INTEGER)
RETURNS JSONB AS $$
DECLARE
    user_permissions JSONB;
BEGIN
    SELECT permissions_json
    FROM users
    WHERE id = user_id
    INTO user_permissions;
    
    RETURN COALESCE(user_permissions, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- إضافة دالة لإضافة صلاحية للمستخدم
CREATE OR REPLACE FUNCTION add_user_permission(user_id INTEGER, permission_code TEXT, permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    current_permissions JSONB;
    permission_exists BOOLEAN;
BEGIN
    -- الحصول على الصلاحيات الحالية
    SELECT permissions_json
    FROM users
    WHERE id = user_id
    INTO current_permissions;
    
    -- التحقق من وجود الصلاحية
    SELECT EXISTS (
        SELECT 1
        FROM jsonb_array_elements(COALESCE(current_permissions, '[]'::jsonb)) AS perm
        WHERE perm->>'code' = permission_code
    ) INTO permission_exists;
    
    -- إذا كانت الصلاحية غير موجودة، أضفها
    IF NOT permission_exists THEN
        UPDATE users
        SET permissions_json = COALESCE(permissions_json, '[]'::jsonb) || 
                              jsonb_build_array(jsonb_build_object('code', permission_code, 'name', permission_name))
        WHERE id = user_id;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- إضافة دالة لإزالة صلاحية من المستخدم
CREATE OR REPLACE FUNCTION remove_user_permission(user_id INTEGER, permission_code TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    current_permissions JSONB;
    new_permissions JSONB;
    permission_exists BOOLEAN;
BEGIN
    -- الحصول على الصلاحيات الحالية
    SELECT permissions_json
    FROM users
    WHERE id = user_id
    INTO current_permissions;
    
    -- التحقق من وجود الصلاحية
    SELECT EXISTS (
        SELECT 1
        FROM jsonb_array_elements(COALESCE(current_permissions, '[]'::jsonb)) AS perm
        WHERE perm->>'code' = permission_code
    ) INTO permission_exists;
    
    -- إذا كانت الصلاحية موجودة، أزلها
    IF permission_exists THEN
        -- إنشاء مصفوفة جديدة بدون الصلاحية المحددة
        SELECT jsonb_agg(perm)
        FROM jsonb_array_elements(COALESCE(current_permissions, '[]'::jsonb)) AS perm
        WHERE perm->>'code' != permission_code
        INTO new_permissions;
        
        -- تحديث الصلاحيات
        UPDATE users
        SET permissions_json = COALESCE(new_permissions, '[]'::jsonb)
        WHERE id = user_id;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

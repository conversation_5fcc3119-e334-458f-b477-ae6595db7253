# سكريبت شامل لإعداد خدمة محاكاة الموقع لنظام "يمن ناف"

# 1. إنشاء ملف العميل للاتصال بخدمة الموقع
$clientFilePath = "E:\yemen gps\location-client.js"
$clientContent = @'
// دالة للحصول على الموقع من الخدمة المخصصة
async function getLocationFromCustomService() {
  try {
    const response = await fetch('http://localhost:3001/api/location');
    const locationData = await response.json();
    return {
      coords: {
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy
      },
      timestamp: locationData.timestamp
    };
  } catch (error) {
    console.error('خطأ في الحصول على الموقع من الخدمة المخصصة:', error);
    // استخدام موقع افتراضي في حالة الفشل
    return {
      coords: {
        latitude: 15.3694,
        longitude: 44.191,
        accuracy: 10
      },
      timestamp: Date.now()
    };
  }
}
'@
Set-Content -Path $clientFilePath -Value $clientContent -Encoding UTF8
Write-Host "تم إنشاء ملف العميل: $clientFilePath" -ForegroundColor Green

# 2. إنشاء سكريبت مراقبة الخدمة
$monitorScriptPath = "E:\yemen gps\monitor-location-service.ps1"
$monitorScriptContent = @'
# سكريبت لمراقبة خدمة محاكاة الموقع وإعادة تشغيلها إذا توقفت
$servicePath = "E:\yemen gps\location-simulator.js"
$servicePort = 3001

# التحقق مما إذا كانت الخدمة تعمل
try {
    $connection = New-Object System.Net.Sockets.TcpClient("localhost", $servicePort)
    if ($connection.Connected) {
        $connection.Close()
        Write-Host "خدمة محاكاة الموقع تعمل بشكل طبيعي" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "خدمة محاكاة الموقع متوقفة، جاري إعادة تشغيلها..." -ForegroundColor Yellow
}

# إعادة تشغيل الخدمة
try {
    $nodePath = "C:\Program Files\nodejs\node.exe"
    if (!(Test-Path $nodePath)) {
        $nodePath = "node"
    }
    
    # إيقاف أي عمليات سابقة
    $processes = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*location-simulator.js*" }
    if ($processes) {
        $processes | ForEach-Object { Stop-Process -Id $_.Id -Force }
        Write-Host "تم إيقاف العمليات السابقة" -ForegroundColor Yellow
    }
    
    # بدء الخدمة
    Start-Process -FilePath $nodePath -ArgumentList $servicePath -WorkingDirectory "E:\yemen gps" -WindowStyle Hidden
    Write-Host "تم إعادة تشغيل خدمة محاكاة الموقع" -ForegroundColor Green
} catch {
    Write-Host "حدث خطأ أثناء إعادة تشغيل الخدمة: $_" -ForegroundColor Red
    exit 1
}
'@
Set-Content -Path $monitorScriptPath -Value $monitorScriptContent -Encoding UTF8
Write-Host "تم إنشاء سكريبت المراقبة: $monitorScriptPath" -ForegroundColor Green

# 3. إنشاء مهمة مجدولة لتشغيل الخدمة عند بدء تشغيل النظام
$startupTaskName = "YemenNavLocationService"
$startupTaskExists = Get-ScheduledTask -TaskName $startupTaskName -ErrorAction SilentlyContinue

if ($startupTaskExists) {
    Unregister-ScheduledTask -TaskName $startupTaskName -Confirm:$false
    Write-Host "تم حذف المهمة المجدولة السابقة: $startupTaskName" -ForegroundColor Yellow
}

$action = New-ScheduledTaskAction -Execute "node" -Argument "E:\yemen gps\location-simulator.js" -WorkingDirectory "E:\yemen gps"
$trigger = New-ScheduledTaskTrigger -AtStartup
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable

Register-ScheduledTask -TaskName $startupTaskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings
Write-Host "تم إنشاء مهمة مجدولة لتشغيل خدمة محاكاة الموقع عند بدء تشغيل النظام" -ForegroundColor Green

# 4. إنشاء مهمة مجدولة لمراقبة الخدمة كل 5 دقائق
$monitorTaskName = "MonitorYemenNavLocationService"
$monitorTaskExists = Get-ScheduledTask -TaskName $monitorTaskName -ErrorAction SilentlyContinue

if ($monitorTaskExists) {
    Unregister-ScheduledTask -TaskName $monitorTaskName -Confirm:$false
    Write-Host "تم حذف مهمة المراقبة السابقة: $monitorTaskName" -ForegroundColor Yellow
}

$monitorAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$monitorScriptPath`""
$monitorTrigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5)
$monitorPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
$monitorSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

Register-ScheduledTask -TaskName $monitorTaskName -Action $monitorAction -Trigger $monitorTrigger -Principal $monitorPrincipal -Settings $monitorSettings
Write-Host "تم إنشاء مهمة مجدولة لمراقبة خدمة محاكاة الموقع كل 5 دقائق" -ForegroundColor Green

# 5. إنشاء ملف README للتوثيق
$readmePath = "E:\yemen gps\README-LOCATION-SERVICE.txt"
$readmeContent = @'
خدمة محاكاة الموقع لنظام "يمن ناف"
===================================

هذه خدمة بسيطة توفر إحداثيات ثابتة لصنعاء لاستخدامها في تطبيق "يمن ناف".

الملفات المهمة:
- location-simulator.js: الخدمة الرئيسية التي توفر إحداثيات الموقع
- location-client.js: دالة JavaScript للاتصال بالخدمة من التطبيق
- monitor-location-service.ps1: سكريبت PowerShell لمراقبة الخدمة وإعادة تشغيلها إذا توقفت

المهام المجدولة:
- YemenNavLocationService: تشغيل الخدمة عند بدء تشغيل النظام
- MonitorYemenNavLocationService: مراقبة الخدمة كل 5 دقائق وإعادة تشغيلها إذا توقفت

عنوان الخدمة:
- للحصول على الموقع: http://localhost:3001/api/location
- لتعيين موقع مخصص: POST إلى http://localhost:3001/api/location مع بيانات JSON تحتوي على latitude و longitude

لاختبار الخدمة:
1. افتح متصفح ويب وانتقل إلى: http://localhost:3001/api/location
2. يجب أن ترى بيانات JSON تحتوي على إحداثيات صنعاء

لتعديل الموقع الافتراضي:
1. قم بتعديل ملف location-simulator.js وتغيير قيم DEFAULT_LOCATION
2. أعد تشغيل الخدمة باستخدام الأمر: node location-simulator.js
'@
Set-Content -Path $readmePath -Value $readmeContent -Encoding UTF8
Write-Host "تم إنشاء ملف التوثيق: $readmePath" -ForegroundColor Green

# 6. تشغيل المهمة المجدولة للمراقبة فور<|im_start|>
Start-ScheduledTask -TaskName $monitorTaskName
Write-Host "تم تشغيل مهمة المراقبة" -ForegroundColor Green

Write-Host "`nتم إعداد خدمة محاكاة الموقع بنجاح!" -ForegroundColor Green
Write-Host "الخدمة تعمل الآن على المنفذ 3001 وستعمل تلقائ<|im_start|> عند إعادة تشغيل النظام" -ForegroundColor Green
Write-Host "يمكنك اختبار الخدمة بفتح المتصفح والانتقال إلى: http://localhost:3001/api/location" -ForegroundColor Yellow
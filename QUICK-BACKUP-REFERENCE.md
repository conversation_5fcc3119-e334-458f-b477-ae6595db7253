# مرجع سريع للنسخ الاحتياطية - Yemen GPS

## 📍 مواقع ملفات النسخ الاحتياطية

### الملف الحالي الموجود:
```
📁 e:\yemen gps\yemen_gps_backup.sql
```

### النسخ الجديدة (بعد تشغيل النظام):
```
📁 e:\yemen gps\backups\
   ├── yemen_gps_backup_2025-05-24_17-30-45.sql
   ├── yemen_gps_backup_2025-05-25_09-15-30.sql
   └── ...
```

## 🚀 طرق إنشاء نسخة احتياطية

### 1. الطريقة السريعة:
```bash
backup-database.bat
```

### 2. الطريقة المتقدمة:
```bash
node database-backup-manager.js backup
```

### 3. الطريقة اليدوية:
```bash
set PGPASSWORD=admin
pg_dump -h localhost -p 5432 -U yemen -d yemen_gps -f my_backup.sql --verbose --clean --if-exists --create
```

## 📤 نقل النسخة الاحتياطية لجهاز آخر

### الخطوات:
1. **انسخ الملف** من:
   - `e:\yemen gps\yemen_gps_backup.sql` (النسخة الحالية)
   - أو من `e:\yemen gps\backups\` (أحدث نسخة)

2. **انقل الملف** عبر:
   - فلاش ميموري (USB)
   - قرص صلب خارجي  
   - خدمة تخزين سحابية (Google Drive, OneDrive)
   - البريد الإلكتروني (إذا كان الحجم صغير)

3. **ضع الملف** في الجهاز الجديد في مجلد مثل:
   ```
   C:\temp\yemen_gps_backup.sql
   ```

## 📥 استعادة النسخة الاحتياطية على جهاز جديد

### المتطلبات:
- ✅ تثبيت PostgreSQL
- ✅ ملف النسخة الاحتياطية

### الطرق:

#### 1. الطريقة التلقائية (الأسهل):
```bash
restore-on-new-computer.bat
```
ثم أدخل مسار الملف عندما يُطلب منك

#### 2. الطريقة اليدوية:
```bash
set PGPASSWORD=admin
psql -h localhost -p 5432 -U yemen -d postgres -f "C:\temp\yemen_gps_backup.sql"
```

#### 3. باستخدام pgAdmin:
- افتح pgAdmin
- انقر بزر الماوس الأيمن على "Databases"
- اختر "Restore..."
- اختر ملف النسخة الاحتياطية

## 🔍 التحقق من نجاح الاستعادة

```bash
# الاتصال بقاعدة البيانات
psql -h localhost -p 5432 -U yemen -d yemen_gps

# عرض الجداول (يجب أن تظهر 20 جدول)
\dt

# عرض عدد المستخدمين
SELECT COUNT(*) FROM users;

# عرض عدد المواقع
SELECT COUNT(*) FROM locations;

# الخروج
\q
```

## ⚠️ نصائح مهمة

### قبل النقل:
- ✅ تأكد من حجم الملف (يجب أن يكون أكبر من 10 KB)
- ✅ تحقق من تاريخ الملف (أحدث نسخة)
- ✅ اختبر النسخة الاحتياطية قبل النقل

### على الجهاز الجديد:
- ✅ ثبت PostgreSQL أولاً
- ✅ تأكد من تشغيل خدمة PostgreSQL
- ✅ استخدم `restore-on-new-computer.bat` للسهولة

### بعد الاستعادة:
- ✅ تحقق من وجود جميع الجداول (20 جدول)
- ✅ تحقق من البيانات (المستخدمين، المواقع، إلخ)
- ✅ اختبر تسجيل الدخول بالمستخدم admin

## 🆘 حل المشاكل الشائعة

### "role yemen does not exist"
```sql
CREATE USER yemen WITH PASSWORD 'admin';
ALTER USER yemen CREATEDB;
ALTER USER yemen WITH SUPERUSER;
```

### "database yemen_gps already exists"
```sql
DROP DATABASE IF EXISTS yemen_gps;
```

### "PostgreSQL غير مثبت"
- حمل من: https://www.postgresql.org/download/
- ثبت مع الإعدادات الافتراضية

## 📋 قائمة الجداول المتوقعة (20 جدول)

1. users - المستخدمين
2. locations - المواقع
3. categories - التصنيفات
4. clients - العملاء
5. advertisements - الإعلانات
6. events - الأحداث
7. reviews - التقييمات
8. favorites - المفضلة
9. routes - المسارات
10. geo_areas - المناطق الجغرافية
11. notifications - الإشعارات
12. sessions - الجلسات
13. devices - الأجهزة
14. location_images - صور المواقع
15. location_history - تاريخ المواقع
16. system_settings - إعدادات النظام
17. roles - الأدوار
18. permissions - الصلاحيات
19. role_permissions - ربط الأدوار
20. logs - السجلات

## 📞 للمساعدة

راجع الملفات التالية للتفاصيل الكاملة:
- `DATABASE-BACKUP-GUIDE.md` - الدليل الشامل
- `TRANSFER-BACKUP-GUIDE.md` - دليل النقل المفصل

---
**💡 نصيحة:** احتفظ دائماً بأكثر من نسخة احتياطية في أماكن مختلفة!

const express = require('express');
const router = express.Router();
const db = require('../db');
const logger = require('../../utils/logger');

// جلب جميع المستخدمين
router.get('/admin/users', async (req, res) => {
  try {
    const result = await db.query('SELECT id, full_name, username, phone, password, email, created_at, is_admin, is_active FROM users');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إضافة مستخدم جديد
router.post('/users', async (req, res) => {
  const { full_name, username, email, phone, password } = req.body;
  try {
    logger.debug('إضافة مستخدم جديد إلى قاعدة البيانات');
    const result = await db.query(
      `INSERT INTO users (full_name, username, email, phone, password) VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [full_name, username, email, phone, password]
    );
    logger.debug('المستخدم الجديد:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (err) {
    logger.error('خطأ في إضافة المستخدم:', err);
    res.status(500).json({ error: err.message });
  }
});

// حذف مستخدم
router.delete('/users/:id', async (req, res) => {
  try {
    logger.debug('حذف مستخدم من قاعدة البيانات');
    await db.query('DELETE FROM users WHERE id = $1', [req.params.id]);
    logger.debug('حذف المستخدم بنجاح');
    res.json({ success: true });
  } catch (err) {
    logger.error('خطأ في حذف المستخدم:', err);
    res.status(500).json({ error: err.message });
  }
});

router.get('/api/admin/categories', async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM categories');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب التصنيفات' });
  }
});

router.get('/api/admin/clients', async (req, res) => {
  try {
    const result = await db.query('SELECT * FROM clients');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في جلب العملاء' });
  }
});

module.exports = router;

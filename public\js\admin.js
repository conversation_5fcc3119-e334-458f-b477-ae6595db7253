/**
 * ملف جافا سكريبت للوحة تحكم يمن GPS
 * يتضمن جميع الوظائف اللازمة للتفاعل مع قاعدة البيانات وعرض البيانات
 */

// متغيرات عامة
let allUsers = [];
let allLocations = [];
let allCategories = [];
let allClients = [];
let currentUser = null;
let authToken = null;

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة');

    // إضافة تأثيرات بصرية للصفحة
    addVisualEffects();

    // التحقق من المصادقة
    checkAuthentication();

    // إضافة أحداث البحث
    setupSearchEvents();

    // إضافة أحداث النماذج
    setupModalEvents();

    // إضافة حدث تسجيل الخروج
    document.getElementById('logoutBtn').addEventListener('click', logout);
});

/**
 * إضافة تأثيرات بصرية للصفحة
 */
function addVisualEffects() {
    // إضافة تأثير ظهور تدريجي للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // إضافة تأثير نبض للأزرار
    const buttons = document.querySelectorAll('.btn-success');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.animation = 'pulse 0.5s';
        });

        button.addEventListener('animationend', function() {
            this.style.animation = '';
        });
    });

    // إضافة تأثير تلميح للجداول
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.style.transition = 'all 0.3s ease';

        table.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        });

        table.addEventListener('mouseleave', function() {
            this.style.boxShadow = 'none';
        });
    });

    // إضافة نمط CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);
}

/**
 * التحقق من المصادقة
 */
function checkAuthentication() {
    // التحقق من وجود بيانات المستخدم
    const localUser = localStorage.getItem('adminUser');
    const sessionUser = sessionStorage.getItem('adminUser');

    if (localUser) {
        try {
            currentUser = JSON.parse(localUser);
            authToken = localStorage.getItem('yemenGpsToken');
        } catch (e) {
            console.error('خطأ في تحليل بيانات المستخدم من التخزين المحلي:', e);
            logout();
            return;
        }
    } else if (sessionUser) {
        try {
            currentUser = JSON.parse(sessionUser);
            authToken = sessionStorage.getItem('yemenGpsToken');
        } catch (e) {
            console.error('خطأ في تحليل بيانات المستخدم من تخزين الجلسة:', e);
            logout();
            return;
        }
    } else {
        // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
        window.location.href = 'admin-login.html';
        return;
    }

    console.log('تم العثور على بيانات المستخدم:', currentUser);
    updateUserInfo();

    // تحميل البيانات الأولية
    loadInitialData();
}

/**
 * تحديث معلومات المستخدم في الواجهة
 */
function updateUserInfo() {
    if (currentUser) {
        document.getElementById('userName').textContent = currentUser.fullName || currentUser.username;
    }
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات المصادقة
    localStorage.removeItem('yemenGpsToken');
    localStorage.removeItem('adminUser');
    sessionStorage.removeItem('yemenGpsToken');
    sessionStorage.removeItem('adminUser');

    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * تحميل البيانات الأولية
 */
function loadInitialData() {
    console.log('جاري تحميل البيانات الأولية...');

    // تحميل المستخدمين
    loadUsers();

    // تحميل المواقع
    loadLocations();

    // تحميل التصنيفات
    loadCategories();

    // تحميل العملاء
    loadClients();
}

/**
 * إعداد أحداث البحث
 */
function setupSearchEvents() {
    // إضافة حدث البحث لجدول المستخدمين
    const usersSearch = document.getElementById('usersSearch');
    if (usersSearch) {
        usersSearch.addEventListener('input', function() {
            filterUsers(this.value);
        });
    }

    // إضافة حدث البحث لجدول المواقع
    const locationsSearch = document.getElementById('locationsSearch');
    if (locationsSearch) {
        locationsSearch.addEventListener('input', function() {
            filterLocations(this.value);
        });
    }

    // إضافة حدث البحث لجدول التصنيفات
    const categoriesSearch = document.getElementById('categoriesSearch');
    if (categoriesSearch) {
        categoriesSearch.addEventListener('input', function() {
            filterCategories(this.value);
        });
    }

    // إضافة حدث البحث لجدول العملاء
    const clientsSearch = document.getElementById('clientsSearch');
    if (clientsSearch) {
        clientsSearch.addEventListener('input', function() {
            filterClients(this.value);
        });
    }
}

/**
 * تحميل المستخدمين
 */
function loadUsers() {
    showLoading();
    console.log('جاري تحميل المستخدمين...');

    apiRequest('/api/users')
        .then(data => {
            console.log('تم تحميل المستخدمين بنجاح:', data);
            allUsers = data;
            displayUsers(data);
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل المستخدمين:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء تحميل بيانات المستخدمين', 'error');
        });
}

/**
 * عرض المستخدمين في الجدول
 */
function displayUsers(users) {
    const tableBody = document.getElementById('usersTable');
    if (!tableBody) {
        console.error('لم يتم العثور على عنصر جدول المستخدمين');
        return;
    }

    tableBody.innerHTML = '';

    if (!users || users.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="8" class="text-center">لا يوجد مستخدمين</td>';
        tableBody.appendChild(row);
        return;
    }

    console.log('عرض المستخدمين:', users.length);

    users.forEach(user => {
        const row = document.createElement('tr');

        // تحديد فئة حالة المستخدم
        const statusClass = user.is_active ? 'text-success' : 'text-danger';
        const statusText = user.is_active ? 'نشط' : 'غير نشط';

        // تنسيق التاريخ
        let formattedDate = '-';
        if (user.registration_date) {
            const registrationDate = new Date(user.registration_date);
            formattedDate = registrationDate.toLocaleDateString('ar-SA');
        }

        // تحديد اسم الدور
        let roleName = 'مستخدم عادي';
        if (user.role_id === 1 || user.role_name === 'admin') {
            roleName = 'مدير';
        } else if (user.role_id === 3 || user.role_name === 'developer') {
            roleName = 'مطور';
        }

        row.innerHTML = `
            <td>${user.full_name || '-'}</td>
            <td>${user.username || '-'}</td>
            <td>${user.email || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>${roleName}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
            <td>${formattedDate}</td>
            <td>
                <button class="btn btn-sm btn-primary btn-action edit-user-btn" data-id="${user.user_id || user.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-action delete-user-btn" data-id="${user.user_id || user.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // إضافة أحداث للأزرار
    addUserButtonsEvents();
}

/**
 * إضافة أحداث للأزرار في جدول المستخدمين
 */
function addUserButtonsEvents() {
    // أزرار تعديل المستخدم
    const editButtons = document.querySelectorAll('.edit-user-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            editUser(userId);
        });
    });

    // أزرار حذف المستخدم
    const deleteButtons = document.querySelectorAll('.delete-user-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            deleteUser(userId);
        });
    });
}

/**
 * تحميل المواقع
 */
function loadLocations() {
    showLoading();
    console.log('جاري تحميل المواقع...');

    apiRequest('/api/admin/locations')
        .then(data => {
            console.log('تم تحميل المواقع بنجاح:', data);
            allLocations = data;
            displayLocations(data);
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل المواقع:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء تحميل بيانات المواقع', 'error');
        });
}

/**
 * عرض المواقع في الجدول
 */
function displayLocations(locations) {
    const tableBody = document.getElementById('locationsTable');
    if (!tableBody) {
        console.error('لم يتم العثور على عنصر جدول المواقع');
        return;
    }

    tableBody.innerHTML = '';

    if (!locations || locations.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">لا يوجد مواقع</td>';
        tableBody.appendChild(row);
        return;
    }

    console.log('عرض المواقع:', locations.length);

    locations.forEach(location => {
        const row = document.createElement('tr');

        // تحديد فئة حالة الموقع
        const statusClass = location.status === 'active' ? 'text-success' : 'text-warning';
        const statusText = location.status === 'active' ? 'نشط' : 'قيد المراجعة';

        row.innerHTML = `
            <td>${location.id}</td>
            <td>${location.name || '-'}</td>
            <td>${location.description ? location.description.substring(0, 50) + '...' : '-'}</td>
            <td>${location.category_name || '-'}</td>
            <td>${location.lat}, ${location.lng}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
        `;

        tableBody.appendChild(row);
    });
}

/**
 * تحميل التصنيفات
 */
function loadCategories() {
    showLoading();
    console.log('جاري تحميل التصنيفات...');

    apiRequest('/api/categories')
        .then(data => {
            console.log('تم تحميل التصنيفات بنجاح:', data);
            allCategories = data;
            displayCategories(data);
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل التصنيفات:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء تحميل بيانات التصنيفات', 'error');
        });
}

/**
 * عرض التصنيفات في الجدول
 */
function displayCategories(categories) {
    const tableBody = document.getElementById('categoriesTable');
    if (!tableBody) {
        console.error('لم يتم العثور على عنصر جدول التصنيفات');
        return;
    }

    tableBody.innerHTML = '';

    if (!categories || categories.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">لا يوجد تصنيفات</td>';
        tableBody.appendChild(row);
        return;
    }

    console.log('عرض التصنيفات:', categories.length);

    categories.forEach(category => {
        const row = document.createElement('tr');

        // تحديد لون خلفية الأيقونة
        const iconBgColor = category.color || '#198754';

        row.innerHTML = `
            <td>${category.id}</td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="category-icon-wrapper me-2" style="background-color: ${iconBgColor}; width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas ${category.icon || 'fa-tag'}"></i>
                    </div>
                    <div>
                        <strong>${category.name || '-'}</strong>
                    </div>
                </div>
            </td>
            <td>${category.description || '-'}</td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="color-preview me-2" style="width: 20px; height: 20px; border-radius: 50%; background-color: ${category.color || '#198754'}; border: 1px solid #dee2e6;"></div>
                    <span>${category.color || '#198754'}</span>
                </div>
            </td>
            <td><span class="badge bg-success rounded-pill">نشط</span></td>
            <td>
                <button class="btn btn-sm btn-primary btn-action edit-category-btn" data-id="${category.id}">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-action delete-category-btn" data-id="${category.id}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // إضافة أحداث للأزرار
    addCategoryButtonsEvents();
}

/**
 * إضافة أحداث للأزرار في جدول التصنيفات
 */
function addCategoryButtonsEvents() {
    // أزرار تعديل التصنيف
    const editButtons = document.querySelectorAll('.edit-category-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            editCategory(categoryId);
        });
    });

    // أزرار حذف التصنيف
    const deleteButtons = document.querySelectorAll('.delete-category-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            const categoryName = this.closest('tr').querySelector('strong').textContent;
            showDeleteConfirmModal(categoryId, 'category', categoryName);
        });
    });
}

/**
 * تحميل العملاء
 */
function loadClients() {
    showLoading();
    console.log('جاري تحميل العملاء...');

    apiRequest('/api/admin/clients')
        .then(data => {
            console.log('تم تحميل العملاء بنجاح:', data);
            allClients = data;
            displayClients(data);
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل العملاء:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء تحميل بيانات العملاء', 'error');
        });
}

/**
 * عرض العملاء في الجدول
 */
function displayClients(clients) {
    const tableBody = document.getElementById('clientsTable');
    if (!tableBody) {
        console.error('لم يتم العثور على عنصر جدول العملاء');
        return;
    }

    tableBody.innerHTML = '';

    if (!clients || clients.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">لا يوجد عملاء</td>';
        tableBody.appendChild(row);
        return;
    }

    console.log('عرض العملاء:', clients.length);

    clients.forEach(client => {
        const row = document.createElement('tr');

        // تحديد فئة حالة العميل
        const statusClass = client.status === 'active' ? 'text-success' : 'text-danger';
        const statusText = client.status === 'active' ? 'نشط' : 'غير نشط';

        row.innerHTML = `
            <td>${client.id}</td>
            <td>${client.name || '-'}</td>
            <td>${client.email || '-'}</td>
            <td>${client.phone || '-'}</td>
            <td>${client.address || '-'}</td>
            <td><span class="${statusClass}">${statusText}</span></td>
        `;

        tableBody.appendChild(row);
    });
}

/**
 * تصفية المستخدمين
 */
function filterUsers(searchTerm) {
    if (!searchTerm) {
        displayUsers(allUsers);
        return;
    }

    searchTerm = searchTerm.toLowerCase();

    const filteredUsers = allUsers.filter(user => {
        return (
            (user.username && user.username.toLowerCase().includes(searchTerm)) ||
            (user.full_name && user.full_name.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm))
        );
    });

    displayUsers(filteredUsers);
}

/**
 * تصفية المواقع
 */
function filterLocations(searchTerm) {
    if (!searchTerm) {
        displayLocations(allLocations);
        return;
    }

    searchTerm = searchTerm.toLowerCase();

    const filteredLocations = allLocations.filter(location => {
        return (
            (location.name && location.name.toLowerCase().includes(searchTerm)) ||
            (location.description && location.description.toLowerCase().includes(searchTerm)) ||
            (location.category_name && location.category_name.toLowerCase().includes(searchTerm))
        );
    });

    displayLocations(filteredLocations);
}

/**
 * تصفية التصنيفات
 */
function filterCategories(searchTerm) {
    if (!searchTerm) {
        displayCategories(allCategories);
        return;
    }

    searchTerm = searchTerm.toLowerCase();

    const filteredCategories = allCategories.filter(category => {
        return (
            (category.name && category.name.toLowerCase().includes(searchTerm)) ||
            (category.description && category.description.toLowerCase().includes(searchTerm))
        );
    });

    displayCategories(filteredCategories);
}

/**
 * تصفية العملاء
 */
function filterClients(searchTerm) {
    if (!searchTerm) {
        displayClients(allClients);
        return;
    }

    searchTerm = searchTerm.toLowerCase();

    const filteredClients = allClients.filter(client => {
        return (
            (client.name && client.name.toLowerCase().includes(searchTerm)) ||
            (client.email && client.email.toLowerCase().includes(searchTerm)) ||
            (client.phone && client.phone.toLowerCase().includes(searchTerm)) ||
            (client.address && client.address.toLowerCase().includes(searchTerm))
        );
    });

    displayClients(filteredClients);
}

/**
 * إعداد أحداث النماذج
 */
function setupModalEvents() {
    // أزرار إضافة جديد
    document.getElementById('addUserBtn').addEventListener('click', () => showUserModal());
    document.getElementById('addLocationBtn').addEventListener('click', () => showLocationModal());
    document.getElementById('addCategoryBtn').addEventListener('click', () => showCategoryModal());
    document.getElementById('addClientBtn').addEventListener('click', () => showClientModal());

    // أزرار حفظ
    document.getElementById('saveUserBtn').addEventListener('click', saveUser);
    document.getElementById('saveLocationBtn').addEventListener('click', saveLocation);
    document.getElementById('saveCategoryBtn').addEventListener('click', saveCategory);
    document.getElementById('saveClientBtn').addEventListener('click', saveClient);

    // زر تأكيد الحذف
    document.getElementById('confirmDeleteBtn').addEventListener('click', confirmDelete);

    // تحديث معاينة أيقونة التصنيف
    document.getElementById('categoryIcon').addEventListener('input', updateIconPreview);
}

/**
 * تحديث معاينة أيقونة التصنيف
 */
function updateIconPreview() {
    const iconInput = document.getElementById('categoryIcon');
    const iconPreview = document.getElementById('iconPreview');

    // إزالة جميع الفئات
    iconPreview.className = '';

    // إضافة الفئات الجديدة
    if (iconInput.value) {
        iconPreview.classList.add('fas');
        iconPreview.classList.add(iconInput.value);
    } else {
        iconPreview.classList.add('fas');
        iconPreview.classList.add('fa-tag');
    }
}

/**
 * عرض نموذج المستخدم
 */
function showUserModal(userId = null) {
    // إعادة تعيين النموذج
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';

    // تحديث عنوان النموذج
    const modalTitle = document.getElementById('userModalLabel');

    if (userId) {
        // وضع بيانات المستخدم في النموذج
        const user = allUsers.find(u => u.id == userId);
        if (user) {
            console.log('تعبئة بيانات المستخدم في النموذج:', user);

            document.getElementById('userId').value = user.id;
            document.getElementById('fullName').value = user.full_name || '';
            document.getElementById('userUsername').value = user.username || '';
            document.getElementById('userEmail').value = user.email || '';
            document.getElementById('userPhone').value = user.phone || '';
            document.getElementById('userRole').value = user.role_id || 2;
            document.getElementById('userActive').checked = user.is_active === true || user.is_active === 1 || user.is_active === 'true' || user.is_active === 'active';

            modalTitle.textContent = 'تعديل المستخدم';
        } else {
            console.error(`لم يتم العثور على المستخدم رقم ${userId} في المصفوفة`);
            showNotification('لم يتم العثور على بيانات المستخدم', 'error');
        }
    } else {
        modalTitle.textContent = 'إضافة مستخدم جديد';
    }

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

/**
 * حفظ المستخدم
 */
function saveUser() {
    // الحصول على بيانات النموذج
    const userId = document.getElementById('userId').value;
    const fullName = document.getElementById('fullName').value;
    const username = document.getElementById('userUsername').value;
    const email = document.getElementById('userEmail').value;
    const phone = document.getElementById('userPhone').value;
    const password = document.getElementById('userPassword').value;
    const roleId = document.getElementById('userRole').value;
    const isActive = document.getElementById('userActive').checked;

    // التحقق من صحة البيانات
    if (!fullName || !username || !email) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // إعداد بيانات الطلب
    const userData = {
        full_name: fullName,
        username: username,
        email: email,
        phone: phone,
        role_id: roleId,
        is_active: isActive
    };

    // إضافة كلمة المرور إذا تم إدخالها
    if (password) {
        userData.password = password;
    }

    // إظهار مؤشر التحميل
    showLoading();

    // تحديد طريقة الطلب والمسار
    const method = userId ? 'PUT' : 'POST';
    const url = userId ? `/api/admin/users/${userId}` : '/api/admin/users';

    // إرسال الطلب
    apiRequest(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
    })
    .then(response => {
        hideLoading();

        // إغلاق النموذج
        bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();

        // عرض رسالة نجاح
        showNotification(userId ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح');

        // تحديث قائمة المستخدمين
        loadUsers();
    })
    .catch(error => {
        console.error('خطأ في حفظ المستخدم:', error);
        hideLoading();
        showNotification('حدث خطأ أثناء حفظ المستخدم', 'error');
    });
}

/**
 * عرض نموذج الموقع
 */
function showLocationModal(locationId = null) {
    // إعادة تعيين النموذج
    document.getElementById('locationForm').reset();
    document.getElementById('locationId').value = '';

    // ملء قائمة التصنيفات
    const categorySelect = document.getElementById('locationCategory');
    categorySelect.innerHTML = '<option value="">اختر التصنيف</option>';

    allCategories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categorySelect.appendChild(option);
    });

    // تحديث عنوان النموذج
    const modalTitle = document.getElementById('locationModalLabel');

    if (locationId) {
        // وضع بيانات الموقع في النموذج
        const location = allLocations.find(l => l.id == locationId);
        if (location) {
            document.getElementById('locationId').value = location.id;
            document.getElementById('locationName').value = location.name || '';
            document.getElementById('locationDescription').value = location.description || '';
            document.getElementById('locationCategory').value = location.category_id || '';
            document.getElementById('locationLat').value = location.lat || '';
            document.getElementById('locationLng').value = location.lng || '';
            document.getElementById('locationActive').checked = location.status === 'active';

            modalTitle.textContent = 'تعديل الموقع';
        }
    } else {
        modalTitle.textContent = 'إضافة موقع جديد';
    }

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('locationModal'));
    modal.show();
}

/**
 * حفظ الموقع
 */
function saveLocation() {
    // الحصول على بيانات النموذج
    const locationId = document.getElementById('locationId').value;
    const name = document.getElementById('locationName').value;
    const description = document.getElementById('locationDescription').value;
    const categoryId = document.getElementById('locationCategory').value;
    const lat = document.getElementById('locationLat').value;
    const lng = document.getElementById('locationLng').value;
    const isActive = document.getElementById('locationActive').checked;

    // التحقق من صحة البيانات
    if (!name || !categoryId || !lat || !lng) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // إعداد بيانات الطلب
    const locationData = {
        name: name,
        description: description,
        category_id: categoryId,
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        status: isActive ? 'active' : 'inactive'
    };

    // إظهار مؤشر التحميل
    showLoading();

    // تحديد طريقة الطلب والمسار
    const method = locationId ? 'PUT' : 'POST';
    const url = locationId ? `/api/admin/locations/${locationId}` : '/api/admin/locations';

    // إرسال الطلب
    apiRequest(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(locationData)
    })
    .then(response => {
        hideLoading();

        // إغلاق النموذج
        bootstrap.Modal.getInstance(document.getElementById('locationModal')).hide();

        // عرض رسالة نجاح
        showNotification(locationId ? 'تم تحديث الموقع بنجاح' : 'تم إضافة الموقع بنجاح');

        // تحديث قائمة المواقع
        loadLocations();
    })
    .catch(error => {
        console.error('خطأ في حفظ الموقع:', error);
        hideLoading();
        showNotification('حدث خطأ أثناء حفظ الموقع', 'error');
    });
}

/**
 * عرض نموذج التصنيف
 */
function showCategoryModal(categoryId = null) {
    // إعادة تعيين النموذج
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';

    // ملء قائمة التصنيفات الأب
    const parentSelect = document.getElementById('categoryParent');
    parentSelect.innerHTML = '<option value="">بدون تصنيف أب</option>';

    allCategories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        parentSelect.appendChild(option);
    });

    // تحديث عنوان النموذج
    const modalTitle = document.getElementById('categoryModalLabel');

    if (categoryId) {
        // وضع بيانات التصنيف في النموذج
        const category = allCategories.find(c => c.id == categoryId);
        if (category) {
            document.getElementById('categoryId').value = category.id;
            document.getElementById('categoryName').value = category.name || '';
            document.getElementById('categoryDescription').value = category.description || '';
            document.getElementById('categoryIcon').value = category.icon || 'fa-tag';
            document.getElementById('categoryColor').value = category.color || '#198754';
            document.getElementById('categoryParent').value = category.parent_id || '';

            // تحديث معاينة الأيقونة
            updateIconPreview();

            modalTitle.textContent = 'تعديل التصنيف';
        }
    } else {
        modalTitle.textContent = 'إضافة تصنيف جديد';
        updateIconPreview();
    }

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
    modal.show();
}

/**
 * حفظ التصنيف
 */
function saveCategory() {
    // الحصول على بيانات النموذج
    const categoryId = document.getElementById('categoryId').value;
    const name = document.getElementById('categoryName').value;
    const description = document.getElementById('categoryDescription').value;
    const icon = document.getElementById('categoryIcon').value;
    const color = document.getElementById('categoryColor').value;
    const parentId = document.getElementById('categoryParent').value;

    // التحقق من صحة البيانات
    if (!name) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // إعداد بيانات الطلب
    const categoryData = {
        name: name,
        description: description,
        icon: icon,
        color: color,
        parent_id: parentId || null
    };

    // إظهار مؤشر التحميل
    showLoading();

    // تحديد طريقة الطلب والمسار
    const method = categoryId ? 'PUT' : 'POST';
    const url = categoryId ? `/api/admin/categories/${categoryId}` : '/api/admin/categories';

    // إرسال الطلب
    apiRequest(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(categoryData)
    })
    .then(response => {
        hideLoading();

        // إغلاق النموذج
        bootstrap.Modal.getInstance(document.getElementById('categoryModal')).hide();

        // عرض رسالة نجاح
        showNotification(categoryId ? 'تم تحديث التصنيف بنجاح' : 'تم إضافة التصنيف بنجاح');

        // تحديث قائمة التصنيفات
        loadCategories();
    })
    .catch(error => {
        console.error('خطأ في حفظ التصنيف:', error);
        hideLoading();
        showNotification('حدث خطأ أثناء حفظ التصنيف', 'error');
    });
}

/**
 * عرض نموذج العميل
 */
function showClientModal(clientId = null) {
    // إعادة تعيين النموذج
    document.getElementById('clientForm').reset();
    document.getElementById('clientId').value = '';

    // تحديث عنوان النموذج
    const modalTitle = document.getElementById('clientModalLabel');

    if (clientId) {
        // وضع بيانات العميل في النموذج
        const client = allClients.find(c => c.id == clientId);
        if (client) {
            document.getElementById('clientId').value = client.id;
            document.getElementById('clientName').value = client.name || '';
            document.getElementById('clientEmail').value = client.email || '';
            document.getElementById('clientPhone').value = client.phone || '';
            document.getElementById('clientAddress').value = client.address || '';
            document.getElementById('clientDeviceSN').value = client.deviceSN || '';
            document.getElementById('clientLicenseN').value = client.licenseN || '';
            document.getElementById('clientActive').checked = client.status === 'active';

            modalTitle.textContent = 'تعديل العميل';
        }
    } else {
        modalTitle.textContent = 'إضافة عميل جديد';
    }

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('clientModal'));
    modal.show();
}

/**
 * حفظ العميل
 */
function saveClient() {
    // الحصول على بيانات النموذج
    const clientId = document.getElementById('clientId').value;
    const name = document.getElementById('clientName').value;
    const email = document.getElementById('clientEmail').value;
    const phone = document.getElementById('clientPhone').value;
    const address = document.getElementById('clientAddress').value;
    const deviceSN = document.getElementById('clientDeviceSN').value;
    const licenseN = document.getElementById('clientLicenseN').value;
    const isActive = document.getElementById('clientActive').checked;

    // التحقق من صحة البيانات
    if (!name) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // إعداد بيانات الطلب
    const clientData = {
        name: name,
        email: email,
        phone: phone,
        address: address,
        deviceSN: deviceSN,
        licenseN: licenseN,
        status: isActive ? 'active' : 'inactive'
    };

    // إظهار مؤشر التحميل
    showLoading();

    // تحديد طريقة الطلب والمسار
    const method = clientId ? 'PUT' : 'POST';
    const url = clientId ? `/api/admin/clients/${clientId}` : '/api/admin/clients';

    // إرسال الطلب
    apiRequest(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientData)
    })
    .then(response => {
        hideLoading();

        // إغلاق النموذج
        bootstrap.Modal.getInstance(document.getElementById('clientModal')).hide();

        // عرض رسالة نجاح
        showNotification(clientId ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح');

        // تحديث قائمة العملاء
        loadClients();
    })
    .catch(error => {
        console.error('خطأ في حفظ العميل:', error);
        hideLoading();
        showNotification('حدث خطأ أثناء حفظ العميل', 'error');
    });
}

/**
 * تعديل مستخدم
 */
function editUser(userId) {
    console.log(`تعديل المستخدم رقم ${userId}`);

    // البحث عن المستخدم في المصفوفة
    const user = allUsers.find(u => u.id == userId);

    if (!user) {
        console.error(`لم يتم العثور على المستخدم رقم ${userId}`);
        showNotification('لم يتم العثور على المستخدم', 'error');
        return;
    }

    console.log('بيانات المستخدم:', user);
    showUserModal(userId);
}

/**
 * تعديل موقع
 */
function editLocation(locationId) {
    showLocationModal(locationId);
}

/**
 * تعديل تصنيف
 */
function editCategory(categoryId) {
    showCategoryModal(categoryId);
}

/**
 * تعديل عميل
 */
function editClient(clientId) {
    showClientModal(clientId);
}

/**
 * عرض نموذج تأكيد الحذف
 */
function showDeleteConfirmModal(itemId, itemType, itemName) {
    // تعيين بيانات العنصر
    document.getElementById('deleteItemId').value = itemId;
    document.getElementById('deleteItemType').value = itemType;

    // تحديث رسالة التأكيد
    let message = 'هل أنت متأكد من حذف هذا العنصر؟';

    switch (itemType) {
        case 'user':
            message = `هل أنت متأكد من حذف المستخدم "${itemName}"؟`;
            break;
        case 'location':
            message = `هل أنت متأكد من حذف الموقع "${itemName}"؟`;
            break;
        case 'category':
            message = `هل أنت متأكد من حذف التصنيف "${itemName}"؟`;
            break;
        case 'client':
            message = `هل أنت متأكد من حذف العميل "${itemName}"؟`;
            break;
    }

    document.getElementById('deleteConfirmMessage').textContent = message;

    // عرض النموذج
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

/**
 * تأكيد الحذف
 */
function confirmDelete() {
    // الحصول على بيانات العنصر
    const itemId = document.getElementById('deleteItemId').value;
    const itemType = document.getElementById('deleteItemType').value;

    // إغلاق النموذج
    bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();

    // تنفيذ الحذف
    switch (itemType) {
        case 'user':
            deleteUser(itemId);
            break;
        case 'location':
            deleteLocation(itemId);
            break;
        case 'category':
            deleteCategory(itemId);
            break;
        case 'client':
            deleteClient(itemId);
            break;
    }
}

/**
 * حذف مستخدم
 */
function deleteUser(userId) {
    showLoading();

    apiRequest(`/api/admin/users/${userId}`, { method: 'DELETE' })
        .then(response => {
            hideLoading();
            showNotification('تم حذف المستخدم بنجاح');

            // تحديث قائمة المستخدمين
            loadUsers();
        })
        .catch(error => {
            console.error('خطأ في حذف المستخدم:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء حذف المستخدم', 'error');
        });
}

/**
 * حذف موقع
 */
function deleteLocation(locationId) {
    showLoading();

    apiRequest(`/api/admin/locations/${locationId}`, { method: 'DELETE' })
        .then(response => {
            hideLoading();
            showNotification('تم حذف الموقع بنجاح');

            // تحديث قائمة المواقع
            loadLocations();
        })
        .catch(error => {
            console.error('خطأ في حذف الموقع:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء حذف الموقع', 'error');
        });
}

/**
 * حذف تصنيف
 */
function deleteCategory(categoryId) {
    showLoading();

    apiRequest(`/api/admin/categories/${categoryId}`, { method: 'DELETE' })
        .then(response => {
            hideLoading();
            showNotification('تم حذف التصنيف بنجاح');

            // تحديث قائمة التصنيفات
            loadCategories();
        })
        .catch(error => {
            console.error('خطأ في حذف التصنيف:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء حذف التصنيف', 'error');
        });
}

/**
 * حذف عميل
 */
function deleteClient(clientId) {
    showLoading();

    apiRequest(`/api/admin/clients/${clientId}`, { method: 'DELETE' })
        .then(response => {
            hideLoading();
            showNotification('تم حذف العميل بنجاح');

            // تحديث قائمة العملاء
            loadClients();
        })
        .catch(error => {
            console.error('خطأ في حذف العميل:', error);
            hideLoading();
            showNotification('حدث خطأ أثناء حذف العميل', 'error');
        });
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json'
    };

    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };

    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = 'http://localhost:3000' + endpoint;
    }

    // طباعة معلومات الطلب للتشخيص
    console.log(`طلب API: ${endpoint}`);
    console.log(`نوع الطلب: ${options.method || 'GET'}`);

    if (options.body) {
        console.log(`بيانات الطلب:`, JSON.parse(options.body));
    }

    // إضافة معلمة لتجنب التخزين المؤقت
    const nocacheEndpoint = endpoint + (endpoint.includes('?') ? '&' : '?') + '_nocache=' + new Date().getTime();

    // إرسال الطلب
    return fetch(nocacheEndpoint, requestOptions)
        .then(response => {
            // طباعة معلومات الاستجابة
            console.log(`حالة الاستجابة: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                console.error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log(`بيانات الاستجابة:`, data);
            return data;
        })
        .catch(error => {
            console.error(`خطأ في طلب API: ${endpoint}`, error);

            // في حالة فشل الاتصال، نعرض رسالة خطأ محددة
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                showNotification('فشل الاتصال بالخادم. تأكد من تشغيل الخادم وإعادة المحاولة.', 'error');
            }

            throw error;
        });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('show');
    }
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('show');
    }
}

/**
 * إظهار إشعار
 */
function showNotification(message, type = 'success') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.remove('show');

        // إزالة الإشعار من الصفحة بعد انتهاء التأثير
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * دالة مساعدة لإرسال طلبات API
 */
function apiRequest(endpoint, options = {}) {
    const baseUrl = window.location.origin; // استخدام نفس المضيف والمنفذ
    const url = `${baseUrl}${endpoint}`;

    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    // دمج الخيارات
    const finalOptions = { ...defaultOptions, ...options };

    console.log(`إرسال طلب API إلى: ${url}`);

    return fetch(url, finalOptions)
        .then(response => {
            console.log(`استجابة API من ${url}:`, response.status);

            if (!response.ok) {
                throw new Error(`خطأ في الطلب: ${response.status} ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log(`بيانات API من ${url}:`, data);
            return data;
        })
        .catch(error => {
            console.error(`خطأ في طلب API ${url}:`, error);
            throw error;
        });
}

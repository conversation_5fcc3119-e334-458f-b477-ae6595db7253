package com.yemengps.app.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.yemengps.app.model.User;

import java.util.List;

/**
 * Data Access Object for the User entity
 */
@Dao
public interface UserDao {

    /**
     * Insert a new user
     * @param user The user to insert
     * @return The row ID of the inserted user
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(User user);

    /**
     * Update a user
     * @param user The user to update
     */
    @Update
    void update(User user);

    /**
     * Delete a user
     * @param user The user to delete
     */
    @Delete
    void delete(User user);

    /**
     * Get a user by ID
     * @param id The ID of the user
     * @return The user
     */
    @Query("SELECT * FROM users WHERE id = :id")
    LiveData<User> getUserById(String id);

    /**
     * Get a user by email
     * @param email The email of the user
     * @return The user
     */
    @Query("SELECT * FROM users WHERE email = :email")
    LiveData<User> getUserByEmail(String email);

    /**
     * Get a user by device ID
     * @param deviceId The device ID of the user
     * @return The user
     */
    @Query("SELECT * FROM users WHERE deviceId = :deviceId")
    LiveData<User> getUserByDeviceId(String deviceId);

    /**
     * Get all users
     * @return All users
     */
    @Query("SELECT * FROM users WHERE active = 1")
    LiveData<List<User>> getAllUsers();

    /**
     * Get all admin users
     * @return All admin users
     */
    @Query("SELECT * FROM users WHERE isAdmin = 1 AND active = 1")
    LiveData<List<User>> getAllAdmins();

    /**
     * Check if a user exists with the given email
     * @param email The email to check
     * @return True if a user exists with the given email, false otherwise
     */
    @Query("SELECT EXISTS(SELECT 1 FROM users WHERE email = :email)")
    boolean userExistsByEmail(String email);

    /**
     * Check if a user exists with the given device ID
     * @param deviceId The device ID to check
     * @return True if a user exists with the given device ID, false otherwise
     */
    @Query("SELECT EXISTS(SELECT 1 FROM users WHERE deviceId = :deviceId)")
    boolean userExistsByDeviceId(String deviceId);

    /**
     * Update a user's authentication token
     * @param id The ID of the user
     * @param authToken The new authentication token
     */
    @Query("UPDATE users SET authToken = :authToken WHERE id = :id")
    void updateAuthToken(String id, String authToken);

    /**
     * Update a user's last login date
     * @param id The ID of the user
     */
    @Query("UPDATE users SET lastLoginAt = CURRENT_TIMESTAMP WHERE id = :id")
    void updateLastLoginDate(String id);

    /**
     * Deactivate a user
     * @param id The ID of the user to deactivate
     */
    @Query("UPDATE users SET active = 0 WHERE id = :id")
    void deactivateUser(String id);

    /**
     * Activate a user
     * @param id The ID of the user to activate
     */
    @Query("UPDATE users SET active = 1 WHERE id = :id")
    void activateUser(String id);

    /**
     * Make a user an administrator
     * @param id The ID of the user to make an administrator
     */
    @Query("UPDATE users SET isAdmin = 1 WHERE id = :id")
    void makeAdmin(String id);

    /**
     * Remove administrator privileges from a user
     * @param id The ID of the user to remove administrator privileges from
     */
    @Query("UPDATE users SET isAdmin = 0 WHERE id = :id")
    void removeAdmin(String id);
}

// يمن ناف - ملف لتطبيق مخطط الصلاحيات على قاعدة البيانات
const fs = require('fs');
const path = require('path');
const db = require('./postgres-db');

// قراءة ملف SQL
const sqlFilePath = path.join(__dirname, 'permissions-schema.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// تقسيم الملف إلى أوامر SQL منفصلة
const sqlCommands = sqlContent
  .replace(/--.*$/gm, '') // إزالة التعليقات
  .split(';')
  .filter(cmd => cmd.trim() !== '');

// تنفيذ الأوامر SQL
async function executeQueries() {
  const client = await db.getClient();
  
  try {
    await client.query('BEGIN');
    
    console.log(`تنفيذ ${sqlCommands.length} أمر SQL...`);
    
    for (let i = 0; i < sqlCommands.length; i++) {
      const cmd = sqlCommands[i].trim();
      if (cmd) {
        try {
          await client.query(cmd);
          console.log(`تم تنفيذ الأمر ${i + 1} بنجاح`);
        } catch (err) {
          console.error(`خطأ في تنفيذ الأمر ${i + 1}:`, err.message);
          // استمر في التنفيذ حتى لو فشل أمر واحد
        }
      }
    }
    
    await client.query('COMMIT');
    console.log('تم تنفيذ جميع الأوامر بنجاح');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('حدث خطأ أثناء تنفيذ الأوامر:', err);
  } finally {
    client.release();
    // إغلاق الاتصال بقاعدة البيانات
    await db.end();
  }
}

// تنفيذ الأوامر
executeQueries().catch(err => {
  console.error('خطأ غير متوقع:', err);
  process.exit(1);
});

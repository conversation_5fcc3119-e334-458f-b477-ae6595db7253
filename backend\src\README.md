# إعداد قاعدة بيانات PostgreSQL ليمن ناف

هذا الدليل يشرح كيفية إعداد قاعدة بيانات PostgreSQL لاستخدامها مع تطبيق يمن ناف.

## المتطلبات

- PostgreSQL 14 أو أحدث
- Node.js 14 أو أحدث
- npm 6 أو أحدث

## خطوات الإعداد

### 1. تثبيت PostgreSQL

#### على نظام Windows

1. قم بتنزيل وتثبيت PostgreSQL من [الموقع الرسمي](https://www.postgresql.org/download/windows/)
2. أثناء التثبيت، قم بتعيين كلمة مرور للمستخدم `postgres`
3. يمكنك اختيار تثبيت pgAdmin كواجهة رسومية لإدارة قاعدة البيانات

#### على نظام Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

#### على نظام macOS

```bash
brew install postgresql
```

### 2. إنشاء قاعدة البيانات والمستخدم

#### باستخدام psql

1. قم بتسجيل الدخول إلى PostgreSQL:

```bash
# على Windows (افتح موجه الأوامر كمسؤول)
psql -U postgres

# على Linux
sudo -u postgres psql
```

2. قم بإنشاء المستخدم وقاعدة البيانات:

```sql
CREATE USER yemen WITH PASSWORD 'admin';
CREATE DATABASE yemen_gps OWNER yemen;
GRANT ALL PRIVILEGES ON DATABASE yemen_gps TO yemen;
```

3. قم بالاتصال بقاعدة البيانات:

```sql
\c yemen_gps
```

#### باستخدام pgAdmin

1. افتح pgAdmin
2. قم بإنشاء مستخدم جديد باسم `yemen` وكلمة مرور `admin`
3. قم بإنشاء قاعدة بيانات جديدة باسم `yemen_gps` وتعيين المالك إلى `yemen`

### 3. إنشاء جداول قاعدة البيانات

#### باستخدام ملف SQL

1. قم بتنفيذ ملف `postgres-schema.sql` لإنشاء الجداول:

```bash
# على Windows
psql -U yemen -d yemen_gps -f backend/src/postgres-schema.sql

# على Linux
sudo -u postgres psql -d yemen_gps -f backend/src/postgres-schema.sql
```

#### باستخدام pgAdmin

1. افتح pgAdmin
2. اتصل بقاعدة البيانات `yemen_gps`
3. افتح أداة الاستعلام
4. قم بنسخ محتوى ملف `postgres-schema.sql` ولصقه في أداة الاستعلام
5. قم بتنفيذ الاستعلام

### 4. تكوين التطبيق

1. قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع:

```
DB_TYPE=postgres
DB_USER=yemen
DB_PASSWORD=admin
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yemen_gps
```

2. قم بتثبيت حزم Node.js المطلوبة:

```bash
npm install pg
```

## اختبار الاتصال

يمكنك اختبار الاتصال بقاعدة البيانات باستخدام الأمر التالي:

```bash
node -e "const { Pool } = require('pg'); const pool = new Pool({ user: 'yemen', password: 'admin', host: 'localhost', port: 5432, database: 'yemen_gps' }); pool.query('SELECT NOW()', (err, res) => { console.log(err ? err : res.rows[0]); pool.end(); });"
```

إذا نجح الاتصال، سترى التاريخ والوقت الحاليين.

## استكشاف الأخطاء وإصلاحها

### مشكلة: لا يمكن الاتصال بقاعدة البيانات

- تأكد من أن خدمة PostgreSQL قيد التشغيل
- تحقق من صحة بيانات الاتصال (اسم المستخدم، كلمة المرور، المضيف، المنفذ، اسم قاعدة البيانات)
- تأكد من أن المستخدم لديه صلاحيات كافية للوصول إلى قاعدة البيانات

### مشكلة: خطأ في إنشاء الجداول

- تأكد من أن المستخدم لديه صلاحيات كافية لإنشاء الجداول
- تحقق من عدم وجود أخطاء في ملف `postgres-schema.sql`

## الاستخدام في لوحة الإدارة

بعد إعداد قاعدة البيانات، يمكنك الوصول إلى لوحة الإدارة واستخدام وظائف إدارة العملاء والمستخدمين والتصنيفات ونقاط الموقع.

1. افتح المتصفح وانتقل إلى `http://localhost:3000/admin.html`
2. انقر على زر "تفعيل وضع الإدارة" في الزاوية العلوية اليسرى
3. انقر على زر "فحص الاتصال" للتحقق من الاتصال بقاعدة البيانات

## ملاحظات إضافية

- يتم تخزين كلمات المرور بشكل مشفر باستخدام bcrypt
- يتم تحديث حقل `updated_at` تلقائيًا عند تحديث أي سجل
- يمكنك استخدام واجهة برمجة التطبيق (API) للوصول إلى البيانات من خلال `/api/admin/postgres/`

// public/js/routing/astar.js
class AStarRouter {
  constructor(roadNetwork) {
    this.roadNetwork = roadNetwork;
  }
  
  // حساب المسار باستخدام خوارزمية A*
  findPath(startNode, endNode) {
    const openSet = new PriorityQueue();
    const closedSet = new Set();
    const cameFrom = new Map();
    
    const gScore = new Map();
    const fScore = new Map();
    
    gScore.set(startNode.id, 0);
    fScore.set(startNode.id, this.heuristic(startNode, endNode));
    
    openSet.enqueue(startNode, fScore.get(startNode.id));
    
    while (!openSet.isEmpty()) {
      const current = openSet.dequeue();
      
      if (current.id === endNode.id) {
        return this.reconstructPath(cameFrom, current);
      }
      
      closedSet.add(current.id);
      
      for (const neighbor of this.roadNetwork.getNeighbors(current)) {
        if (closedSet.has(neighbor.id)) {
          continue;
        }
        
        const tentativeGScore = gScore.get(current.id) + this.roadNetwork.getDistance(current, neighbor);
        
        if (!gScore.has(neighbor.id) || tentativeGScore < gScore.get(neighbor.id)) {
          cameFrom.set(neighbor.id, current);
          gScore.set(neighbor.id, tentativeGScore);
          fScore.set(neighbor.id, gScore.get(neighbor.id) + this.heuristic(neighbor, endNode));
          
          if (!openSet.contains(neighbor)) {
            openSet.enqueue(neighbor, fScore.get(neighbor.id));
          }
        }
      }
    }
    
    return null; // لم يتم العثور على مسار
  }
  
  // دالة التقدير (المسافة الإقليدية)
  heuristic(nodeA, nodeB) {
    const dx = nodeA.x - nodeB.x;
    const dy = nodeA.y - nodeB.y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  // إعادة بناء المسار
  reconstructPath(cameFrom, current) {
    const path = [current];
    
    while (cameFrom.has(current.id)) {
      current = cameFrom.get(current.id);
      path.unshift(current);
    }
    
    return path;
  }
}

// صف الأولوية لخوارزمية A*
class PriorityQueue {
  constructor() {
    this.elements = [];
  }
  
  isEmpty() {
    return this.elements.length === 0;
  }
  
  enqueue(element, priority) {
    this.elements.push({ element, priority });
    this.elements.sort((a, b) => a.priority - b.priority);
  }
  
  dequeue() {
    return this.elements.shift().element;
  }
  
  contains(element) {
    return this.elements.some(item => item.element.id === element.id);
  }
}

// إضافة الوحدة إلى النافذة العالمية
window.AStarRouter = AStarRouter;

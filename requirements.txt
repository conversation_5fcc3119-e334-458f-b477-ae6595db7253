# Yemen Maps Complete - Python Dependencies
# متطلبات المشروع

# Flask Framework
Flask==2.3.3
Flask-CORS==4.0.0

# Database
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21

# Google APIs
googlemaps==4.10.0
google-api-python-client==2.100.0
google-auth==2.23.3
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1

# Image Processing
Pillow==10.0.1
opencv-python==4.8.1.78

# HTTP Requests
requests==2.31.0
urllib3==2.0.5

# Data Processing
pandas==2.1.1
numpy==1.25.2

# JSON and Data
jsonschema==4.19.1

# Utilities
python-dotenv==1.0.0
pathlib2==2.3.7
uuid==1.30

# Logging and Monitoring
colorlog==6.7.0

# Development Tools
pytest==7.4.2
pytest-flask==1.2.0

# Map Tiles Server
TileStache==1.51.15

# Geospatial
Shapely==2.0.1
Fiona==1.9.4
geopandas==0.14.0

# Date and Time
python-dateutil==2.8.2

# Configuration
configparser==6.0.0

# Progress Bars
tqdm==4.66.1

# Async Support
aiohttp==3.8.6
asyncio==3.4.3

# File Operations
watchdog==3.0.0

# Caching
redis==5.0.0
flask-caching==2.1.0

# Security
cryptography==41.0.4
bcrypt==4.0.1

# Environment
python-decouple==3.8

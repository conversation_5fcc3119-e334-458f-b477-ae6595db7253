/**
 * Yemen Maps Database Helper - وحدة مساعدة لقاعدة بيانات خرائط اليمن
 * توفر وظائف مساعدة للتعامل مع قاعدة البيانات وعرض الصور ومعلومات Google Places
 */

// مدير الصور
class ImageManager {
    // الحصول على مسار الصورة
    static getImagePath(placeId, index = 0) {
        if (!placeId) return 'assets/images/placeholder.jpg';
        return `/images/places/${placeId}_${index}.jpg`;
    }
    
    // التحقق من وجود صورة
    static checkImageExists(url) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = url;
        });
    }
    
    // الحصول على صور المكان
    static async getPlaceImages(placeId, maxImages = 5) {
        if (!placeId) return ['assets/images/placeholder.jpg'];
        
        const images = [];
        let imageFound = false;
        
        // تحقق من وجود صور
        for (let i = 0; i < maxImages; i++) {
            const imagePath = this.getImagePath(placeId, i);
            const exists = await this.checkImageExists(imagePath);
            
            if (exists) {
                images.push(imagePath);
                imageFound = true;
            }
        }
        
        // إذا لم يتم العثور على صور، استخدم الصورة الافتراضية
        if (!imageFound) {
            images.push('assets/images/placeholder.jpg');
        }
        
        return images;
    }
}

// مدير المواقع المحفوظة
class SavedPlacesManager {
    constructor() {
        this.savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
    }
    
    // التحقق من حفظ مكان
    isPlaceSaved(locationId) {
        return this.savedPlaces.some(place => place.id === locationId);
    }
    
    // حفظ مكان
    savePlace(locationId, name, lat, lng) {
        const index = this.savedPlaces.findIndex(place => place.id === locationId);
        
        if (index !== -1) {
            // إزالة المكان من المواقع المحفوظة
            this.savedPlaces.splice(index, 1);
            return false; // تم إلغاء الحفظ
        } else {
            // إضافة المكان إلى المواقع المحفوظة
            this.savedPlaces.push({
                id: locationId,
                name: name,
                latitude: lat,
                longitude: lng,
                saved_at: new Date().toISOString()
            });
            return true; // تم الحفظ
        }
    }
    
    // حفظ في قاعدة البيانات
    savePlaceToDatabase(locationId) {
        const userId = localStorage.getItem('user_id') || 'guest';
        
        return fetch('/api/save-place', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                location_id: locationId,
                user_id: userId,
                saved_at: new Date().toISOString()
            }),
        })
        .then(response => response.json())
        .catch(error => {
            console.error('خطأ في حفظ المكان في قاعدة البيانات:', error);
            return { error: true, message: error.message };
        });
    }
    
    // الحصول على المواقع المحفوظة
    getSavedPlaces() {
        return this.savedPlaces;
    }
    
    // حفظ التغييرات في التخزين المحلي
    saveChanges() {
        localStorage.setItem('savedPlaces', JSON.stringify(this.savedPlaces));
    }
}

// مشاركة المواقع
class LocationSharer {
    // مشاركة موقع
    static shareLocation(lat, lng, name) {
        if (navigator.share) {
            // استخدام واجهة برمجة المشاركة إذا كانت متاحة
            return navigator.share({
                title: name,
                text: `موقع ${name} على خرائط اليمن`,
                url: `${window.location.origin}?lat=${lat}&lng=${lng}&name=${encodeURIComponent(name)}`
            })
            .then(() => true)
            .catch((error) => {
                console.error('خطأ في المشاركة:', error);
                return false;
            });
        } else {
            // استخدام آلية النسخ إذا لم تكن واجهة المشاركة متاحة
            const shareUrl = `${window.location.origin}?lat=${lat}&lng=${lng}&name=${encodeURIComponent(name)}`;
            
            try {
                // إنشاء عنصر مؤقت للنسخ
                const el = document.createElement('textarea');
                el.value = shareUrl;
                document.body.appendChild(el);
                el.select();
                document.execCommand('copy');
                document.body.removeChild(el);
                return true;
            } catch (e) {
                console.error('خطأ في نسخ الرابط:', e);
                return false;
            }
        }
    }
    
    // التعامل مع المواقع المشاركة من URL
    static handleSharedLocation(map) {
        const urlParams = new URLSearchParams(window.location.search);
        const lat = urlParams.get('lat');
        const lng = urlParams.get('lng');
        const name = urlParams.get('name');
        
        if (lat && lng) {
            // عرض الموقع المشارك على الخريطة
            map.setView([lat, lng], 15);
            
            // إنشاء علامة للموقع المشارك
            const marker = L.marker([lat, lng]).addTo(map);
            marker.bindPopup(`<div class="custom-popup"><h3>${name || 'الموقع المشارك'}</h3></div>`).openPopup();
            
            // إزالة المعلمات من URL
            window.history.replaceState({}, document.title, window.location.pathname);
            
            return true;
        }
        
        return false;
    }
}

// مدير بيانات Google Places
class GooglePlacesManager {
    // الحصول على بيانات الموقع من Google Places
    static async getPlaceDetails(placeId) {
        if (!placeId) return null;
        
        try {
            const response = await fetch(`/api/google-place-details?place_id=${placeId}`);
            if (!response.ok) throw new Error('Failed to fetch place details');
            
            return await response.json();
        } catch (error) {
            console.error('خطأ في الحصول على تفاصيل Google Places:', error);
            return null;
        }
    }
    
    // الحصول على صور الموقع من Google Places
    static async getPlacePhotos(placeId, maxPhotos = 5) {
        if (!placeId) return [];
        
        try {
            const response = await fetch(`/api/google-place-photos?place_id=${placeId}&max_photos=${maxPhotos}`);
            if (!response.ok) throw new Error('Failed to fetch place photos');
            
            return await response.json();
        } catch (error) {
            console.error('خطأ في الحصول على صور Google Places:', error);
            return [];
        }
    }
    
    // دمج بيانات Google Places مع بيانات الموقع المحلية
    static async enrichLocationWithGoogleData(location) {
        if (!location || !location.google_place_id) return location;
        
        try {
            // الحصول على تفاصيل الموقع من Google
            const placeDetails = await this.getPlaceDetails(location.google_place_id);
            if (!placeDetails) return location;
            
            // الحصول على صور الموقع من Google
            const placePhotos = await this.getPlacePhotos(location.google_place_id);
            
            // دمج البيانات
            return {
                ...location,
                google_data: placeDetails,
                description: location.description || placeDetails.editorial_summary || placeDetails.formatted_address,
                photos: placePhotos,
                address: location.address || placeDetails.formatted_address,
                contact: location.contact || placeDetails.formatted_phone_number,
                website: location.website || placeDetails.website,
                rating: placeDetails.rating,
                hours: placeDetails.opening_hours ? placeDetails.opening_hours.weekday_text : null,
                categories: placeDetails.types || [],
                reviews: placeDetails.reviews || []
            };
        } catch (error) {
            console.error('خطأ في دمج بيانات Google Places:', error);
            return location;
        }
    }
    
    // دمج بيانات Google Places مع قائمة من المواقع
    static async enrichLocationsWithGoogleData(locations) {
        if (!locations || !Array.isArray(locations)) return locations;
        
        // معالجة كل موقع بشكل متوازي
        const enrichedLocations = await Promise.all(
            locations.map(async location => {
                if (location.google_place_id) {
                    return await this.enrichLocationWithGoogleData(location);
                }
                return location;
            })
        );
        
        return enrichedLocations;
    }
    
    // تنسيق معلومات التصنيف
    static formatCategoryInfo(categories) {
        if (!categories || !Array.isArray(categories) || categories.length === 0) {
            return { icon: 'fa-map-marker-alt', name: 'موقع' };
        }
        
        // قاموس من التصنيفات والأيقونات المقابلة
        const categoryIcons = {
            'restaurant': { icon: 'fa-utensils', name: 'مطعم' },
            'cafe': { icon: 'fa-coffee', name: 'مقهى' },
            'hotel': { icon: 'fa-hotel', name: 'فندق' },
            'shopping_mall': { icon: 'fa-shopping-cart', name: 'مركز تسوق' },
            'school': { icon: 'fa-school', name: 'مدرسة' },
            'university': { icon: 'fa-university', name: 'جامعة' },
            'hospital': { icon: 'fa-hospital', name: 'مستشفى' },
            'pharmacy': { icon: 'fa-prescription-bottle-alt', name: 'صيدلية' },
            'mosque': { icon: 'fa-mosque', name: 'مسجد' },
            'park': { icon: 'fa-tree', name: 'حديقة' },
            'tourist_attraction': { icon: 'fa-monument', name: 'معلم سياحي' },
            'gas_station': { icon: 'fa-gas-pump', name: 'محطة وقود' },
            'bank': { icon: 'fa-university', name: 'بنك' },
            'atm': { icon: 'fa-credit-card', name: 'صراف آلي' },
            'airport': { icon: 'fa-plane', name: 'مطار' },
            'bus_station': { icon: 'fa-bus', name: 'محطة حافلات' },
            'train_station': { icon: 'fa-train', name: 'محطة قطار' },
            'library': { icon: 'fa-book', name: 'مكتبة' },
            'gym': { icon: 'fa-dumbbell', name: 'صالة رياضية' },
            'store': { icon: 'fa-store', name: 'متجر' },
            'supermarket': { icon: 'fa-shopping-basket', name: 'سوبرماركت' },
            'post_office': { icon: 'fa-mail-bulk', name: 'مكتب بريد' },
            'police': { icon: 'fa-shield-alt', name: 'مركز شرطة' },
            'fire_station': { icon: 'fa-fire-extinguisher', name: 'مركز إطفاء' },
            'cinema': { icon: 'fa-film', name: 'سينما' }
        };
        
        // البحث عن أول فئة معروفة
        for (const category of categories) {
            if (categoryIcons[category]) {
                return categoryIcons[category];
            }
        }
        
        // إذا لم يتم العثور على فئة معروفة، استخدم القيمة الافتراضية
        return { icon: 'fa-map-marker-alt', name: 'موقع' };
    }
    
    // تنسيق ساعات العمل
    static formatOpeningHours(hours) {
        if (!hours || !Array.isArray(hours) || hours.length === 0) {
            return null;
        }
        
        // تنسيق ساعات العمل للعرض في نافذة المعلومات
        return hours.map(day => {
            // ترجمة أيام الأسبوع إلى العربية
            const arabicDay = day.replace('Monday', 'الاثنين')
                                .replace('Tuesday', 'الثلاثاء')
                                .replace('Wednesday', 'الأربعاء')
                                .replace('Thursday', 'الخميس')
                                .replace('Friday', 'الجمعة')
                                .replace('Saturday', 'السبت')
                                .replace('Sunday', 'الأحد');
            
            return arabicDay;
        });
    }
    
    // تنسيق التقييمات
    static formatRating(rating) {
        if (!rating) return null;
        
        // تنسيق التقييم لعرضه بالنجوم
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        return {
            rating: rating,
            stars: {
                full: fullStars,
                half: halfStar ? 1 : 0,
                empty: emptyStars
            }
        };
    }
}

// تصدير الوظائف
window.YemenDB = {
    ImageManager,
    SavedPlacesManager,
    LocationSharer,
    GooglePlacesManager
};

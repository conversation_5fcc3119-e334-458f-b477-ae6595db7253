// يمن ناف - ملف JavaScript للميزات المتقدمة للخرائط
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود عنصر الخريطة
    if (document.getElementById('map')) {
        initAdvancedMap();
    }
});

// المتغيرات العامة
let advancedMap; // كائن الخريطة
let heatmapLayer; // طبقة خريطة الحرارة
let drawnItems; // العناصر المرسومة
let markerClusters; // مجموعات العلامات
let currentRoute; // المسار الحالي
let isothermLayer; // طبقة خطوط التساوي
let routeControl; // التحكم بالمسارات
let geocoder; // محول العنوان إلى إحداثيات

// تكوين الخريطة المتقدمة
function initAdvancedMap() {
    console.log("تهيئة الخريطة المتقدمة...");
    
    // إنشاء الخريطة مع التركيز على صنعاء
    advancedMap = L.map('map', {
        center: [15.3694, 44.1910], // صنعاء
        zoom: 13,
        zoomControl: false, // سيتم إضافته في مكان مخصص لاحقاً
        attributionControl: true,
    });
    
    // إضافة التحكم بالتكبير إلى الزاوية اليمنى العلوية (مع مراعاة RTL)
    L.control.zoom({
        position: 'topright'
    }).addTo(advancedMap);

    // تعيين advancedMap كمرجع عالمي للخريطة
    window.map = advancedMap;
    
    // إضافة طبقات الخريطة المختلفة
    setupBaseLayers();
    
    // إضافة ميزة البحث عن الموقع
    setupGeocoding();
    
    // إضافة ميزة تجميع العلامات
    setupMarkerClusters();
    
    // إضافة ميزة رسم العناصر على الخريطة
    setupDrawControls();
    
    // إضافة ميزة قياس المسافات
    setupMeasurement();
    
    // إضافة ميزة توجيه المرور
    setupRouting();
    
    // إضافة ميزة خرائط الحرارة
    setupHeatmap();
    
    // إضافة ميزة تحديد الموقع الحالي
    setupGeolocation();
    
    // إضافة ميزة مشاركة الخريطة
    setupSharingOptions();
    
    // إضافة معلومات الموقع عند النقر
    setupLocationInfo();
    
    // إضافة خطوط التساوي الحرارية
    setupIsotherms();
    
    // إعداد القدرة على حفظ الخريطة كصورة
    setupMapExport();
    
    // تحميل نقاط الاهتمام الافتراضية
    loadDefaultPOIs();
    
    console.log("تم تهيئة الخريطة المتقدمة بنجاح");
}

// إعداد طبقات الخريطة الأساسية
function setupBaseLayers() {
    // طبقة OpenStreetMap الأساسية
    const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors | يمن ناف',
        maxZoom: 19
    });
    
    // طبقة صور الأقمار الصناعية
    const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: 'خريطة صور | Esri | يمن ناف',
        maxZoom: 19
    });
    
    // طبقة أسماء المناطق (تستخدم مع طبقة الأقمار الصناعية)
    const labelsLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/Reference/World_Boundaries_and_Places/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri',
        maxZoom: 19,
        className: 'yellow-labels' // لتطبيق اللون الأصفر على التسميات
    });
    
    // طبقة التضاريس
    const terrainLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://opentopomap.org">OpenTopoMap</a> | يمن ناف',
        maxZoom: 17
    });
    
    // طبقة الوضع الليلي المظلم
    const darkLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
        attribution: '© <a href="https://carto.com/">CARTO</a> | يمن ناف',
        maxZoom: 19
    });
    
    // طبقة خالية من الألوان (أبيض وأسود)
    const grayscaleLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> | يمن ناف',
        maxZoom: 19,
        className: 'map-grayscale' // سيتم تطبيق تأثير الأبيض والأسود عبر CSS
    });

    // إنشاء مجموعة طبقات للأقمار الصناعية مع أسماء المناطق
    const satelliteWithLabelsGroup = L.layerGroup([satelliteLayer, labelsLayer]);
    
    // إعداد طبقات القاعدة للتبديل بينها
    const baseLayers = {
        "خريطة الشوارع": osmLayer,
        "صور الأقمار الصناعية": satelliteWithLabelsGroup,
        "خريطة التضاريس": terrainLayer,
        "الوضع الليلي": darkLayer,
        "أبيض وأسود": grayscaleLayer
    };
    
    // إضافة طبقة افتراضية للخريطة
    osmLayer.addTo(advancedMap);
    
    // إعداد طبقات إضافية يمكن تفعيلها/إلغاءها
    const overlays = {};
    
    // إضافة تحكم الطبقات
    L.control.layers(baseLayers, overlays, {
        position: 'bottomright',
        collapsed: true
    }).addTo(advancedMap);

    // إضافة CSS للطبقة الرمادية
    const style = document.createElement('style');
    style.textContent = `.map-grayscale { filter: grayscale(100%) !important; -webkit-filter: grayscale(100%) !important; }`;
    document.head.appendChild(style);
    
    // تعريف وظيفة لتبديل طبقات الخريطة (للاستخدام من واجهة المستخدم)
    window.setMapLayer = function(layerType) {
        // إزالة جميع الطبقات الحالية
        advancedMap.eachLayer(function(layer) {
            if (layer instanceof L.TileLayer) {
                advancedMap.removeLayer(layer);
            }
        });
        
        // إضافة الطبقة المطلوبة
        switch (layerType) {
            case 'osm':
                osmLayer.addTo(advancedMap);
                break;
            case 'satellite':
                satelliteWithLabelsGroup.addTo(advancedMap);
                break;
            case 'terrain':
                terrainLayer.addTo(advancedMap);
                break;
            case 'dark':
                darkLayer.addTo(advancedMap);
                break;
            case 'grayscale':
                grayscaleLayer.addTo(advancedMap);
                break;
            default:
                osmLayer.addTo(advancedMap);
                break;
        }
    };
}

// إعداد ميزة البحث عن الموقع
function setupGeocoding() {
    // إضافة محول العناوين Nominatim (OpenStreetMap)
    geocoder = L.Control.geocoder({
        defaultMarkGeocode: false, // لن يتم إضافة علامة تلقائيًا
        position: 'topleft',
        placeholder: 'بحث عن موقع...',
        errorMessage: 'لم يتم العثور على الموقع',
        suggestMinLength: 3,
        suggestTimeout: 250,
        queryMinLength: 1,
        collapsed: true,
        autoClose: true, // إغلاق قائمة النتائج تلقائيًا بعد الاختيار
        showResultIcons: true // إظهار أيقونات بجانب النتائج
    }).on('markgeocode', function(e) {
        // عند العثور على موقع والنقر عليه
        const bbox = e.geocode.bbox;
        advancedMap.fitBounds([
            [bbox.getSouth(), bbox.getWest()],
            [bbox.getNorth(), bbox.getEast()]
        ]);
        
        // إضافة علامة في الموقع
        const marker = L.marker(e.geocode.center).addTo(advancedMap);
        marker.bindPopup(`<b>${e.geocode.name}</b>`).openPopup();
        
        // إخفاء واجهة البحث بعد النقر على نتيجة
        if (geocoder._collapse) {
            geocoder._collapse();
        }
    }).addTo(advancedMap);
    
    // إضافة حدث النقر على الخريطة لإغلاق نتائج البحث
    advancedMap.on('click', function() {
        // إخفاء واجهة البحث عند النقر على الخريطة
        if (geocoder._collapse) {
            geocoder._collapse();
        }
        
        // إغلاق أي نتائج بحث مفتوحة
        const geocoderResultsList = document.querySelector('.leaflet-control-geocoder-alternatives');
        if (geocoderResultsList && geocoderResultsList.style.display !== 'none') {
            geocoderResultsList.style.display = 'none';
        }
    });
    
    // تخصيص مظهر محول العناوين ليناسب الواجهة العربية RTL
    const geocoderContainer = document.querySelector('.leaflet-control-geocoder');
    if (geocoderContainer) {
        geocoderContainer.style.direction = 'rtl';
        geocoderContainer.style.textAlign = 'right';
    }
}

// إعداد ميزة تجميع العلامات
function setupMarkerClusters() {
    // إنشاء مجموعة للعلامات المتجمعة
    markerClusters = L.markerClusterGroup({
        disableClusteringAtZoom: 18, // تعطيل التجميع عند التكبير بشدة
        spiderfyOnMaxZoom: true, // تمديد المجموعات عند أقصى تكبير
        showCoverageOnHover: false, // عدم إظهار منطقة التغطية عند التحويم
        zoomToBoundsOnClick: true, // التكبير إلى حدود المجموعة عند النقر
        maxClusterRadius: 40, // نصف قطر التجميع الأقصى بالبكسل
        iconCreateFunction: function(cluster) {
            // تخصيص أيقونة المجموعة
            const count = cluster.getChildCount();
            let size, className;
            
            if (count < 10) {
                size = 'small';
                className = 'cluster-small';
            } else if (count < 100) {
                size = 'medium';
                className = 'cluster-medium';
            } else {
                size = 'large';
                className = 'cluster-large';
            }
            
            return L.divIcon({
                html: `<div><span>${count}</span></div>`,
                className: `marker-cluster marker-cluster-${size} ${className}`,
                iconSize: L.point(40, 40)
            });
        }
    });
    
    // إضافة طبقة المجموعات إلى الخريطة
    advancedMap.addLayer(markerClusters);
    
    // إضافة CSS لتخصيص مظهر المجموعات
    const style = document.createElement('style');
    style.textContent = `
        .marker-cluster-small { background-color: rgba(181, 226, 140, 0.6); }
        .marker-cluster-small div { background-color: rgba(110, 204, 57, 0.6); }
        
        .marker-cluster-medium { background-color: rgba(241, 211, 87, 0.6); }
        .marker-cluster-medium div { background-color: rgba(240, 194, 12, 0.6); }
        
        .marker-cluster-large { background-color: rgba(253, 156, 115, 0.6); }
        .marker-cluster-large div { background-color: rgba(241, 128, 23, 0.6); }
        
        .marker-cluster { 
            background-clip: padding-box;
            border-radius: 20px;
        }
        
        .marker-cluster div {
            width: 30px;
            height: 30px;
            margin-left: 5px;
            margin-top: 5px;
            text-align: center;
            border-radius: 15px;
            font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .marker-cluster span {
            color: white;
            font-weight: bold;
        }
    `;
    document.head.appendChild(style);
}

// إعداد ميزة رسم العناصر على الخريطة
function setupDrawControls() {
    // إنشاء مجموعة للعناصر المرسومة
    drawnItems = new L.FeatureGroup();
    advancedMap.addLayer(drawnItems);
    
    // إعداد خيارات الرسم
    const drawOptions = {
        position: 'topright',
        draw: {
            polyline: {
                shapeOptions: {
                    color: '#4CAF50',
                    weight: 5
                },
                showLength: true, // عرض طول الخط
                metric: true // استخدام النظام المتري
            },
            polygon: {
                allowIntersection: false, // منع التقاطعات
                drawError: {
                    color: '#e1e100',
                    message: '<strong>خطأ:</strong> لا يمكن للمضلع أن يتقاطع مع نفسه!'
                },
                shapeOptions: {
                    color: '#3388ff',
                    fillOpacity: 0.3
                },
                showArea: true // عرض المساحة
            },
            rectangle: {
                shapeOptions: {
                    color: '#f44336',
                    fillOpacity: 0.3
                }
            },
            circle: {
                shapeOptions: {
                    color: '#ff9800',
                    fillOpacity: 0.3
                }
            },
            marker: {
                // هنا يمكن تخصيص أيقونة العلامة إذا لزم الأمر
            }
        },
        edit: {
            featureGroup: drawnItems, // المجموعة القابلة للتعديل
            remove: true, // تفعيل زر الحذف
            edit: true // تفعيل زر التعديل
        }
    };
    
    // إنشاء أداة الرسم وإضافتها إلى الخريطة
    const drawControl = new L.Control.Draw(drawOptions);
    advancedMap.addControl(drawControl);
    
    // معالجة حدث إنشاء عنصر جديد
    advancedMap.on(L.Draw.Event.CREATED, function(event) {
        const layer = event.layer;
        const type = event.layerType;
        
        // إضافة نافذة منبثقة للعنصر المرسوم
        if (type === 'marker') {
            layer.bindPopup('علامة جديدة');
        } else if (type === 'circle') {
            const radius = layer.getRadius().toFixed(2);
            layer.bindPopup(`دائرة (نصف القطر: ${radius} متر)`);
        } else if (type === 'polygon') {
            const area = L.GeometryUtil.geodesicArea(layer.getLatLngs()[0]).toFixed(2);
            layer.bindPopup(`مضلع (المساحة: ${area} متر مربع)`);
        } else if (type === 'polyline') {
            const length = L.GeometryUtil.geodesicLength(layer.getLatLngs()).toFixed(2);
            layer.bindPopup(`خط (الطول: ${length} متر)`);
        } else if (type === 'rectangle') {
            const bounds = layer.getBounds();
            const area = (bounds.getNorthEast().distanceTo(bounds.getSouthEast()) *
                       bounds.getNorthEast().distanceTo(bounds.getNorthWest())).toFixed(2);
            layer.bindPopup(`مستطيل (المساحة: ${area} متر مربع)`);
        }
        
        // إضافة العنصر إلى المجموعة
        drawnItems.addLayer(layer);
        
        // عرض معلومات العنصر المرسوم
        console.log(`تم إنشاء عنصر ${type} جديد`);
    });
    
    // معالجة حدث تعديل العناصر
    advancedMap.on(L.Draw.Event.EDITED, function(e) {
        const layers = e.layers;
        const countOfEditedLayers = layers.getLayers().length;
        console.log(`تم تحرير ${countOfEditedLayers} من العناصر`);
        
        // تحديث النوافذ المنبثقة بعد التعديل
        layers.eachLayer(function(layer) {
            updatePopupContent(layer);
        });
    });
    
    // معالجة حدث حذف العناصر
    advancedMap.on(L.Draw.Event.DELETED, function(e) {
        const layers = e.layers;
        const countOfDeletedLayers = layers.getLayers().length;
        console.log(`تم حذف ${countOfDeletedLayers} من العناصر`);
    });
    
    // وظيفة لتحديث محتوى النوافذ المنبثقة بعد التعديل
    function updatePopupContent(layer) {
        if (layer instanceof L.Marker) {
            layer.setPopupContent('علامة (تم تحريرها)');
        } else if (layer instanceof L.Circle) {
            const radius = layer.getRadius().toFixed(2);
            layer.setPopupContent(`دائرة (نصف القطر: ${radius} متر)`);
        } else if (layer instanceof L.Polygon) {
            const area = L.GeometryUtil.geodesicArea(layer.getLatLngs()[0]).toFixed(2);
            layer.setPopupContent(`مضلع (المساحة: ${area} متر مربع)`);
        } else if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
            const length = L.GeometryUtil.geodesicLength(layer.getLatLngs()).toFixed(2);
            layer.setPopupContent(`خط (الطول: ${length} متر)`);
        } else if (layer instanceof L.Rectangle) {
            const bounds = layer.getBounds();
            const area = (bounds.getNorthEast().distanceTo(bounds.getSouthEast()) *
                       bounds.getNorthEast().distanceTo(bounds.getNorthWest())).toFixed(2);
            layer.setPopupContent(`مستطيل (المساحة: ${area} متر مربع)`);
        }
    }
}

// إعداد ميزة قياس المسافات
function setupMeasurement() {
    try {
        // إضافة أداة قياس المسافة
        const measureControl = new L.control.measure({
            position: 'topright',
            primaryLengthUnit: 'meters',
            secondaryLengthUnit: 'kilometers',
            primaryAreaUnit: 'sqmeters',
            secondaryAreaUnit: 'hectares',
            activeColor: '#4CAF50',
            completedColor: '#ff9800',
            localization: 'ar', // استخدام اللغة العربية إذا كانت متوفرة
            popupOptions: {
                className: 'leaflet-measure-resultpopup',
                autoPanPadding: [10, 10]
            },
            captureZIndex: 10000
        });
        measureControl.addTo(advancedMap);

        // إضافة CSS لتخصيص أدوات القياس بدعم RTL
        const style = document.createElement('style');
        style.textContent = `
            .leaflet-control-measure {
                direction: rtl;
            }
            
            .leaflet-measure-resultpopup {
                direction: rtl;
                text-align: right;
            }
        `;
        document.head.appendChild(style);
    } catch (error) {
        console.error("خطأ في إعداد أداة القياس:", error);
    }
}

// إعداد ميزة توجيه المرور
function setupRouting() {
    // إضافة تحكم التوجيه
    routeControl = L.Routing.control({
        position: 'topright',
        lineOptions: {
            styles: [{
                color: '#4CAF50',
                opacity: 0.7,
                weight: 6
            }],
            addWaypoints: true,
            missingRouteTolerance: 0
        },
        router: L.Routing.osrmv1({
            serviceUrl: 'https://router.project-osrm.org/route/v1',
            profile: 'driving', // يمكن تغييره إلى walking أو cycling
            useHints: true,
            suppressDemoServerWarning: true,
            language: 'ar' // اللغة العربية للتوجيهات إذا كانت متوفرة
        }),
        geocoder: L.Control.Geocoder.nominatim(),
        routeWhileDragging: true,
        reverseWaypoints: false,
        showAlternatives: true,
        altLineOptions: {
            styles: [
                {color: '#9370DB', opacity: 0.5, weight: 5},
                {color: '#4169E1', opacity: 0.5, weight: 5},
                {color: '#6495ED', opacity: 0.5, weight: 5}
            ]
        },
        createMarker: function(i, wp, nWps) {
            return L.marker(wp.latLng, {
                draggable: true,
                icon: L.divIcon({
                    className: 'custom-route-marker',
                    html: `<div class="route-marker">${i+1}</div>`,
                    iconSize: [25, 25]
                })
            });
        }
    }).addTo(advancedMap);
    
    // إخفاء التحكم بالتوجيه في البداية
    hideRoutingControl();
    
    // إضافة زر لإظهار/إخفاء تحكم التوجيه
    const toggleRoutingButton = L.control({
        position: 'topright'
    });
    
    toggleRoutingButton.onAdd = function() {
        const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-toggle-routing');
        container.innerHTML = '<a href="#" title="إظهار/إخفاء التوجيه" role="button" aria-label="إظهار/إخفاء التوجيه"><span>🚗</span></a>';
        
        container.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleRoutingControl();
            return false;
        };
        
        return container;
    };
    
    toggleRoutingButton.addTo(advancedMap);
    
    // وظيفة لإخفاء تحكم التوجيه
    function hideRoutingControl() {
        const routingContainer = document.querySelector('.leaflet-routing-container');
        if (routingContainer) {
            routingContainer.style.display = 'none';
        }
    }
    
    // وظيفة لإظهار تحكم التوجيه
    function showRoutingControl() {
        const routingContainer = document.querySelector('.leaflet-routing-container');
        if (routingContainer) {
            routingContainer.style.display = 'block';
        }
    }
    
    // وظيفة لتبديل حالة تحكم التوجيه
    function toggleRoutingControl() {
        const routingContainer = document.querySelector('.leaflet-routing-container');
        if (routingContainer) {
            if (routingContainer.style.display === 'none') {
                showRoutingControl();
            } else {
                hideRoutingControl();
            }
        }
    }

    // إضافة CSS لتخصيص علامات التوجيه ودعم RTL
    const style = document.createElement('style');
    style.textContent = `
        .custom-route-marker {
            background-color: transparent;
        }
        
        .route-marker {
            border-radius: 50%;
            background-color: #4CAF50;
            color: white;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
        }
        
        .leaflet-routing-container {
            direction: rtl;
            text-align: right;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
            padding: 10px;
            margin-right: 10px !important;
        }
        
        .leaflet-routing-alt {
            max-height: 300px;
            overflow-y: auto;
            padding: 5px;
        }
        
        .leaflet-routing-geocoder input {
            direction: rtl;
            text-align: right;
            width: 100%;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin-bottom: 5px;
        }
    `;
    document.head.appendChild(style);
}

// إعداد ميزة خرائط الحرارة
function setupHeatmap() {
    // إضافة CSS للزر
    const style = document.createElement('style');
    style.textContent = `
        .leaflet-control-heatmap {
            background-color: white;
            padding: 5px;
            border-radius: 3px;
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
            cursor: pointer;
        }
    `;
    document.head.appendChild(style);
    
    // إنشاء زر لتفعيل/تعطيل خريطة الحرارة
    const heatmapControl = L.control({
        position: 'topright'
    });
    
    heatmapControl.onAdd = function() {
        const container = L.DomUtil.create('div', 'leaflet-control leaflet-control-heatmap');
        container.innerHTML = '<span title="خريطة الحرارة">🔥</span>';
        
        container.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleHeatmap();
            return false;
        };
        
        return container;
    };
    
    heatmapControl.addTo(advancedMap);
    
    // تفعيل/تعطيل خريطة الحرارة
    function toggleHeatmap() {
        if (!heatmapLayer) {
            // إنشاء بيانات عشوائية في المنطقة المحيطة بمركز الخريطة
            const center = advancedMap.getCenter();
            const heatmapData = generateRandomHeatmapData(center, 100, 0.05);
            
            // إنشاء طبقة خريطة الحرارة
            heatmapLayer = L.heatLayer(heatmapData, {
                radius: 25,
                blur: 15,
                maxZoom: 17,
                max: 1.0,
                gradient: {
                    0.4: 'blue',
                    0.6: 'lime',
                    0.8: 'yellow',
                    1.0: 'red'
                }
            }).addTo(advancedMap);
            
            // تغيير مظهر الزر عند تفعيل الخريطة
            document.querySelector('.leaflet-control-heatmap').style.backgroundColor = '#4CAF50';
            document.querySelector('.leaflet-control-heatmap span').style.color = 'white';
        } else {
            // إزالة طبقة خريطة الحرارة
            advancedMap.removeLayer(heatmapLayer);
            heatmapLayer = null;
            
            // استعادة مظهر الزر الأصلي
            document.querySelector('.leaflet-control-heatmap').style.backgroundColor = 'white';
            document.querySelector('.leaflet-control-heatmap span').style.color = 'black';
        }
    }
    
    // وظيفة لإنشاء بيانات عشوائية لخريطة الحرارة
    function generateRandomHeatmapData(center, count, spread) {
        const data = [];
        for (let i = 0; i < count; i++) {
            // إنشاء نقاط عشوائية حول المركز
            const lat = center.lat + (Math.random() * 2 - 1) * spread;
            const lng = center.lng + (Math.random() * 2 - 1) * spread;
            
            // تحديد كثافة عشوائية
            const intensity = Math.random();
            
            data.push([lat, lng, intensity]);
        }
        return data;
    }
}

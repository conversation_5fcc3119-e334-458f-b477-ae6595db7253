/**
 * واجهة برمجة التطبيقات الخاصة بلوحة تحكم يمن GPS
 * توفر جميع نقاط النهاية اللازمة للتفاعل مع قاعدة البيانات بشكل مباشر
 */

const express = require('express');
const router = express.Router();
const db = require('../db');
const logger = require('../../utils/logger');

// ===== واجهات المستخدمين =====

/**
 * الحصول على جميع المستخدمين
 */
router.get('/admin/users', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT u.id, u.username, u.full_name, u.email, u.phone, 
                   u.role_id, u.is_active, u.registration_date, u.last_login
            FROM users u
            ORDER BY u.registration_date DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب المستخدمين:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات المستخدمين' });
    }
});

/**
 * الحصول على عدد المستخدمين
 */
router.get('/admin/users/count', async (req, res) => {
    try {
        const result = await db.query('SELECT COUNT(*) as count FROM users');
        res.json({ count: result.rows[0].count });
    } catch (error) {
        logger.error('خطأ في جلب عدد المستخدمين:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب عدد المستخدمين' });
    }
});

/**
 * الحصول على آخر المستخدمين المسجلين
 */
router.get('/admin/users/recent', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT id, username, full_name, email, phone, 
                   is_active, registration_date
            FROM users
            ORDER BY registration_date DESC
            LIMIT 5
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب آخر المستخدمين:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات آخر المستخدمين' });
    }
});

/**
 * إضافة مستخدم جديد
 */
router.post('/admin/users', async (req, res) => {
    try {
        const { full_name, username, email, phone, role_id, is_active } = req.body;
        
        // التحقق من البيانات المطلوبة
        if (!full_name || !username) {
            return res.status(400).json({ error: 'يرجى توفير الاسم الكامل واسم المستخدم' });
        }
        
        // التحقق من عدم وجود مستخدم بنفس اسم المستخدم
        const existingUser = await db.query('SELECT id FROM users WHERE username = $1', [username]);
        if (existingUser.rows.length > 0) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }
        
        // إدخال المستخدم الجديد
        const result = await db.query(`
            INSERT INTO users (full_name, username, email, phone, role_id, is_active, registration_date)
            VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
            RETURNING id, username, full_name, email, phone, role_id, is_active, registration_date
        `, [full_name, username, email, phone, role_id || 2, is_active || true]);
        
        logger.info('تمت إضافة مستخدم جديد:', username);
        res.status(201).json(result.rows[0]);
    } catch (error) {
        logger.error('خطأ في إضافة مستخدم جديد:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء إضافة المستخدم الجديد' });
    }
});

/**
 * تحديث بيانات مستخدم
 */
router.put('/admin/users/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        const { full_name, username, email, phone, role_id, is_active } = req.body;
        
        // التحقق من البيانات المطلوبة
        if (!full_name || !username) {
            return res.status(400).json({ error: 'يرجى توفير الاسم الكامل واسم المستخدم' });
        }
        
        // التحقق من وجود المستخدم
        const userCheck = await db.query('SELECT id FROM users WHERE id = $1', [userId]);
        if (userCheck.rows.length === 0) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
        const existingUser = await db.query('SELECT id FROM users WHERE username = $1 AND id != $2', [username, userId]);
        if (existingUser.rows.length > 0) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }
        
        // تحديث بيانات المستخدم
        const result = await db.query(`
            UPDATE users
            SET full_name = $1, username = $2, email = $3, phone = $4, role_id = $5, is_active = $6
            WHERE id = $7
            RETURNING id, username, full_name, email, phone, role_id, is_active
        `, [full_name, username, email, phone, role_id, is_active, userId]);
        
        logger.info('تم تحديث بيانات المستخدم:', username);
        res.json(result.rows[0]);
    } catch (error) {
        logger.error('خطأ في تحديث بيانات المستخدم:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء تحديث بيانات المستخدم' });
    }
});

/**
 * حذف مستخدم
 */
router.delete('/admin/users/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        
        // التحقق من وجود المستخدم
        const userCheck = await db.query('SELECT id, username FROM users WHERE id = $1', [userId]);
        if (userCheck.rows.length === 0) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        // حذف المستخدم
        await db.query('DELETE FROM users WHERE id = $1', [userId]);
        
        logger.info('تم حذف المستخدم:', userCheck.rows[0].username);
        res.json({ success: true, message: 'تم حذف المستخدم بنجاح' });
    } catch (error) {
        logger.error('خطأ في حذف المستخدم:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء حذف المستخدم' });
    }
});

// ===== واجهات المواقع =====

/**
 * الحصول على جميع المواقع
 */
router.get('/admin/locations', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT l.id, l.name, l.description, l.lat, l.lng, l.address, l.phone, l.website,
                   l.category_id, c.name as category_name, l.status, l.created_at, l.updated_at
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            ORDER BY l.created_at DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب المواقع:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات المواقع' });
    }
});
        
        // تشفير كلمة المرور
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        
        // إدخال المستخدم الجديد
        const result = await db.query(`
            INSERT INTO users (full_name, username, email, phone, password, role_id, is_active, registration_date)
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
            RETURNING id, username, full_name, email, phone, role_id, is_active, registration_date
        `, [full_name, username, email, phone, passwordHash, role_id, is_active]);
        
        logger.info('تمت إضافة مستخدم جديد:', username);
        res.status(201).json(result.rows[0]);
    } catch (error) {
        logger.error('خطأ في إضافة مستخدم جديد:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء إضافة المستخدم الجديد' });
    }
});

/**
 * تحديث بيانات مستخدم
 */
router.put('/admin/users/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        const { full_name, username, email, phone, password, role_id, is_active } = req.body;
        
        // التحقق من البيانات المطلوبة
        if (!full_name || !username) {
            return res.status(400).json({ error: 'يرجى توفير جميع البيانات المطلوبة' });
        }
        
        // التحقق من وجود المستخدم
        const userCheck = await db.query('SELECT id FROM users WHERE id = $1', [userId]);
        if (userCheck.rows.length === 0) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        // التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
        const existingUser = await db.query('SELECT id FROM users WHERE username = $1 AND id != $2', [username, userId]);
        if (existingUser.rows.length > 0) {
            return res.status(400).json({ error: 'اسم المستخدم موجود بالفعل' });
        }
        
        let query = `
            UPDATE users
            SET full_name = $1, username = $2, email = $3, phone = $4, role_id = $5, is_active = $6
        `;
        
        let params = [full_name, username, email, phone, role_id, is_active];
        
        // إذا تم توفير كلمة مرور جديدة، قم بتشفيرها وتحديثها
        if (password) {
            const saltRounds = 10;
            const passwordHash = await bcrypt.hash(password, saltRounds);
            query += ', password = $7';
            params.push(passwordHash);
        }
        
        query += ' WHERE id = $' + (params.length + 1) + ' RETURNING id, username, full_name, email, phone, role_id, is_active';
        params.push(userId);
        
        const result = await db.query(query, params);
        
        logger.info('تم تحديث بيانات المستخدم:', username);
        res.json(result.rows[0]);
    } catch (error) {
        logger.error('خطأ في تحديث بيانات المستخدم:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء تحديث بيانات المستخدم' });
    }
});

/**
 * حذف مستخدم
 */
router.delete('/admin/users/:id', async (req, res) => {
    try {
        const userId = req.params.id;
        
        // التحقق من وجود المستخدم
        const userCheck = await db.query('SELECT id, username FROM users WHERE id = $1', [userId]);
        if (userCheck.rows.length === 0) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }
        
        // حذف المستخدم
        await db.query('DELETE FROM users WHERE id = $1', [userId]);
        
        logger.info('تم حذف المستخدم:', userCheck.rows[0].username);
        res.json({ success: true, message: 'تم حذف المستخدم بنجاح' });
    } catch (error) {
        logger.error('خطأ في حذف المستخدم:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء حذف المستخدم' });
    }
});

// ===== واجهات المواقع =====

/**
 * الحصول على جميع المواقع
 */
router.get('/admin/locations', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT l.id, l.name, l.description, l.lat, l.lng, l.address, l.phone, l.website,
                   l.category_id, c.name as category_name, l.status, l.created_at, l.updated_at,
                   u.username as added_by_username
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            LEFT JOIN users u ON l.added_by = u.id
            ORDER BY l.created_at DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب المواقع:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات المواقع' });
    }
});

/**
 * الحصول على عدد المواقع
 */
router.get('/admin/locations/count', async (req, res) => {
    try {
        const result = await db.query('SELECT COUNT(*) as count FROM locations');
        res.json({ count: parseInt(result.rows[0].count) });
    } catch (error) {
        logger.error('خطأ في جلب عدد المواقع:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب عدد المواقع' });
    }
});

/**
 * الحصول على آخر المواقع المضافة
 */
router.get('/admin/locations/recent', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT l.id, l.name, l.address, l.status, l.created_at,
                   c.name as category_name
            FROM locations l
            LEFT JOIN categories c ON l.category_id = c.id
            ORDER BY l.created_at DESC
            LIMIT 5
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب آخر المواقع:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات آخر المواقع' });
    }
});

// ===== واجهات التصنيفات =====

/**
 * الحصول على جميع التصنيفات
 */
router.get('/admin/categories', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT c.id, c.name, c.icon, c.color, c.parent_id, p.name as parent_name, c.created_at
            FROM categories c
            LEFT JOIN categories p ON c.parent_id = p.id
            ORDER BY c.name
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب التصنيفات:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات التصنيفات' });
    }
});

/**
 * الحصول على عدد التصنيفات
 */
router.get('/admin/categories/count', async (req, res) => {
    try {
        const result = await db.query('SELECT COUNT(*) as count FROM categories');
        res.json({ count: parseInt(result.rows[0].count) });
    } catch (error) {
        logger.error('خطأ في جلب عدد التصنيفات:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب عدد التصنيفات' });
    }
});

// ===== واجهات العملاء =====

/**
 * الحصول على جميع العملاء
 */
router.get('/admin/clients', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT id, name, email, phone, address, deviceSN, licenseN, status, created_at, updated_at
            FROM clients
            ORDER BY created_at DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب العملاء:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات العملاء' });
    }
});

/**
 * الحصول على عدد العملاء
 */
router.get('/admin/clients/count', async (req, res) => {
    try {
        const result = await db.query('SELECT COUNT(*) as count FROM clients');
        res.json({ count: parseInt(result.rows[0].count) });
    } catch (error) {
        logger.error('خطأ في جلب عدد العملاء:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب عدد العملاء' });
    }
});

// ===== واجهات الأحداث =====

/**
 * الحصول على جميع الأحداث
 */
router.get('/admin/events', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT e.id, e.title, e.description, e.location_id, e.custom_location,
                   e.start_date, e.end_date, e.organizer, e.contact_info, e.image_url,
                   e.status, e.created_at, e.updated_at, l.name as location_name
            FROM events e
            LEFT JOIN locations l ON e.location_id = l.id
            ORDER BY e.start_date DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب الأحداث:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات الأحداث' });
    }
});

// ===== واجهات الإعلانات =====

/**
 * الحصول على جميع الإعلانات
 */
router.get('/admin/advertisements', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT a.id, a.title, a.description, a.image_url, a.link_url,
                   a.start_date, a.end_date, a.status, a.position, a.client_id,
                   a.created_at, a.updated_at, c.name as client_name
            FROM advertisements a
            LEFT JOIN clients c ON a.client_id = c.id
            ORDER BY a.start_date DESC
        `);
        
        res.json(result.rows);
    } catch (error) {
        logger.error('خطأ في جلب الإعلانات:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات الإعلانات' });
    }
});

// ===== واجهات إعدادات النظام =====

/**
 * الحصول على إعدادات النظام
 */
router.get('/admin/settings', async (req, res) => {
    try {
        const result = await db.query(`
            SELECT setting_key, setting_value, setting_type, description, is_public
            FROM system_settings
            ORDER BY setting_key
        `);
        
        // تحويل النتائج إلى كائن
        const settings = {};
        result.rows.forEach(row => {
            let value = row.setting_value;
            
            // تحويل القيمة إلى النوع المناسب
            if (row.setting_type === 'boolean') {
                value = value === 'true';
            } else if (row.setting_type === 'number') {
                value = parseFloat(value);
            } else if (row.setting_type === 'json') {
                try {
                    value = JSON.parse(value);
                } catch (e) {
                    value = {};
                }
            }
            
            settings[row.setting_key] = {
                value: value,
                type: row.setting_type,
                description: row.description,
                isPublic: row.is_public
            };
        });
        
        res.json(settings);
    } catch (error) {
        logger.error('خطأ في جلب إعدادات النظام:', error);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب إعدادات النظام' });
    }
});

module.exports = router;

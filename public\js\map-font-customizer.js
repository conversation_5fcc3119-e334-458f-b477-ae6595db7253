/**
 * ملف JavaScript لتخصيص خطوط الخريطة
 * يقوم بتطبيق خط Khalid-Art-bold على جميع عناصر الخريطة بما في ذلك أسماء الأماكن
 */

(function() {
    // التأكد من تحميل Leaflet
    if (typeof L === 'undefined') {
        console.error('Leaflet غير محمل. تأكد من تضمين مكتبة Leaflet قبل هذا الملف.');
        return;
    }

    // تعديل الخط الافتراضي لـ Leaflet
    function customizeMapFonts() {
        // تطبيق الخط على عناصر الخريطة
        document.querySelectorAll('.leaflet-container, .leaflet-popup-content, .leaflet-tooltip, .leaflet-control').forEach(el => {
            el.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
        });

        // تطبيق الخط على العناصر SVG في الخريطة
        document.querySelectorAll('.leaflet-pane svg text').forEach(el => {
            el.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
            el.style.fontWeight = 'bold';
        });

        // تعديل الخط في عناصر الخريطة التي تظهر لاحقًا
        const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // تطبيق الخط على العنصر الجديد
                            if (node.classList && (
                                node.classList.contains('leaflet-popup-content') || 
                                node.classList.contains('leaflet-tooltip') ||
                                node.classList.contains('leaflet-control') ||
                                node.classList.contains('leaflet-marker-icon')
                            )) {
                                node.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
                            }

                            // تطبيق الخط على العناصر الفرعية
                            node.querySelectorAll('.leaflet-popup-content, .leaflet-tooltip, .leaflet-control, svg text').forEach(el => {
                                el.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
                                if (el.tagName.toLowerCase() === 'text') {
                                    el.style.fontWeight = 'bold';
                                }
                            });
                        }
                    });
                }
            });
        });

        // مراقبة التغييرات في DOM
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تعديل طريقة إنشاء النص في Leaflet لاستخدام الخط المخصص
    if (L.Canvas && L.Canvas.prototype._updateText) {
        const originalUpdateText = L.Canvas.prototype._updateText;
        L.Canvas.prototype._updateText = function(layer, position) {
            originalUpdateText.call(this, layer, position);
            // تطبيق الخط المخصص على النص
            if (this._ctx) {
                this._ctx.font = 'bold 12px YemenGPSFont, Arial, sans-serif';
            }
        };
    }

    // تعديل طريقة إنشاء التلميحات في Leaflet
    if (L.Tooltip && L.Tooltip.prototype._initLayout) {
        const originalInitLayout = L.Tooltip.prototype._initLayout;
        L.Tooltip.prototype._initLayout = function() {
            originalInitLayout.call(this);
            this._container.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
        };
    }

    // تعديل طريقة إنشاء النوافذ المنبثقة في Leaflet
    if (L.Popup && L.Popup.prototype._initLayout) {
        const originalInitLayout = L.Popup.prototype._initLayout;
        L.Popup.prototype._initLayout = function() {
            originalInitLayout.call(this);
            this._container.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
            if (this._contentNode) {
                this._contentNode.style.fontFamily = 'YemenGPSFont, Arial, sans-serif';
            }
        };
    }

    // تطبيق التخصيصات عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', customizeMapFonts);
    } else {
        customizeMapFonts();
    }

    // تطبيق التخصيصات عند تحميل الخريطة
    if (window.map) {
        window.map.on('load', customizeMapFonts);
    }

    // تحميل خط Khalid-Art-bold مسبقًا للتأكد من جاهزيته
    const font = new FontFace('Khalid-Art', "url('/fonts/Khalid-Art-bold.ttf')", {
        style: 'normal',
        weight: 'bold',
        display: 'swap'
    });
    
    // إضافة الخط إلى document.fonts
    font.load().then(function(loadedFont) {
        document.fonts.add(loadedFont);
        console.log('تم تحميل خط Khalid-Art-bold بنجاح');
    }).catch(function(error) {
        console.error('فشل في تحميل خط Khalid-Art-bold:', error);
    });
    
    const style = document.createElement('style');
    style.textContent = `
        @font-face {
            font-family: 'Khalid-Art';
            src: url('/fonts/Khalid-Art-bold.ttf') format('truetype');
            font-weight: bold;
            font-style: normal;
            font-display: swap;
        }
        .leaflet-tile-loaded {
            filter: contrast(1.1) saturate(1.05);
        }
    `;
    document.head.appendChild(style);

    console.log('تم تطبيق خط Khalid-Art-bold على عناصر الخريطة');
})();

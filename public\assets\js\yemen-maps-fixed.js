/**
 * Yemen Maps - نظام خرائط اليمن
 * تطبيق خرائط متكامل يحاكي Google Maps ويعمل بدون اتصال بالإنترنت
 */

// تهيئة التطبيق
class YemenMaps {
    constructor() {
        // المتغيرات الأساسية
        this.map = null;
        this.currentMarker = null;
        this.markers = [];
        this.currentLocation = null;
        this.directionsLayer = null;
        this.currentLayer = 'streets';
        this.baseLayers = {};
        this.savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
        this.offlineMode = false;
        
        // تحقق من الاتصال بالإنترنت
        this.checkOnlineStatus();
        
        // تهيئة الخريطة
        this.initMap();
        
        // ربط الأحداث
        this.bindEvents();
        
        // تحميل النقاط من الخادم
        this.loadLocations();
        
        // الحصول على الموقع الحالي
        this.getCurrentLocation();
        
        // تحقق من وجود موقع مشارك
        this.handleSharedLocation();
    }
    
    // تحقق من حالة الاتصال بالإنترنت
    checkOnlineStatus() {
        this.offlineMode = !navigator.onLine;
        
        // إضافة مؤشر وضع عدم الاتصال
        if (this.offlineMode) {
            this.showOfflineIndicator();
        }
        
        window.addEventListener('online', () => {
            this.offlineMode = false;
            this.hideOfflineIndicator();
            this.showNotification('أنت متصل بالإنترنت الآن', 'success');
        });
        
        window.addEventListener('offline', () => {
            this.offlineMode = true;
            this.showOfflineIndicator();
            this.showNotification('أنت الآن في وضع عدم الاتصال بالإنترنت', 'warning');
        });
    }
    
    // إظهار مؤشر وضع عدم الاتصال
    showOfflineIndicator() {
        const offlineIndicator = document.createElement('div');
        offlineIndicator.id = 'offline-indicator';
        offlineIndicator.textContent = 'وضع عدم الاتصال بالإنترنت';
        document.body.appendChild(offlineIndicator);
    }
    
    // إخفاء مؤشر وضع عدم الاتصال
    hideOfflineIndicator() {
        const offlineIndicator = document.getElementById('offline-indicator');
        if (offlineIndicator) {
            offlineIndicator.remove();
        }
    }
    
    // إظهار إشعار للمستخدم
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }
    
    // تهيئة الخريطة
    initMap() {
        // إظهار مؤشر التحميل
        this.showLoading(true);
        
        // حل مشكلة الخريطة في أندرويد - تأخير تهيئة الخريطة
        setTimeout(() => {
            try {
                // إنشاء الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.map = L.map('map', {
                    center: [15.3694, 44.1910], // مركز اليمن (صنعاء)
                    zoom: 7,
                    zoomControl: false,
                    attributionControl: true,
                    maxBounds: [
                        [10.0, 40.0], // الجنوب الغربي
                        [20.0, 55.0]  // الشمال الشرقي
                    ],
                    minZoom: 6,
                    maxZoom: 18,
                    tap: true,            // تمكين النقر للأجهزة اللمسية
                    tapTolerance: 30,     // زيادة مساحة النقر للأجهزة اللمسية
                    bounceAtZoomLimits: false // منع الارتداد عند حدود التكبير
                });
                
                // إضافة طبقات الخريطة مع إعدادات محسنة للأجهزة المحمولة
                this.baseLayers = {
                    streets: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                        maxZoom: 19,
                        subdomains: 'abc',
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,     // تحديث الطبقة فقط عند التوقف لتحسين الأداء
                        updateWhenZooming: false   // لا تحدث أثناء التكبير/التصغير
                    }),
                    
                    satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                        attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                        maxZoom: 18,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    }),
                    
                    terrain: L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://opentopomap.org">OpenTopoMap</a> contributors',
                        maxZoom: 17,
                        crossOrigin: "anonymous",
                        updateWhenIdle: true,
                        updateWhenZooming: false
                    })
                };
                
                // إضافة الطبقة الافتراضية
                this.baseLayers[this.currentLayer].addTo(this.map);
                
                // إعداد طبقة العلامات
                this.markersLayer = L.layerGroup().addTo(this.map);
                
                // إضافة طبقة المسارات
                this.directionsLayer = L.layerGroup().addTo(this.map);
                
                // التعامل مع النقر على الخريطة
                this.map.on('click', (e) => {
                    this.handleMapClick(e);
                });
                
                // إضافة معالجة خاصة لأحداث اللمس على الأجهزة المحمولة
                if (L.Browser.mobile) {
                    // لمعالجة مشكلة النقر على الأجهزة المحمولة
                    document.querySelector('#map').addEventListener('touchend', function(e) {
                        // منع التمرير بعد النقر
                        if (e.touches && e.touches.length === 0) {
                            e.preventDefault();
                        }
                    }, false);
                }
                
                // إنهاء التحميل
                this.map.whenReady(() => {
                    this.showLoading(false);
                    console.log('تم تحميل الخريطة بنجاح');
                    
                    // إعادة ضبط حجم الخريطة بعد التحميل لضمان العرض الصحيح
                    setTimeout(() => {
                        this.map.invalidateSize();
                    }, 200);
                });
            } catch (error) {
                console.error('خطأ في تهيئة الخريطة:', error);
                this.showLoading(false);
            }
        }, 500);
    }
    
    // إظهار أو إخفاء مؤشر التحميل
    showLoading(show) {
        const loader = document.getElementById('loading-indicator');
        if (loader) {
            loader.style.display = show ? 'flex' : 'none';
        }
    }
}

// إنشاء كائن YemenMaps عند تحميل الصفحة
let yemenMaps;
document.addEventListener('DOMContentLoaded', () => {
    yemenMaps = new YemenMaps();
});

package com.yemengps.app.model;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.yemengps.app.data.converters.DateConverter;

import java.util.Date;

/**
 * Entity representing a user in the system
 */
@Entity(tableName = "users")
@TypeConverters(DateConverter.class)
public class User {

    @PrimaryKey
    @NonNull
    private String id;
    
    private String name;
    private String email;
    private String phone;
    private String deviceId;
    private String authToken;
    private boolean isAdmin;
    private Date createdAt;
    private Date lastLoginAt;
    private boolean active;

    /**
     * Default constructor
     */
    public User() {
        this.id = java.util.UUID.randomUUID().toString();
        this.createdAt = new Date();
        this.lastLoginAt = new Date();
        this.active = true;
        this.isAdmin = false;
    }

    /**
     * Constructor with parameters
     * 
     * @param name The user's name
     * @param email The user's email
     * @param phone The user's phone number
     * @param deviceId The user's device ID
     */
    public User(String name, String email, String phone, String deviceId) {
        this();
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.deviceId = deviceId;
    }

    /**
     * Get the user's ID
     * @return The ID
     */
    @NonNull
    public String getId() {
        return id;
    }

    /**
     * Set the user's ID
     * @param id The ID
     */
    public void setId(@NonNull String id) {
        this.id = id;
    }

    /**
     * Get the user's name
     * @return The name
     */
    public String getName() {
        return name;
    }

    /**
     * Set the user's name
     * @param name The name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Get the user's email
     * @return The email
     */
    public String getEmail() {
        return email;
    }

    /**
     * Set the user's email
     * @param email The email
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * Get the user's phone number
     * @return The phone number
     */
    public String getPhone() {
        return phone;
    }

    /**
     * Set the user's phone number
     * @param phone The phone number
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * Get the user's device ID
     * @return The device ID
     */
    public String getDeviceId() {
        return deviceId;
    }

    /**
     * Set the user's device ID
     * @param deviceId The device ID
     */
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    /**
     * Get the user's authentication token
     * @return The authentication token
     */
    public String getAuthToken() {
        return authToken;
    }

    /**
     * Set the user's authentication token
     * @param authToken The authentication token
     */
    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    /**
     * Check if the user is an administrator
     * @return True if the user is an administrator, false otherwise
     */
    public boolean isAdmin() {
        return isAdmin;
    }

    /**
     * Set whether the user is an administrator
     * @param admin True if the user is an administrator, false otherwise
     */
    public void setAdmin(boolean admin) {
        isAdmin = admin;
    }

    /**
     * Get the user's creation date
     * @return The creation date
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * Set the user's creation date
     * @param createdAt The creation date
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * Get the user's last login date
     * @return The last login date
     */
    public Date getLastLoginAt() {
        return lastLoginAt;
    }

    /**
     * Set the user's last login date
     * @param lastLoginAt The last login date
     */
    public void setLastLoginAt(Date lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }

    /**
     * Check if the user is active
     * @return True if the user is active, false otherwise
     */
    public boolean isActive() {
        return active;
    }

    /**
     * Set whether the user is active
     * @param active True if the user is active, false otherwise
     */
    public void setActive(boolean active) {
        this.active = active;
    }
}

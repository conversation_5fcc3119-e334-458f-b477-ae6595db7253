/* Yemen GPS - Main Stylesheet */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif;
}

html, body {
    height: 100%;
    width: 100%;
    direction: rtl;
}

body {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

/* Loading Screen */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4285F4;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header Styles */
header {
    background-color: #4285F4;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    margin-left: 10px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.search-container {
    display: flex;
    flex: 1;
    max-width: 500px;
    margin: 0 20px;
}

#search-input {
    width: 100%;
    padding: 10px 15px;
    border: none;
    border-radius: 25px 0 0 25px;
    outline: none;
    font-size: 0.9rem;
}

#search-button {
    background-color: #34A853;
    color: white;
    border: none;
    border-radius: 0 25px 25px 0;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#search-button:hover {
    background-color: #2d9249;
}

/* Main Content and Map */
main {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#map {
    height: 100%;
    width: 100%;
    z-index: 1;
}

/* Info Panel for Location Details */
.info-panel {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    max-height: 60%;
    background: white;
    border-radius: 15px 15px 0 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 800;
    overflow-y: auto;
    transition: transform 0.3s ease;
    transform: translateY(0);
    padding: 20px;
}

.info-panel.hidden {
    transform: translateY(100%);
}

.close-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #555;
}

.info-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-header h2 {
    font-size: 1.3rem;
    color: #333;
}

.info-actions {
    display: flex;
    gap: 10px;
}

.info-actions button {
    background: #f5f5f5;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background 0.2s;
}

.info-actions button:hover {
    background: #e0e0e0;
}

.info-content {
    padding: 10px 0;
}

#location-description {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.contact-info {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-item i {
    width: 20px;
    color: #4285F4;
}

.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.gallery-image {
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.gallery-image:hover img {
    transform: scale(1.05);
}

/* Custom Leaflet Control Styles */
.custom-map-control {
    border-radius: 4px;
    background: white;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.leaflet-touch .custom-map-control a {
    display: block;
    text-align: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
    color: #555;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .info-panel {
        width: 400px;
        height: 100%;
        max-height: none;
        right: 0;
        top: 0;
        border-radius: 0;
        transform: translateX(0);
    }
    
    .info-panel.hidden {
        transform: translateX(100%);
    }
}

@media (max-width: 600px) {
    header {
        flex-direction: column;
        padding: 10px;
    }
    
    .logo {
        margin-bottom: 10px;
    }
    
    .search-container {
        width: 100%;
        margin: 0;
    }
}

/* Destination Marker Animation */
@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

.destination-marker-icon {
    animation: pulse 1.5s infinite;
}

/* Custom Route Styles */
.route-line {
    stroke-dasharray: 10, 5;
    animation: dash 30s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -1000;
    }
}

/* Styling for maps downloaded for offline use */
.offline-map-item {
    background: #f0f9ff;
    border: 1px solid #ddecf7;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.offline-map-actions button {
    background: #4285F4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    margin-left: 5px;
}

.offline-map-actions button.delete {
    background: #f44336;
}

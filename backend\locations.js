// backend/src/locations.js
const db = require('./database');

// دالة للحصول على قائمة التصنيفات
async function getCategories() {
  try {
    const result = await db.query(
      'SELECT * FROM location_categories WHERE is_active = true ORDER BY display_order, name'
    );
    return result.rows;
  } catch (err) {
    console.error('خطأ في الحصول على التصنيفات:', err.message);
    throw err;
  }
}

// دالة للبحث عن المواقع
async function searchLocations(query, categoryId = null, limit = 20, offset = 0) {
  try {
    let sql = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      WHERE l.is_public = true
    `;
    
    const params = [];
    let paramIndex = 1;
    
    if (query) {
      sql += ` AND (l.name ILIKE $${paramIndex} OR l.name_ar ILIKE $${paramIndex} OR l.address ILIKE $${paramIndex})`;
      params.push(`%${query}%`);
      paramIndex++;
    }
    
    if (categoryId) {
      sql += ` AND l.category_id = $${paramIndex}`;
      params.push(categoryId);
      paramIndex++;
    }
    
    sql += ` ORDER BY l.rating DESC, l.views DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);
    
    const result = await db.query(sql, params);
    
    return result.rows;
  } catch (err) {
    console.error('خطأ في البحث عن المواقع:', err.message);
    throw err;
  }
}

// دالة للحصول على المواقع القريبة
async function getNearbyLocations(lat, lng, radius = 5000, categoryId = null, limit = 20) {
  try {
    let sql = `
      SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color,
      ST_Distance(l.geom, ST_SetSRID(ST_MakePoint($1, $2), 4326)) as distance
      FROM locations l
      LEFT JOIN location_categories c ON l.category_id = c.category_id
      WHERE l.is_public = true
      AND ST_DWithin(l.geom, ST_SetSRID(ST_MakePoint($1, $2), 4326), $3)
    `;
    
    const params = [lng, lat, radius];
    let paramIndex = 4;
    
    if (categoryId) {
      sql += ` AND l.category_id = $${paramIndex}`;
      params.push(categoryId);
      paramIndex++;
    }
    
    sql += ` ORDER BY distance LIMIT $${paramIndex}`;
    params.push(limit);
    
    const result = await db.query(sql, params);
    
    return result.rows;
  } catch (err) {
    console.error('خطأ في الحصول على المواقع القريبة:', err.message);
    throw err;
  }
}

// دالة لإضافة موقع جديد
async function addLocation(locationData, userId) {
  const client = await db.getClient();
  
  try {
    await client.query('BEGIN');
    
    const { name, nameAr, categoryId, lat, lng, address, description, phone, website, openingHours } = locationData;
    
    const result = await client.query(
      `INSERT INTO locations (name, name_ar, category_id, geom, address, description, phone, website, opening_hours, added_by)
       VALUES ($1, $2, $3, ST_SetSRID(ST_MakePoint($4, $5), 4326), $6, $7, $8, $9, $10, $11)
       RETURNING *`,
      [name, nameAr, categoryId, lng, lat, address, description, phone, website, openingHours, userId]
    );
    
    await client.query('COMMIT');
    
    return result.rows[0];
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('خطأ في إضافة موقع:', err.message);
    throw err;
  } finally {
    client.release();
  }
}

// دالة للحصول على تفاصيل موقع
async function getLocationById(locationId) {
  try {
    // زيادة عدد المشاهدات
    await db.query(
      'UPDATE locations SET views = views + 1 WHERE location_id = $1',
      [locationId]
    );
    
    // الحصول على تفاصيل الموقع
    const locationResult = await db.query(
      `SELECT l.*, c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color
       FROM locations l
       LEFT JOIN location_categories c ON l.category_id = c.category_id
       WHERE l.location_id = $1`,
      [locationId]
    );
    
    if (locationResult.rows.length === 0) {
      throw new Error('لم يتم العثور على الموقع');
    }
    
    const location = locationResult.rows[0];
    
    // الحصول على صور الموقع
    const imagesResult = await db.query(
      'SELECT * FROM location_images WHERE location_id = $1 ORDER BY is_primary DESC',
      [locationId]
    );
    
    location.images = imagesResult.rows;
    
    // الحصول على تقييمات الموقع
    const reviewsResult = await db.query(
      `SELECT r.*, u.username, u.full_name, u.profile_image
       FROM location_reviews r
       JOIN users u ON r.user_id = u.user_id
       WHERE r.location_id = $1 AND r.is_approved = true
       ORDER BY r.review_date DESC`,
      [locationId]
    );
    
    location.reviews = reviewsResult.rows;
    
    return location;
  } catch (err) {
    console.error('خطأ في الحصول على تفاصيل الموقع:', err.message);
    throw err;
  }
}

// دالة لإضافة تقييم لموقع
async function addReview(locationId, userId, rating, comment) {
  const client = await db.getClient();
  
  try {
    await client.query('BEGIN');
    
    // التحقق من عدم وجود تقييم سابق
    const checkResult = await client.query(
      'SELECT * FROM location_reviews WHERE location_id = $1 AND user_id = $2',
      [locationId, userId]
    );
    
    if (checkResult.rows.length > 0) {
      throw new Error('لقد قمت بتقييم هذا الموقع مسبقًا');
    }
    
    // إضافة التقييم
    const reviewResult = await client.query(
      `INSERT INTO location_reviews (location_id, user_id, rating, comment)
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [locationId, userId, rating, comment]
    );
    
    // تحديث متوسط التقييم وعدد التقييمات
    await client.query(
      `UPDATE locations
       SET rating = (
         SELECT AVG(rating) FROM location_reviews WHERE location_id = $1
       ),
       rating_count = (
         SELECT COUNT(*) FROM location_reviews WHERE location_id = $1
       )
       WHERE location_id = $1`,
      [locationId]
    );
    
    await client.query('COMMIT');
    
    return reviewResult.rows[0];
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('خطأ في إضافة تقييم:', err.message);
    throw err;
  } finally {
    client.release();
  }
}

// دالة لإضافة موقع إلى المفضلة
async function addToFavorites(locationId, userId, notes = null) {
  try {
    const result = await db.query(
      `INSERT INTO user_favorites (user_id, location_id, notes)
       VALUES ($1, $2, $3)
       ON CONFLICT (user_id, location_id) DO NOTHING
       RETURNING *`,
      [userId, locationId, notes]
    );
    
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إضافة الموقع إلى المفضلة:', err.message);
    throw err;
  }
}

// دالة للحصول على المواقع المفضلة للمستخدم
async function getUserFavorites(userId) {
  try {
    const result = await db.query(
      `SELECT f.*, l.name, l.name_ar, l.geom, l.address, l.rating, l.rating_count,
       c.name as category_name, c.name_ar as category_name_ar, c.icon as category_icon, c.color as category_color
       FROM user_favorites f
       JOIN locations l ON f.location_id = l.location_id
       LEFT JOIN location_categories c ON l.category_id = c.category_id
       WHERE f.user_id = $1
       ORDER BY f.added_date DESC`,
      [userId]
    );
    
    return result.rows;
  } catch (err) {
    console.error('خطأ في الحصول على المواقع المفضلة:', err.message);
    throw err;
  }
}

module.exports = {
  getCategories,
  searchLocations,
  getNearbyLocations,
  addLocation,
  getLocationById,
  addReview,
  addToFavorites,
  getUserFavorites
};
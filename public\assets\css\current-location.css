/**
 * Yemen GPS - نظام خرائط اليمن
 * تنسيقات علامة الموقع الحالي
 */

/* علامة الموقع الحالي */
.current-location-marker {
    background-color: transparent !important;
    border: none !important;
}

.current-marker-inner {
    position: relative;
    width: 20px;
    height: 20px;
    background-color: rgba(33, 150, 243, 0.4);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.current-marker-inner:before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgb(33, 150, 243);
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.current-marker-pulse {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: rgba(33, 150, 243, 0.2);
    border-radius: 50%;
    animation: pulse 2s infinite;
    pointer-events: none;
    top: -10px;
    left: -10px;
}

@keyframes pulse {
    0% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* علامة الموقع الافتراضي */
.fallback-location-marker {
    background-color: transparent !important;
    border: none !important;
}

.fallback-marker-inner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
}

.fallback-marker-inner i {
    color: #FF5722;
    font-size: 30px;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    animation: bounce 1.5s infinite alternate;
}

@keyframes bounce {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-5px);
    }
}

/* أزرار التحكم بالخريطة */
.map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 500;
    display: flex;
    flex-direction: column;
}

/* أزرار طبقات الخريطة */
.map-layer-controls {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 500;
    display: flex;
    flex-direction: column;
    gap: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.map-layer-btn {
    width: 40px;
    height: 40px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s;
    color: #555;
}

.map-layer-btn:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
}

.map-layer-btn.active {
    background-color: #4CAF50;
    color: white;
    border-color: #3d8b40;
}

.map-layer-btn.active:hover {
    background-color: #43A047;
}

.map-control-btn {
    width: 40px;
    height: 40px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s;
}

.map-control-btn:hover {
    background-color: #f5f5f5;
}

/* مؤشر التحميل */
#loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(33, 150, 243, 0.2);
    border-radius: 50%;
    border-top-color: rgb(33, 150, 243);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* مؤشر وضع عدم الاتصال */
.offline-indicator {
    position: fixed;
    top: 10px;
    left: 10px;
    background-color: rgba(255, 59, 48, 0.9);
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.offline-indicator i {
    font-size: 16px;
}

/* إشعارات */
.notification {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    color: white;
    z-index: 1000;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    opacity: 1;
    transition: opacity 0.3s;
}

.notification.fade-out {
    opacity: 0;
}

.notification.info {
    background-color: rgba(33, 150, 243, 0.9);
}

.notification.success {
    background-color: rgba(76, 175, 80, 0.9);
}

.notification.warning {
    background-color: rgba(255, 152, 0, 0.9);
}

.notification.error {
    background-color: rgba(255, 59, 48, 0.9);
}

// وحدة واجهة برمجة التطبيقات للخرائط في نظام Yemen GPS

// استيراد المكتبات اللازمة
const fetch = require('node-fetch');
require('dotenv').config();

// تصدير الوحدة
module.exports = function(app, db) {
    // نقطة نهاية للبحث عن المواقع باستخدام Google Maps
    app.get('/api/maps/search', async (req, res) => {
        const { query } = req.query;
        
        if (!query) {
            return res.status(400).json({ error: 'يجب توفير استعلام البحث' });
        }
        
        try {
            // استخدام Google Places API للبحث
            const url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${process.env.GOOGLE_MAPS_API_KEY}&language=ar&region=ye`;
            
            const response = await fetch(url);
            const data = await response.json();
            
            res.json(data.results);
        } catch (err) {
            console.error('خطأ في البحث عن المواقع:', err);
            res.status(500).json({ error: 'حدث خطأ أثناء البحث عن المواقع' });
        }
    });

    // نقطة نهاية للحصول على تفاصيل العنوان من الإحداثيات
    app.get('/api/maps/geocode', async (req, res) => {
        const { lat, lng } = req.query;
        
        if (!lat || !lng) {
            return res.status(400).json({ error: 'يجب توفير الإحداثيات' });
        }
        
        try {
            // استخدام Google Geocoding API للحصول على العنوان
            const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.GOOGLE_MAPS_API_KEY}&language=ar`;
            
            const response = await fetch(url);
            const data = await response.json();
            
            res.json(data.results);
        } catch (err) {
            console.error('خطأ في الحصول على العنوان:', err);
            res.status(500).json({ error: 'حدث خطأ أثناء الحصول على العنوان' });
        }
    });

    // نقطة نهاية للبحث عن المواقع في منطقة محددة
    app.get('/api/maps/search-area', async (req, res) => {
        const { lat, lng, radius, north, east, south, west } = req.query;
        
        try {
            let url;
            
            if (lat && lng && radius) {
                // البحث في دائرة
                url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=${radius}&key=${process.env.GOOGLE_MAPS_API_KEY}&language=ar`;
            } else if (north && east && south && west) {
                // البحث في مستطيل
                const centerLat = (parseFloat(north) + parseFloat(south)) / 2;
                const centerLng = (parseFloat(east) + parseFloat(west)) / 2;
                
                // حساب نصف القطر التقريبي للمنطقة (بالمتر)
                const radius = Math.max(
                    haversineDistance(centerLat, centerLng, parseFloat(north), centerLng),
                    haversineDistance(centerLat, centerLng, centerLat, parseFloat(east))
                );
                
                url = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${centerLat},${centerLng}&radius=${radius}&key=${process.env.GOOGLE_MAPS_API_KEY}&language=ar`;
            } else {
                return res.status(400).json({ error: 'يجب توفير إحداثيات ونصف قطر أو حدود المنطقة' });
            }
            
            const response = await fetch(url);
            const data = await response.json();
            
            res.json({
                success: true,
                places: data.results
            });
        } catch (err) {
            console.error('خطأ في البحث عن المواقع في المنطقة:', err);
            res.status(500).json({ error: 'حدث خطأ أثناء البحث عن المواقع في المنطقة' });
        }
    });

    // دالة مساعدة لحساب المسافة بين نقطتين باستخدام صيغة هافرساين
    function haversineDistance(lat1, lon1, lat2, lon2) {
        const R = 6371000; // نصف قطر الأرض بالمتر
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = 
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
            Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c;
        return distance;
    }
};

/**
 * تكامل خصائص البحث المتقدمة مع الصفحة الرئيسية
 */

document.addEventListener('DOMContentLoaded', function() {
  // تم إزالة إعداد رأس النظام والأيقونات

  // إعداد وظائف البحث
  setupSearch();

  // إضافة مستمعي أحداث للخريطة
  setupMapEventListeners();
});

/**
 * تم إزالة وظيفة إعداد رأس الصفحة مع اسم النظام والأيقونات
 */

/**
 * إعداد وظائف البحث
 */
function setupSearch() {
  // تم حذف مربع البحث المكرر وفقًا لطلب المستخدم
  // نستخدم فقط مربع البحث الرئيسي الموجود في الصفحة (modern-search-box)
  
  // تهيئة مستمع حدث البحث لمربع البحث الرئيسي
  const searchInput = document.querySelector('.modern-search-box #search-input');
  const searchButton = document.querySelector('.modern-search-box #search-button');

  if (searchInput && searchButton && window.yemenNavSearch) {
    // مستمع حدث للنقر على زر البحث
    searchButton.addEventListener('click', function() {
      const query = searchInput.value.trim();
      if (query && window.map) {
        window.yemenNavSearch.handleSearch(query, window.map);
      }
    });

    // مستمع حدث للضغط على Enter في حقل البحث
    searchInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        const query = searchInput.value.trim();
        if (query && window.map) {
          window.yemenNavSearch.handleSearch(query, window.map);
        }
      }
    });
  }
}

/**
 * إعداد مستمعي أحداث الخريطة
 */
function setupMapEventListeners() {
  // التأكد من وجود كائن الخريطة قبل إضافة المستمعات
  const waitForMap = setInterval(() => {
    if (window.map) {
      clearInterval(waitForMap);

      // إضافة مستمع حدث للنقر على الخريطة لإغلاق النوافذ المنبثقة
      window.map.on('click', function() {
        // إخفاء معلومات الموقع
        const locationInfo = document.getElementById('location-info');
        if (locationInfo) {
          locationInfo.style.display = 'none';
        }

        // إغلاق النوافذ المنبثقة للبحث
        if (window.yemenNavSearch && window.yemenNavSearch.clearMarkers) {
          window.yemenNavSearch.clearMarkers();
        }
      });
    }
  }, 1000); // التحقق كل ثانية
}

# دليل نقل واستعادة النسخة الاحتياطية على جهاز آخر

## مواقع ملفات النسخ الاحتياطية

### 1. الموقع الافتراضي للنسخ الاحتياطية:
```
e:\yemen gps\backups\
```

### 2. النسخة الاحتياطية الحالية الموجودة:
```
e:\yemen gps\yemen_gps_backup.sql
```

### 3. النسخ الاحتياطية الجديدة (بعد تشغيل النظام):
```
e:\yemen gps\backups\yemen_gps_backup_2025-05-24_17-30-45.sql
e:\yemen gps\backups\yemen_gps_backup_2025-05-25_09-15-30.sql
```

## خطوات نقل النسخة الاحتياطية لجهاز آخر

### الخطوة 1: العثور على ملف النسخة الاحتياطية

1. **افتح مجلد المشروع:**
   ```
   e:\yemen gps\
   ```

2. **ابحث عن الملفات التالية:**
   - `yemen_gps_backup.sql` (النسخة الحالية)
   - مجلد `backups\` (النسخ الجديدة)

3. **تحديد أحدث نسخة احتياطية:**
   - اختر الملف الذي له أحدث تاريخ ووقت

### الخطوة 2: نسخ الملف

1. **انسخ ملف النسخة الاحتياطية** إلى:
   - فلاش ميموري (USB)
   - قرص صلب خارجي
   - خدمة تخزين سحابية (Google Drive, OneDrive)
   - شبكة محلية

2. **تأكد من حجم الملف:**
   - الملف الحالي حوالي 50-100 KB
   - إذا كان الحجم 0 KB، فالملف تالف

### الخطوة 3: نقل الملف للجهاز الجديد

1. **انسخ الملف** إلى الجهاز الجديد
2. **ضعه في مجلد مؤقت** مثل:
   ```
   C:\temp\yemen_gps_backup.sql
   ```

## استعادة النسخة الاحتياطية على الجهاز الجديد

### المتطلبات المسبقة:

1. **تثبيت PostgreSQL** على الجهاز الجديد
2. **إنشاء مستخدم قاعدة البيانات:**
   ```sql
   CREATE USER yemen WITH PASSWORD 'admin';
   ALTER USER yemen CREATEDB;
   ALTER USER yemen WITH SUPERUSER;
   ```

### طريقة الاستعادة الأولى: استخدام psql

1. **افتح Command Prompt كمدير**

2. **تنفيذ الأمر التالي:**
   ```bash
   set PGPASSWORD=admin
   psql -h localhost -p 5432 -U yemen -d postgres -f "C:\temp\yemen_gps_backup.sql"
   ```

3. **التحقق من نجاح العملية:**
   ```bash
   psql -h localhost -p 5432 -U yemen -d yemen_gps -c "\dt"
   ```

### طريقة الاستعادة الثانية: استخدام pgAdmin

1. **افتح pgAdmin**
2. **اتصل بخادم PostgreSQL**
3. **انقر بزر الماوس الأيمن على "Databases"**
4. **اختر "Restore..."**
5. **اختر ملف النسخة الاحتياطية**
6. **انقر "Restore"**

### طريقة الاستعادة الثالثة: استخدام سكريبت جاهز

إنشاء ملف `restore-on-new-computer.bat`:

```batch
@echo off
echo ===== استعادة قاعدة بيانات Yemen GPS على جهاز جديد =====
echo.

REM طلب مسار ملف النسخة الاحتياطية
set /p BACKUP_PATH="أدخل المسار الكامل لملف النسخة الاحتياطية: "

REM التحقق من وجود الملف
if not exist "%BACKUP_PATH%" (
    echo خطأ: الملف غير موجود في المسار المحدد
    pause
    exit /b 1
)

echo.
echo جاري استعادة قاعدة البيانات...
echo الملف: %BACKUP_PATH%
echo.

REM تحديد متغيرات قاعدة البيانات
set PGUSER=yemen
set PGPASSWORD=admin
set PGHOST=localhost
set PGPORT=5432

REM تنفيذ أمر الاستعادة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -f "%BACKUP_PATH%" --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===== تم استعادة قاعدة البيانات بنجاح =====
    echo.
    echo التحقق من الجداول المستعادة...
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d yemen_gps -c "\dt"
) else (
    echo.
    echo ===== فشل في استعادة قاعدة البيانات =====
    echo تحقق من:
    echo 1. تثبيت PostgreSQL
    echo 2. إنشاء المستخدم yemen
    echo 3. صحة ملف النسخة الاحتياطية
)

echo.
pause
```

## خطوات مفصلة للنقل والاستعادة

### 1. على الجهاز الأصلي (المصدر):

#### أ. إنشاء نسخة احتياطية جديدة:
```bash
# تشغيل أحد هذه الأوامر
backup-database.bat
# أو
node database-backup-manager.js backup
```

#### ب. العثور على الملف:
1. افتح مجلد `e:\yemen gps\`
2. ابحث عن:
   - `yemen_gps_backup.sql` (النسخة الموجودة)
   - `backups\yemen_gps_backup_[تاريخ].sql` (النسخة الجديدة)

#### ج. نسخ الملف:
1. انسخ الملف إلى فلاش ميموري أو قرص صلب خارجي
2. أو ارفعه إلى خدمة تخزين سحابية

### 2. على الجهاز الجديد (الهدف):

#### أ. تثبيت المتطلبات:
1. **تثبيت PostgreSQL:**
   - حمل من: https://www.postgresql.org/download/
   - اختر كلمة مرور للمستخدم postgres

2. **تثبيت Node.js (اختياري):**
   - حمل من: https://nodejs.org/

#### ب. إعداد قاعدة البيانات:
1. **افتح pgAdmin أو psql**
2. **أنشئ المستخدم:**
   ```sql
   CREATE USER yemen WITH PASSWORD 'admin';
   ALTER USER yemen CREATEDB;
   ALTER USER yemen WITH SUPERUSER;
   ```

#### ج. استعادة النسخة الاحتياطية:
1. **انسخ ملف النسخة الاحتياطية** إلى الجهاز الجديد
2. **ضعه في مجلد مثل:** `C:\temp\`
3. **استخدم إحدى الطرق التالية:**

**الطريقة السريعة:**
```bash
set PGPASSWORD=admin
psql -h localhost -p 5432 -U yemen -d postgres -f "C:\temp\yemen_gps_backup.sql"
```

**الطريقة التفاعلية:**
- استخدم ملف `restore-on-new-computer.bat` المذكور أعلاه

## نصائح مهمة

### 1. التحقق من سلامة النسخة الاحتياطية:
```bash
# تحقق من حجم الملف (يجب أن يكون أكبر من 10 KB)
dir yemen_gps_backup.sql

# تحقق من محتوى الملف
type yemen_gps_backup.sql | more
```

### 2. حل المشاكل الشائعة:

#### مشكلة: "role yemen does not exist"
**الحل:**
```sql
CREATE USER yemen WITH PASSWORD 'admin';
ALTER USER yemen CREATEDB;
ALTER USER yemen WITH SUPERUSER;
```

#### مشكلة: "database yemen_gps already exists"
**الحل:**
```sql
DROP DATABASE IF EXISTS yemen_gps;
```

#### مشكلة: "permission denied"
**الحل:**
- تشغيل Command Prompt كمدير
- التأكد من صلاحيات المستخدم yemen

### 3. التحقق من نجاح الاستعادة:
```bash
# الاتصال بقاعدة البيانات
psql -h localhost -p 5432 -U yemen -d yemen_gps

# عرض الجداول
\dt

# عرض عدد المستخدمين
SELECT COUNT(*) FROM users;

# عرض عدد المواقع
SELECT COUNT(*) FROM locations;

# الخروج
\q
```

## ملفات إضافية للنقل

عند نقل المشروع كاملاً، تأكد من نسخ:

1. **ملف النسخة الاحتياطية** (`yemen_gps_backup.sql`)
2. **ملف الإعدادات** (`.env`)
3. **ملفات المشروع** (إذا كنت تريد نقل التطبيق كاملاً)
4. **الصور والملفات المرفوعة** (مجلد `uploads/` إن وجد)

## خطة النسخ الاحتياطي المنتظم

للحفاظ على البيانات:

1. **نسخ احتياطي يومي** باستخدام `backup-database.bat`
2. **نسخ احتياطي أسبوعي** إلى قرص صلب خارجي
3. **نسخ احتياطي شهري** إلى خدمة تخزين سحابية
4. **اختبار الاستعادة** كل شهر للتأكد من سلامة النسخ

---

**ملاحظة:** احتفظ دائماً بأكثر من نسخة احتياطية في أماكن مختلفة لضمان أمان البيانات.

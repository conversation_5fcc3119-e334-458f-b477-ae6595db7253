// public/js/area-select.js
// وظائف تحديد المنطقة وتنزيل الخرائط دون اتصال

(function() {
  'use strict';

  // متغيرات عالمية
  window.areaSelectMode = false;
  window.areaSelectRectangle = null;
  window.areaSelectControl = null;
  window._areaSelectCreatedHandler = null;

  // تفعيل وضع تحديد المنطقة
  function enableAreaSelectMode() {
    if (window.areaSelectMode) return;
    window.areaSelectMode = true;

    // تغيير مؤشر الفأرة
    const mapDiv = document.getElementById('map');
    if (mapDiv) mapDiv.style.cursor = 'crosshair';

    // منع تكرار رسالة الإرشاد
    if (!document.getElementById('area-select-help')) {
      const helpMsg = document.createElement('div');
      helpMsg.id = 'area-select-help';
      helpMsg.className = 'area-select-help';
      helpMsg.textContent = 'انقر واسحب لتحديد المنطقة المطلوب تنزيلها';
      helpMsg.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        direction: rtl;
        font-family: Arial, sans-serif;
        text-align: center;
      `;
      document.body.appendChild(helpMsg);
    }

    // إضافة أداة الرسم للخريطة
    window.areaSelectControl = new L.Draw.Rectangle(window.map, {
      shapeOptions: {
        color: '#3388ff',
        weight: 2,
        opacity: 0.7,
        fillOpacity: 0.2
      }
    });
    window.areaSelectControl.enable();

    // إزالة أي مستمع سابق قبل إضافة الجديد
    if (window._areaSelectCreatedHandler) {
      window.map.off('draw:created', window._areaSelectCreatedHandler);
    }
    window._areaSelectCreatedHandler = function(e) {
      // إزالة المستطيل السابق إذا وجد
      if (window.areaSelectRectangle) {
        window.map.removeLayer(window.areaSelectRectangle);
      }
      // حفظ المستطيل الجديد
      window.areaSelectRectangle = e.layer;
      window.map.addLayer(window.areaSelectRectangle);
      // إظهار معلومات المنطقة المحددة
      const bounds = window.areaSelectRectangle.getBounds();
      const area = L.GeometryUtil.geodesicArea(window.areaSelectRectangle.getLatLngs()[0]);
      const areaInKm = (area / 1000000).toFixed(2);
      const selectedAreaInfo = document.getElementById('selected-area-info');
      if (selectedAreaInfo) {
        selectedAreaInfo.innerHTML = `المساحة المحددة: ${areaInKm} كم²`;
        selectedAreaInfo.style.display = 'block';
        // معلومات إضافية
        const additionalInfo = document.createElement('div');
        additionalInfo.innerHTML = `<p>حدود المنطقة: شمال ${bounds.getNorth().toFixed(4)}، جنوب ${bounds.getSouth().toFixed(4)}، شرق ${bounds.getEast().toFixed(4)}، غرب ${bounds.getWest().toFixed(4)}</p>`;
        selectedAreaInfo.appendChild(additionalInfo);
        // تقدير حجم البيانات
        const minZoom = parseInt(document.getElementById('min-zoom')?.value || 10);
        const maxZoom = parseInt(document.getElementById('max-zoom')?.value || 16);
        let estimatedTiles = 0;
        for (let z = minZoom; z <= maxZoom; z++) {
          const tilesAtZoom = Math.pow(4, z - minZoom);
          estimatedTiles += tilesAtZoom;
        }
        const estimatedSize = (estimatedTiles * 15 / 1024).toFixed(2);
        const sizeInfo = document.createElement('div');
        sizeInfo.innerHTML = `<p>الحجم التقريبي: ${estimatedSize} ميجابايت (${estimatedTiles} بلاطة تقريبًا)</p>`;
        selectedAreaInfo.appendChild(sizeInfo);
      }
      // إظهار لوحة الخرائط دون اتصال
      const offlineMapsPanel = document.querySelector('.offline-maps-panel');
      if (offlineMapsPanel) offlineMapsPanel.style.display = 'block';
      // تعطيل وضع التحديد بعد الانتهاء
      disableAreaSelectMode();
    };
    window.map.on('draw:created', window._areaSelectCreatedHandler);
  }

  // تعطيل وضع تحديد المنطقة
  function disableAreaSelectMode() {
    if (!window.areaSelectMode) return;
    window.areaSelectMode = false;
    // إعادة مؤشر الفأرة
    const mapDiv = document.getElementById('map');
    if (mapDiv) mapDiv.style.cursor = '';
    // إزالة الرسالة الإرشادية
    const helpMsg = document.getElementById('area-select-help');
    if (helpMsg) helpMsg.remove();
    // تعطيل أداة الرسم
    if (window.areaSelectControl) {
      window.areaSelectControl.disable();
    }
    // إزالة مستمع حدث إنشاء المستطيل
    if (window._areaSelectCreatedHandler) {
      window.map.off('draw:created', window._areaSelectCreatedHandler);
      window._areaSelectCreatedHandler = null;
    }
  }

  // وظيفة تنزيل المنطقة المحددة
  function downloadSelectedRegion() {
    if (!window.offlineMapManager) {
      alert('لم يتم تهيئة مدير الخرائط دون اتصال بعد.');
      return;
    }
    const minZoomElem = document.getElementById('min-zoom');
    const maxZoomElem = document.getElementById('max-zoom');
    const regionNameElem = document.getElementById('region-name');
    if (!minZoomElem || !maxZoomElem || !regionNameElem) {
      alert('عناصر واجهة المستخدم غير متوفرة.');
      return;
    }
    const minZoom = parseInt(minZoomElem.value);
    const maxZoom = parseInt(maxZoomElem.value);
    const regionName = regionNameElem.value.trim();
    if (!regionName) {
      alert('يرجى إدخال اسم المنطقة.');
      return;
    }
    if (!window.areaSelectRectangle) {
      alert('يرجى تحديد منطقة على الخريطة أولاً.');
      return;
    }
    // تنفيذ عملية التنزيل (مثال)
    window.offlineMapManager.downloadRegion({
      bounds: window.areaSelectRectangle.getBounds(),
      minZoom,
      maxZoom,
      name: regionName,
      onProgress: function(p) {
        // يمكن تحديث شريط التقدم هنا
        // مثال: document.getElementById('download-progress').value = p;
      },
      onComplete: function() {
        alert('تم تنزيل المنطقة بنجاح!');
        displaySavedRegions();
      },
      onError: function(err) {
        alert('حدث خطأ أثناء التنزيل: ' + err);
      }
    });
  }

  // عرض المناطق المحفوظة (مثال)
  function displaySavedRegions() {
    if (!window.offlineMapManager) return;
    const listElem = document.getElementById('offline-regions-list');
    if (!listElem) return;
    const regions = window.offlineMapManager.getSavedRegions();
    listElem.innerHTML = '';
    regions.forEach(function(region) {
      const li = document.createElement('li');
      li.textContent = region.name + ' (' + region.date + ')';
      listElem.appendChild(li);
    });
  }

  // تصدير الوظائف للاستخدام الخارجي
  window.AreaSelect = {
    enable: enableAreaSelectMode,
    disable: disableAreaSelectMode,
    download: downloadSelectedRegion,
    refresh: displaySavedRegions
  };

})();

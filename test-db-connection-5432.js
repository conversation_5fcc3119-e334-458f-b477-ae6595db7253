// سكريبت اختبار الاتصال بقاعدة البيانات على المنفذ 5432
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432, // المنفذ الذي تم تعديله
  database: 'yemen_gps',
  user: 'yemen',
  password: 'yemen123' // استخدام كلمة المرور التي ذكرتها سابقاً
});

async function testConnection() {
  try {
    console.log('محاولة الاتصال بقاعدة البيانات على المنفذ 5432...');
    console.log('إعدادات الاتصال:');
    console.log('- المضيف: localhost');
    console.log('- المنفذ: 5432');
    console.log('- قاعدة البيانات: yemen_gps');
    console.log('- المستخدم: yemen');
    
    // محاولة الاستعلام البسيط
    const result = await pool.query('SELECT NOW()');
    
    console.log('\n✅ تم الاتصال بقاعدة البيانات بنجاح!');
    console.log('الوقت على الخادم:', result.rows[0].now);
    
    // عرض معلومات الجداول
    console.log('\nجاري التحقق من جداول قاعدة البيانات...');
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log(`تم العثور على ${tablesResult.rows.length} جدول:`);
    tablesResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.table_name}`);
    });
    
  } catch (error) {
    console.error('\n❌ فشل الاتصال بقاعدة البيانات!');
    console.error('الخطأ:', error.message);
    
    if (error.message.includes('connect ECONNREFUSED')) {
      console.error('\nتلميحات للإصلاح:');
      console.error('1. تأكد من أن خدمة PostgreSQL تعمل');
      console.error('2. تأكد من أن ملف postgresql.conf تم تحديثه بشكل صحيح');
      console.error('3. تأكد من إعادة تشغيل خدمة PostgreSQL بعد تعديل الملف');
      console.error('4. تحقق من أن المنفذ 5432 غير مستخدم من قبل تطبيق آخر');
    }
  } finally {
    // إغلاق اتصال المجمع
    await pool.end();
  }
}

// تنفيذ اختبار الاتصال
testConnection();

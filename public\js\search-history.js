/**
 * سجل البحث وتحسينات البحث التلقائي
 */

// متغيرات عامة
// تحقق من وجود المتغير قبل إعادة تعريفه
window.searchHistory = window.searchHistory || [];
const MAX_HISTORY_ITEMS = 10;
let searchSuggestions = null;

/**
 * تهيئة سجل البحث
 */
function initSearchHistory() {
  // قراءة سجل البحث من التخزين المحلي
  loadSearchHistory();
  
  // إنشاء عنصر الاقتراحات
  createSuggestionsElement();
  
  // ربط وظائف الاقتراحات بحقل البحث
  const searchInput = document.getElementById('search-input');
  if (searchInput) {
    searchInput.addEventListener('focus', showSearchSuggestions);
    searchInput.addEventListener('blur', function() {
      // تأخير الإخفاء لتمكين النقر على الاقتراحات
      setTimeout(hideSearchSuggestions, 200);
    });
    
    searchInput.addEventListener('input', function() {
      // إظهار الاقتراحات فقط إذا كان الحقل غير فارغ
      if (this.value.trim().length > 0) {
        filterSuggestions(this.value.trim());
      } else {
        showRecentSearches();
      }
    });
  }
}

/**
 * قراءة سجل البحث من التخزين المحلي
 */
function loadSearchHistory() {
  try {
    const saved = localStorage.getItem('yemenNavSearchHistory');
    if (saved) {
      searchHistory = JSON.parse(saved);
      // التأكد من أن العدد لا يتجاوز الحد الأقصى
      if (searchHistory.length > MAX_HISTORY_ITEMS) {
        searchHistory = searchHistory.slice(0, MAX_HISTORY_ITEMS);
      }
    }
  } catch (e) {
    console.warn('خطأ في قراءة سجل البحث:', e);
    searchHistory = [];
  }
}

/**
 * حفظ سجل البحث في التخزين المحلي
 */
function saveSearchHistory() {
  try {
    localStorage.setItem('yemenNavSearchHistory', JSON.stringify(searchHistory));
  } catch (e) {
    console.warn('خطأ في حفظ سجل البحث:', e);
  }
}

/**
 * إضافة عبارة بحث إلى السجل
 * @param {string} query - عبارة البحث
 */
function addToSearchHistory(query) {
  // التأكد من عدم وجود قيمة فارغة
  if (!query || query.trim() === '') return;
  
  // إزالة العبارة إذا كانت موجودة سابقاً
  searchHistory = searchHistory.filter(item => item.toLowerCase() !== query.toLowerCase());
  
  // إضافة العبارة في بداية المصفوفة
  searchHistory.unshift(query);
  
  // تقليص المصفوفة إذا تجاوزت الحد الأقصى
  if (searchHistory.length > MAX_HISTORY_ITEMS) {
    searchHistory.pop();
  }
  
  // حفظ السجل المحدث
  saveSearchHistory();
}

/**
 * إنشاء عنصر الاقتراحات
 */
function createSuggestionsElement() {
  // إزالة أي عنصر سابق
  const existingSuggestions = document.getElementById('search-suggestions');
  if (existingSuggestions) {
    existingSuggestions.remove();
  }
  
  // إنشاء عنصر جديد
  searchSuggestions = document.createElement('div');
  searchSuggestions.id = 'search-suggestions';
  searchSuggestions.style.cssText = 'position: absolute; top: 130px; right: 20px; width: 300px; background-color: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); z-index: 1001; max-height: 300px; overflow-y: auto; display: none;';
  
  document.body.appendChild(searchSuggestions);
}

/**
 * عرض اقتراحات البحث
 */
function showSearchSuggestions() {
  // عرض الاقتراحات الأخيرة من سجل البحث
  showRecentSearches();
  
  // إظهار عنصر الاقتراحات
  if (searchSuggestions) {
    searchSuggestions.style.display = 'block';
  }
}

/**
 * إخفاء اقتراحات البحث
 */
function hideSearchSuggestions() {
  if (searchSuggestions) {
    searchSuggestions.style.display = 'none';
  }
}

/**
 * عرض عمليات البحث الأخيرة
 */
function showRecentSearches() {
  if (!searchSuggestions) return;
  
  // تفريغ المحتوى الحالي
  searchSuggestions.innerHTML = '';
  
  // إضافة عنوان
  const header = document.createElement('div');
  header.style.cssText = 'padding: 10px; font-weight: bold; border-bottom: 1px solid #eee; text-align: right;';
  header.textContent = 'عمليات البحث الأخيرة';
  searchSuggestions.appendChild(header);
  
  // إضافة عمليات البحث الأخيرة
  if (searchHistory.length === 0) {
    const emptyItem = document.createElement('div');
    emptyItem.style.cssText = 'padding: 10px; text-align: center; color: #999;';
    emptyItem.textContent = 'لا توجد عمليات بحث سابقة';
    searchSuggestions.appendChild(emptyItem);
  } else {
    searchHistory.forEach(query => {
      const item = document.createElement('div');
      item.className = 'suggestion-item';
      item.style.cssText = 'padding: 8px 15px; cursor: pointer; text-align: right; border-bottom: 1px solid #f5f5f5;';
      item.textContent = query;
      
      // إضافة حدث النقر
      item.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f5f5f5';
      });
      
      item.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'white';
      });
      
      item.addEventListener('click', function() {
        // تعبئة حقل البحث بالعبارة المختارة
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
          searchInput.value = query;
          
          // تنفيذ البحث
          const searchButton = document.getElementById('search-button');
          if (searchButton) {
            searchButton.click();
          } else if (window.yemenNavSearch && window.yemenNavSearch.handleSearch && window.map) {
            window.yemenNavSearch.handleSearch(query, window.map);
          }
        }
        
        // إخفاء الاقتراحات
        hideSearchSuggestions();
      });
      
      searchSuggestions.appendChild(item);
    });
  }
  
  // إضافة زر لمسح السجل
  const clearButton = document.createElement('div');
  clearButton.style.cssText = 'padding: 8px 15px; text-align: center; background-color: #f5f5f5; cursor: pointer; color: #f44336; font-weight: bold;';
  clearButton.textContent = 'مسح سجل البحث';
  clearButton.addEventListener('click', function() {
    // مسح سجل البحث
    searchHistory = [];
    saveSearchHistory();
    
    // تحديث العرض
    showRecentSearches();
  });
  
  searchSuggestions.appendChild(clearButton);
}

/**
 * تصفية الاقتراحات حسب النص المدخل
 * @param {string} text - النص المدخل
 */
function filterSuggestions(text) {
  if (!searchSuggestions) return;
  
  // تفريغ المحتوى الحالي
  searchSuggestions.innerHTML = '';
  
  // تصفية سجل البحث
  const filteredHistory = searchHistory.filter(
    item => item.toLowerCase().includes(text.toLowerCase())
  );
  
  // إضافة عنوان
  const header = document.createElement('div');
  header.style.cssText = 'padding: 10px; font-weight: bold; border-bottom: 1px solid #eee; text-align: right;';
  header.textContent = 'اقتراحات البحث';
  searchSuggestions.appendChild(header);
  
  // إضافة الاقتراحات المصفاة
  if (filteredHistory.length === 0) {
    const searchItem = document.createElement('div');
    searchItem.className = 'suggestion-item';
    searchItem.style.cssText = 'padding: 8px 15px; cursor: pointer; text-align: right; border-bottom: 1px solid #f5f5f5;';
    searchItem.innerHTML = `بحث عن: <strong>${text}</strong>`;
    
    searchItem.addEventListener('click', function() {
      // تنفيذ البحث باستخدام النص المدخل
      const searchInput = document.getElementById('search-input');
      if (searchInput) {
        searchInput.value = text;
        
        // تنفيذ البحث
        const searchButton = document.getElementById('search-button');
        if (searchButton) {
          searchButton.click();
        } else if (window.yemenNavSearch && window.yemenNavSearch.handleSearch && window.map) {
          window.yemenNavSearch.handleSearch(text, window.map);
        }
      }
      
      // إخفاء الاقتراحات
      hideSearchSuggestions();
    });
    
    searchSuggestions.appendChild(searchItem);
  } else {
    filteredHistory.forEach(query => {
      const item = document.createElement('div');
      item.className = 'suggestion-item';
      item.style.cssText = 'padding: 8px 15px; cursor: pointer; text-align: right; border-bottom: 1px solid #f5f5f5;';
      
      // تمييز النص المطابق
      const regex = new RegExp(`(${escapeRegExp(text)})`, 'gi');
      item.innerHTML = query.replace(regex, '<strong>$1</strong>');
      
      // إضافة حدث النقر
      item.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f5f5f5';
      });
      
      item.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'white';
      });
      
      item.addEventListener('click', function() {
        // تعبئة حقل البحث بالعبارة المختارة
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
          searchInput.value = query;
          
          // تنفيذ البحث
          const searchButton = document.getElementById('search-button');
          if (searchButton) {
            searchButton.click();
          }
        }
        
        // إخفاء الاقتراحات
        hideSearchSuggestions();
      });
      
      searchSuggestions.appendChild(item);
    });
  }
}

/**
 * هروب الأحرف الخاصة في تعبير منتظم
 * @param {string} string - النص المراد معالجته
 * @return {string} - النص بعد الهروب
 */
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// بدء التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
  // تهيئة سجل البحث
  initSearchHistory();
  
  // تعديل وظيفة البحث لإضافة العبارة إلى السجل
  const originalSearchFunction = window.yemenNavSearch && window.yemenNavSearch.handleSearch;
  if (originalSearchFunction) {
    window.yemenNavSearch.handleSearch = function(query, map) {
      // إضافة العبارة إلى السجل
      addToSearchHistory(query);
      
      // استدعاء الوظيفة الأصلية
      return originalSearchFunction(query, map);
    };
  }
});

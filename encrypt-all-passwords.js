// سكريبت لتشفير جميع كلمات المرور في قاعدة البيانات
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// إعدادات الاتصال بقاعدة البيانات
const pool = new Pool({
  user: 'yemen',
  host: 'localhost',
  database: 'yemen_gps',
  password: 'admin',
  port: 5432
});

async function encryptAllPasswords() {
  try {
    // الحصول على جميع المستخدمين
    const usersQuery = 'SELECT id, username, password FROM users';
    const usersResult = await pool.query(usersQuery);
    const users = usersResult.rows;
    
    console.log(`تم العثور على ${users.length} مستخدم في قاعدة البيانات`);
    
    // تحديث كلمات المرور لكل مستخدم
    for (const user of users) {
      // تخطي كلمات المرور المشفرة بالفعل
      if (user.password.startsWith('$2')) {
        console.log(`كلمة المرور للمستخدم ${user.username} مشفرة بالفعل، تخطي...`);
        continue;
      }
      
      // تشفير كلمة المرور
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(user.password, saltRounds);
      
      // تحديث كلمة المرور في قاعدة البيانات
      const updateQuery = 'UPDATE users SET password = $1 WHERE id = $2';
      await pool.query(updateQuery, [hashedPassword, user.id]);
      
      console.log(`تم تشفير كلمة المرور للمستخدم ${user.username} بنجاح`);
    }
    
    console.log('تم تشفير جميع كلمات المرور بنجاح');
  } catch (error) {
    console.error('خطأ في تشفير كلمات المرور:', error);
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    await pool.end();
    console.log('تم إغلاق الاتصال بقاعدة البيانات');
  }
}

// تنفيذ الدالة
encryptAllPasswords();

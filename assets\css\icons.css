/* Font Awesome Icons - Simplified for Yemen GPS */
@font-face {
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("../webfonts/fa-solid-900.woff2") format("woff2");
}

.fas,
.fa-solid {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.fa,
.fas {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  line-height: 1;
  text-rendering: auto;
}

/* Icon definitions for Yemen GPS */
.fa-search:before { content: "\f002"; }
.fa-share-alt:before { content: "\f1e0"; }
.fa-crosshairs:before { content: "\f05b"; }
.fa-layers:before { content: "\f5fd"; }
.fa-times:before { content: "\f00d"; }
.fa-plus:before { content: "\f067"; }
.fa-minus:before { content: "\f068"; }
.fa-satellite:before { content: "\f7bf"; }
.fa-road:before { content: "\f018"; }
.fa-mountain:before { content: "\f6fc"; }
.fa-expand:before { content: "\f065"; }
.fa-bookmark:before { content: "\f02e"; }
.fa-directions:before { content: "\f5eb"; }
.fa-share:before { content: "\f064"; }
.fa-map-marker-alt:before { content: "\f3c5"; }
.fa-dot-circle:before { content: "\f192"; }
.fa-trash:before { content: "\f1f8"; }

/* Size utilities */
.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em;
}

.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }

/* Rotation utilities */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

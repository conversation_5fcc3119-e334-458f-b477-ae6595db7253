#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yemen Maps Complete - Main Server Application
الخادم الرئيسي لنظام خرائط اليمن الشامل
"""

import os
import sys
import json
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS
import psycopg2
import psycopg2.extras
from datetime import datetime
import logging
from pathlib import Path

# إعداد المسارات
BASE_DIR = Path(__file__).parent.parent
IMAGES_DIR = BASE_DIR / "images"
STATIC_DIR = BASE_DIR / "public"
TEMPLATES_DIR = BASE_DIR / "templates"

# إعداد التطبيق
app = Flask(__name__, 
           static_folder=str(STATIC_DIR),
           template_folder=str(TEMPLATES_DIR))
CORS(app)

# إعداد قاعدة البيانات
DB_CONFIG = {
    'host': 'localhost',
    'database': 'yemen_maps_complete',
    'user': 'yemen_maps_user',
    'password': 'YemenMaps2024!',
    'port': 5432
}

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.conn = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            return True
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.conn:
            self.conn.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        try:
            cursor = self.conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return []

# إنشاء مدير قاعدة البيانات
db = DatabaseManager()

@app.before_first_request
def initialize():
    """تهيئة التطبيق"""
    if not db.connect():
        logger.error("Failed to connect to database")
        # لا نوقف الخادم، سنحاول الاتصال لاحقاً
    logger.info("Application initialized successfully")

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/admin')
def admin():
    """لوحة الإدارة"""
    return render_template('admin.html')

@app.route('/api/places')
def get_places():
    """جلب الأماكن"""
    try:
        if not db.conn:
            db.connect()
            
        # معاملات البحث
        lat = request.args.get('lat', type=float)
        lng = request.args.get('lng', type=float)
        radius = request.args.get('radius', 10, type=int)  # كيلومتر
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        limit = request.args.get('limit', 100, type=int)
        
        # بناء الاستعلام
        query = """
            SELECT 
                p.place_id, p.name, p.name_ar, p.latitude, p.longitude,
                p.category, p.subcategory, p.rating, p.phone, p.address,
                l.name_ar as governorate_name,
                (SELECT photo_path FROM place_photos 
                 WHERE place_id = p.place_id AND is_primary = true 
                 LIMIT 1) as primary_photo
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE p.is_active = true
        """
        
        params = []
        
        # فلترة حسب الموقع
        if lat and lng:
            query += """
                AND ST_DWithin(
                    location_point, 
                    ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                    %s
                )
            """
            params.extend([lng, lat, radius * 1000])  # تحويل إلى متر
        
        # فلترة حسب الفئة
        if category:
            query += " AND p.category = %s"
            params.append(category)
        
        # البحث النصي
        if search:
            query += " AND (p.name ILIKE %s OR p.name_ar ILIKE %s)"
            search_term = f"%{search}%"
            params.extend([search_term, search_term])
        
        query += " ORDER BY p.rating DESC NULLS LAST LIMIT %s"
        params.append(limit)
        
        places = db.execute_query(query, params)
        
        # تحويل النتائج
        result = []
        for place in places:
            place_data = dict(place)
            # تحويل مسار الصورة إلى URL
            if place_data['primary_photo']:
                place_data['primary_photo'] = f"/images/{os.path.basename(place_data['primary_photo'])}"
            result.append(place_data)
        
        return jsonify({
            'success': True,
            'places': result,
            'count': len(result)
        })
        
    except Exception as e:
        logger.error(f"Error getting places: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/place/<place_id>')
def get_place_details(place_id):
    """جلب تفاصيل مكان محدد"""
    try:
        if not db.conn:
            db.connect()
            
        # جلب بيانات المكان
        place_query = """
            SELECT 
                p.*, l.name_ar as governorate_name
            FROM places p
            LEFT JOIN locations l ON p.governorate_id = l.id
            WHERE p.place_id = %s AND p.is_active = true
        """
        
        places = db.execute_query(place_query, [place_id])
        if not places:
            return jsonify({'success': False, 'error': 'Place not found'}), 404
        
        place = dict(places[0])
        
        # جلب صور المكان
        photos_query = """
            SELECT photo_path, photo_type, is_primary, display_order
            FROM place_photos
            WHERE place_id = %s AND is_active = true
            ORDER BY is_primary DESC, display_order ASC
        """
        
        photos = db.execute_query(photos_query, [place_id])
        place['photos'] = []
        
        for photo in photos:
            photo_data = dict(photo)
            photo_data['photo_url'] = f"/images/{os.path.basename(photo_data['photo_path'])}"
            place['photos'].append(photo_data)
        
        return jsonify({
            'success': True,
            'place': place
        })
        
    except Exception as e:
        logger.error(f"Error getting place details: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/categories')
def get_categories():
    """جلب فئات الأماكن"""
    try:
        if not db.conn:
            db.connect()
            
        query = """
            SELECT 
                pc.name, pc.name_ar, pc.icon, pc.color,
                COUNT(p.id) as places_count
            FROM place_categories pc
            LEFT JOIN places p ON p.category = pc.name AND p.is_active = true
            WHERE pc.is_active = true
            GROUP BY pc.id, pc.name, pc.name_ar, pc.icon, pc.color, pc.display_order
            ORDER BY pc.display_order ASC, places_count DESC
        """
        
        categories = db.execute_query(query)
        
        return jsonify({
            'success': True,
            'categories': [dict(cat) for cat in categories]
        })
        
    except Exception as e:
        logger.error(f"Error getting categories: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/governorates')
def get_governorates():
    """جلب المحافظات مع إحصائيات"""
    try:
        if not db.conn:
            db.connect()
            
        query = """
            SELECT 
                l.id, l.name, l.name_ar, l.latitude, l.longitude,
                l.data_status, l.places_count, l.photos_count,
                COUNT(p.id) as actual_places_count
            FROM locations l
            LEFT JOIN places p ON p.governorate_id = l.id AND p.is_active = true
            WHERE l.type = 'governorate'
            GROUP BY l.id, l.name, l.name_ar, l.latitude, l.longitude, 
                     l.data_status, l.places_count, l.photos_count
            ORDER BY l.name_ar
        """
        
        governorates = db.execute_query(query)
        
        return jsonify({
            'success': True,
            'governorates': [dict(gov) for gov in governorates]
        })
        
    except Exception as e:
        logger.error(f"Error getting governorates: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """جلب إحصائيات عامة"""
    try:
        if not db.conn:
            db.connect()
            
        stats = {}
        
        # إجمالي الأماكن
        total_places = db.execute_query(
            "SELECT COUNT(*) as count FROM places WHERE is_active = true"
        )
        stats['total_places'] = total_places[0]['count'] if total_places else 0
        
        # إجمالي الصور
        total_photos = db.execute_query(
            "SELECT COUNT(*) as count FROM place_photos WHERE is_active = true"
        )
        stats['total_photos'] = total_photos[0]['count'] if total_photos else 0
        
        # الأماكن حسب الفئة
        categories_stats = db.execute_query("""
            SELECT category, COUNT(*) as count
            FROM places 
            WHERE is_active = true
            GROUP BY category
            ORDER BY count DESC
            LIMIT 10
        """)
        stats['categories'] = [dict(cat) for cat in categories_stats]
        
        # المحافظات حسب عدد الأماكن
        governorates_stats = db.execute_query("""
            SELECT l.name_ar, COUNT(p.id) as count
            FROM locations l
            LEFT JOIN places p ON p.governorate_id = l.id AND p.is_active = true
            WHERE l.type = 'governorate'
            GROUP BY l.id, l.name_ar
            ORDER BY count DESC
            LIMIT 10
        """)
        stats['governorates'] = [dict(gov) for gov in governorates_stats]
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/images/<path:filename>')
def serve_image(filename):
    """تقديم الصور"""
    try:
        # البحث في مجلدات الصور المختلفة
        image_dirs = [
            IMAGES_DIR / "places",
            IMAGES_DIR / "imported",
            IMAGES_DIR / "temp"
        ]
        
        for img_dir in image_dirs:
            if (img_dir / filename).exists():
                return send_from_directory(str(img_dir), filename)
        
        # إذا لم توجد الصورة، إرجاع صورة افتراضية
        return send_from_directory(str(STATIC_DIR / "assets"), "no-image.png")
        
    except Exception as e:
        logger.error(f"Error serving image {filename}: {e}")
        return "Image not found", 404

@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # إنشاء المجلدات المطلوبة
    os.makedirs(IMAGES_DIR / "places", exist_ok=True)
    os.makedirs(IMAGES_DIR / "imported", exist_ok=True)
    os.makedirs(IMAGES_DIR / "temp", exist_ok=True)
    os.makedirs(STATIC_DIR / "assets", exist_ok=True)
    os.makedirs(BASE_DIR / "logs", exist_ok=True)
    
    # تشغيل الخادم
    print("🚀 Starting Yemen Maps Complete Server...")
    print(f"📁 Base directory: {BASE_DIR}")
    print(f"🖼️ Images directory: {IMAGES_DIR}")
    print(f"🌐 Server will be available at: http://localhost:5000")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )

const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'yemen_gps',
  user: 'yemen',
  password: 'admin'
});

async function checkUsers() {
  try {
    const res = await pool.query('SELECT * FROM users');
    console.log('المستخدمون:', res.rows);
  } catch (error) {
    console.error('خطأ في الاستعلام:', error);
  } finally {
    await pool.end();
  }
}

checkUsers();

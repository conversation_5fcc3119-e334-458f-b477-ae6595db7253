// سكريبت لإصلاح جميع ملفات login.html
const fs = require('fs');
const path = require('path');

// البحث عن جميع ملفات login.html
function findFiles(dir, fileName, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findFiles(filePath, fileName, results);
    } else if (file === fileName) {
      results.push(filePath);
    }
  }
  
  return results;
}

// إصلاح ملف login.html
function fixLoginFile(filePath) {
  console.log(`إصلاح ملف: ${filePath}`);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // استبدال جميع الروابط لـ placeholder.com
    content = content.replace(/https:\/\/via\.placeholder\.com\/[^"'\s]*/g, 'img/default-logo.png');
    
    // التأكد من استخدام صورة محلية للشعار
    content = content.replace(/<img[^>]*src=["'][^"']*placeholder[^"']*["'][^>]*>/g, 
      '<img src="img/logo.png" alt="شعار يمن GPS" class="logo" onerror="this.src=\'img/default-logo.png\'">'
    );
    
    // حفظ التغييرات
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`تم إصلاح الملف: ${filePath}`);
    
    return true;
  } catch (error) {
    console.error(`خطأ في إصلاح الملف ${filePath}:`, error);
    return false;
  }
}

// التأكد من وجود مجلد img وملف default-logo.png
const imgDir = './public/img';
if (!fs.existsSync(imgDir)) {
  fs.mkdirSync(imgDir, { recursive: true });
  console.log(`تم إنشاء مجلد ${imgDir}`);
}

// إنشاء ملف default-logo.png بسيط إذا لم يكن موجودًا
const defaultLogoPath = path.join(imgDir, 'default-logo.png');
if (!fs.existsSync(defaultLogoPath)) {
  // إنشاء ملف صورة بسيط (1x1 بكسل أبيض)
  const simpleImage = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==', 'base64');
  fs.writeFileSync(defaultLogoPath, simpleImage);
  console.log(`تم إنشاء صورة شعار افتراضية بسيطة في ${defaultLogoPath}`);
}

// البحث عن جميع ملفات login.html وإصلاحها
const loginFiles = findFiles('.', 'login.html');
console.log('تم العثور على ملفات login.html في المسارات التالية:');
let fixedCount = 0;

loginFiles.forEach(file => {
  console.log(file);
  if (fixLoginFile(file)) {
    fixedCount++;
  }
});

console.log(`تم إصلاح ${fixedCount} من ${loginFiles.length} ملف login.html`);

package com.yemengps.app.ui.auth;

import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.yemengps.app.R;
import com.yemengps.app.YemenGpsApplication;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.data.UserRepository;
import com.yemengps.app.model.User;
import com.yemengps.app.network.ApiClient;
import com.yemengps.app.network.ApiResponse;
import com.yemengps.app.ui.MainActivity;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Activity for user login
 */
public class LoginActivity extends AppCompatActivity {

    private TextInputLayout emailLayout;
    private TextInputEditText emailInput;
    private TextInputLayout passwordLayout;
    private TextInputEditText passwordInput;
    private Button loginButton;
    private TextView registerLink;
    private TextView forgotPasswordLink;
    private View progressBar;

    private ApiClient apiClient;
    private UserRepository userRepository;
    private PreferenceManager preferenceManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // Initialize components
        emailLayout = findViewById(R.id.email_layout);
        emailInput = findViewById(R.id.email_input);
        passwordLayout = findViewById(R.id.password_layout);
        passwordInput = findViewById(R.id.password_input);
        loginButton = findViewById(R.id.login_button);
        registerLink = findViewById(R.id.register_link);
        forgotPasswordLink = findViewById(R.id.forgot_password_link);
        progressBar = findViewById(R.id.progress_bar);

        // Initialize repositories
        apiClient = YemenGpsApplication.getInstance().getApiClient();
        userRepository = new UserRepository(YemenGpsApplication.getInstance().getDatabase());
        preferenceManager = YemenGpsApplication.getInstance().getPreferenceManager();

        // Check if already logged in
        if (preferenceManager.isLoggedIn()) {
            navigateToMainActivity();
            return;
        }

        // Set click listeners
        loginButton.setOnClickListener(v -> login());
        registerLink.setOnClickListener(v -> navigateToRegister());
        forgotPasswordLink.setOnClickListener(v -> navigateToForgotPassword());
    }

    /**
     * Login the user
     */
    private void login() {
        // Validate input
        String email = emailInput.getText().toString().trim();
        String password = passwordInput.getText().toString().trim();

        if (email.isEmpty()) {
            emailLayout.setError("Please enter your email");
            return;
        }

        if (password.isEmpty()) {
            passwordLayout.setError("Please enter your password");
            return;
        }

        // Show progress
        showProgress(true);

        // Get device ID
        String deviceId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        // Login with API
        apiClient.loginUser(email, deviceId, new Callback<ApiResponse<User>>() {
            @Override
            public void onResponse(Call<ApiResponse<User>> call, Response<ApiResponse<User>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    User user = response.body().getData();
                    if (user != null) {
                        // Save user to local database
                        userRepository.insert(user);

                        // Save login info to preferences
                        preferenceManager.saveUserLogin(
                                user.getId(),
                                user.getName(),
                                user.getEmail(),
                                user.getAuthToken(),
                                user.isAdmin()
                        );

                        // Navigate to main activity
                        runOnUiThread(() -> {
                            showProgress(false);
                            Toast.makeText(LoginActivity.this, R.string.success_login, Toast.LENGTH_SHORT).show();
                            navigateToMainActivity();
                        });
                    } else {
                        runOnUiThread(() -> {
                            showProgress(false);
                            Toast.makeText(LoginActivity.this, R.string.error_login, Toast.LENGTH_LONG).show();
                        });
                    }
                } else {
                    runOnUiThread(() -> {
                        showProgress(false);
                        Toast.makeText(LoginActivity.this, R.string.error_login, Toast.LENGTH_LONG).show();
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<User>> call, Throwable t) {
                runOnUiThread(() -> {
                    showProgress(false);
                    Toast.makeText(LoginActivity.this, R.string.error_network, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * Navigate to the register activity
     */
    private void navigateToRegister() {
        Intent intent = new Intent(this, RegisterActivity.class);
        startActivity(intent);
    }

    /**
     * Navigate to the forgot password activity
     */
    private void navigateToForgotPassword() {
        // This would be implemented in a real app
        Toast.makeText(this, "Forgot password not implemented", Toast.LENGTH_SHORT).show();
    }

    /**
     * Navigate to the main activity
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * Show or hide the progress bar
     * @param show True to show the progress bar, false to hide it
     */
    private void showProgress(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        loginButton.setEnabled(!show);
        registerLink.setEnabled(!show);
        forgotPasswordLink.setEnabled(!show);
    }
}

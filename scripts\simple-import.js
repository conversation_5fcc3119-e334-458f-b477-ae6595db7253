// استيراد مبسط للبيانات المسحوبة
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
    user: 'yemen',
    host: 'localhost',
    database: 'yemen_gps',
    password: 'admin',
    port: 5432,
});

async function simpleImport() {
    try {
        console.log('📥 بدء الاستيراد المبسط للبيانات المسحوبة...\n');
        
        // قراءة البيانات الخام
        const dataPath = path.join(__dirname, '../data/yemen_places_scraped.json');
        
        if (!fs.existsSync(dataPath)) {
            console.error('❌ ملف البيانات غير موجود:', dataPath);
            return;
        }
        
        const rawData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        console.log(`📊 عدد الأماكن في الملف: ${rawData.length}\n`);
        
        let successCount = 0;
        let skipCount = 0;
        
        // الحصول على معرفات الفئات والمحافظات
        const categories = await pool.query('SELECT id, name_ar FROM place_categories');
        const governorates = await pool.query('SELECT id, name_ar FROM governorates');
        
        const categoryMap = {};
        categories.rows.forEach(cat => {
            categoryMap[cat.name_ar] = cat.id;
        });
        
        const governorateMap = {};
        governorates.rows.forEach(gov => {
            governorateMap[gov.name_ar] = gov.id;
        });
        
        // استيراد كل مكان
        for (const place of rawData) {
            try {
                console.log(`📝 فحص: ${place.name_ar}`);
                
                // التحقق من وجود المكان مسبقاً
                const existingPlace = await pool.query(`
                    SELECT id FROM places 
                    WHERE name_ar = $1 OR name_en = $2
                    LIMIT 1
                `, [place.name_ar, place.name_en]);
                
                if (existingPlace.rows.length > 0) {
                    console.log(`   ℹ️ المكان موجود مسبقاً - تم التجاهل`);
                    skipCount++;
                    continue;
                }
                
                // تحديد معرف الفئة
                let categoryId = null;
                if (place.category_ar === 'فنادق') {
                    categoryId = categoryMap['فنادق 4 نجوم'] || categoryMap['فنادق'];
                } else if (place.category_ar === 'مطاعم') {
                    categoryId = categoryMap['مطاعم يمنية'] || categoryMap['مطاعم'];
                } else if (place.category_ar === 'معالم سياحية') {
                    categoryId = categoryMap['مواقع تاريخية'] || categoryMap['معالم سياحية'];
                } else if (place.category_ar === 'مستشفيات') {
                    categoryId = categoryMap['مستشفيات'] || categoryMap['مراكز طبية'];
                } else if (place.category_ar === 'جامعات') {
                    categoryId = categoryMap['جامعات'] || categoryMap['تعليم'];
                }
                
                // تحديد معرف المحافظة
                let governorateId = governorateMap[place.city_ar] || 1; // افتراضي صنعاء
                
                // إدراج المكان
                const insertQuery = `
                    INSERT INTO places (
                        name_ar, name_en, description_ar, 
                        latitude, longitude, address_ar, 
                        phone, website, rating, reviews_count, 
                        category_id, governorate_id, is_verified
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    RETURNING id
                `;
                
                const values = [
                    place.name_ar,
                    place.name_en,
                    `${place.category_ar} في ${place.city_ar} - مسحوب من Google Places API`,
                    place.latitude,
                    place.longitude,
                    place.address || '',
                    place.phone || '',
                    place.website || '',
                    place.rating || 0,
                    place.reviews_count || 0,
                    categoryId,
                    governorateId,
                    true
                ];
                
                const result = await pool.query(insertQuery, values);
                const placeId = result.rows[0].id;
                
                console.log(`   ✅ تم الإدراج بمعرف: ${placeId}`);
                
                // إدراج الصور إذا كانت متوفرة
                if (place.photos && place.photos.length > 0) {
                    for (let i = 0; i < Math.min(place.photos.length, 3); i++) {
                        const photo = place.photos[i];
                        try {
                            await pool.query(`
                                INSERT INTO place_images (place_id, image_url, image_type, title_ar, sort_order)
                                VALUES ($1, $2, $3, $4, $5)
                            `, [placeId, photo.url, i === 0 ? 'main' : 'gallery', `صورة ${i + 1}`, i + 1]);
                            
                            console.log(`     📸 تم إدراج صورة ${i + 1}`);
                        } catch (imgError) {
                            console.log(`     ⚠️ خطأ في إدراج الصورة: ${imgError.message}`);
                        }
                    }
                }
                
                // إدراج التقييمات إذا كانت متوفرة
                if (place.reviews && place.reviews.length > 0) {
                    for (const review of place.reviews.slice(0, 3)) {
                        try {
                            await pool.query(`
                                INSERT INTO place_reviews (place_id, user_name, rating, comment_ar, is_verified)
                                VALUES ($1, $2, $3, $4, $5)
                            `, [placeId, review.author, review.rating, review.text, true]);
                            
                            console.log(`     💬 تم إدراج تقييم من: ${review.author}`);
                        } catch (reviewError) {
                            console.log(`     ⚠️ خطأ في إدراج التقييم: ${reviewError.message}`);
                        }
                    }
                }
                
                successCount++;
                
            } catch (error) {
                console.error(`   ❌ خطأ في إدراج ${place.name_ar}:`, error.message);
            }
        }
        
        console.log(`\n🎉 انتهى الاستيراد!`);
        console.log(`✅ تم إدراج: ${successCount} مكان جديد`);
        console.log(`ℹ️ تم تجاهل: ${skipCount} مكان موجود مسبقاً`);
        
        // التحقق من النتائج النهائية
        const finalResult = await pool.query(`
            SELECT COUNT(*) as total_places 
            FROM places 
            WHERE is_verified = TRUE
        `);
        
        console.log(`\n📈 إجمالي الأماكن المؤكدة: ${finalResult.rows[0].total_places}`);
        
        // عرض الأماكن الجديدة
        const newPlaces = await pool.query(`
            SELECT name_ar, rating, reviews_count 
            FROM places 
            WHERE description_ar LIKE '%Google Places API%'
            ORDER BY rating DESC, reviews_count DESC
            LIMIT 10
        `);
        
        if (newPlaces.rows.length > 0) {
            console.log('\n🆕 الأماكن الجديدة المضافة:');
            newPlaces.rows.forEach((place, index) => {
                console.log(`   ${index + 1}. ${place.name_ar} - ${place.rating} نجمة (${place.reviews_count} تقييم)`);
            });
        }
        
        // إحصائيات الفئات
        const categoryStats = await pool.query(`
            SELECT 
                c.name_ar as category,
                COUNT(p.id) as count
            FROM places p
            LEFT JOIN place_categories c ON p.category_id = c.id
            WHERE p.is_verified = TRUE
            GROUP BY c.name_ar
            ORDER BY count DESC
            LIMIT 10
        `);
        
        console.log('\n📊 إحصائيات الفئات:');
        categoryStats.rows.forEach(row => {
            console.log(`   ${row.category || 'غير محدد'}: ${row.count} مكان`);
        });
        
    } catch (error) {
        console.error('❌ خطأ في الاستيراد:', error);
    } finally {
        await pool.end();
    }
}

simpleImport();

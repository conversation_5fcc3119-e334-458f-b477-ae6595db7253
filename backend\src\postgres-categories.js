// يمن ناف - ملف إدارة التصنيفات باستخدام قاعدة بيانات PostgreSQL
const db = require('./postgres-db');

// دالة للحصول على جميع التصنيفات
async function getAllCategories() {
    try {
        const query = `
            SELECT 
                id as category_id,
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            FROM categories
            ORDER BY display_order, name
        `;
        
        const result = await db.query(query);
        return result.rows;
    } catch (error) {
        console.error('خطأ في الحصول على التصنيفات:', error);
        throw error;
    }
}

// دالة للحصول على تصنيف بواسطة المعرف
async function getCategoryById(categoryId) {
    try {
        const query = `
            SELECT 
                id as category_id,
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            FROM categories
            WHERE id = $1
        `;
        
        const result = await db.query(query, [categoryId]);
        return result.rows[0] || null;
    } catch (error) {
        console.error('خطأ في الحصول على التصنيف:', error);
        throw error;
    }
}

// دالة لإنشاء تصنيف جديد
async function createCategory(categoryData) {
    const client = await db.getClient();
    
    try {
        await client.query('BEGIN');
        
        // الحصول على أعلى ترتيب عرض
        const orderQuery = 'SELECT MAX(display_order) as max_order FROM categories';
        const orderResult = await client.query(orderQuery);
        const maxOrder = orderResult.rows[0].max_order || 0;
        
        // إدراج التصنيف الجديد
        const insertQuery = `
            INSERT INTO categories (
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING id
        `;
        
        const values = [
            categoryData.name,
            categoryData.icon || 'fa-map-marker',
            categoryData.color || '#4CAF50',
            categoryData.display_order || (maxOrder + 1),
            categoryData.is_active !== undefined ? categoryData.is_active : true
        ];
        
        const result = await client.query(insertQuery, values);
        const categoryId = result.rows[0].id;
        
        // الحصول على بيانات التصنيف المدرج
        const categoryQuery = `
            SELECT 
                id as category_id,
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            FROM categories
            WHERE id = $1
        `;
        
        const categoryResult = await client.query(categoryQuery, [categoryId]);
        
        await client.query('COMMIT');
        
        return categoryResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في إنشاء التصنيف:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لتحديث بيانات تصنيف
async function updateCategory(categoryId, categoryData) {
    const client = await db.getClient();
    
    try {
        await client.query('BEGIN');
        
        // بناء استعلام التحديث
        let updateQuery = 'UPDATE categories SET ';
        const values = [];
        const updateFields = [];
        let paramIndex = 1;
        
        if (categoryData.name !== undefined) {
            updateFields.push(`name = $${paramIndex++}`);
            values.push(categoryData.name);
        }
        
        if (categoryData.icon !== undefined) {
            updateFields.push(`icon = $${paramIndex++}`);
            values.push(categoryData.icon);
        }
        
        if (categoryData.color !== undefined) {
            updateFields.push(`color = $${paramIndex++}`);
            values.push(categoryData.color);
        }
        
        if (categoryData.display_order !== undefined) {
            updateFields.push(`display_order = $${paramIndex++}`);
            values.push(categoryData.display_order);
        }
        
        if (categoryData.is_active !== undefined) {
            updateFields.push(`is_active = $${paramIndex++}`);
            values.push(categoryData.is_active);
        }
        
        // إضافة تاريخ التحديث
        updateFields.push(`updated_at = $${paramIndex++}`);
        values.push(new Date());
        
        // إذا لم تكن هناك حقول للتحديث
        if (updateFields.length === 0) {
            return await getCategoryById(categoryId);
        }
        
        // إكمال استعلام التحديث
        updateQuery += updateFields.join(', ');
        updateQuery += ` WHERE id = $${paramIndex}`;
        values.push(categoryId);
        
        // تنفيذ استعلام التحديث
        await client.query(updateQuery, values);
        
        // الحصول على بيانات التصنيف المحدثة
        const categoryQuery = `
            SELECT 
                id as category_id,
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            FROM categories
            WHERE id = $1
        `;
        
        const categoryResult = await client.query(categoryQuery, [categoryId]);
        
        await client.query('COMMIT');
        
        return categoryResult.rows[0];
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في تحديث التصنيف:', error);
        throw error;
    } finally {
        client.release();
    }
}

// دالة لحذف تصنيف
async function deleteCategory(categoryId) {
    try {
        // التحقق من استخدام التصنيف في نقاط الموقع
        const checkQuery = 'SELECT COUNT(*) as count FROM locations WHERE category_id = $1';
        const checkResult = await db.query(checkQuery, [categoryId]);
        
        if (parseInt(checkResult.rows[0].count) > 0) {
            throw new Error('لا يمكن حذف التصنيف لأنه مستخدم في نقاط الموقع');
        }
        
        const query = 'DELETE FROM categories WHERE id = $1';
        await db.query(query, [categoryId]);
        
        return true;
    } catch (error) {
        console.error('خطأ في حذف التصنيف:', error);
        throw error;
    }
}

// دالة للبحث عن التصنيفات
async function searchCategories(searchTerm) {
    try {
        const query = `
            SELECT 
                id as category_id,
                name,
                icon,
                color,
                display_order,
                is_active,
                created_at,
                updated_at
            FROM categories
            WHERE 
                name ILIKE $1 OR
                icon ILIKE $1
            ORDER BY display_order, name
        `;
        
        const result = await db.query(query, [`%${searchTerm}%`]);
        return result.rows;
    } catch (error) {
        console.error('خطأ في البحث عن التصنيفات:', error);
        throw error;
    }
}

// دالة للحصول على عدد التصنيفات
async function getCategoriesCount() {
    try {
        const query = 'SELECT COUNT(*) as count FROM categories';
        const result = await db.query(query);
        return parseInt(result.rows[0].count);
    } catch (error) {
        console.error('خطأ في الحصول على عدد التصنيفات:', error);
        throw error;
    }
}

// دالة لتحديث ترتيب عرض التصنيفات
async function updateCategoriesOrder(orderData) {
    const client = await db.getClient();
    
    try {
        await client.query('BEGIN');
        
        for (const item of orderData) {
            await client.query(
                'UPDATE categories SET display_order = $1, updated_at = NOW() WHERE id = $2',
                [item.order, item.id]
            );
        }
        
        await client.query('COMMIT');
        
        return true;
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('خطأ في تحديث ترتيب التصنيفات:', error);
        throw error;
    } finally {
        client.release();
    }
}

// تصدير الدوال
module.exports = {
    getAllCategories,
    getCategoryById,
    createCategory,
    updateCategory,
    deleteCategory,
    searchCategories,
    getCategoriesCount,
    updateCategoriesOrder
};

package com.yemengps.app;

import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.os.Build;

import com.yemengps.app.data.AppDatabase;
import com.yemengps.app.data.LocationRepository;
import com.yemengps.app.data.PreferenceManager;
import com.yemengps.app.network.ApiClient;
import com.yemengps.app.offline.ConnectivityMonitor;
import com.yemengps.app.offline.OfflineLocationRepository;
import com.yemengps.app.offline.OfflineMapManager;

/**
 * Main application class for Yemen GPS
 * Handles initialization of app components
 */
public class YemenGpsApplication extends Application {

    public static final String LOCATION_CHANNEL_ID = "location_channel";
    public static final String ALERT_CHANNEL_ID = "alert_channel";
    
    private static YemenGpsApplication instance;
    private AppDatabase database;
    private PreferenceManager preferenceManager;
    private ApiClient apiClient;
    private LocationRepository locationRepository;
    private OfflineMapManager offlineMapManager;
    private OfflineLocationRepository offlineLocationRepository;

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        
        // Initialize database
        database = AppDatabase.getInstance(this);
        
        // Initialize preferences
        preferenceManager = new PreferenceManager(this);
        
        // Initialize API client
        apiClient = new ApiClient(this);
        
        // Initialize repositories
        locationRepository = new LocationRepository(database.locationDao(), apiClient);
        
        // Initialize offline components
        ConnectivityMonitor connectivityMonitor = new ConnectivityMonitor(this);
        offlineLocationRepository = new OfflineLocationRepository(database, apiClient, locationRepository, connectivityMonitor);
        offlineMapManager = new OfflineMapManager(this, locationRepository);
        
        // Create notification channels
        createNotificationChannels();
    }

    /**
     * Create notification channels for Android O and above
     */
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Location service channel
            NotificationChannel locationChannel = new NotificationChannel(
                    LOCATION_CHANNEL_ID,
                    "Location Service",
                    NotificationManager.IMPORTANCE_LOW);
            locationChannel.setDescription("Used for the location tracking service");
            
            // Alert channel
            NotificationChannel alertChannel = new NotificationChannel(
                    ALERT_CHANNEL_ID,
                    "Location Alerts",
                    NotificationManager.IMPORTANCE_HIGH);
            alertChannel.setDescription("Used for location proximity alerts");
            alertChannel.enableVibration(true);
            
            // Register the channels
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(locationChannel);
                manager.createNotificationChannel(alertChannel);
            }
        }
    }

    /**
     * Get the application instance
     * @return The application instance
     */
    public static YemenGpsApplication getInstance() {
        return instance;
    }

    /**
     * Get the database instance
     * @return The database instance
     */
    public AppDatabase getDatabase() {
        return database;
    }

    /**
     * Get the preference manager
     * @return The preference manager
     */
    public PreferenceManager getPreferenceManager() {
        return preferenceManager;
    }

    /**
     * Get the API client
     * @return The API client
     */
    public ApiClient getApiClient() {
        return apiClient;
    }
    
    /**
     * Get the location repository
     * @return The location repository
     */
    public LocationRepository getLocationRepository() {
        return locationRepository;
    }
    
    /**
     * Get the offline map manager
     * @return The offline map manager
     */
    public OfflineMapManager getOfflineMapManager() {
        return offlineMapManager;
    }
    
    /**
     * Get the offline location repository
     * @return The offline location repository
     */
    public OfflineLocationRepository getOfflineLocationRepository() {
        return offlineLocationRepository;
    }
}

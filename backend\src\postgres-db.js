// يمن ناف - ملف إدارة قاعدة بيانات PostgreSQL
const { Pool } = require('pg');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
dotenv.config();

// طباعة معلومات الاتصال للتصحيح
console.log('معلومات الاتصال بقاعدة البيانات:');
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_PORT:', process.env.DB_PORT);

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    user: process.env.DB_USER || 'yemen',
    password: process.env.DB_PASSWORD || 'yemen123',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'yemen_gps',
    // إعدادات إضافية
    max: 20, // الحد الأقصى لعدد الاتصالات في المجمع
    idleTimeoutMillis: 30000, // وقت انتهاء صلاحية الاتصال الخامل
    connectionTimeoutMillis: parseInt(process.env.DB_CONNECT_TIMEOUT || '10') * 1000, // وقت انتهاء صلاحية محاولة الاتصال
    ssl: process.env.DB_SSL_MODE === 'require' ? { rejectUnauthorized: false } : false
};

// طباعة معلومات الاتصال (بدون كلمة المرور للأمان)
console.log('محاولة الاتصال بقاعدة البيانات PostgreSQL باستخدام المعلومات التالية:');
console.log(`host: ${dbConfig.host}, port: ${dbConfig.port}, database: ${dbConfig.database}, user: ${dbConfig.user}`);
console.log(`max connections: ${dbConfig.max}, connection timeout: ${dbConfig.connectionTimeoutMillis}ms`);
console.log(`ssl mode: ${dbConfig.ssl ? 'enabled' : 'disabled'}`);

// إنشاء مجمع اتصالات قاعدة البيانات
const pool = new Pool(dbConfig);

// الاستماع لأحداث الاتصال
pool.on('connect', () => {
    console.log('تم الاتصال بقاعدة بيانات PostgreSQL بنجاح');
});

pool.on('error', (err) => {
    console.error('خطأ في اتصال قاعدة بيانات PostgreSQL:', err);
});

// اختبار الاتصال عند بدء التشغيل
pool.query('SELECT NOW()')
  .then(result => {
    console.log('تم اختبار الاتصال بقاعدة البيانات PostgreSQL بنجاح!');
    console.log('وقت الخادم:', result.rows[0].now);
  })
  .catch(err => {
    console.error('فشل اختبار الاتصال بقاعدة البيانات PostgreSQL:');
    console.error(err);
  });

// دالة للتحقق من اتصال قاعدة البيانات
async function checkConnection() {
    let client;
    try {
        console.log('جاري التحقق من اتصال قاعدة بيانات PostgreSQL...');
        console.log('معلومات الاتصال:', {
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database,
            user: dbConfig.user,
            ssl: dbConfig.ssl ? 'enabled' : 'disabled'
        });

        client = await pool.connect();
        console.log('تم الاتصال بقاعدة البيانات بنجاح!');

        // التحقق من الاتصال بإجراء استعلام بسيط
        const result = await client.query('SELECT 1 as connected');
        const isConnected = result.rows[0].connected === 1;

        console.log('تم التحقق من اتصال قاعدة بيانات PostgreSQL بنجاح:', isConnected);

        // التحقق من وجود الجداول
        try {
            const tablesResult = await client.query(`
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            `);

            console.log('الجداول الموجودة في قاعدة البيانات:');
            tablesResult.rows.forEach(row => {
                console.log(`- ${row.table_name}`);
            });

            if (tablesResult.rows.length === 0) {
                console.warn('لا توجد جداول في قاعدة البيانات! يجب تنفيذ سكريبت إنشاء الجداول.');
            }
        } catch (tablesError) {
            console.error('خطأ في الحصول على قائمة الجداول:', tablesError.message);
        }

        return {
            connected: isConnected,
            message: isConnected ? 'تم الاتصال بقاعدة البيانات بنجاح' : 'فشل الاتصال بقاعدة البيانات',
            database: dbConfig.database,
            host: dbConfig.host,
            port: dbConfig.port,
            user: dbConfig.user
        };
    } catch (error) {
        console.error('فشل التحقق من اتصال قاعدة بيانات PostgreSQL:', error.message);
        console.error('تفاصيل الخطأ:', error);

        // محاولة إعادة الاتصال
        try {
            console.log('محاولة إعادة الاتصال بقاعدة البيانات...');
            // إنشاء اتصال جديد للتحقق
            const { Pool: TestPool } = require('pg');
            const testPool = new TestPool(dbConfig);
            const testClient = await testPool.connect();
            testClient.release();
            testPool.end();
            console.log('تم إعادة الاتصال بقاعدة البيانات بنجاح');
            return {
                connected: true,
                message: 'تم إعادة الاتصال بقاعدة البيانات بنجاح',
                database: dbConfig.database,
                host: dbConfig.host,
                port: dbConfig.port,
                user: dbConfig.user
            };
        } catch (reconnectError) {
            console.error('فشل إعادة الاتصال بقاعدة البيانات:', reconnectError.message);
            console.error('تفاصيل خطأ إعادة الاتصال:', reconnectError);
            return {
                connected: false,
                message: 'فشل الاتصال بقاعدة البيانات: ' + error.message,
                error: error.message,
                database: dbConfig.database,
                host: dbConfig.host,
                port: dbConfig.port,
                user: dbConfig.user
            };
        }
    } finally {
        if (client) {
            client.release();
        }
    }
}

// دالة لتنفيذ استعلام
async function query(text, params) {
    const start = Date.now();
    try {
        const res = await pool.query(text, params);
        const duration = Date.now() - start;
        console.log('تم تنفيذ الاستعلام:', { text, duration, rows: res.rowCount });
        return res;
    } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error);
        throw error;
    }
}

// دالة للحصول على عميل من المجمع
async function getClient() {
    const client = await pool.connect();
    const query = client.query;
    const release = client.release;

    // تعديل دالة الإصدار للتسجيل
    client.release = () => {
        release.apply(client);
        console.log('تم إصدار عميل قاعدة البيانات');
    };

    // تعديل دالة الاستعلام للتسجيل
    client.query = (...args) => {
        client.lastQuery = args;
        return query.apply(client, args);
    };

    return client;
}

// دالة للحصول على معلومات قاعدة البيانات
async function getDatabaseInfo() {
    try {
        // الحصول على حجم قاعدة البيانات
        const dbSizeQuery = `
            SELECT pg_size_pretty(pg_database_size($1)) as size
            FROM pg_database
            WHERE datname = $1
        `;
        const dbSizeResult = await query(dbSizeQuery, [dbConfig.database]);
        const dbSize = dbSizeResult.rows[0]?.size || '0 bytes';

        // الحصول على معلومات الجداول
        const tablesQuery = `
            SELECT
                table_name,
                (SELECT count(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns,
                pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as size
            FROM information_schema.tables t
            WHERE table_schema = 'public'
            ORDER BY table_name
        `;

        // استخدام استعلام بديل في حالة فشل الاستعلام السابق
        const fallbackTablesQuery = `
            SELECT
                table_name,
                (SELECT count(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns
            FROM information_schema.tables t
            WHERE table_schema = 'public'
            ORDER BY table_name
        `;

        let tablesResult;
        try {
            tablesResult = await query(tablesQuery, []);
        } catch (error) {
            console.warn('فشل الحصول على معلومات مفصلة للجداول، استخدام استعلام بديل:', error);
            tablesResult = await query(fallbackTablesQuery, []);
        }

        // الحصول على عدد السجلات في كل جدول
        const tables = [];
        for (const row of tablesResult.rows) {
            let recordCount = 0;
            try {
                const countQuery = `SELECT count(*) as count FROM "${row.table_name}"`;
                const countResult = await query(countQuery);
                recordCount = parseInt(countResult.rows[0]?.count || '0');
            } catch (error) {
                console.warn(`فشل الحصول على عدد السجلات في جدول ${row.table_name}:`, error);
            }

            tables.push({
                name: row.table_name,
                columns: row.columns,
                records: recordCount,
                size: row.size || '0 bytes',
                lastUpdate: new Date().toISOString()
            });
        }

        // الحصول على معلومات النسخ الاحتياطي
        let lastBackup = null;
        try {
            const backupQuery = `
                SELECT max(backup_time) as last_backup
                FROM backup_log
            `;
            const backupResult = await query(backupQuery, []);
            lastBackup = backupResult.rows[0]?.last_backup || null;
        } catch (error) {
            console.warn('فشل الحصول على معلومات النسخ الاحتياطي:', error);
        }

        return {
            connected: true,
            type: 'PostgreSQL',
            version: 'PostgreSQL 14.x',
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database,
            user: dbConfig.user,
            tables,
            size: dbSize,
            lastBackup,
            connections: {
                max: dbConfig.max,
                current: (await query('SELECT count(*) as count FROM pg_stat_activity', [])).rows[0]?.count || 0
            }
        };
    } catch (error) {
        console.error('خطأ في الحصول على معلومات قاعدة البيانات:', error);
        return {
            connected: false,
            error: error.message
        };
    }
}

// دالة لإنشاء نسخة احتياطية من قاعدة البيانات
async function backupDatabase(backupPath) {
    try {
        const { exec } = require('child_process');
        const path = require('path');

        // إنشاء اسم ملف النسخة الاحتياطية
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupPath || './backups', `${dbConfig.database}_${timestamp}.sql`);

        // إنشاء أمر النسخ الاحتياطي
        const command = `pg_dump -U ${dbConfig.user} -h ${dbConfig.host} -p ${dbConfig.port} -d ${dbConfig.database} -f "${backupFile}"`;

        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    console.error('خطأ في إنشاء نسخة احتياطية:', error);
                    reject(error);
                    return;
                }

                console.log('تم إنشاء نسخة احتياطية بنجاح:', backupFile);

                // تسجيل النسخة الاحتياطية في قاعدة البيانات
                query(
                    'INSERT INTO backup_log (backup_file, backup_time) VALUES ($1, $2)',
                    [backupFile, new Date()]
                ).catch(err => console.error('خطأ في تسجيل النسخة الاحتياطية:', err));

                resolve(backupFile);
            });
        });
    } catch (error) {
        console.error('خطأ في إنشاء نسخة احتياطية:', error);
        throw error;
    }
}

// تصدير الدوال
module.exports = {
    pool,
    query,
    getClient,
    checkConnection,
    getDatabaseInfo,
    backupDatabase
};

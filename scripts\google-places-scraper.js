// سكريبت لسحب بيانات الأماكن من Google Places API
// Google Places API Data Scraper

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// مفتاح Google Places API (يجب الحصول عليه من Google Cloud Console)
const GOOGLE_API_KEY = 'YOUR_GOOGLE_PLACES_API_KEY'; // ضع مفتاحك هنا

// إحداثيات المحافظات اليمنية
const YEMEN_CITIES = [
    { name: 'صنعاء', name_en: 'Sanaa', lat: 15.3547, lng: 44.2066 },
    { name: 'عدن', name_en: 'Aden', lat: 12.7797, lng: 45.0365 },
    { name: 'تعز', name_en: 'Taiz', lat: 13.5795, lng: 44.0205 },
    { name: 'الحديدة', name_en: '<PERSON>', lat: 14.7978, lng: 42.9545 },
    { name: 'إ<PERSON>', name_en: 'Ibb', lat: 13.9667, lng: 44.1833 },
    { name: 'ذما<PERSON>', name_en: 'Dhamar', lat: 14.5426, lng: 44.4054 },
    { name: 'المكلا', name_en: 'Al Mukalla', lat: 14.5425, lng: 49.1242 },
    { name: 'مأرب', name_en: 'Marib', lat: 15.4694, lng: 45.3222 }
];

// أنواع الأماكن المطلوبة
const PLACE_TYPES = [
    'lodging',           // فنادق
    'restaurant',        // مطاعم
    'tourist_attraction', // معالم سياحية
    'hospital',          // مستشفيات
    'school',            // مدارس
    'university',        // جامعات
    'shopping_mall',     // مراكز تسوق
    'gas_station',       // محطات وقود
    'bank',              // بنوك
    'mosque',            // مساجد
    'airport',           // مطارات
    'museum',            // متاحف
    'park',              // حدائق
    'pharmacy',          // صيدليات
    'cafe'               // مقاهي
];

class GooglePlacesScraper {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://maps.googleapis.com/maps/api/place';
        this.results = [];
        this.delay = 1000; // تأخير بين الطلبات لتجنب تجاوز الحدود
    }

    // البحث عن الأماكن القريبة
    async searchNearbyPlaces(lat, lng, type, radius = 5000) {
        try {
            const url = `${this.baseUrl}/nearbysearch/json`;
            const params = {
                location: `${lat},${lng}`,
                radius: radius,
                type: type,
                key: this.apiKey
            };

            console.log(`🔍 البحث عن ${type} في نطاق ${radius}م من (${lat}, ${lng})`);

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                console.log(`✅ تم العثور على ${response.data.results.length} مكان`);
                return response.data.results;
            } else {
                console.error(`❌ خطأ في البحث: ${response.data.status}`);
                return [];
            }
        } catch (error) {
            console.error('خطأ في طلب البحث:', error.message);
            return [];
        }
    }

    // الحصول على تفاصيل مكان محدد
    async getPlaceDetails(placeId) {
        try {
            const url = `${this.baseUrl}/details/json`;
            const params = {
                place_id: placeId,
                fields: 'name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,photos,opening_hours,geometry,types,reviews,price_level',
                key: this.apiKey
            };

            const response = await axios.get(url, { params });

            if (response.data.status === 'OK') {
                return response.data.result;
            } else {
                console.error(`❌ خطأ في الحصول على التفاصيل: ${response.data.status}`);
                return null;
            }
        } catch (error) {
            console.error('خطأ في طلب التفاصيل:', error.message);
            return null;
        }
    }

    // الحصول على رابط الصورة
    getPhotoUrl(photoReference, maxWidth = 800) {
        return `${this.baseUrl}/photo?maxwidth=${maxWidth}&photoreference=${photoReference}&key=${this.apiKey}`;
    }

    // تحميل صورة وحفظها محلياً
    async downloadPhoto(photoReference, filename) {
        try {
            const photoUrl = this.getPhotoUrl(photoReference);
            const response = await axios.get(photoUrl, { responseType: 'stream' });

            const imagePath = path.join(__dirname, '../public/images/places', filename);
            const writer = fs.createWriteStream(imagePath);

            response.data.pipe(writer);

            return new Promise((resolve, reject) => {
                writer.on('finish', () => resolve(imagePath));
                writer.on('error', reject);
            });
        } catch (error) {
            console.error('خطأ في تحميل الصورة:', error.message);
            return null;
        }
    }

    // معالجة وتنظيف البيانات
    processPlaceData(place, details, cityName) {
        const processedPlace = {
            google_place_id: place.place_id,
            name_ar: this.translateToArabic(place.name),
            name_en: place.name,
            description_ar: details?.reviews?.[0]?.text || '',
            description_en: details?.reviews?.[0]?.text || '',
            category: this.mapGoogleTypeToCategory(place.types[0]),
            city: cityName,
            latitude: place.geometry.location.lat,
            longitude: place.geometry.location.lng,
            address_ar: details?.formatted_address || '',
            address_en: details?.formatted_address || '',
            phone: details?.formatted_phone_number || '',
            website: details?.website || '',
            rating: details?.rating || 0,
            reviews_count: details?.user_ratings_total || 0,
            price_range: this.mapPriceLevel(details?.price_level),
            photos: details?.photos?.map(photo => ({
                reference: photo.photo_reference,
                url: this.getPhotoUrl(photo.photo_reference),
                width: photo.width,
                height: photo.height
            })) || [],
            opening_hours: details?.opening_hours?.weekday_text || [],
            reviews: details?.reviews?.slice(0, 5)?.map(review => ({
                author: review.author_name,
                rating: review.rating,
                text: review.text,
                time: review.time
            })) || []
        };

        return processedPlace;
    }

    // ترجمة تقريبية للعربية (يمكن تحسينها)
    translateToArabic(englishName) {
        const translations = {
            'Hotel': 'فندق',
            'Restaurant': 'مطعم',
            'Cafe': 'مقهى',
            'Hospital': 'مستشفى',
            'School': 'مدرسة',
            'University': 'جامعة',
            'Mall': 'مول',
            'Bank': 'بنك',
            'Mosque': 'مسجد',
            'Airport': 'مطار',
            'Museum': 'متحف',
            'Park': 'حديقة',
            'Pharmacy': 'صيدلية'
        };

        let arabicName = englishName;
        Object.keys(translations).forEach(en => {
            arabicName = arabicName.replace(new RegExp(en, 'gi'), translations[en]);
        });

        return arabicName;
    }

    // تحويل نوع Google إلى فئة محلية
    mapGoogleTypeToCategory(googleType) {
        const mapping = {
            'lodging': 'فنادق',
            'restaurant': 'مطاعم',
            'tourist_attraction': 'معالم سياحية',
            'hospital': 'مستشفيات',
            'school': 'مدارس',
            'university': 'جامعات',
            'shopping_mall': 'مراكز تسوق',
            'gas_station': 'محطات وقود',
            'bank': 'بنوك',
            'mosque': 'مساجد',
            'airport': 'مطارات',
            'museum': 'متاحف',
            'park': 'حدائق',
            'pharmacy': 'صيدليات',
            'cafe': 'مقاهي'
        };

        return mapping[googleType] || 'أخرى';
    }

    // تحويل مستوى السعر
    mapPriceLevel(priceLevel) {
        const mapping = {
            0: '$',
            1: '$',
            2: '$$',
            3: '$$$',
            4: '$$$$'
        };

        return mapping[priceLevel] || null;
    }

    // تأخير بين الطلبات
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // سحب جميع البيانات
    async scrapeAllPlaces() {
        console.log('🚀 بدء سحب بيانات الأماكن من Google Places API...\n');

        // إنشاء مجلد الصور إذا لم يكن موجوداً
        const imagesDir = path.join(__dirname, '../public/images/places');
        if (!fs.existsSync(imagesDir)) {
            fs.mkdirSync(imagesDir, { recursive: true });
        }

        for (const city of YEMEN_CITIES) {
            console.log(`\n🏙️ معالجة مدينة: ${city.name} (${city.name_en})`);

            for (const placeType of PLACE_TYPES) {
                console.log(`\n📍 البحث عن: ${placeType}`);

                // البحث عن الأماكن القريبة
                const places = await this.searchNearbyPlaces(
                    city.lat,
                    city.lng,
                    placeType,
                    10000 // نطاق 10 كم
                );

                for (const place of places) {
                    try {
                        console.log(`   📋 معالجة: ${place.name}`);

                        // الحصول على التفاصيل
                        const details = await this.getPlaceDetails(place.place_id);

                        if (details) {
                            // معالجة البيانات
                            const processedPlace = this.processPlaceData(place, details, city.name);

                            // تحميل الصور
                            if (details.photos && details.photos.length > 0) {
                                for (let i = 0; i < Math.min(3, details.photos.length); i++) {
                                    const photo = details.photos[i];
                                    const filename = `${place.place_id}_${i + 1}.jpg`;

                                    console.log(`     📸 تحميل صورة: ${filename}`);
                                    const imagePath = await this.downloadPhoto(photo.photo_reference, filename);

                                    if (imagePath) {
                                        processedPlace.photos[i].local_path = `images/places/${filename}`;
                                    }

                                    await this.delay(500); // تأخير بين تحميل الصور
                                }
                            }

                            this.results.push(processedPlace);
                        }

                        await this.delay(this.delay); // تأخير بين الطلبات

                    } catch (error) {
                        console.error(`❌ خطأ في معالجة ${place.name}:`, error.message);
                    }
                }

                await this.delay(2000); // تأخير بين أنواع الأماكن
            }

            await this.delay(3000); // تأخير بين المدن
        }

        // حفظ النتائج
        await this.saveResults();

        console.log(`\n🎉 تم الانتهاء! تم سحب ${this.results.length} مكان`);
        return this.results;
    }

    // حفظ النتائج في ملفات
    async saveResults() {
        try {
            // حفظ البيانات الخام
            const rawDataPath = path.join(__dirname, '../data/google_places_raw.json');
            fs.writeFileSync(rawDataPath, JSON.stringify(this.results, null, 2), 'utf8');

            // حفظ SQL للإدراج في قاعدة البيانات
            const sqlPath = path.join(__dirname, '../database/google_places_import.sql');
            const sqlContent = this.generateSQL();
            fs.writeFileSync(sqlPath, sqlContent, 'utf8');

            console.log(`💾 تم حفظ البيانات في:`);
            console.log(`   - ${rawDataPath}`);
            console.log(`   - ${sqlPath}`);

        } catch (error) {
            console.error('خطأ في حفظ النتائج:', error.message);
        }
    }

    // توليد SQL للإدراج
    generateSQL() {
        let sql = '-- بيانات الأماكن المسحوبة من Google Places API\n\n';

        this.results.forEach((place, index) => {
            const placeId = index + 100; // بدء من 100 لتجنب التضارب

            // إدراج المكان الأساسي
            sql += `-- ${place.name_ar} (${place.name_en})\n`;
            sql += `INSERT INTO places (name_ar, name_en, description_ar, latitude, longitude, address_ar, phone, website, rating, reviews_count, price_range, is_verified) VALUES (\n`;
            sql += `  '${place.name_ar.replace(/'/g, "''")}',\n`;
            sql += `  '${place.name_en.replace(/'/g, "''")}',\n`;
            sql += `  '${place.description_ar.replace(/'/g, "''")}',\n`;
            sql += `  ${place.latitude},\n`;
            sql += `  ${place.longitude},\n`;
            sql += `  '${place.address_ar.replace(/'/g, "''")}',\n`;
            sql += `  '${place.phone}',\n`;
            sql += `  '${place.website}',\n`;
            sql += `  ${place.rating},\n`;
            sql += `  ${place.reviews_count},\n`;
            sql += `  ${place.price_range ? `'${place.price_range}'` : 'NULL'},\n`;
            sql += `  TRUE\n`;
            sql += `);\n\n`;

            // إدراج الصور
            if (place.photos && place.photos.length > 0) {
                place.photos.forEach((photo, photoIndex) => {
                    sql += `INSERT INTO place_images (place_id, image_url, image_type, title_ar, sort_order) VALUES (\n`;
                    sql += `  (SELECT id FROM places WHERE name_en = '${place.name_en.replace(/'/g, "''")}' LIMIT 1),\n`;
                    sql += `  '${photo.url}',\n`;
                    sql += `  '${photoIndex === 0 ? 'main' : 'gallery'}',\n`;
                    sql += `  'صورة ${photoIndex + 1}',\n`;
                    sql += `  ${photoIndex + 1}\n`;
                    sql += `);\n`;
                });
                sql += '\n';
            }

            // إدراج التقييمات
            if (place.reviews && place.reviews.length > 0) {
                place.reviews.forEach(review => {
                    sql += `INSERT INTO place_reviews (place_id, user_name, rating, comment_ar, is_verified) VALUES (\n`;
                    sql += `  (SELECT id FROM places WHERE name_en = '${place.name_en.replace(/'/g, "''")}' LIMIT 1),\n`;
                    sql += `  '${review.author.replace(/'/g, "''")}',\n`;
                    sql += `  ${review.rating},\n`;
                    sql += `  '${review.text.replace(/'/g, "''")}',\n`;
                    sql += `  TRUE\n`;
                    sql += `);\n`;
                });
                sql += '\n';
            }

            // إدراج أوقات العمل
            if (place.opening_hours && place.opening_hours.length > 0) {
                place.opening_hours.forEach((hours, dayIndex) => {
                    const timeMatch = hours.match(/(\d{1,2}):(\d{2})\s*–\s*(\d{1,2}):(\d{2})/);
                    if (timeMatch) {
                        sql += `INSERT INTO opening_hours (place_id, day_of_week, open_time, close_time) VALUES (\n`;
                        sql += `  (SELECT id FROM places WHERE name_en = '${place.name_en.replace(/'/g, "''")}' LIMIT 1),\n`;
                        sql += `  ${dayIndex},\n`;
                        sql += `  '${timeMatch[1].padStart(2, '0')}:${timeMatch[2]}:00',\n`;
                        sql += `  '${timeMatch[3].padStart(2, '0')}:${timeMatch[4]}:00'\n`;
                        sql += `);\n`;
                    }
                });
                sql += '\n';
            }

            sql += `-- نهاية بيانات ${place.name_ar}\n\n`;
        });

        return sql;
    }
}

// تشغيل السكريبت
async function main() {
    if (!GOOGLE_API_KEY || GOOGLE_API_KEY === 'YOUR_GOOGLE_PLACES_API_KEY') {
        console.error('❌ يجب تعيين مفتاح Google Places API');
        console.log('📝 احصل على المفتاح من: https://console.cloud.google.com/');
        return;
    }

    const scraper = new GooglePlacesScraper(GOOGLE_API_KEY);
    await scraper.scrapeAllPlaces();
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
    main().catch(console.error);
}

module.exports = GooglePlacesScraper;

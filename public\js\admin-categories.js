/**
 * Yemen GPS - إدارة التصنيفات
 * ملف JavaScript لإدارة التصنيفات في لوحة التحكم
 */

// المتغيرات العامة
const API_BASE_URL = 'http://localhost:3000/api';
let allCategories = [];
let filteredCategories = [];

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات
    loadCategories();
    
    // إضافة مستمعي الأحداث
    document.getElementById('category-search').addEventListener('input', filterCategories);
    document.getElementById('category-form').addEventListener('submit', saveCategory);
    
    // تهيئة الوضع المتجاوب للشاشات الصغيرة
    initResponsiveLayout();
});

/**
 * تهيئة الوضع المتجاوب للشاشات الصغيرة
 */
function initResponsiveLayout() {
    const toggleButton = document.querySelector('.toggle-sidebar');
    
    if (window.innerWidth <= 768) {
        document.querySelector('.sidebar').classList.remove('active');
        document.querySelector('.main-content').style.marginRight = '0';
        toggleButton.style.display = 'block';
    } else {
        document.querySelector('.sidebar').classList.add('active');
        document.querySelector('.main-content').style.marginRight = '250px';
        toggleButton.style.display = 'none';
    }
    
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar').classList.remove('active');
            document.querySelector('.main-content').style.marginRight = '0';
            toggleButton.style.display = 'block';
        } else {
            document.querySelector('.sidebar').classList.add('active');
            document.querySelector('.main-content').style.marginRight = '250px';
            toggleButton.style.display = 'none';
        }
    });
}

/**
 * تبديل حالة القائمة الجانبية
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('active');
    
    if (sidebar.classList.contains('active')) {
        mainContent.style.marginRight = '250px';
    } else {
        mainContent.style.marginRight = '0';
    }
}

/**
 * تحميل التصنيفات
 */
function loadCategories() {
    showLoading();
    
    apiRequest('/admin/categories')
        .then(data => {
            allCategories = data;
            filteredCategories = [...allCategories];
            displayCategories(filteredCategories);
            updateCategoryStats();
            populateParentCategoryDropdowns();
            hideLoading();
        })
        .catch(error => {
            console.error('خطأ في تحميل التصنيفات:', error);
            document.getElementById('categories-error').textContent = 'حدث خطأ أثناء تحميل بيانات التصنيفات: ' + error.message;
            document.getElementById('categories-error').style.display = 'block';
            hideLoading();
        });
}

/**
 * ملء قوائم التصنيفات الرئيسية
 */
function populateParentCategoryDropdowns() {
    // الحصول على التصنيفات الرئيسية فقط (بدون تصنيف رئيسي)
    const mainCategories = allCategories.filter(category => !category.parent_id);
    
    // ملء قائمة التصنيف الرئيسي في نموذج إضافة/تعديل التصنيف
    const categoryParentSelect = document.getElementById('category-parent');
    categoryParentSelect.innerHTML = '<option value="">-- بدون تصنيف رئيسي --</option>';
    
    mainCategories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categoryParentSelect.appendChild(option);
    });
}

/**
 * عرض التصنيفات في الجدول
 */
function displayCategories(categories) {
    const tableBody = document.getElementById('categories-table-body');
    tableBody.innerHTML = '';
    
    if (categories.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" class="no-data">لا توجد تصنيفات للعرض</td></tr>';
        return;
    }
    
    categories.forEach(category => {
        const row = document.createElement('tr');
        
        // العثور على اسم التصنيف الرئيسي
        let parentName = '-';
        if (category.parent_id) {
            const parentCategory = allCategories.find(c => c.id === category.parent_id);
            if (parentCategory) {
                parentName = parentCategory.name;
            }
        }
        
        // تنسيق تاريخ الإنشاء
        const createdDate = new Date(category.created_at);
        const formattedDate = createdDate.toLocaleDateString('ar-SA');
        
        // إنشاء عرض الأيقونة
        const iconDisplay = category.icon ? 
            `<i class="fas ${category.icon.startsWith('fa-') ? category.icon : 'fa-' + category.icon}"></i> ${category.icon}` : 
            '-';
        
        // إنشاء عرض اللون
        const colorDisplay = category.color ? 
            `<span class="color-preview" style="background-color: ${category.color}"></span> ${category.color}` : 
            '-';
        
        row.innerHTML = `
            <td>${category.id}</td>
            <td>${category.name}</td>
            <td>${iconDisplay}</td>
            <td>${colorDisplay}</td>
            <td>${parentName}</td>
            <td>${formattedDate}</td>
            <td>
                <button class="btn btn-primary btn-sm" onclick="editCategory(${category.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteCategory(${category.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
    
    updateCategoryStats();
}

/**
 * تحديث إحصائيات التصنيفات
 */
function updateCategoryStats() {
    document.getElementById('total-categories').textContent = allCategories.length;
    document.getElementById('displayed-categories').textContent = filteredCategories.length;
}

/**
 * تصفية التصنيفات حسب المعايير المحددة
 */
function filterCategories() {
    const searchTerm = document.getElementById('category-search').value.trim().toLowerCase();
    const parentFilter = document.getElementById('parent-category-filter').value;
    
    // إظهار/إخفاء زر مسح البحث
    document.getElementById('clear-category-search').style.display = searchTerm ? 'block' : 'none';
    
    filteredCategories = allCategories.filter(category => {
        // تصفية حسب نص البحث
        const matchesSearch = 
            category.name.toLowerCase().includes(searchTerm) ||
            (category.icon && category.icon.toLowerCase().includes(searchTerm));
        
        // تصفية حسب التصنيف الرئيسي
        let matchesParent = true;
        if (parentFilter === 'main') {
            matchesParent = !category.parent_id;
        } else if (parentFilter === 'sub') {
            matchesParent = !!category.parent_id;
        } else if (parentFilter !== 'all') {
            matchesParent = category.parent_id == parentFilter;
        }
        
        return matchesSearch && matchesParent;
    });
    
    displayCategories(filteredCategories);
}

/**
 * مسح البحث
 */
function clearCategorySearch() {
    document.getElementById('category-search').value = '';
    filterCategories();
}

/**
 * عرض نافذة إضافة تصنيف جديد
 */
function showAddCategoryModal() {
    // تغيير عنوان النافذة
    document.getElementById('category-modal-title').textContent = 'إضافة تصنيف جديد';
    
    // إعادة تعيين النموذج
    document.getElementById('category-form').reset();
    document.getElementById('category-id').value = '';
    
    // تعيين اللون الافتراضي
    document.getElementById('category-color').value = '#023e8a';
    
    // عرض النافذة
    document.getElementById('category-modal').style.display = 'block';
}

/**
 * عرض نافذة تعديل تصنيف
 */
function editCategory(categoryId) {
    // البحث عن التصنيف
    const category = allCategories.find(cat => cat.id === categoryId);
    
    if (!category) {
        alert('لم يتم العثور على التصنيف');
        return;
    }
    
    // تغيير عنوان النافذة
    document.getElementById('category-modal-title').textContent = 'تعديل التصنيف';
    
    // ملء النموذج ببيانات التصنيف
    document.getElementById('category-id').value = category.id;
    document.getElementById('category-name').value = category.name;
    document.getElementById('category-icon').value = category.icon || '';
    document.getElementById('category-color').value = category.color || '#023e8a';
    document.getElementById('category-parent').value = category.parent_id || '';
    
    // عرض النافذة
    document.getElementById('category-modal').style.display = 'block';
}

/**
 * إغلاق نافذة إضافة/تعديل التصنيف
 */
function closeCategoryModal() {
    document.getElementById('category-modal').style.display = 'none';
}

/**
 * حفظ بيانات التصنيف (إضافة/تعديل)
 */
function saveCategory(event) {
    event.preventDefault();
    
    // جمع بيانات النموذج
    const categoryId = document.getElementById('category-id').value;
    const categoryData = {
        name: document.getElementById('category-name').value,
        icon: document.getElementById('category-icon').value,
        color: document.getElementById('category-color').value,
        parent_id: document.getElementById('category-parent').value || null
    };
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // تحديد ما إذا كانت عملية إضافة أو تعديل
    const isUpdate = categoryId !== '';
    const url = isUpdate ? `/admin/categories/${categoryId}` : '/admin/categories';
    const method = isUpdate ? 'PUT' : 'POST';
    
    // إرسال البيانات إلى الخادم
    apiRequest(url, {
        method: method,
        body: JSON.stringify(categoryData)
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إغلاق النافذة
        closeCategoryModal();
        
        // إظهار رسالة نجاح
        alert(isUpdate ? 'تم تحديث التصنيف بنجاح' : 'تم إضافة التصنيف بنجاح');
        
        // إعادة تحميل التصنيفات
        loadCategories();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حفظ التصنيف: ' + error.message);
    });
}

/**
 * حذف تصنيف
 */
function deleteCategory(categoryId) {
    // التحقق من وجود تصنيفات فرعية
    const hasSubcategories = allCategories.some(cat => cat.parent_id === categoryId);
    
    if (hasSubcategories) {
        alert('لا يمكن حذف هذا التصنيف لأنه يحتوي على تصنيفات فرعية. يرجى حذف التصنيفات الفرعية أولاً.');
        return;
    }
    
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذا التصنيف؟')) {
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoading();
    
    // إرسال طلب الحذف إلى الخادم
    apiRequest(`/admin/categories/${categoryId}`, {
        method: 'DELETE'
    })
    .then(data => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة نجاح
        alert('تم حذف التصنيف بنجاح');
        
        // إعادة تحميل التصنيفات
        loadCategories();
    })
    .catch(error => {
        // إخفاء مؤشر التحميل
        hideLoading();
        
        // إظهار رسالة خطأ
        alert('حدث خطأ أثناء حذف التصنيف: ' + error.message);
    });
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    document.getElementById('categories-loading').style.display = 'flex';
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    document.getElementById('categories-loading').style.display = 'none';
}

/**
 * تسجيل الخروج
 */
function logout() {
    // حذف بيانات الجلسة من تخزين الجلسة
    sessionStorage.removeItem('yemenNavToken');
    sessionStorage.removeItem('yemenNavCurrentUser');
    sessionStorage.removeItem('yemenNavLoggedIn');
    
    // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'admin-login.html';
}

/**
 * إرسال طلب إلى واجهة برمجة التطبيقات
 */
function apiRequest(endpoint, options = {}) {
    // إضافة الرأس الافتراضي
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + sessionStorage.getItem('yemenNavToken')
    };
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    const requestOptions = {
        ...options,
        headers: {
            ...headers,
            ...options.headers
        }
    };
    
    // إضافة بادئة API إذا لم تكن موجودة
    if (!endpoint.startsWith('http')) {
        endpoint = `${API_BASE_URL}${endpoint}`;
    }
    
    // إرسال الطلب
    return fetch(endpoint, requestOptions)
        .then(response => {
            if (!response.ok) {
                throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
            }
            return response.json();
        });
}

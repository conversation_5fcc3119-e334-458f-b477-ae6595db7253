# ملف تثبيت نظام "يمن ناف" (Yemen Nav)
# هذا الملف النصي يقوم بتثبيت جميع المتطلبات الأساسية وإعداد النظام تلقائيًا

# تشغيل السكربت بصلاحيات المسؤول
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "يرجى تشغيل هذا السكربت بصلاحيات المسؤول"
    Break
}

# إعداد المتغيرات
$installDir = "C:\yemen-nav"
$backendDir = "$installDir\backend"
$frontendDir = "$installDir\frontend"
$storageDir = "$installDir\storage"
$tempDir = "$env:TEMP\yemen-nav-temp"
$nodeVersion = "16.20.0"
$nodeUrl = "https://nodejs.org/dist/v$nodeVersion/node-v$nodeVersion-x64.msi"
$nodeMsi = "$tempDir\node-v$nodeVersion-x64.msi"
$postgresVersion = "13.7"
$postgresUrl = "https://get.enterprisedb.com/postgresql/postgresql-$postgresVersion-1-windows-x64.exe"
$postgresExe = "$tempDir\postgresql-$postgresVersion-1-windows-x64.exe"
$gitUrl = "https://github.com/git-for-windows/git/releases/download/v2.36.1.windows.1/Git-2.36.1-64-bit.exe"
$gitExe = "$tempDir\Git-2.36.1-64-bit.exe"
$urlRewriteUrl = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
$urlRewriteMsi = "$tempDir\rewrite_amd64_en-US.msi"
$dbPassword = "Yemen@Nav2023"
$dbUser = "yemen_nav_user"
$dbName = "yemen_nav"

# إنشاء المجلدات
function CreateDirectories {
    Write-Host "إنشاء المجلدات..." -ForegroundColor Green
    
    if (!(Test-Path -Path $tempDir)) {
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    }
    
    if (!(Test-Path -Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }
    
    if (!(Test-Path -Path $backendDir)) {
        New-Item -ItemType Directory -Path $backendDir -Force | Out-Null
    }
    
    if (!(Test-Path -Path $frontendDir)) {
        New-Item -ItemType Directory -Path $frontendDir -Force | Out-Null
    }
    
    if (!(Test-Path -Path $storageDir)) {
        New-Item -ItemType Directory -Path $storageDir -Force | Out-Null
    }
    
    Write-Host "تم إنشاء المجلدات بنجاح" -ForegroundColor Green
}

# تنزيل الملفات
function DownloadFiles {
    Write-Host "تنزيل الملفات المطلوبة..." -ForegroundColor Green
    
    # تنزيل Node.js
    if (!(Test-Path -Path $nodeMsi)) {
        Write-Host "تنزيل Node.js..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeMsi
    }
    
    # تنزيل PostgreSQL
    if (!(Test-Path -Path $postgresExe)) {
        Write-Host "تنزيل PostgreSQL..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $postgresUrl -OutFile $postgresExe
    }
    
    # تنزيل Git
    if (!(Test-Path -Path $gitExe)) {
        Write-Host "تنزيل Git..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $gitUrl -OutFile $gitExe
    }
    
    # تنزيل URL Rewrite Module
    if (!(Test-Path -Path $urlRewriteMsi)) {
        Write-Host "تنزيل URL Rewrite Module..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $urlRewriteUrl -OutFile $urlRewriteMsi
    }
    
    Write-Host "تم تنزيل جميع الملفات بنجاح" -ForegroundColor Green
}

# تثبيت Node.js
function InstallNode {
    Write-Host "تثبيت Node.js..." -ForegroundColor Green
    
    # التحقق مما إذا كان Node.js مثبتًا بالفعل
    try {
        $nodeCheck = node --version
        Write-Host "Node.js مثبت بالفعل: $nodeCheck" -ForegroundColor Yellow
    }
    catch {
        # تثبيت Node.js
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$nodeMsi`" /quiet /norestart" -Wait
        
        # التحقق من التثبيت
        try {
            $nodeCheck = node --version
            Write-Host "تم تثبيت Node.js بنجاح: $nodeCheck" -ForegroundColor Green
        }
        catch {
            Write-Error "فشل تثبيت Node.js"
            Exit
        }
    }
}

# تثبيت Git
function InstallGit {
    Write-Host "تثبيت Git..." -ForegroundColor Green
    
    # التحقق مما إذا كان Git مثبتًا بالفعل
    try {
        $gitCheck = git --version
        Write-Host "Git مثبت بالفعل: $gitCheck" -ForegroundColor Yellow
    }
    catch {
        # تثبيت Git
        Start-Process -FilePath $gitExe -ArgumentList "/VERYSILENT /NORESTART /NOCANCEL /SP- /CLOSEAPPLICATIONS /RESTARTAPPLICATIONS /COMPONENTS=`"icons,ext\reg\shellhere,assoc,assoc_sh`"" -Wait
        
        # تحديث متغيرات البيئة
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
        
        # التحقق من التثبيت
        try {
            $gitCheck = git --version
            Write-Host "تم تثبيت Git بنجاح: $gitCheck" -ForegroundColor Green
        }
        catch {
            Write-Error "فشل تثبيت Git"
            Exit
        }
    }
}

# تثبيت PostgreSQL
function InstallPostgres {
    Write-Host "تثبيت PostgreSQL..." -ForegroundColor Green
    
    # التحقق مما إذا كان PostgreSQL مثبتًا بالفعل
    $pgService = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
    if ($pgService) {
        Write-Host "PostgreSQL مثبت بالفعل" -ForegroundColor Yellow
    }
    else {
        # تثبيت PostgreSQL
        Start-Process -FilePath $postgresExe -ArgumentList "--mode unattended --superpassword $dbPassword --servicename PostgreSQL --servicepassword $dbPassword --serverport 5432" -Wait
        
        # التحقق من التثبيت
        $pgService = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
        if ($pgService) {
            Write-Host "تم تثبيت PostgreSQL بنجاح" -ForegroundColor Green
        }
        else {
            Write-Error "فشل تثبيت PostgreSQL"
            Exit
        }
    }
}

# تثبيت URL Rewrite Module
function InstallUrlRewrite {
    Write-Host "تثبيت URL Rewrite Module..." -ForegroundColor Green
    
    # التحقق مما إذا كان URL Rewrite Module مثبتًا بالفعل
    $urlRewriteKey = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\IIS Extensions\URL Rewrite" -ErrorAction SilentlyContinue
    if ($urlRewriteKey) {
        Write-Host "URL Rewrite Module مثبت بالفعل" -ForegroundColor Yellow
    }
    else {
        # تثبيت URL Rewrite Module
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$urlRewriteMsi`" /quiet /norestart" -Wait
        
        # التحقق من التثبيت
        $urlRewriteKey = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\IIS Extensions\URL Rewrite" -ErrorAction SilentlyContinue
        if ($urlRewriteKey) {
            Write-Host "تم تثبيت URL Rewrite Module بنجاح" -ForegroundColor Green
        }
        else {
            Write-Error "فشل تثبيت URL Rewrite Module"
            Exit
        }
    }
}

# تثبيت IIS
function InstallIIS {
    Write-Host "تثبيت IIS..." -ForegroundColor Green
    
    # التحقق مما إذا كان IIS مثبتًا بالفعل
    $iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
    if ($iisService) {
        Write-Host "IIS مثبت بالفعل" -ForegroundColor Yellow
    }
    else {
        # تثبيت IIS وميزاته
        dism /online /enable-feature /featurename:IIS-WebServerRole /featurename:IIS-WebServer /featurename:IIS-CommonHttpFeatures /featurename:IIS-ManagementConsole /featurename:IIS-ApplicationDevelopment /featurename:IIS-ASPNET /featurename:IIS-CGI /featurename:IIS-ISAPIExtensions /featurename:IIS-ISAPIFilter /featurename:IIS-HealthAndDiagnostics /featurename:IIS-HttpLogging /featurename:IIS-LoggingLibraries /featurename:IIS-RequestMonitor /featurename:IIS-HttpTracing /featurename:IIS-Security /featurename:IIS-RequestFiltering /featurename:IIS-Performance /featurename:IIS-WebServerManagementTools /featurename:IIS-StaticContent /featurename:IIS-DefaultDocument /featurename:IIS-DirectoryBrowsing /featurename:IIS-WebSockets /featurename:IIS-ApplicationInit /featurename:IIS-NetFxExtensibility45 /featurename:IIS-ASPNET45 /featurename:IIS-HttpCompressionStatic /featurename:IIS-HttpCompressionDynamic /all
        
        # التحقق من التثبيت
        $iisService = Get-Service -Name "W3SVC" -ErrorAction SilentlyContinue
        if ($iisService) {
            Write-Host "تم تثبيت IIS بنجاح" -ForegroundColor Green
        }
        else {
            Write-Error "فشل تثبيت IIS"
            Exit
        }
    }
}

# تثبيت PM2
function InstallPM2 {
    Write-Host "تثبيت PM2..." -ForegroundColor Green
    
    # التحقق مما إذا كان PM2 مثبتًا بالفعل
    try {
        $pm2Check = pm2 --version
        Write-Host "PM2 مثبت بالفعل: $pm2Check" -ForegroundColor Yellow
    }
    catch {
        # تثبيت PM2
        npm install -g pm2
        npm install -g pm2-windows-startup
        
        # التحقق من التثبيت
        try {
            $pm2Check = pm2 --version
            Write-Host "تم تثبيت PM2 بنجاح: $pm2Check" -ForegroundColor Green
            
            # تكوين PM2 للبدء تلقائيًا عند بدء تشغيل Windows
            pm2-startup install
        }
        catch {
            Write-Error "فشل تثبيت PM2"
            Exit
        }
    }
}

# إنشاء قاعدة البيانات
function CreateDatabase {
    Write-Host "إنشاء قاعدة البيانات..." -ForegroundColor Green
    
    # إنشاء ملف SQL مؤقت
    $sqlFile = "$tempDir\create_db.sql"
    @"
CREATE DATABASE $dbName;
CREATE USER $dbUser WITH ENCRYPTED PASSWORD '$dbPassword';
GRANT ALL PRIVILEGES ON DATABASE $dbName TO $dbUser;
\c $dbName
CREATE EXTENSION postgis;
CREATE EXTENSION postgis_topology;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO $dbUser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO $dbUser;
"@ | Out-File -FilePath $sqlFile -Encoding utf8
    
    # تنفيذ الأوامر SQL
    $pgPath = "C:\Program Files\PostgreSQL\13\bin"
    if (Test-Path -Path $pgPath) {
        Set-Location -Path $pgPath
        .\psql.exe -U postgres -f $sqlFile
        Write-Host "تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    }
    else {
        Write-Error "لم يتم العثور على PostgreSQL في المسار المتوقع"
        Exit
    }
}

# إعداد الواجهة الخلفية (Backend)
function SetupBackend {
    Write-Host "إعداد الواجهة الخلفية..." -ForegroundColor Green
    
    # الانتقال إلى مجلد الواجهة الخلفية
    Set-Location -Path $backendDir
    
    # إنشاء مشروع Node.js جديد
    npm init -y
    
    # تثبيت الحزم اللازمة
    npm install express cors body-parser pg pg-hstore sequelize dotenv jsonwebtoken bcrypt multer mapbox-gl @turf/turf
    npm install nodemon morgan winston --save-dev
    
    # إنشاء ملف .env
    @"
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=5432
DB_NAME=$dbName
DB_USER=$dbUser
DB_PASSWORD=$dbPassword

# إعدادات الخادم
PORT=3000
NODE_ENV=development
JWT_SECRET=yemen_nav_secret_key_2023

# إعدادات التخزين
STORAGE_PATH=$storageDir
"@ | Out-File -FilePath "$backendDir\.env" -Encoding utf8
    
    # إنشاء ملف server.js
    @"
// Yemen Nav Backend Server
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to Yemen Nav API' });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
"@ | Out-File -FilePath "$backendDir\server.js" -Encoding utf8
    
    # تعديل ملف package.json لإضافة سكربتات التشغيل
    $packageJson = Get-Content -Path "$backendDir\package.json" -Raw | ConvertFrom-Json
    $packageJson.scripts.start = "node server.js"
    $packageJson.scripts.dev = "nodemon server.js"
    $packageJson | ConvertTo-Json | Out-File -FilePath "$backendDir\package.json" -Encoding utf8
    
    Write-Host "تم إعداد الواجهة الخلفية بنجاح" -ForegroundColor Green
}

# إعداد الواجهة الأمامية (Frontend)
function SetupFrontend {
    Write-Host "إعداد الواجهة الأمامية..." -ForegroundColor Green
    
    # تثبيت Create React App عالميًا
    npm install -g create-react-app
    
    # الانتقال إلى مجلد المشروع
    Set-Location -Path $installDir
    
    # إنشاء تطبيق React جديد
    npx create-react-app frontend
    
    # الانتقال إلى مجلد الواجهة الأمامية
    Set-Location -Path $frontendDir
    
    # تثبيت الحزم اللازمة
    npm install react-router-dom axios mapbox-gl @turf/turf styled-components
    
    # إنشاء ملف .env
    @"
REACT_APP_API_URL=http://localhost:3000
REACT_APP_MAPBOX_TOKEN=pk.eyJ1IjoieWVtZW5uYXYiLCJhIjoiY2xvNXBtcnp1MDJrMjJrcGR5ZDFkaHl5ZyJ9.6Xvq9Aqq9z9Z9Z9Z9Z9Z9Z
"@ | Out-File -FilePath "$frontendDir\.env" -Encoding utf8
    
    # بناء المشروع للإنتاج
    npm run build
    
    Write-Host "تم إعداد الواجهة الأمامية بنجاح" -ForegroundColor Green
}

# إعداد IIS
function SetupIIS {
    Write-Host "إعداد IIS..." -ForegroundColor Green
    
    # إنشاء موقع IIS جديد
    Import-Module WebAdministration
    
    # إزالة الموقع الافتراضي
    Remove-WebSite -Name "Default Web Site" -ErrorAction SilentlyContinue
    
    # إنشاء موقع جديد
    New-WebSite -Name "Yemen Nav" -PhysicalPath $frontendDir\build -Port 80 -Force
    
    # إنشاء ملف web.config
    @"
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="React Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
        <rule name="API Proxy" stopProcessing="true">
          <match url="^api/(.*)" />
          <action type="Rewrite" url="http://localhost:3000/{R:1}" />
        </rule>
      </rules>
    </rewrite>
    <httpErrors errorMode="Custom" existingResponse="Replace">
      <remove statusCode="404" />
      <error statusCode="404" path="/" responseMode="ExecuteURL" />
    </httpErrors>
  </system.webServer>
</configuration>
"@ | Out-File -FilePath "$frontendDir\build\web.config" -Encoding utf8
    
    # إضافة سجل إلى ملف hosts
    $hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
    $hostsContent = Get-Content -Path $hostsFile
    if (!($hostsContent -match "yemen-nav.local")) {
        Add-Content -Path $hostsFile -Value "`r`n127.0.0.1 yemen-nav.local"
    }
    
    # إعادة تشغيل IIS
    iisreset
    
    Write-Host "تم إعداد IIS بنجاح" -ForegroundColor Green
}

# إعداد PM2
function SetupPM2 {
    Write-Host "إعداد PM2..." -ForegroundColor Green
    
    # إنشاء ملف تكوين PM2
    @"
module.exports = {
  apps: [{
    name: 'yemen-nav-backend',
    script: '$backendDir/server.js',
    env: {
      NODE_ENV: 'production',
    },
  }],
};
"@ | Out-File -FilePath "$installDir\ecosystem.config.js" -Encoding utf8
    
    # تشغيل الواجهة الخلفية باستخدام PM2
    Set-Location -Path $installDir
    pm2 start ecosystem.config.js
    pm2 save
    
    Write-Host "تم إعداد PM2 بنجاح" -ForegroundColor Green
}

# تنظيف الملفات المؤقتة
function Cleanup {
    Write-Host "تنظيف الملفات المؤقتة..." -ForegroundColor Green
    
    # حذف المجلد المؤقت
    if (Test-Path -Path $tempDir) {
        Remove-Item -Path $tempDir -Recurse -Force
    }
    
    Write-Host "تم تنظيف الملفات المؤقتة بنجاح" -ForegroundColor Green
}

# تشغيل النظام
function StartSystem {
    Write-Host "تشغيل نظام يمن ناف..." -ForegroundColor Green
    
    # فتح المتصفح
    Start-Process "http://yemen-nav.local"
    
    Write-Host "تم تشغيل نظام يمن ناف بنجاح" -ForegroundColor Green
    Write-Host "يمكنك الوصول إلى النظام من خلال المتصفح على العنوان: http://yemen-nav.local" -ForegroundColor Yellow
}

# تنفيذ الوظائف
try {
    Write-Host "بدء تثبيت نظام يمن ناف..." -ForegroundColor Green
    
    CreateDirectories
    DownloadFiles
    InstallNode
    InstallGit
    InstallPostgres
    InstallIIS
    InstallUrlRewrite
    InstallPM2
    CreateDatabase
    SetupBackend
    SetupFrontend
    SetupIIS
    SetupPM2
    Cleanup
    StartSystem
    
    Write-Host "تم تثبيت وتشغيل نظام يمن ناف بنجاح!" -ForegroundColor Green
}
catch {
    Write-Error "حدث خطأ أثناء تثبيت نظام يمن ناف: $_"
}

// Yemen GPS Offline Map Application
class OfflineYemenMap {
    constructor() {
        this.map = null;
        this.markers = [];
        this.placesData = [];
        this.currentLocation = null;
        this.loadingProgress = 0;
        
        this.init();
    }

    async init() {
        console.log('🚀 تهيئة خرائط اليمن المستقلة...');
        
        // إظهار شاشة التحميل
        this.showLoading();
        
        // تهيئة الخريطة
        await this.initMap();
        
        // تحميل البيانات
        await this.loadOfflineData();
        
        // ربط الأحداث
        this.bindEvents();
        
        // إخفاء شاشة التحميل
        this.hideLoading();
        
        console.log('✅ تم تهيئة الخريطة المستقلة بنجاح');
    }

    async initMap() {
        this.updateProgress(20, 'تهيئة الخريطة...');
        
        // إنشاء الخريطة باستخدام OpenStreetMap
        this.map = L.map('map', {
            center: [15.3547, 44.2066], // صنعاء
            zoom: 7,
            zoomControl: false,
            attributionControl: false
        });

        // إضافة طبقة OpenStreetMap (تعمل بدون إنترنت إذا تم تخزينها مسبق<|im_start|>)
        const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        });

        // إضافة طبقة بديلة للعمل بدون إنترنت
        const offlineLayer = L.tileLayer('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', {
            attribution: 'خرائط اليمن المستقلة'
        });

        // محاولة تحميل OSM، وإذا فشل استخدم الطبقة البديلة
        try {
            osmLayer.addTo(this.map);
            console.log('✅ تم تحميل خرائط OpenStreetMap');
        } catch (error) {
            console.log('⚠️ لا يوجد اتصال إنترنت، استخدام الوضع المستقل');
            offlineLayer.addTo(this.map);
            this.showToast('تعمل الخريطة في الوضع المستقل', 'warning');
        }

        // إضافة أحداث الخريطة
        this.map.on('moveend', () => {
            this.updateCoordinates();
        });

        this.map.on('zoomend', () => {
            this.updateZoomLevel();
        });

        this.updateProgress(40, 'تم تهيئة الخريطة');
    }

    async loadOfflineData() {
        this.updateProgress(50, 'تحميل البيانات المحلية...');
        
        try {
            // محاولة تحميل البيانات من الخادم المحلي
            const response = await fetch('/api/places');
            if (response.ok) {
                this.placesData = await response.json();
                console.log(`📍 تم تحميل ${this.placesData.length} مكان من الخادم المحلي`);
            } else {
                throw new Error('فشل في تحميل البيانات من الخادم');
            }
        } catch (error) {
            console.log('⚠️ لا يمكن الاتصال بالخادم، استخدام البيانات المحفوظة محلي<|im_start|>');
            this.placesData = this.getOfflinePlacesData();
        }

        this.updateProgress(70, 'معالجة البيانات...');
        
        // إضافة العلامات للخريطة
        this.addMarkersToMap();
        
        // تحديث الإحصائيات
        this.updateStats();
        
        this.updateProgress(90, 'إنهاء التحميل...');
    }

    getOfflinePlacesData() {
        // بيانات أساسية للأماكن المهمة في اليمن (تعمل بدون خادم)
        return [
            {
                id: 1,
                name_ar: 'صنعاء القديمة',
                name_en: 'Old Sana\'a',
                description_ar: 'مدينة صنعاء القديمة التاريخية',
                latitude: 15.3535,
                longitude: 44.2139,
                category_id: 1,
                photos: ['/images/places/sanaa_old_1.jpg', '/images/places/sanaa_old_2.jpg'],
                rating: 4.8
            },
            {
                id: 2,
                name_ar: 'الجامع الكبير',
                name_en: 'Great Mosque of Sana\'a',
                description_ar: 'أحد أقدم المساجد في العالم',
                latitude: 15.3542,
                longitude: 44.2145,
                category_id: 2,
                photos: ['/images/places/great_mosque_1.jpg'],
                rating: 4.9
            },
            {
                id: 3,
                name_ar: 'سوق الملح',
                name_en: 'Salt Market',
                description_ar: 'سوق تقليدي في صنعاء القديمة',
                latitude: 15.3528,
                longitude: 44.2151,
                category_id: 8,
                photos: ['/images/places/salt_market_1.jpg'],
                rating: 4.5
            },
            {
                id: 4,
                name_ar: 'قصر غمدان',
                name_en: 'Ghumdan Palace',
                description_ar: 'قصر تاريخي أثري',
                latitude: 15.3580,
                longitude: 44.2100,
                category_id: 1,
                photos: ['/images/places/ghumdan_palace_1.jpg'],
                rating: 4.7
            },
            {
                id: 5,
                name_ar: 'مطار صنعاء الدولي',
                name_en: 'Sana\'a International Airport',
                description_ar: 'المطار الرئيسي في صنعاء',
                latitude: 15.4761,
                longitude: 44.2194,
                category_id: 9,
                photos: [],
                rating: 3.5
            },
            {
                id: 6,
                name_ar: 'جامعة صنعاء',
                name_en: 'University of Sana\'a',
                description_ar: 'أكبر جامعة في اليمن',
                latitude: 15.3700,
                longitude: 44.1900,
                category_id: 6,
                photos: ['/images/places/sanaa_university_1.jpg'],
                rating: 4.2
            },
            {
                id: 7,
                name_ar: 'مستشفى الثورة',
                name_en: 'Al-Thawra Hospital',
                description_ar: 'مستشفى حكومي رئيسي',
                latitude: 15.3600,
                longitude: 44.2000,
                category_id: 5,
                photos: [],
                rating: 3.8
            },
            {
                id: 8,
                name_ar: 'فندق موفنبيك',
                name_en: 'Movenpick Hotel',
                description_ar: 'فندق فاخر في صنعاء',
                latitude: 15.3650,
                longitude: 44.2050,
                category_id: 4,
                photos: ['/images/places/movenpick_1.jpg'],
                rating: 4.6
            }
        ];
    }

    addMarkersToMap() {
        // مسح العلامات السابقة
        this.clearMarkers();

        this.placesData.forEach(place => {
            if (place.latitude && place.longitude) {
                // إنشاء أيقونة مخصصة حسب الفئة
                const icon = this.createCustomIcon(place.category_id);
                
                const marker = L.marker([place.latitude, place.longitude], { icon })
                    .addTo(this.map);

                // إضافة نافذة منبثقة
                const popupContent = this.createPopupContent(place);
                marker.bindPopup(popupContent, {
                    maxWidth: 300,
                    className: 'custom-popup'
                });

                this.markers.push(marker);
            }
        });

        console.log(`🎯 تم إضافة ${this.markers.length} علامة للخريطة`);
    }

    createCustomIcon(categoryId) {
        const colors = {
            1: '#e74c3c',  // سياحة - أحمر
            2: '#9b59b6',  // دينية - بنفسجي
            3: '#f39c12',  // مطاعم - برتقالي
            4: '#3498db',  // فنادق - أزرق
            5: '#2ecc71',  // صحة - أخضر
            6: '#f1c40f',  // تعليم - أصفر
            7: '#34495e',  // خدمات - رمادي غامق
            8: '#e67e22',  // تسوق - برتقالي غامق
            9: '#95a5a6',  // نقل - رمادي
            10: '#7f8c8d' // أخرى - رمادي فاتح
        };

        const color = colors[categoryId] || colors[10];

        return L.divIcon({
            className: 'custom-marker',
            html: `<div style="background: ${color}; width: 25px; height: 25px; border-radius: 50% 50% 50% 0; transform: rotate(-45deg); border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.3);"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(45deg); width: 8px; height: 8px; background: white; border-radius: 50%;"></div></div>`,
            iconSize: [25, 25],
            iconAnchor: [12, 25],
            popupAnchor: [0, -25]
        });
    }

    createPopupContent(place) {
        const photos = place.photos && place.photos.length > 0 ? 
            place.photos.slice(0, 3).map(photo => 
                `<img src="${photo}" class="popup-photo" onclick="openPhotoModal('${photo}')" onerror="this.style.display='none'">`
            ).join('') : '';

        const photosHtml = photos ? `<div class="popup-photos">${photos}</div>` : '';

        return `
            <div class="popup-content">
                <div class="popup-title">${place.name_ar || place.name_en}</div>
                ${place.name_en && place.name_ar !== place.name_en ? `<div style="font-size: 12px; color: #7f8c8d; margin-bottom: 8px;">${place.name_en}</div>` : ''}
                ${photosHtml}
                ${place.description_ar ? `<div class="popup-description">${place.description_ar}</div>` : ''}
                <div class="popup-details">
                    ${place.phone ? `<div class="popup-detail">📞 ${place.phone}</div>` : ''}
                    ${place.rating ? `<div class="popup-detail">⭐ ${place.rating}/5</div>` : ''}
                    <div class="popup-detail">📍 ${place.latitude}, ${place.longitude}</div>
                </div>
                <div class="popup-actions">
                    <button class="popup-btn directions-btn" onclick="getDirections(${place.latitude}, ${place.longitude})">🧭 الاتجاهات</button>
                    <button class="popup-btn share-btn" onclick="sharePlace('${place.name_ar}', ${place.latitude}, ${place.longitude})">📤 مشاركة</button>
                    <button class="popup-btn save-btn" onclick="savePlace(${place.id})">💾 حفظ</button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // البحث
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.liveSearch(e.target.value);
        });

        // التحكم في التكبير
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.map.zoomIn();
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.map.zoomOut();
        });

        // الموقع الحالي
        document.getElementById('locationBtn').addEventListener('click', () => {
            this.getCurrentLocation();
        });

        // معلومات
        document.getElementById('infoBtn').addEventListener('click', () => {
            this.showInfo();
        });

        // إخفاء نتائج البحث عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSearchResults();
            }
        });
    }

    performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (query.length < 2) {
            this.showToast('يرجى إدخال كلمة بحث أطول', 'warning');
            return;
        }

        const results = this.placesData.filter(place => 
            (place.name_ar && place.name_ar.includes(query)) ||
            (place.name_en && place.name_en.toLowerCase().includes(query.toLowerCase())) ||
            (place.description_ar && place.description_ar.includes(query))
        );

        if (results.length === 0) {
            this.showToast('لا توجد نتائج للبحث', 'warning');
            return;
        }

        // التركيز على أول نتيجة
        const firstResult = results[0];
        this.map.setView([firstResult.latitude, firstResult.longitude], 15);
        
        // العثور على العلامة وفتح النافذة المنبثقة
        const marker = this.markers.find(m => 
            Math.abs(m.getLatLng().lat - firstResult.latitude) < 0.0001 &&
            Math.abs(m.getLatLng().lng - firstResult.longitude) < 0.0001
        );
        
        if (marker) {
            marker.openPopup();
        }

        this.showToast(`تم العثور على ${results.length} نتيجة`, 'success');
    }

    liveSearch(query) {
        if (query.length < 2) {
            this.hideSearchResults();
            return;
        }

        const results = this.placesData.filter(place => 
            (place.name_ar && place.name_ar.includes(query)) ||
            (place.name_en && place.name_en.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        this.displaySearchResults(results);
    }

    displaySearchResults(results) {
        const container = document.getElementById('searchResults');
        
        if (results.length === 0) {
            container.style.display = 'none';
            return;
        }

        container.innerHTML = results.map(place => `
            <div class="search-result-item" onclick="selectPlace(${place.id})">
                <div class="result-title">${place.name_ar || place.name_en}</div>
                <div class="result-description">${place.description_ar || ''}</div>
            </div>
        `).join('');
        
        container.style.display = 'block';
    }

    hideSearchResults() {
        document.getElementById('searchResults').style.display = 'none';
    }

    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    this.currentLocation = [position.coords.latitude, position.coords.longitude];
                    this.map.setView(this.currentLocation, 15);
                    
                    // إضافة علامة للموقع الحالي
                    const locationIcon = L.divIcon({
                        className: 'current-location-marker',
                        html: '<div style="background: #3498db; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.3);"></div>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    });
                    
                    L.marker(this.currentLocation, { icon: locationIcon })
                        .addTo(this.map)
                        .bindPopup('موقعك الحالي')
                        .openPopup();
                    
                    this.showToast('تم تحديد موقعك الحالي', 'success');
                },
                (error) => {
                    this.showToast('لا يمكن تحديد موقعك الحالي', 'error');
                }
            );
        } else {
            this.showToast('المتصفح لا يدعم تحديد الموقع', 'error');
        }
    }

    showInfo() {
        const info = `
خرائط اليمن المستقلة
===================

✅ تعمل بدون إنترنت
✅ بيانات محلية محفوظة
✅ ${this.placesData.length} مكان متاح
✅ صور عالية الجودة
✅ بحث سريع ودقيق

تم تطويرها خصيص<|im_start|> لليمن 🇾🇪
        `;
        
        alert(info);
    }

    clearMarkers() {
        this.markers.forEach(marker => this.map.removeLayer(marker));
        this.markers = [];
    }

    updateCoordinates() {
        const center = this.map.getCenter();
        document.getElementById('coordinates').textContent = 
            `${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
    }

    updateZoomLevel() {
        document.getElementById('zoomLevel').textContent = this.map.getZoom();
    }

    updateStats() {
        document.getElementById('placesCount').textContent = `${this.placesData.length} مكان`;
    }

    showLoading() {
        document.getElementById('loadingScreen').style.display = 'flex';
    }

    hideLoading() {
        setTimeout(() => {
            document.getElementById('loadingScreen').style.display = 'none';
        }, 500);
    }

    updateProgress(percent, text) {
        document.getElementById('progressFill').style.width = `${percent}%`;
        document.getElementById('progressText').textContent = `${percent}%`;
        if (text) {
            document.querySelector('.loading-text').textContent = text;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// دوال عامة للاستخدام في النوافذ المنبثقة
function selectPlace(placeId) {
    const place = window.yemenMap.placesData.find(p => p.id === placeId);
    if (place) {
        window.yemenMap.map.setView([place.latitude, place.longitude], 15);
        window.yemenMap.hideSearchResults();
        document.getElementById('searchInput').value = place.name_ar || place.name_en;
    }
}

function getDirections(lat, lng) {
    if (window.yemenMap.currentLocation) {
        const url = `https://www.google.com/maps/dir/${window.yemenMap.currentLocation[0]},${window.yemenMap.currentLocation[1]}/${lat},${lng}`;
        window.open(url, '_blank');
    } else {
        const url = `https://www.google.com/maps/dir//${lat},${lng}`;
        window.open(url, '_blank');
    }
}

function sharePlace(name, lat, lng) {
    const url = `${window.location.origin}/offline-map.html?lat=${lat}&lng=${lng}&place=${encodeURIComponent(name)}`;
    
    if (navigator.share) {
        navigator.share({
            title: name,
            text: `موقع ${name} على خرائط اليمن`,
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            window.yemenMap.showToast('تم نسخ الرابط', 'success');
        });
    }
}

function savePlace(placeId) {
    // حفظ في التخزين المحلي
    const savedPlaces = JSON.parse(localStorage.getItem('savedPlaces') || '[]');
    if (!savedPlaces.includes(placeId)) {
        savedPlaces.push(placeId);
        localStorage.setItem('savedPlaces', JSON.stringify(savedPlaces));
        window.yemenMap.showToast('تم حفظ المكان', 'success');
    } else {
        window.yemenMap.showToast('المكان محفوظ مسبق<|im_start|>', 'warning');
    }
}

function openPhotoModal(photoUrl) {
    // يمكن تطوير هذه الوظيفة لاحق<|im_start|>
    window.open(photoUrl, '_blank');
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.yemenMap = new OfflineYemenMap();
});

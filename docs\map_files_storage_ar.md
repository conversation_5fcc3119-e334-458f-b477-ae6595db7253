# تخزين وإدارة ملفات الخرائط في نظام "يمن ناف"

## مقدمة

يعتمد نظام "يمن ناف" على خرائط مخزنة محليًا للعمل دون اتصال بالإنترنت. هذا المستند يشرح كيفية تخزين وإدارة ملفات الخرائط في التطبيق.

## مصادر بيانات الخرائط

يستخدم التطبيق مزيجًا من مصادر البيانات التالية:

1. **خرائط OpenStreetMap**: بيانات خرائط مفتوحة المصدر تغطي معظم مناطق اليمن
2. **بيانات خرائط محلية**: خرائط مخصصة لليمن تم إنشاؤها بواسطة فريق التطوير
3. **بيانات المستخدمين**: نقاط ومعالم أضافها المستخدمون وتمت الموافقة عليها

## هيكل تخزين الخرائط

### 1. موقع تخزين ملفات الخرائط

يتم تخزين ملفات الخرائط في المواقع التالية:

- **الخرائط الأساسية**: تُخزن في مجلد التطبيق الداخلي:
  ```
  /data/data/com.yemengps.app/files/maps/
  ```

- **الخرائط المنزلة بواسطة المستخدم**: تُخزن في مساحة التخزين الخارجية للتطبيق:
  ```
  /storage/emulated/0/Android/data/com.yemengps.app/files/offline_maps/
  ```

- **بيانات النقاط والمعالم**: تُخزن في قاعدة بيانات SQLite:
  ```
  /data/data/com.yemengps.app/databases/yemen_nav_db.db
  ```

### 2. تنسيق ملفات الخرائط

يستخدم التطبيق تنسيقات الملفات التالية:

- **ملفات MBTiles**: تنسيق قياسي لتخزين بلاطات الخرائط في قاعدة بيانات SQLite
- **ملفات GeoJSON**: لتخزين البيانات الجغرافية مثل الحدود والمناطق
- **ملفات PBF**: تنسيق مضغوط لبيانات OpenStreetMap

### 3. هيكل المجلدات

```
/data/data/com.yemengps.app/files/maps/
├── base/                  # الخرائط الأساسية
│   ├── yemen_base.mbtiles # خريطة اليمن الأساسية
│   └── cities/            # خرائط تفصيلية للمدن
│       ├── sanaa.mbtiles
│       ├── aden.mbtiles
│       └── ...
├── styles/                # أنماط عرض الخرائط
│   ├── day.json
│   └── night.json
└── fonts/                 # خطوط للنصوص العربية على الخرائط

/storage/emulated/0/Android/data/com.yemengps.app/files/offline_maps/
├── regions/               # مناطق منزلة بواسطة المستخدم
│   ├── region_1234.mbtiles
│   ├── region_5678.mbtiles
│   └── ...
└── metadata/              # بيانات وصفية للمناطق المنزلة
    ├── region_1234.json
    ├── region_5678.json
    └── ...
```

## آلية تنزيل وتحديث الخرائط

### 1. التنزيل الأولي

عند تثبيت التطبيق لأول مرة، يتم تنزيل الخرائط الأساسية التالية:

- خريطة اليمن الأساسية (حجم صغير، تغطية كاملة بدقة منخفضة)
- خرائط المدن الرئيسية (صنعاء، عدن، تعز، الحديدة) بدقة متوسطة

يتم تنزيل هذه الخرائط أثناء عملية التثبيت الأولي، ويمكن للمستخدم إلغاء تنزيل بعضها لتوفير مساحة التخزين.

### 2. تنزيل المناطق حسب الطلب

يمكن للمستخدم تنزيل مناطق إضافية بدقة عالية من خلال:

1. فتح شاشة "الخرائط دون اتصال" من القائمة الجانبية
2. النقر على زر "تنزيل منطقة جديدة"
3. رسم حدود المنطقة المطلوبة على الخريطة
4. تحديد مستوى التفاصيل (عالي، متوسط، منخفض)
5. تأكيد التنزيل

### 3. تحديث الخرائط

يتم تحديث الخرائط بالطرق التالية:

- **تحديثات تلقائية**: عند توفر اتصال إنترنت، يتحقق التطبيق من وجود تحديثات للخرائط الأساسية
- **تحديثات يدوية**: يمكن للمستخدم تحديث منطقة محددة يدويًا من شاشة "الخرائط دون اتصال"
- **تحديثات جزئية**: يدعم التطبيق تنزيل التغييرات فقط (وليس الخريطة كاملة) لتوفير البيانات

## إدارة مساحة التخزين

### 1. حجم ملفات الخرائط

فيما يلي تقدير لحجم ملفات الخرائط:

- **الخرائط الأساسية**: ~50 ميجابايت
- **خريطة مدينة بدقة عالية**: ~20-30 ميجابايت لكل مدينة
- **منطقة ريفية بدقة عالية**: ~10-20 ميجابايت لكل 100 كم²

### 2. آلية إدارة التخزين

يوفر التطبيق أدوات لإدارة مساحة التخزين:

- **عرض حجم الخرائط**: يعرض التطبيق حجم كل منطقة منزلة
- **حذف المناطق**: يمكن للمستخدم حذف المناطق غير المستخدمة
- **ضغط البيانات**: يستخدم التطبيق خوارزميات ضغط متقدمة لتقليل حجم الخرائط
- **إدارة تلقائية**: يمكن للتطبيق اقتراح حذف المناطق غير المستخدمة عند امتلاء مساحة التخزين

## تكامل ملفات الخرائط مع النظام

### 1. قراءة الخرائط

يستخدم التطبيق مكتبة `MapLibre GL` لعرض الخرائط المخزنة محليًا. تتم قراءة الخرائط من خلال:

```java
// مثال لكود قراءة ملف خريطة
OfflineManager offlineManager = OfflineManager.getInstance(context);
offlineManager.setOfflineMapboxMapStyle(new URL("file:///data/data/com.yemengps.app/files/maps/styles/day.json"));
```

### 2. تحديث بيانات النقاط

تتم إضافة نقاط المستخدمين (مثل الازدحامات، المطبات، إلخ) إلى الخريطة من خلال:

1. تخزين البيانات في قاعدة البيانات المحلية
2. إنشاء طبقة GeoJSON في الذاكرة
3. إضافة الطبقة فوق الخريطة الأساسية

```java
// مثال لكود إضافة نقاط إلى الخريطة
List<LocationPoint> points = locationRepository.getAllLocations();
GeoJsonSource source = new GeoJsonSource("user-points", FeatureCollection.fromFeatures(
    points.stream().map(point -> Feature.fromGeometry(
        Point.fromLngLat(point.getLongitude(), point.getLatitude())
    )).collect(Collectors.toList())
));
mapboxMap.getStyle().addSource(source);
```

## الأمان وسلامة البيانات

### 1. تشفير البيانات

يتم تشفير ملفات الخرائط الحساسة (مثل المناطق العسكرية) باستخدام:

- خوارزمية AES-256 للتشفير
- مفتاح تشفير فريد لكل تثبيت للتطبيق
- تخزين المفتاح في Android Keystore System

### 2. النسخ الاحتياطي والاستعادة

يدعم التطبيق:

- نسخ احتياطي للمناطق المنزلة
- استعادة الخرائط من النسخ الاحتياطي
- مزامنة قائمة المناطق المنزلة عبر أجهزة المستخدم المختلفة (عند تسجيل الدخول بنفس الحساب)

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

1. **خطأ "لا يمكن تحميل الخريطة"**:
   - تحقق من وجود الملفات في المسار الصحيح
   - تأكد من وجود مساحة تخزين كافية
   - أعد تنزيل الخريطة الأساسية

2. **بطء في عرض الخرائط**:
   - قم بتقليل مستوى التفاصيل
   - تحقق من وجود ملفات تالفة
   - أعد تشغيل التطبيق

3. **فشل تنزيل المناطق**:
   - تحقق من اتصال الإنترنت
   - تأكد من وجود مساحة تخزين كافية
   - حاول تنزيل منطقة أصغر

## الخلاصة

يستخدم نظام "يمن ناف" نهجًا متعدد المستويات لتخزين وإدارة ملفات الخرائط، مما يوفر:

- عمل موثوق دون اتصال بالإنترنت
- استهلاك معقول لمساحة التخزين
- تحديثات فعالة للبيانات
- أمان وخصوصية للمستخدمين

يمكن للمستخدمين إدارة الخرائط المخزنة بسهولة من خلال واجهة "الخرائط دون اتصال" في التطبيق، مما يتيح لهم تخصيص تجربتهم وفقًا لاحتياجاتهم ومساحة التخزين المتاحة.

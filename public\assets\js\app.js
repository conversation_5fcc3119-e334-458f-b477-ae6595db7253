// Yemen GPS Map Application
class YemenGPS {
    constructor() {
        this.map = null;
        this.currentMarker = null;
        this.savedPlaces = JSON.parse(localStorage.getItem('savedPlaces')) || [];
        this.currentLocation = null;
        this.currentLocationMarker = null;
        this.accuracyCircle = null;
        this.searchResults = [];
        this.routeLine = null;
        this.labelsLayer = null;
        this.currentMapType = 'street';

        this.init();
    }

    init() {
        this.initMap();
        this.bindEvents();
        this.loadSavedPlaces();
        this.getCurrentLocation();

        // Initialize labels option visibility
        this.updateLabelsOptionVisibility();

        // معالجة معاملات الاتجاهات من URL
        this.handleDirectionsParams();

        // إخفاء عناصر Google Maps وتخصيص الروابط
        this.customizeGoogleMapsElements();
    }

    initMap() {
        // Initialize map centered on Yemen (Sana'a)
        this.map = L.map('map').setView([15.3694, 44.1910], 7);

        // Add OpenStreetMap tiles (works offline if cached)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 19
        }).addTo(this.map);

        // Add click event to map
        this.map.on('click', (e) => {
            this.addMarker(e.latlng);
        });

        // Add geolocation control
        this.map.on('locationfound', (e) => {
            this.currentLocation = e.latlng;
            this.showCurrentLocation(e.latlng, e.accuracy);
        });

        this.map.on('locationerror', (e) => {
            this.showToast('لا يمكن تحديد موقعك الحالي', 'error');
        });
    }

    bindEvents() {
        // Search functionality
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        document.getElementById('searchInput').addEventListener('input', (e) => {
            if (e.target.value.length > 2) {
                this.showSearchSuggestions(e.target.value);
            } else {
                this.hideSearchResults();
            }
        });

        // Location button
        document.getElementById('locationBtn').addEventListener('click', () => {
            this.getCurrentLocation();
        });

        // Share button
        document.getElementById('shareBtn').addEventListener('click', () => {
            this.shareCurrentView();
        });

        // Layers button
        document.getElementById('layersBtn').addEventListener('click', () => {
            this.toggleLayersPanel();
        });

        // Map controls
        document.getElementById('zoomIn').addEventListener('click', () => {
            this.map.zoomIn();
        });

        document.getElementById('zoomOut').addEventListener('click', () => {
            this.map.zoomOut();
        });

        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // View controls
        document.getElementById('streetView').addEventListener('click', () => {
            this.changeMapType('street');
        });

        document.getElementById('satelliteView').addEventListener('click', () => {
            this.changeMapType('satellite');
        });

        document.getElementById('terrainView').addEventListener('click', () => {
            this.changeMapType('terrain');
        });

        // Info window controls
        document.getElementById('closeInfo').addEventListener('click', () => {
            this.hideInfoWindow();
        });

        // These buttons are now handled dynamically in showInfoWindow

        // Layers panel controls
        document.getElementById('closeLayersPanel').addEventListener('click', () => {
            this.hideLayersPanel();
        });

        // Layer options
        document.querySelectorAll('input[name="mapType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.changeMapType(e.target.value);
            });
        });

        // Labels layer toggle
        document.getElementById('labelsLayer').addEventListener('change', (e) => {
            this.toggleLabelsLayer(e.target.checked);
        });

        // Sidebar controls
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.hideSidebar();
        });
    }

    performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (!query) return;

        this.showLoading();

        // Use Nominatim for geocoding (free and works offline if cached)
        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=ye&limit=5`)
            .then(response => response.json())
            .then(data => {
                this.hideLoading();
                this.displaySearchResults(data);
            })
            .catch(error => {
                this.hideLoading();
                this.showToast('خطأ في البحث', 'error');
                console.error('Search error:', error);
            });
    }

    displaySearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        resultsContainer.innerHTML = '';

        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
            resultsContainer.style.display = 'block';
            return;
        }

        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';
            resultItem.innerHTML = `
                <div class="result-title">${result.display_name}</div>
                <div class="result-type">${result.type}</div>
            `;

            resultItem.addEventListener('click', () => {
                this.selectSearchResult(result);
            });

            resultsContainer.appendChild(resultItem);
        });

        resultsContainer.style.display = 'block';
    }

    selectSearchResult(result) {
        const lat = parseFloat(result.lat);
        const lon = parseFloat(result.lon);
        const latlng = L.latLng(lat, lon);

        this.map.setView(latlng, 15);
        this.addMarker(latlng, result.display_name);
        this.hideSearchResults();

        document.getElementById('searchInput').value = result.display_name;
    }

    addMarker(latlng, title = null) {
        // Check if directions panel is open
        const directionsPanel = document.getElementById('directionsPanel');
        if (!directionsPanel.classList.contains('hidden')) {
            // If directions panel is open, just set destination and return
            document.getElementById('toLocation').value = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            return;
        }

        // Remove existing marker
        if (this.currentMarker) {
            this.map.removeLayer(this.currentMarker);
        }

        // Create custom red marker icon
        const redMarkerIcon = L.divIcon({
            className: 'custom-red-marker',
            html: `
                <div class="marker-pin">
                    <div class="marker-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                </div>
            `,
            iconSize: [30, 40],
            iconAnchor: [15, 40],
            popupAnchor: [0, -40]
        });

        // Add new marker with custom icon
        this.currentMarker = L.marker(latlng, {icon: redMarkerIcon}).addTo(this.map);

        // Show info window
        this.showInfoWindow(latlng, title);
    }

    showInfoWindow(latlng, title = null) {
        const infoWindow = document.getElementById('infoWindow');
        const infoTitle = document.getElementById('infoTitle');
        const infoBody = document.getElementById('infoBody');

        // Reset to original info window state
        infoTitle.innerHTML = title || 'معلومات الموقع';

        // Get location information
        const lat = latlng.lat.toFixed(6);
        const lng = latlng.lng.toFixed(6);

        infoBody.innerHTML = `
            <div class="location-info">
                <p><strong>الإحداثيات:</strong> ${lat}, ${lng}</p>
                <p><strong>خط العرض:</strong> ${lat}</p>
                <p><strong>خط الطول:</strong> ${lng}</p>
            </div>
            <div class="info-actions">
                <button id="directionsInfoBtn" class="info-btn">
                    <i class="fas fa-directions"></i>
                    الاتجاهات
                </button>
                <button id="saveLocationInfoBtn" class="info-btn">
                    <i class="fas fa-bookmark"></i>
                    حفظ الموقع
                </button>
                <button id="shareLocationInfoBtn" class="info-btn">
                    <i class="fas fa-share"></i>
                    مشاركة
                </button>
            </div>
        `;

        // Add event listeners for the buttons
        setTimeout(() => {
            document.getElementById('directionsInfoBtn')?.addEventListener('click', () => {
                this.getDirections();
            });

            document.getElementById('saveLocationInfoBtn')?.addEventListener('click', () => {
                this.saveLocation(parseFloat(lat), parseFloat(lng));
            });

            document.getElementById('shareLocationInfoBtn')?.addEventListener('click', () => {
                this.shareLocation(parseFloat(lat), parseFloat(lng));
            });
        }, 0);

        infoWindow.classList.remove('hidden');
    }

    hideInfoWindow() {
        document.getElementById('infoWindow').classList.add('hidden');
        this.clearRoute(); // Clear route when closing info window
    }

    showDirectionsInInfoWindow() {
        const infoWindow = document.getElementById('infoWindow');
        const infoTitle = document.getElementById('infoTitle');
        const infoBody = document.getElementById('infoBody');

        // Change title
        infoTitle.innerHTML = `
            <button id="backToInfo" class="back-btn">
                <i class="fas fa-arrow-right"></i>
            </button>
            <span>الاتجاهات</span>
        `;

        // Get destination coordinates
        let destinationCoords = '';
        if (this.currentMarker) {
            const latlng = this.currentMarker.getLatLng();
            destinationCoords = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
        }

        // Transform body to directions interface
        infoBody.innerHTML = `
            <div class="directions-form">
                <div class="direction-input">
                    <label>من:</label>
                    <input type="text" id="fromLocationInfo" placeholder="نقطة البداية" value="الموقع الحالي">
                    <button id="useCurrentLocationInfo" class="location-btn">
                        <i class="fas fa-location-arrow"></i>
                    </button>
                </div>

                <div class="direction-input">
                    <label>إلى:</label>
                    <input type="text" id="toLocationInfo" placeholder="الوجهة" value="${destinationCoords}" readonly>
                </div>

                <button id="swapLocationsInfo" class="swap-btn">
                    <i class="fas fa-exchange-alt"></i>
                </button>

                <div class="transport-options">
                    <button class="transport-btn active" data-mode="driving">
                        <i class="fas fa-car"></i>
                        <span>سيارة</span>
                    </button>
                    <button class="transport-btn" data-mode="walking">
                        <i class="fas fa-walking"></i>
                        <span>مشي</span>
                    </button>
                    <button class="transport-btn" data-mode="cycling">
                        <i class="fas fa-bicycle"></i>
                        <span>دراجة</span>
                    </button>
                </div>

                <div class="route-preferences">
                    <label class="preference-label">تفضيل المسار:</label>
                    <div class="preference-options">
                        <label class="preference-option">
                            <input type="radio" name="routePreferenceInfo" value="shortest" checked>
                            <span>أقصر مسافة</span>
                        </label>
                        <label class="preference-option">
                            <input type="radio" name="routePreferenceInfo" value="fastest">
                            <span>أسرع وقت</span>
                        </label>
                    </div>
                </div>

                <button id="calculateRouteInfo" class="calculate-btn">
                    <i class="fas fa-route"></i>
                    حساب المسار
                </button>

                <div id="routeResultsInfo" class="route-results hidden">
                    <div class="route-summary">
                        <div class="route-info">
                            <span class="distance">المسافة: <strong id="routeDistanceInfo">-</strong></span>
                            <span class="duration">الوقت: <strong id="routeDurationInfo">-</strong></span>
                        </div>
                    </div>
                    <div id="routeInstructionsInfo" class="route-instructions">
                        <!-- Route steps will be displayed here -->
                    </div>
                </div>
            </div>
        `;

        // Add event listeners for the new interface
        this.bindDirectionsEvents();

        // Set current location if available
        if (this.currentLocation) {
            document.getElementById('fromLocationInfo').value = 'الموقع الحالي';
        }

        infoWindow.classList.remove('hidden');
    }

    bindDirectionsEvents() {
        // Back button
        document.getElementById('backToInfo').addEventListener('click', () => {
            this.clearRoute(); // Clear route when going back
            this.showOriginalInfoWindow();
        });

        // Transport mode selection
        document.querySelectorAll('#infoBody .transport-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('#infoBody .transport-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // Calculate route
        document.getElementById('calculateRouteInfo').addEventListener('click', () => {
            this.calculateRouteFromInfo();
        });

        // Use current location
        document.getElementById('useCurrentLocationInfo').addEventListener('click', () => {
            if (this.currentLocation) {
                document.getElementById('fromLocationInfo').value = 'الموقع الحالي';
            } else {
                this.showToast('لم يتم تحديد الموقع الحالي', 'error');
            }
        });

        // Swap locations
        document.getElementById('swapLocationsInfo').addEventListener('click', () => {
            const fromInput = document.getElementById('fromLocationInfo');
            const toInput = document.getElementById('toLocationInfo');

            const temp = fromInput.value;
            fromInput.value = toInput.value;
            toInput.value = temp;
        });
    }

    showOriginalInfoWindow() {
        if (this.currentMarker) {
            const latlng = this.currentMarker.getLatLng();
            this.showInfoWindow(latlng);
        }
    }

    async calculateRouteFromInfo() {
        const fromInput = document.getElementById('fromLocationInfo').value;
        const toInput = document.getElementById('toLocationInfo').value;
        const transportMode = document.querySelector('#infoBody .transport-btn.active').dataset.mode;
        const routePreference = document.querySelector('input[name="routePreferenceInfo"]:checked').value;

        if (!fromInput || !toInput) {
            this.showToast('يرجى تحديد نقطة البداية والوجهة', 'error');
            return;
        }

        // Show loading
        const calculateBtn = document.getElementById('calculateRouteInfo');
        calculateBtn.disabled = true;
        calculateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحساب...';

        try {
            let fromCoords, toCoords;

            // Get coordinates for "from" location
            if (fromInput === 'الموقع الحالي' && this.currentLocation) {
                fromCoords = [this.currentLocation.lng, this.currentLocation.lat];
            } else {
                fromCoords = await this.geocodeLocation(fromInput);
            }

            // Get coordinates for "to" location
            if (toInput.includes(',')) {
                const [lat, lng] = toInput.split(',').map(coord => parseFloat(coord.trim()));
                toCoords = [lng, lat];
            } else {
                toCoords = await this.geocodeLocation(toInput);
            }

            // Calculate route using OSRM with preference
            const route = await this.getRoute(fromCoords, toCoords, transportMode, routePreference);
            this.displayRouteInInfo(route);

        } catch (error) {
            console.error('Error calculating route:', error);
            this.showToast('حدث خطأ في حساب المسار', 'error');
        } finally {
            // Reset button
            calculateBtn.disabled = false;
            calculateBtn.innerHTML = '<i class="fas fa-route"></i> حساب المسار';
        }
    }

    displayRouteInInfo(route) {
        // Clear previous route
        this.clearRoute();

        // Add route to map
        const coordinates = route.geometry.coordinates.map(coord => [coord[1], coord[0]]);
        this.routeLine = L.polyline(coordinates, {
            color: '#4285f4',
            weight: 5,
            opacity: 0.8
        }).addTo(this.map);

        // Fit map to route
        this.map.fitBounds(this.routeLine.getBounds(), { padding: [20, 20] });

        // Display route information in info window
        const distance = (route.distance / 1000).toFixed(1);
        const duration = Math.round(route.duration / 60);

        // Update summary
        document.getElementById('routeDistanceInfo').textContent = `${distance} كم`;
        document.getElementById('routeDurationInfo').textContent = `${duration} دقيقة`;

        // Display route steps
        const instructionsContainer = document.getElementById('routeInstructionsInfo');
        instructionsContainer.innerHTML = '';

        route.legs[0].steps.forEach((step, index) => {
            const stepElement = document.createElement('div');
            stepElement.className = 'route-step';

            const instruction = this.translateInstruction(step.maneuver.type, step.name);
            const stepDistance = step.distance > 1000 ?
                `${(step.distance / 1000).toFixed(1)} كم` :
                `${Math.round(step.distance)} م`;

            stepElement.innerHTML = `
                <div class="step-icon">${index + 1}</div>
                <div class="step-content">
                    <div class="step-instruction">${instruction}</div>
                    <div class="step-distance">${stepDistance}</div>
                </div>
            `;

            instructionsContainer.appendChild(stepElement);
        });

        // Show results
        document.getElementById('routeResultsInfo').classList.remove('hidden');
    }

    getCurrentLocation() {
        this.showLoading();
        this.map.locate({setView: true, maxZoom: 16});
    }

    showCurrentLocation(latlng, accuracy) {
        this.hideLoading();

        // Remove previous current location markers
        if (this.currentLocationMarker) {
            this.map.removeLayer(this.currentLocationMarker);
        }
        if (this.accuracyCircle) {
            this.map.removeLayer(this.accuracyCircle);
        }

        // Add current location marker
        const currentLocationIcon = L.divIcon({
            className: 'current-location-marker',
            html: '<i class="fas fa-dot-circle"></i>',
            iconSize: [20, 20]
        });

        this.currentLocationMarker = L.marker(latlng, {icon: currentLocationIcon}).addTo(this.map);

        // Add accuracy circle with limited radius (max 100 meters for better UX)
        const limitedAccuracy = Math.min(accuracy, 100);
        this.accuracyCircle = L.circle(latlng, {
            radius: limitedAccuracy,
            color: '#4285f4',
            fillColor: '#4285f4',
            fillOpacity: 0.1,
            weight: 2
        }).addTo(this.map);

        this.showToast('تم تحديد موقعك الحالي');
    }

    shareCurrentView() {
        const center = this.map.getCenter();
        const zoom = this.map.getZoom();
        const url = `${window.location.origin}${window.location.pathname}?lat=${center.lat}&lng=${center.lng}&zoom=${zoom}`;

        if (navigator.share) {
            navigator.share({
                title: 'خرائط اليمن',
                text: 'شاهد هذا الموقع على خرائط اليمن',
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('تم نسخ الرابط');
            });
        }
    }

    toggleLayersPanel() {
        const panel = document.getElementById('layersPanel');
        panel.classList.toggle('hidden');
    }

    hideLayersPanel() {
        document.getElementById('layersPanel').classList.add('hidden');
    }

    toggleLabelsLayer(enabled) {
        if (this.labelsLayer) {
            if (enabled) {
                // Add labels layer if not already added
                if (!this.map.hasLayer(this.labelsLayer)) {
                    this.labelsLayer.addTo(this.map);
                }
            } else {
                // Remove labels layer
                if (this.map.hasLayer(this.labelsLayer)) {
                    this.map.removeLayer(this.labelsLayer);
                }
            }
        }
    }

    updateLabelsOptionVisibility() {
        const labelsOption = document.querySelector('.layer-option.labels-only');
        if (this.currentMapType === 'satellite') {
            labelsOption.classList.add('show');
        } else {
            labelsOption.classList.remove('show');
        }
    }

    changeMapType(type) {
        // Remove existing tile layers
        this.map.eachLayer(layer => {
            if (layer instanceof L.TileLayer) {
                this.map.removeLayer(layer);
            }
        });

        // Store current map type
        this.currentMapType = type;

        // Add new tile layer based on type
        let tileLayer;

        switch (type) {
            case 'satellite':
                // Add satellite imagery
                tileLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: 'Tiles © Esri',
                    maxZoom: 19
                });

                // Create labels layer for satellite view
                this.labelsLayer = L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager_only_labels/{z}/{x}/{y}{r}.png', {
                    attribution: '© CARTO © OpenStreetMap',
                    maxZoom: 19,
                    subdomains: 'abcd',
                    opacity: 1
                });
                break;

            case 'terrain':
                tileLayer = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenTopoMap contributors',
                    maxZoom: 17
                });
                this.labelsLayer = null;
                break;

            default:
                tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19
                });
                this.labelsLayer = null;
        }

        // Add base layer
        tileLayer.addTo(this.map);

        // Add labels layer if it exists and labels are enabled
        const labelsEnabled = document.getElementById('labelsLayer').checked;
        if (this.labelsLayer && labelsEnabled) {
            this.labelsLayer.addTo(this.map);
        }

        // Show/hide labels option based on map type
        this.updateLabelsOptionVisibility();

        // Update active button
        document.querySelectorAll('.view-controls .control-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(`${type}View`).classList.add('active');
    }

    saveCurrentLocation() {
        if (!this.currentMarker) {
            this.showToast('لا يوجد موقع محدد للحفظ', 'error');
            return;
        }

        const latlng = this.currentMarker.getLatLng();
        const name = prompt('أدخل اسم الموقع:');

        if (name) {
            const place = {
                id: Date.now(),
                name: name,
                lat: latlng.lat,
                lng: latlng.lng,
                date: new Date().toLocaleDateString('ar-SA')
            };

            this.savedPlaces.push(place);
            localStorage.setItem('savedPlaces', JSON.stringify(this.savedPlaces));
            this.loadSavedPlaces();
            this.showToast('تم حفظ الموقع');
        }
    }

    loadSavedPlaces() {
        const container = document.getElementById('savedPlaces');
        container.innerHTML = '';

        this.savedPlaces.forEach(place => {
            const placeElement = document.createElement('div');
            placeElement.className = 'saved-place';
            placeElement.innerHTML = `
                <div class="place-name">${place.name}</div>
                <div class="place-date">${place.date}</div>
                <div class="place-actions">
                    <button onclick="app.goToPlace(${place.lat}, ${place.lng})" class="place-btn">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button onclick="app.deletePlace(${place.id})" class="place-btn delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            container.appendChild(placeElement);
        });
    }

    goToPlace(lat, lng) {
        this.map.setView([lat, lng], 15);
        this.addMarker(L.latLng(lat, lng), 'موقع محفوظ');
    }

    deletePlace(id) {
        this.savedPlaces = this.savedPlaces.filter(place => place.id !== id);
        localStorage.setItem('savedPlaces', JSON.stringify(this.savedPlaces));
        this.loadSavedPlaces();
        this.showToast('تم حذف الموقع');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showLoading() {
        document.getElementById('loadingSpinner').classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loadingSpinner').classList.add('hidden');
    }

    hideSearchResults() {
        document.getElementById('searchResults').style.display = 'none';
    }

    hideSidebar() {
        document.getElementById('sidebar').classList.remove('open');
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        document.getElementById('toastContainer').appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    getDirections() {
        // Transform info window to directions mode
        this.showDirectionsInInfoWindow();
    }

    showDirectionsPanel() {
        document.getElementById('directionsPanel').classList.remove('hidden');
        this.hideSidebar();
    }

    hideDirectionsPanel() {
        document.getElementById('directionsPanel').classList.add('hidden');
        this.clearRoute();
    }

    async calculateRoute() {
        const fromInput = document.getElementById('fromLocation').value;
        const toInput = document.getElementById('toLocation').value;
        const transportMode = document.querySelector('.transport-btn.active').dataset.mode;
        const routePreference = document.querySelector('input[name="routePreference"]:checked').value;

        if (!fromInput || !toInput) {
            this.showToast('يرجى تحديد نقطة البداية والوجهة', 'error');
            return;
        }

        // Show loading
        document.getElementById('calculateRoute').disabled = true;
        document.getElementById('calculateRoute').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحساب...';

        try {
            let fromCoords, toCoords;

            // Get coordinates for "from" location
            if (fromInput === 'الموقع الحالي' && this.currentLocation) {
                fromCoords = [this.currentLocation.lng, this.currentLocation.lat];
            } else {
                fromCoords = await this.geocodeLocation(fromInput);
            }

            // Get coordinates for "to" location
            if (toInput.includes(',')) {
                const [lat, lng] = toInput.split(',').map(coord => parseFloat(coord.trim()));
                toCoords = [lng, lat];
            } else {
                toCoords = await this.geocodeLocation(toInput);
            }

            // Calculate route using OSRM with preference
            const route = await this.getRoute(fromCoords, toCoords, transportMode, routePreference);
            this.displayRoute(route);

        } catch (error) {
            console.error('Error calculating route:', error);
            this.showToast('حدث خطأ في حساب المسار', 'error');
        } finally {
            // Reset button
            document.getElementById('calculateRoute').disabled = false;
            document.getElementById('calculateRoute').innerHTML = '<i class="fas fa-route"></i> حساب المسار';
        }
    }

    async geocodeLocation(query) {
        const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=1`);
        const data = await response.json();

        if (data.length === 0) {
            throw new Error('Location not found');
        }

        return [parseFloat(data[0].lon), parseFloat(data[0].lat)];
    }

    async getRoute(from, to, mode = 'driving', preference = 'shortest') {
        // Map transport modes to OSRM profiles
        const profiles = {
            driving: 'car',
            walking: 'foot',
            cycling: 'bike'
        };

        const profile = profiles[mode] || 'car';

        // Build URL with alternatives to get multiple route options
        const baseUrl = `https://router.project-osrm.org/route/v1/${profile}/${from[0]},${from[1]};${to[0]},${to[1]}`;
        const params = new URLSearchParams({
            overview: 'full',
            geometries: 'geojson',
            steps: 'true',
            alternatives: 'true',
            annotations: 'true'
        });

        const url = `${baseUrl}?${params.toString()}`;

        try {
            const response = await fetch(url);
            const data = await response.json();

            if (data.code !== 'Ok' || !data.routes || data.routes.length === 0) {
                throw new Error('Route not found');
            }

            // Select best route based on user preference
            let bestRoute = data.routes[0]; // Default to first route

            if (preference === 'shortest') {
                // Find route with shortest distance
                bestRoute = data.routes.reduce((shortest, current) =>
                    current.distance < shortest.distance ? current : shortest
                );
            } else if (preference === 'fastest') {
                // Find route with shortest duration
                bestRoute = data.routes.reduce((fastest, current) =>
                    current.duration < fastest.duration ? current : fastest
                );
            }

            return bestRoute;

        } catch (error) {
            console.warn('Primary routing failed, trying fallback:', error);

            // Fallback to simple route without alternatives
            const fallbackUrl = `${baseUrl}?overview=full&geometries=geojson&steps=true`;
            const fallbackResponse = await fetch(fallbackUrl);
            const fallbackData = await fallbackResponse.json();

            if (fallbackData.code !== 'Ok') {
                throw new Error('Route not found');
            }

            return fallbackData.routes[0];
        }
    }

    displayRoute(route) {
        // Clear previous route
        this.clearRoute();

        // Add route to map
        const coordinates = route.geometry.coordinates.map(coord => [coord[1], coord[0]]);
        this.routeLine = L.polyline(coordinates, {
            color: '#4285f4',
            weight: 5,
            opacity: 0.8
        }).addTo(this.map);

        // Fit map to route
        this.map.fitBounds(this.routeLine.getBounds(), { padding: [20, 20] });

        // Display route information
        this.displayRouteInfo(route);
    }

    displayRouteInfo(route) {
        const distance = (route.distance / 1000).toFixed(1); // Convert to km
        const duration = Math.round(route.duration / 60); // Convert to minutes

        // Update summary
        document.getElementById('routeDistance').textContent = `${distance} كم`;
        document.getElementById('routeDuration').textContent = `${duration} دقيقة`;

        // Display route steps
        const instructionsContainer = document.getElementById('routeInstructions');
        instructionsContainer.innerHTML = '';

        route.legs[0].steps.forEach((step, index) => {
            const stepElement = document.createElement('div');
            stepElement.className = 'route-step';

            const instruction = this.translateInstruction(step.maneuver.type, step.name);
            const distance = step.distance > 1000 ?
                `${(step.distance / 1000).toFixed(1)} كم` :
                `${Math.round(step.distance)} م`;

            stepElement.innerHTML = `
                <div class="step-icon">${index + 1}</div>
                <div class="step-content">
                    <div class="step-instruction">${instruction}</div>
                    <div class="step-distance">${distance}</div>
                </div>
            `;

            instructionsContainer.appendChild(stepElement);
        });

        // Show results
        document.getElementById('routeResults').classList.remove('hidden');
    }

    translateInstruction(type, roadName) {
        const translations = {
            'depart': 'ابدأ الرحلة',
            'turn': 'انعطف',
            'new name': 'تابع على',
            'arrive': 'وصلت إلى الوجهة',
            'merge': 'اندمج',
            'on ramp': 'ادخل إلى الطريق السريع',
            'off ramp': 'اخرج من الطريق السريع',
            'fork': 'اتبع التفرع',
            'end of road': 'نهاية الطريق',
            'continue': 'تابع',
            'roundabout': 'ادخل الدوار',
            'rotary': 'ادخل الدوار',
            'roundabout turn': 'اخرج من الدوار'
        };

        let instruction = translations[type] || 'تابع';

        if (roadName && roadName !== '') {
            instruction += ` على ${roadName}`;
        }

        return instruction;
    }

    clearRoute() {
        if (this.routeLine) {
            this.map.removeLayer(this.routeLine);
            this.routeLine = null;
        }

        // Hide route results in directions panel
        const routeResults = document.getElementById('routeResults');
        if (routeResults) {
            routeResults.classList.add('hidden');
        }

        // Hide route results in info window
        const routeResultsInfo = document.getElementById('routeResultsInfo');
        if (routeResultsInfo) {
            routeResultsInfo.classList.add('hidden');
        }
    }

    shareLocation(lat = null, lng = null) {
        let targetLat, targetLng;

        if (lat !== null && lng !== null) {
            targetLat = lat;
            targetLng = lng;
        } else if (this.currentMarker) {
            const latlng = this.currentMarker.getLatLng();
            targetLat = latlng.lat;
            targetLng = latlng.lng;
        } else {
            this.showToast('لا يوجد موقع محدد للمشاركة', 'error');
            return;
        }

        const url = `${window.location.origin}${window.location.pathname}?lat=${targetLat}&lng=${targetLng}&zoom=15`;

        if (navigator.share) {
            navigator.share({
                title: 'موقع على خرائط اليمن',
                text: 'شاهد هذا الموقع',
                url: url
            });
        } else {
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('تم نسخ رابط الموقع');
            });
        }
    }

    saveLocation(lat = null, lng = null) {
        let targetLat, targetLng;

        if (lat !== null && lng !== null) {
            targetLat = lat;
            targetLng = lng;
        } else if (this.currentMarker) {
            const latlng = this.currentMarker.getLatLng();
            targetLat = latlng.lat;
            targetLng = latlng.lng;
        } else {
            this.showToast('لا يوجد موقع محدد للحفظ', 'error');
            return;
        }

        const name = prompt('أدخل اسم الموقع:');

        if (name) {
            const place = {
                id: Date.now(),
                name: name,
                lat: targetLat,
                lng: targetLng,
                date: new Date().toLocaleDateString('ar-SA')
            };

            this.savedPlaces.push(place);
            localStorage.setItem('savedPlaces', JSON.stringify(this.savedPlaces));
            this.loadSavedPlaces();
            this.showToast('تم حفظ الموقع');
        }
    }
}

// Initialize the application
const app = new YemenGPS();

// Handle URL parameters for shared locations
window.addEventListener('load', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    const zoom = urlParams.get('zoom');

    if (lat && lng) {
        const latlng = L.latLng(parseFloat(lat), parseFloat(lng));
        app.map.setView(latlng, zoom ? parseInt(zoom) : 15);
        app.addMarker(latlng, 'موقع مشارك');
    }
});

// Directions Panel Event Listeners
document.getElementById('closeDirectionsPanel').addEventListener('click', () => {
    app.hideDirectionsPanel();
});

document.getElementById('useCurrentLocation').addEventListener('click', () => {
    if (app.currentLocation) {
        document.getElementById('fromLocation').value = 'الموقع الحالي';
    } else {
        app.showToast('لم يتم تحديد الموقع الحالي', 'error');
    }
});

document.getElementById('swapLocations').addEventListener('click', () => {
    const fromInput = document.getElementById('fromLocation');
    const toInput = document.getElementById('toLocation');

    const temp = fromInput.value;
    fromInput.value = toInput.value;
    toInput.value = temp;
});

// Transport mode selection
document.querySelectorAll('.transport-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        document.querySelectorAll('.transport-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
    });
});

document.getElementById('calculateRoute').addEventListener('click', () => {
    app.calculateRoute();
});

// إضافة دالة معالجة معاملات الاتجاهات في فئة YemenGPS
YemenGPS.prototype.handleDirectionsParams = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    const placeName = urlParams.get('place');
    const isDirections = urlParams.get('directions');

    if (lat && lng && isDirections === 'true') {
        console.log('تم تحديد وجهة للاتجاهات:', { lat, lng, placeName });

        // تحديث عنوان الصفحة
        const decodedPlaceName = decodeURIComponent(placeName || 'الوجهة المحددة');
        document.title = `الاتجاهات إلى ${decodedPlaceName} - يمن ناف`;

        // تحديث مربع البحث
        setTimeout(() => {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = `الاتجاهات إلى ${decodedPlaceName}`;
                searchInput.placeholder = 'جاري حساب الاتجاهات...';
            }
        }, 100);

        // إضافة علامة على الوجهة
        setTimeout(() => {
            const destLat = parseFloat(lat);
            const destLng = parseFloat(lng);
            const destLatLng = L.latLng(destLat, destLng);

            // التركيز على الوجهة
            this.map.setView(destLatLng, 15);

            // إضافة علامة للوجهة
            this.addMarker(destLatLng, decodedPlaceName);

            // فتح نافذة الاتجاهات تلقائياً
            setTimeout(() => {
                this.getDirections();
            }, 500);

        }, 1000);
    }

    // تخصيص عناصر Google Maps وتوجيه الروابط للخريطة المحلية
    customizeGoogleMapsElements() {
        // تشغيل فوري
        setTimeout(() => {
            this.hideGoogleMapsElements();
            this.redirectGoogleMapsLinks();
        }, 1000);

        // تشغيل دوري كل 3 ثوان
        setInterval(() => {
            this.hideGoogleMapsElements();
            this.redirectGoogleMapsLinks();
        }, 3000);
    }

    // إخفاء عناصر Google Maps غير المرغوب فيها
    hideGoogleMapsElements() {
        // إخفاء النصوص المحددة
        const textElements = document.querySelectorAll('*');
        textElements.forEach(element => {
            if (element.textContent &&
                (element.textContent.includes('عرض في خرائط Google') ||
                 element.textContent.includes('View on Google Maps') ||
                 element.textContent.includes('صنعاء') && element.textContent.includes('الإحداثيات'))) {
                element.style.display = 'none !important';
            }
        });
    }

    // توجيه روابط Google Maps للخريطة المحلية
    redirectGoogleMapsLinks() {
        // البحث عن عناصر تحتوي على نص "عرض في خرائط Google"
        const textElements = document.querySelectorAll('*');
        textElements.forEach(element => {
            if (element.textContent &&
                (element.textContent.includes('عرض في خرائط Google') ||
                 element.textContent.includes('View on Google Maps'))) {

                // تغيير النص
                element.textContent = element.textContent.replace(/عرض في خرائط Google/gi, 'عرض في يمن GPS')
                                                        .replace(/View on Google Maps/gi, 'عرض في يمن GPS');

                // إضافة حدث النقر للانتقال للخريطة المحلية
                element.style.cursor = 'pointer';
                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    // الانتقال للخريطة الرئيسية
                    window.location.href = window.location.origin + '/';
                });
            }
        });
    }
};

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new YemenGPS();
});

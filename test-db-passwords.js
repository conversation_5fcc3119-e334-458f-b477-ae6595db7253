// سكريبت لاختبار كلمات مرور مختلفة للاتصال بقاعدة البيانات
const { Pool } = require('pg');

// قائمة كلمات المرور المحتملة
const possiblePasswords = [
  'admin',
  'yemen',
  'postgres',
  'password',
  'yemen123',
  'yemengps'
];

async function testPasswords() {
  console.log('اختبار كلمات المرور المحتملة للمستخدم yemen على قاعدة البيانات yemen_gps...\n');
  
  for (const password of possiblePasswords) {
    const pool = new Pool({
      host: 'localhost',
      port: 5432,
      database: 'yemen_gps',
      user: 'yemen',
      password: password
    });
    
    try {
      console.log(`محاولة الاتصال باستخدام كلمة المرور: ${password}`);
      const result = await pool.query('SELECT NOW()');
      console.log(`✅ تم الاتصال بنجاح باستخدام كلمة المرور: ${password}`);
      await pool.end();
      
      // إذا نجح الاتصال، توقف عن المحاولات الأخرى
      return password;
    } catch (error) {
      console.log(`❌ فشل الاتصال باستخدام كلمة المرور: ${password}`);
      console.log(`   الخطأ: ${error.message}`);
      await pool.end().catch(() => {});
    }
  }
  
  console.log('\nلم تنجح أي من كلمات المرور المحتملة في الاتصال بقاعدة البيانات.');
}

testPasswords()
  .then(validPassword => {
    if (validPassword) {
      console.log(`\nكلمة المرور الصحيحة للمستخدم yemen هي: ${validPassword}`);
    }
  })
  .catch(error => {
    console.error('حدث خطأ أثناء اختبار كلمات المرور:', error);
  });

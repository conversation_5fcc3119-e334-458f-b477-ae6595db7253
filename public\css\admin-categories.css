/* أنماط خاصة بقسم التصنيفات في لوحة الإدارة */

/* أنماط الصفوف القابلة للسحب */
.sortable-row {
    cursor: move;
    transition: background-color 0.3s;
}

.sortable-row:hover {
    background-color: #f5f5f5;
}

/* أنماط أثناء السحب */
.sortable-ghost {
    background-color: #e9f7ef !important;
    opacity: 0.5;
}

.sortable-chosen {
    background-color: #e9f7ef !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sortable-drag {
    opacity: 0.9;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}

/* أنماط مقبض السحب */
.drag-handle {
    cursor: move;
    color: #aaa;
    margin-right: 8px;
    transition: color 0.3s;
}

.drag-handle:hover {
    color: #4CAF50;
}

/* أنماط عرض الألوان */
.color-preview {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    vertical-align: middle;
    margin-right: 5px;
    border: 1px solid #ddd;
}

/* أنماط حالة التصنيف */
.status-active {
    color: #4CAF50;
    font-weight: bold;
}

.status-inactive {
    color: #F44336;
}

/* أنماط أزرار الإجراءات */
.category-actions {
    display: flex;
    gap: 5px;
}

/* تحسين مظهر نموذج إضافة/تعديل التصنيف */
#add-category-form {
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر حقل اختيار اللون */
#category-color {
    height: 35px;
    padding: 0 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

/* أنماط زر الاستيراد */
.import-btn {
    position: relative;
    overflow: hidden;
}

.import-btn input[type="file"] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* أنماط قسم البحث والتصفية */
.search-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
}

#category-search {
    padding-left: 35px;
    padding-right: 35px;
    width: 100%;
    border-radius: 20px;
    border: 1px solid #ddd;
    transition: all 0.3s;
}

#category-search:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.clear-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #aaa;
    font-size: 18px;
    cursor: pointer;
    display: none;
    padding: 0;
    width: 20px;
    height: 20px;
    line-height: 1;
}

.clear-search:hover {
    color: #F44336;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: bold;
    color: #555;
    white-space: nowrap;
}

.filter-group select {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: white;
    min-width: 120px;
}

/* أنماط أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-buttons .btn i {
    margin-right: 5px;
}

/* أنماط إحصائيات التصنيفات */
.categories-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    background-color: #f5f5f5;
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-item span {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #4CAF50;
    margin-bottom: 5px;
}

.stat-item label {
    display: block;
    font-size: 14px;
    color: #555;
}

/* أنماط تمييز نتائج البحث */
.search-highlight {
    background-color: #ffeb3b;
    color: #000;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: inline-block;
    animation: highlight-pulse 1.5s ease-in-out infinite;
}

@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 235, 59, 0.7);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(255, 235, 59, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 235, 59, 0);
    }
}

/* أنماط صف "لا توجد نتائج" */
#no-categories-results td {
    background-color: #f9f9f9;
    color: #757575;
    font-style: italic;
    border-bottom: 1px dashed #ddd;
}
